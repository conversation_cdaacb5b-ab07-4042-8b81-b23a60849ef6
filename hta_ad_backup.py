# -*- coding: utf-8 -*-
# HTA-AD: Hourglass Temporal Autoencoder for Anomaly Detection
# A model that combines the strengths of CNN-based downsampling for efficiency
# and TCN-based feature extraction for performance.

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import os, sys
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import warnings
from torch.nn.utils import weight_norm

warnings.filterwarnings('ignore')

# --- TSB-AD Imports ---
try:
    from TSB_AD.models.base import BaseDetector
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from TSB_AD.models.base import BaseDetector

# --- TCN Components (from gru_tcn_ae.py) ---
class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        self.net = nn.Sequential(self.conv1, self.relu1, self.dropout1,
                                 self.conv2, self.relu2, self.dropout2)
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)

class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            layers.append(TemporalBlock(in_channels, out_channels, kernel_size, stride=1, dilation=dilation_size,
                                        padding=0, dropout=dropout))
        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

# ----------------------------------------------------
# 1. HTA_Model Definition
# ----------------------------------------------------
class HTA_Model(nn.Module):
    def __init__(self, input_dim, window_size, latent_dim, tcn_channels, tcn_kernel_size=3, cnn_channels=16, downsample_stride=2):
        super(HTA_Model, self).__init__()
        
        # --- Encoder ---
        # 1. CNN Downsampler
        self.encoder_cnn = nn.Sequential(
            nn.Conv1d(input_dim, cnn_channels, kernel_size=7, padding=3, stride=downsample_stride),
            nn.GELU()
        )
        
        # Calculate shape after CNN
        with torch.no_grad():
            dummy_input = torch.zeros(1, input_dim, window_size)
            cnn_output_shape = self.encoder_cnn(dummy_input).shape
            self.downsampled_len = cnn_output_shape[2]

        # 2. Lightweight TCN
        self.encoder_tcn = TemporalConvNet(
            num_inputs=cnn_channels,
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size
        )

        # 3. Bottleneck
        self.fc_encode = nn.Linear(tcn_channels[-1] * self.downsampled_len, latent_dim)

        # --- Decoder ---
        # 1. Expand from Bottleneck
        self.decoder_fc = nn.Linear(latent_dim, tcn_channels[-1] * self.downsampled_len)
        
        # 2. Lightweight Inverse TCN
        self.decoder_tcn = TemporalConvNet(
            num_inputs=tcn_channels[-1],
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size
        )
        
        # 3. CNN Upsampler
        self.decoder_cnn = nn.Sequential(
            nn.ConvTranspose1d(tcn_channels[-1], input_dim, kernel_size=7, padding=3, stride=downsample_stride, output_padding=downsample_stride-1),
        )
        self.output_activation = nn.Sigmoid()
        self.tcn_output_channels = tcn_channels[-1]
        self.window_size = window_size


    def forward(self, x):
        # x shape: (batch_size, window_size, input_dim)
        x_permuted = x.permute(0, 2, 1) # -> (batch, input_dim, window_size)

        # === Encoder ===
        encoded_cnn = self.encoder_cnn(x_permuted)
        encoded_tcn = self.encoder_tcn(encoded_cnn)
        encoded_flat = encoded_tcn.flatten(start_dim=1)
        latent_vec = self.fc_encode(encoded_flat)
        
        # === Decoder ===
        decoded_flat = self.decoder_fc(latent_vec)
        decoded_unflat = decoded_flat.view(-1, self.tcn_output_channels, self.downsampled_len)
        decoded_tcn = self.decoder_tcn(decoded_unflat)
        reconstructed_permuted = self.decoder_cnn(decoded_tcn)
        
        # Adjust size if needed (due to padding/stride)
        if reconstructed_permuted.shape[2] != self.window_size:
             reconstructed_permuted = F.interpolate(reconstructed_permuted, size=self.window_size, mode='linear', align_corners=False)

        reconstructed = reconstructed_permuted.permute(0, 2, 1)
        
        return self.output_activation(reconstructed)


# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class HTA_AD(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        # Hyperparameters
        self.window_size = HP.get('window_size', 128)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 32)
        
        print(f"🔄 Initializing HTA-AD Detector... (Device: {self.device})")
        
        self.model = None # Lazy initialization in fit()
        self.ts_scaler = MinMaxScaler()
        self.score_scaler = MinMaxScaler()
        self.criterion = nn.MSELoss()
        self.training_history = {}

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def fit(self, X, y=None):
        input_dim = X.shape[1]
        
        if self.model is None:
            self.model = HTA_Model(
                input_dim=input_dim,
                window_size=self.window_size,
                latent_dim=self.latent_dim,
                tcn_channels=self.HP.get('tcn_channels', [32, 32, 32]),
                cnn_channels=self.HP.get('cnn_channels', 16),
                downsample_stride=self.HP.get('downsample_stride', 2)
            ).to(self.device)

        X_original_for_scoring = X
        if self.normalize:
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        if len(windows) == 0:
            self.decision_scores_ = np.zeros(X_original_for_scoring.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        optimizer = optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        
        self.model.train()
        epoch_losses = []
        for epoch in range(self.epochs):
            batch_losses = []
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                reconstructed = self.model(batch_windows)
                loss = self.criterion(reconstructed, batch_windows)
                loss.backward()
                optimizer.step()
                batch_losses.append(loss.item())
            
            epoch_loss = np.mean(batch_losses) if batch_losses else 0
            epoch_losses.append(epoch_loss)
        
        self.training_history = {'loss': epoch_losses}
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self

    def decision_function(self, X):
        return self._compute_scores(X, fit_scaler=False)
    
    def _compute_scores(self, X, fit_scaler=False):
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed = self.model(batch_windows)
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(np.array(window_scores)):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        if n_samples > self.window_size -1:
             scores_mapped[:self.window_size-1] = scores_mapped[self.window_size-1]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel() 