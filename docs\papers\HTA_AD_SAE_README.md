# HTA-AD with Sparse Autoencoder (SAE) Integration

## 🎯 Overview

HTA-AD with SAE integration provides **interpretable time series anomaly detection** by combining the powerful temporal modeling capabilities of HTA-AD with the feature attribution capabilities of Sparse Autoencoders. This implementation supports both **univariate and multivariate** time series data.

## 🚀 Key Features

- **Interpretable Anomaly Detection**: Understand WHY anomalies were detected
- **Feature Attribution**: Identify which temporal patterns triggered the detection
- **Latent Space Purification**: Remove irrelevant features to improve performance
- **Universal Architecture**: Works with both univariate and multivariate data
- **TSB-AD Integration**: Fully compatible with TSB-AD benchmark framework
- **Pretrained SAE Support**: Use pretrained models for better performance

## 📁 File Structure

```
TSB-AD/TSB_AD/models/HTA_AD_SAE.py    # Main SAE-enhanced HTA-AD model
sae_pretraining_script.py             # SAE pretraining script
hta_ad_sae_example.py                 # Usage examples
HTA_AD_SAE_README.md                  # This documentation
```

## 🔧 Installation & Setup

1. **Install Dependencies**:
```bash
pip install torch numpy pandas scikit-learn tqdm
```

2. **Set up TSB-AD** (if not already done):
```bash
git clone https://github.com/johnpaparrizos/TSB-AD.git
cd TSB-AD
# Follow TSB-AD setup instructions
```

3. **Copy SAE Files**:
```bash
# Copy HTA_AD_SAE.py to TSB-AD models directory
cp HTA_AD_SAE.py TSB-AD/TSB_AD/models/

# The model wrapper is already updated to include HTA_AD_SAE
```

## 📊 Usage Examples

### 1. Basic Usage (No Pretraining)

```python
import sys
sys.path.append('TSB-AD')
from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE
import numpy as np

# Create or load your data
train_data = np.random.randn(1000, 3)  # Multivariate example
test_data = np.random.randn(500, 3)

# Configure model
HP = {
    'window_size': 128,
    'epochs': 30,
    'lr': 1e-3,
    'batch_size': 64,
    'latent_dim': 32,
    'tcn_channels': [32, 32, 32],
    'cnn_channels': 16,
    'downsample_stride': 2,
    'gpu': 0  # Use GPU if available
}

sae_config = {
    'hidden_dim': 128,
    'sparsity_weight': 0.01,
    'purification_strength': 0.5
}

# Initialize and train
detector = HTA_AD_SAE(HP=HP, normalize=True, sae_config=sae_config)
detector.fit(train_data)

# Get anomaly scores
scores = detector.decision_function(test_data)

# Get feature attribution for interpretability
attribution = detector.get_feature_attribution(test_data, top_k=5)
```

### 2. Using TSB-AD Model Wrapper

```python
from TSB_AD.model_wrapper import run_Semisupervise_AD

# Run with default parameters
scores = run_Semisupervise_AD('HTA_AD_SAE', train_data, test_data)

# Run with custom parameters
scores = run_Semisupervise_AD(
    'HTA_AD_SAE', 
    train_data, 
    test_data,
    window_size=128,
    epochs=30,
    sae_hidden_dim=128,
    purification_strength=0.5,
    pretrained_sae_path='pretrained_sae.pth'  # Optional
)
```

### 3. With Pretrained SAE

```python
# First, run pretraining (see next section)
# Then use the pretrained model
detector = HTA_AD_SAE(
    HP=HP, 
    normalize=True, 
    sae_config=sae_config,
    pretrained_sae_path='pretrained_sae.pth'
)
```

## 🧠 SAE Pretraining

### Step 1: Run Pretraining Script

```bash
# Basic pretraining on both univariate and multivariate data
python sae_pretraining_script.py \
    --data_dir TSB-AD/Datasets \
    --data_type both \
    --max_datasets 50 \
    --epochs 50 \
    --save_path pretrained_sae.pth

# Univariate only
python sae_pretraining_script.py \
    --data_type univariate \
    --hidden_dim 128 \
    --sparsity_weight 0.01

# Multivariate only  
python sae_pretraining_script.py \
    --data_type multivariate \
    --hidden_dim 256 \
    --epochs 100
```

### Step 2: Use Pretrained Model

The pretrained SAE will automatically be loaded when you specify the path:

```python
detector = HTA_AD_SAE(
    HP=HP,
    normalize=True,
    sae_config=sae_config,
    pretrained_sae_path='pretrained_sae.pth'
)
```

## 🔍 Interpretability Features

### Feature Attribution

```python
# Get feature attribution for anomaly explanation
attribution = detector.get_feature_attribution(test_data, top_k=10)

# Access results
print(f"Feature dimension: {attribution['feature_dim']}")
print(f"Number of windows: {len(attribution['top_features'])}")

# For each window, get top activated features
for i, window_features in enumerate(attribution['top_features'][:5]):
    print(f"Window {i}:")
    for j, (feature_idx, activation) in enumerate(zip(
        window_features['indices'], 
        window_features['values']
    )):
        print(f"  Feature #{feature_idx}: {activation:.4f}")
```

### Pattern Types

The SAE learns to identify four main temporal pattern types:

1. **Spike Patterns**: Sudden amplitude changes
2. **Level Shift Patterns**: Persistent state transitions  
3. **Oscillatory Patterns**: Periodic behaviors
4. **Discontinuity Patterns**: Temporal phase shifts

## ⚙️ Configuration Parameters

### HTA-AD Parameters (HP)
- `window_size`: Time series window size (default: 128)
- `epochs`: Training epochs (default: 30)
- `lr`: Learning rate (default: 1e-3)
- `batch_size`: Batch size (default: 64)
- `latent_dim`: Latent space dimension (default: 32)
- `tcn_channels`: TCN channel configuration (default: [32, 32, 32])
- `cnn_channels`: CNN channels (default: 16)
- `downsample_stride`: Downsampling stride (default: 2)

### SAE Parameters (sae_config)
- `hidden_dim`: SAE hidden dimension (default: 128)
- `sparsity_weight`: Sparsity regularization weight (default: 0.01)
- `purification_strength`: Latent purification strength (default: 0.5)

### Pretraining Parameters
- `data_type`: 'univariate', 'multivariate', or 'both'
- `max_datasets`: Maximum datasets to use for pretraining
- `epochs`: SAE training epochs
- `lr`: SAE learning rate
- `batch_size`: SAE batch size

## 📈 Performance Benefits

1. **Interpretability**: Understand anomaly detection decisions
2. **Improved Accuracy**: Latent space purification removes noise
3. **Generalization**: Pretrained SAE works across domains
4. **Efficiency**: Shared architecture for univariate/multivariate data

## 🔬 Technical Details

### Architecture
- **HTA-AD**: Hourglass Temporal Autoencoder for reconstruction-based anomaly detection
- **SAE**: Sparse Autoencoder for interpretable feature learning (32→128→32 dimensions)
- **Integration**: SAE processes HTA-AD latent vectors for purification and attribution

### Purification Process
1. Extract latent vector from HTA-AD encoder
2. Pass through SAE to get sparse feature activations
3. Identify and mask irrelevant features
4. Subtract irrelevant contributions from original latent vector
5. Use purified latent vector for reconstruction

### Feature Attribution
1. Get SAE feature activations for input windows
2. Rank features by activation strength
3. Map top features to learned temporal patterns
4. Provide interpretable explanations

## 🚨 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch_size or window_size
2. **Poor Performance**: Try different purification_strength values (0.1-0.8)
3. **No Pretrained SAE**: Run pretraining script first
4. **Import Errors**: Ensure TSB-AD is in Python path

### Performance Tips

1. **Use Pretrained SAE**: Significantly improves performance
2. **Tune Purification Strength**: Start with 0.3-0.5
3. **Adjust Sparsity Weight**: Higher values = sparser features
4. **GPU Acceleration**: Set gpu=0 for faster training

## 📚 References

- Original HTA-AD paper: [Link to paper]
- Sparse Autoencoder theory: Olshausen & Field (1997)
- TSB-AD benchmark: Paparrizos et al. (2022)

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve the SAE integration!
