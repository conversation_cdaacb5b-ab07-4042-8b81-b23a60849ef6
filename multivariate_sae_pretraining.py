#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多变量SAE预训练
为多变量时间序列数据预训练SAE模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
import os
import sys
from sklearn.preprocessing import MinMaxScaler

# 添加TSB-AD路径
sys.path.append('TSB-AD')
from TSB_AD.models.HTA_AD import HTA_AD

class MultivariateSAE(nn.Module):
    """多变量SAE模型"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 更深的编码器，适应多变量复杂性
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim * 3),
            nn.<PERSON>L<PERSON>(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU()
        )
        
        # 对应的解码器
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 3, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, input_dim)
        )
        
    def forward(self, x):
        activations = self.encoder(x)
        reconstructed = self.decoder(activations)
        return reconstructed, activations

def load_multivariate_datasets():
    """加载多变量数据集进行预训练"""
    print("📊 加载多变量数据集用于SAE预训练...")
    
    multivariate_datasets = [
        'TSB-AD/Datasets/TSB-AD-M/001_Genesis_id_1_Sensor_tr_4055_1st_15538.csv',
        'TSB-AD/Datasets/TSB-AD-M/002_MSL_id_1_Sensor_tr_500_1st_900.csv',
        'TSB-AD/Datasets/TSB-AD-M/003_MSL_id_2_Sensor_tr_883_1st_1238.csv',
        'TSB-AD/Datasets/TSB-AD-M/004_MSL_id_3_Sensor_tr_530_1st_630.csv',
        'TSB-AD/Datasets/TSB-AD-M/005_MSL_id_4_Sensor_tr_855_1st_2700.csv',
        'TSB-AD/Datasets/TSB-AD-M/006_MSL_id_5_Sensor_tr_1150_1st_1250.csv',
        'TSB-AD/Datasets/TSB-AD-M/007_MSL_id_6_Sensor_tr_980_1st_3550.csv',
        'TSB-AD/Datasets/TSB-AD-M/008_MSL_id_7_Sensor_tr_656_1st_1630.csv',
        'TSB-AD/Datasets/TSB-AD-M/009_MSL_id_8_Sensor_tr_714_1st_1390.csv',
        'TSB-AD/Datasets/TSB-AD-M/010_MSL_id_9_Sensor_tr_554_1st_1172.csv',
        'TSB-AD/Datasets/TSB-AD-M/011_MSL_id_10_Sensor_tr_1525_1st_4590.csv',
        'TSB-AD/Datasets/TSB-AD-M/012_MSL_id_11_Sensor_tr_539_1st_940.csv',
        'TSB-AD/Datasets/TSB-AD-M/013_MSL_id_12_Sensor_tr_554_1st_1200.csv',
        'TSB-AD/Datasets/TSB-AD-M/014_MSL_id_13_Sensor_tr_1525_1st_4575.csv',
        'TSB-AD/Datasets/TSB-AD-M/015_MSL_id_14_Sensor_tr_575_1st_1250.csv',
        'TSB-AD/Datasets/TSB-AD-M/016_MSL_id_15_Sensor_tr_500_1st_780.csv',
        'TSB-AD/Datasets/TSB-AD-M/017_MSL_id_16_Sensor_tr_512_1st_1850.csv',
        'TSB-AD/Datasets/TSB-AD-M/018_Daphnet_id_1_HumanActivity_tr_9693_1st_20732.csv',
    ]
    
    all_latent_vectors = []
    total_samples = 0
    successful_datasets = 0
    
    # HTA-AD配置
    hp_config = {
        'window_size': 128,
        'epochs': 30,
        'lr': 1e-3,
        'batch_size': 64,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2
    }
    
    for i, dataset_path in enumerate(multivariate_datasets, 1):
        if not os.path.exists(dataset_path):
            print(f"  跳过 {i}/{len(multivariate_datasets)}: 文件不存在")
            continue
        
        try:
            print(f"  加载 {i}/{len(multivariate_datasets)}: {os.path.basename(dataset_path)}")
            
            # 加载数据
            df = pd.read_csv(dataset_path)
            
            # 处理标签列
            if 'label' in df.columns:
                data = df.drop('label', axis=1).values
            elif 'Label' in df.columns:
                data = df.drop('Label', axis=1).values
            elif 'anomaly' in df.columns:
                data = df.drop('anomaly', axis=1).values
            else:
                data = df.iloc[:, :-1].values
            
            # 数据质量检查
            if data.shape[0] < 500 or data.shape[1] < 2:
                print(f"    ⚠️  数据质量不符合要求，跳过")
                continue
            
            # 只使用训练部分（前70%）
            train_size = int(len(data) * 0.7)
            train_data = data[:train_size]
            
            print(f"    ✅ 成功加载 {train_data.shape[0]} 个样本, {train_data.shape[1]} 个特征")
            
            # 训练HTA-AD获取潜在向量
            model = HTA_AD(hp_config)
            model.fit(train_data)
            
            # 提取潜在向量
            latent_vectors = extract_latent_vectors(model, train_data)
            
            if latent_vectors is not None:
                all_latent_vectors.append(latent_vectors)
                total_samples += len(latent_vectors)
                successful_datasets += 1
            
        except Exception as e:
            print(f"    ❌ 加载失败: {e}")
            continue
    
    if len(all_latent_vectors) == 0:
        print("❌ 没有成功加载任何数据集")
        return None
    
    # 合并所有潜在向量
    combined_latent_vectors = np.vstack(all_latent_vectors)
    
    print(f"✅ 多变量预训练数据加载完成:")
    print(f"   成功数据集: {successful_datasets}/{len(multivariate_datasets)}")
    print(f"   总样本数: {total_samples:,}")
    print(f"   潜在向量维度: {combined_latent_vectors.shape}")
    
    return combined_latent_vectors

def extract_latent_vectors(hta_ad_model, data):
    """从HTA-AD模型提取潜在向量"""
    try:
        # 创建窗口
        windows = hta_ad_model._create_windows(data)
        if len(windows) == 0:
            return None
        
        # 批量处理
        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=64, shuffle=False)
        
        hta_ad_model.model.eval()
        latent_vectors = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(hta_ad_model.device)
                
                # HTA-AD编码过程
                x_permuted = batch_windows.permute(0, 2, 1)
                encoded_cnn = hta_ad_model.model.encoder_cnn(x_permuted)
                encoded_tcn = hta_ad_model.model.encoder_tcn(encoded_cnn)
                encoded_flat = encoded_tcn.flatten(start_dim=1)
                latent_vec = hta_ad_model.model.fc_encode(encoded_flat)
                
                latent_vectors.append(latent_vec.cpu().numpy())
        
        return np.vstack(latent_vectors)
        
    except Exception as e:
        print(f"    ⚠️  潜在向量提取失败: {e}")
        return None

def train_multivariate_sae(latent_vectors, epochs=50, lr=1e-3):
    """训练多变量SAE"""
    print(f"\n🚀 开始多变量SAE预训练...")
    print(f"📊 训练数据: {latent_vectors.shape}")

    # 数据归一化 - 这很重要！
    print(f"🔧 数据预处理...")
    print(f"   原始数据范围: [{latent_vectors.min():.6f}, {latent_vectors.max():.6f}]")
    print(f"   原始数据均值: {latent_vectors.mean():.6f}, 标准差: {latent_vectors.std():.6f}")

    scaler = MinMaxScaler(feature_range=(-1, 1))
    latent_vectors_normalized = scaler.fit_transform(latent_vectors)

    print(f"   归一化后范围: [{latent_vectors_normalized.min():.6f}, {latent_vectors_normalized.max():.6f}]")
    print(f"   归一化后均值: {latent_vectors_normalized.mean():.6f}, 标准差: {latent_vectors_normalized.std():.6f}")

    # 创建SAE模型
    input_dim = latent_vectors_normalized.shape[1]
    sae_model = MultivariateSAE(input_dim=input_dim, hidden_dim=128)

    # 训练配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    sae_model = sae_model.to(device)

    optimizer = torch.optim.Adam(sae_model.parameters(), lr=lr)
    criterion = nn.MSELoss()

    # 准备数据
    tensor_data = torch.FloatTensor(latent_vectors_normalized).to(device)
    dataset = torch.utils.data.TensorDataset(tensor_data)
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=256, shuffle=True)
    
    print(f"🔧 训练配置:")
    print(f"   设备: {device}")
    print(f"   输入维度: {input_dim}")
    print(f"   隐藏维度: 128")
    print(f"   训练轮数: {epochs}")
    print(f"   学习率: {lr}")
    
    # 训练循环
    sae_model.train()
    for epoch in range(epochs):
        total_loss = 0
        num_batches = 0
        
        for (batch_data,) in dataloader:
            optimizer.zero_grad()
            
            reconstructed, _ = sae_model(batch_data)
            loss = criterion(reconstructed, batch_data)
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / num_batches
        
        if (epoch + 1) % 10 == 0:
            print(f"  Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
    
    print(f"✅ 多变量SAE预训练完成")

    return sae_model, scaler

def save_multivariate_sae(sae_model, scaler, filepath='multivariate_pretrained_sae.pth'):
    """保存多变量SAE模型和归一化器"""
    import pickle

    # 保存模型状态
    torch.save(sae_model.state_dict(), filepath)

    # 保存归一化器
    scaler_filepath = filepath.replace('.pth', '_scaler.pkl')
    with open(scaler_filepath, 'wb') as f:
        pickle.dump(scaler, f)

    print(f"💾 多变量预训练SAE已保存到: {filepath}")
    print(f"💾 归一化器已保存到: {scaler_filepath}")

def analyze_multivariate_sae(sae_model, sample_size=10000):
    """分析多变量SAE学到的特征"""
    print(f"\n🔍 分析多变量SAE特征...")
    
    sae_model.eval()
    device = next(sae_model.parameters()).device
    
    # 生成测试样本
    with torch.no_grad():
        # 正常模式样本
        normal_samples = torch.randn(sample_size//2, sae_model.input_dim).to(device) * 0.5
        
        # 异常模式样本
        anomaly_samples = torch.randn(sample_size//2, sae_model.input_dim).to(device) * 2.0 + torch.randn(1, sae_model.input_dim).to(device) * 3.0
        
        all_samples = torch.cat([normal_samples, anomaly_samples], dim=0)
        sample_labels = np.concatenate([np.zeros(sample_size//2), np.ones(sample_size//2)])
        
        reconstructed, activations = sae_model(all_samples)
        reconstruction_errors = F.mse_loss(reconstructed, all_samples, reduction='none').mean(dim=1)
    
    activations = activations.cpu().numpy()
    reconstruction_errors = reconstruction_errors.cpu().numpy()
    
    # 分析特征
    feature_analysis = []
    
    for i in range(activations.shape[1]):
        feature_acts = activations[:, i]
        normal_acts = feature_acts[sample_labels == 0]
        anomaly_acts = feature_acts[sample_labels == 1]
        
        analysis = {
            'feature_id': i,
            'activation_rate': np.mean(feature_acts > 0.01),
            'mean_activation': np.mean(feature_acts),
            'std_activation': np.std(feature_acts),
            'discriminative_power': abs(np.mean(normal_acts) - np.mean(anomaly_acts)),
            'sparsity': np.mean(feature_acts == 0),
        }
        
        feature_analysis.append(analysis)
    
    df_features = pd.DataFrame(feature_analysis)
    
    # 识别无关特征
    irrelevant_features = df_features[
        (df_features['discriminative_power'] < df_features['discriminative_power'].quantile(0.3)) &
        ((df_features['activation_rate'] < 0.02) | (df_features['activation_rate'] > 0.98))
    ]
    
    print(f"📊 多变量SAE特征分析:")
    print(f"   总特征数: {len(df_features)}")
    print(f"   高区分性特征: {len(df_features[df_features['discriminative_power'] > df_features['discriminative_power'].quantile(0.8)])}")
    print(f"   稀疏特征: {len(df_features[df_features['activation_rate'] < 0.05])}")
    print(f"   无关特征: {len(irrelevant_features)}")
    
    return irrelevant_features['feature_id'].tolist()

def main():
    """主函数"""
    print("🚀 多变量SAE预训练")
    print("=" * 80)
    
    # 加载多变量数据集
    latent_vectors = load_multivariate_datasets()
    
    if latent_vectors is None:
        print("❌ 预训练失败：无法加载数据")
        return
    
    # 训练SAE
    sae_model, scaler = train_multivariate_sae(latent_vectors)

    # 保存模型
    save_multivariate_sae(sae_model, scaler)
    
    # 分析特征
    irrelevant_indices = analyze_multivariate_sae(sae_model)
    
    print(f"\n🎉 多变量SAE预训练完成！")
    print(f"💡 现在可以在多变量数据集上使用预训练的SAE进行异常检测")
    print(f"📁 模型文件: multivariate_pretrained_sae.pth")
    
    return sae_model, scaler, irrelevant_indices

if __name__ == "__main__":
    main()
