#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify HTA-AD integration with TSB-AD benchmark
"""

import numpy as np
import pandas as pd
import sys
import os

# Add TSB-AD to path
sys.path.insert(0, 'TSB-AD')

def test_hta_ad_integration():
    """Test HTA-AD integration with TSB-AD"""
    
    print("🧪 Testing HTA-AD Integration with TSB-AD...")
    
    try:
        # Test 1: Import the model wrapper
        print("\n1. Testing model wrapper import...")
        from TSB_AD.model_wrapper import run_Semisupervise_AD, Semisupervise_AD_Pool
        
        # Check if HTA_AD is in the pool
        if 'HTA_AD' in Semisupervise_AD_Pool:
            print("✅ HTA_AD found in Semisupervise_AD_Pool")
        else:
            print("❌ HTA_AD not found in Semisupervise_AD_Pool")
            return False
            
        # Test 2: Import the model directly
        print("\n2. Testing direct model import...")
        from TSB_AD.models.HTA_AD import HTA_AD
        print("✅ HTA_AD model imported successfully")
        
        # Test 3: Create synthetic data for testing
        print("\n3. Creating synthetic test data...")
        np.random.seed(42)
        
        # Create synthetic time series data
        n_samples_train = 1000
        n_samples_test = 500
        n_features = 3
        
        # Generate normal data with some patterns
        t_train = np.linspace(0, 10, n_samples_train)
        t_test = np.linspace(10, 15, n_samples_test)
        
        # Training data (normal)
        data_train = np.zeros((n_samples_train, n_features))
        for i in range(n_features):
            data_train[:, i] = np.sin(2 * np.pi * (i + 1) * t_train) + 0.1 * np.random.randn(n_samples_train)
        
        # Test data (with some anomalies)
        data_test = np.zeros((n_samples_test, n_features))
        for i in range(n_features):
            data_test[:, i] = np.sin(2 * np.pi * (i + 1) * t_test) + 0.1 * np.random.randn(n_samples_test)
        
        # Add some anomalies to test data
        anomaly_indices = [100, 200, 300, 400]
        for idx in anomaly_indices:
            data_test[idx:idx+10, :] += 3.0  # Add anomalies
        
        print(f"✅ Created training data: {data_train.shape}")
        print(f"✅ Created test data: {data_test.shape}")
        
        # Test 4: Test direct model usage
        print("\n4. Testing direct model usage...")
        HP = {
            'window_size': 64,
            'epochs': 5,  # Small number for quick test
            'lr': 1e-3,
            'batch_size': 32,
            'latent_dim': 16,
            'tcn_channels': [16, 16],
            'cnn_channels': 8,
            'downsample_stride': 2,
            'gpu': 0
        }
        
        clf = HTA_AD(HP=HP, normalize=True)
        clf.fit(data_train)
        scores = clf.decision_function(data_test)
        
        print(f"✅ Model training completed")
        print(f"✅ Anomaly scores computed: shape={scores.shape}, range=[{scores.min():.4f}, {scores.max():.4f}]")
        
        # Test 5: Test via model wrapper
        print("\n5. Testing via model wrapper...")
        scores_wrapper = run_Semisupervise_AD(
            'HTA_AD', 
            data_train, 
            data_test,
            window_size=64,
            epochs=5,
            batch_size=32,
            latent_dim=16
        )
        
        print(f"✅ Model wrapper test completed")
        print(f"✅ Wrapper scores computed: shape={scores_wrapper.shape}, range=[{scores_wrapper.min():.4f}, {scores_wrapper.max():.4f}]")
        
        # Test 6: Verify scores are reasonable
        print("\n6. Verifying anomaly detection performance...")
        
        # Check if anomaly scores are higher at anomaly locations
        normal_scores = []
        anomaly_scores = []
        
        for i in range(len(scores_wrapper)):
            if any(abs(i - idx) < 10 for idx in anomaly_indices):
                anomaly_scores.append(scores_wrapper[i])
            else:
                normal_scores.append(scores_wrapper[i])
        
        if len(anomaly_scores) > 0 and len(normal_scores) > 0:
            mean_normal = np.mean(normal_scores)
            mean_anomaly = np.mean(anomaly_scores)
            
            print(f"   Mean normal score: {mean_normal:.4f}")
            print(f"   Mean anomaly score: {mean_anomaly:.4f}")
            
            if mean_anomaly > mean_normal:
                print("✅ Anomaly scores are higher than normal scores (good!)")
            else:
                print("⚠️  Anomaly scores are not clearly higher than normal scores")
        
        print("\n🎉 All tests passed! HTA-AD is successfully integrated with TSB-AD.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_benchmark_usage():
    """Test using HTA-AD in benchmark-style evaluation"""
    
    print("\n\n🏁 Testing Benchmark-Style Usage...")
    
    try:
        # Import evaluation utilities
        from TSB_AD.model_wrapper import run_Semisupervise_AD
        
        # Create a simple dataset
        np.random.seed(123)
        n_samples = 500
        n_features = 2
        
        # Generate data with clear anomalies
        data = np.random.randn(n_samples, n_features) * 0.5
        
        # Add clear anomalies
        anomaly_indices = [100, 200, 300, 400]
        for idx in anomaly_indices:
            data[idx:idx+5] += 4.0
        
        # Split into train/test
        split_point = int(0.7 * n_samples)
        data_train = data[:split_point]
        data_test = data[split_point:]
        
        print(f"Training data: {data_train.shape}")
        print(f"Test data: {data_test.shape}")
        
        # Run HTA-AD
        scores = run_Semisupervise_AD(
            'HTA_AD',
            data_train,
            data_test,
            window_size=32,
            epochs=10,
            lr=1e-3,
            batch_size=16
        )
        
        print(f"✅ Benchmark-style evaluation completed")
        print(f"   Scores shape: {scores.shape}")
        print(f"   Score statistics: min={scores.min():.4f}, max={scores.max():.4f}, mean={scores.mean():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Benchmark test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("HTA-AD Integration Test Suite")
    print("=" * 60)
    
    # Run integration tests
    success1 = test_hta_ad_integration()
    success2 = test_benchmark_usage()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED! HTA-AD is ready for TSB-AD benchmark.")
    else:
        print("❌ Some tests failed. Please check the integration.")
    print("=" * 60)
