#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LERN v2.3 全数据集基准测试脚本
在所有TSB-AD-U数据集(870个)上运行LERN模型并计算平均性能
"""

import os
import sys
import pandas as pd
import numpy as np
import torch
import random
import time
import logging
import glob
import json
from datetime import datetime
from pathlib import Path
import argparse
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.utils.slidingWindows import find_length_rank
from sklearn.preprocessing import MinMaxScaler

# 导入LERN检测器
from Run_LERN_Detector import run_LERN_AD_Unsupervised

def set_seed(seed=2024):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True

def setup_logging(save_dir):
    """设置日志系统"""
    log_file = os.path.join(save_dir, f'lern_full_benchmark_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def get_dataset_files(dataset_dir, pattern='*.csv', max_files=None):
    """获取数据集文件列表"""
    files = glob.glob(os.path.join(dataset_dir, pattern))
    files = sorted([os.path.basename(f) for f in files])
    
    if max_files:
        files = files[:max_files]
    
    return files

def run_single_dataset(filename, dataset_dir, logger, hp_config):
    """在单个数据集上运行LERN"""
    dataset_info = {
        'filename': filename,
        'status': 'FAILED',
        'error': '',
        'run_time': 0,
        'data_shape': '',
        'anomaly_ratio': 0,
        'train_size': 0
    }
    
    try:
        logger.info(f"🚀 开始处理: {filename}")
        
        # 加载数据
        file_path = os.path.join(dataset_dir, filename)
        df = pd.read_csv(file_path).dropna()
        data = df.iloc[:, 0:-1].values.astype(float)
        label = df['Label'].astype(int).to_numpy()
        
        # 数据信息
        dataset_info.update({
            'data_shape': str(data.shape),
            'anomaly_ratio': np.sum(label) / len(label),
            'data_length': len(data)
        })
        
        # 获取训练集分割点（从文件名解析）
        try:
            train_index = int(filename.split('_tr_')[1].split('_')[0])
            dataset_info['train_size'] = train_index
        except:
            # 如果无法解析，使用默认分割
            train_index = len(data) // 2
            dataset_info['train_size'] = train_index
            logger.warning(f"无法解析训练集大小，使用默认值: {train_index}")
        
        logger.info(f"   数据形状: {data.shape}, 异常率: {dataset_info['anomaly_ratio']:.3f}, 训练集: {train_index}")
        
        # 运行LERN
        start_time = time.time()
        
        # 调用LERN检测器
        result = run_LERN_AD_Unsupervised(data, **hp_config)
        
        if isinstance(result, tuple) and len(result) == 2:
            anomaly_scores, training_history = result
        else:
            anomaly_scores = result
            training_history = None
        
        end_time = time.time()
        run_time = end_time - start_time
        
        # 数据预处理
        scores_normalized = MinMaxScaler(feature_range=(0,1)).fit_transform(
            anomaly_scores.reshape(-1,1)
        ).ravel()
        
        # 计算slidingWindow用于评估
        slidingWindow = find_length_rank(data, rank=1)
        
        # 计算阈值和预测
        threshold = np.mean(scores_normalized) + 3 * np.std(scores_normalized)
        predictions = scores_normalized > threshold
        
        # 评估性能
        evaluation_result = get_metrics(
            scores_normalized, label, 
            slidingWindow=slidingWindow, 
            pred=predictions
        )
        
        # 更新结果
        dataset_info.update({
            'status': 'SUCCESS',
            'run_time': run_time,
            'threshold': threshold,
            **evaluation_result
        })
        
        # 如果有训练历史，添加训练信息
        if training_history:
            dataset_info.update({
                'total_epochs': training_history.get('total_epochs', 0),
                'best_epoch': training_history.get('best_epoch', 0),
                'final_loss': training_history.get('final_loss', 0),
                'best_loss': training_history.get('best_loss', 0)
            })
        
        logger.info(f"   ✅ 成功! 时间: {run_time:.1f}s, AUC-ROC: {evaluation_result.get('AUC-ROC', 0):.4f}, AUC-PR: {evaluation_result.get('AUC-PR', 0):.4f}")
        
    except Exception as e:
        error_msg = str(e)[:200]  # 限制错误信息长度
        dataset_info.update({
            'status': 'FAILED',
            'error': error_msg,
            'run_time': time.time() - start_time if 'start_time' in locals() else 0
        })
        logger.error(f"   ❌ 失败: {error_msg}")
        
        # 添加默认的评估指标（避免后续统计出错）
        default_metrics = {
            'AUC-ROC': 0, 'AUC-PR': 0, 'VUS-ROC': 0, 'VUS-PR': 0,
            'Standard-F1': 0, 'PA-F1': 0, 'Affiliation-F': 0
        }
        dataset_info.update(default_metrics)
    
    return dataset_info

def calculate_summary_statistics(results_df):
    """计算汇总统计信息"""
    # 筛选成功的结果
    successful_results = results_df[results_df['status'] == 'SUCCESS']
    
    if len(successful_results) == 0:
        return {"error": "没有成功的测试结果"}
    
    # 主要性能指标
    performance_metrics = [
        'AUC-ROC', 'AUC-PR', 'VUS-ROC', 'VUS-PR', 
        'Standard-F1', 'PA-F1', 'Affiliation-F'
    ]
    
    summary = {
        'total_datasets': len(results_df),
        'successful_datasets': len(successful_results),
        'success_rate': len(successful_results) / len(results_df),
        'total_runtime': successful_results['run_time'].sum(),
        'avg_runtime': successful_results['run_time'].mean(),
    }
    
    # 计算各指标的统计信息
    for metric in performance_metrics:
        if metric in successful_results.columns:
            values = pd.to_numeric(successful_results[metric], errors='coerce').dropna()
            if len(values) > 0:
                summary[f'{metric}_mean'] = values.mean()
                summary[f'{metric}_std'] = values.std()
                summary[f'{metric}_median'] = values.median()
                summary[f'{metric}_min'] = values.min()
                summary[f'{metric}_max'] = values.max()
    
    # 按数据集类型分组统计（从文件名提取）
    def extract_dataset_type(filename):
        try:
            return filename.split('_')[3]  # NAB格式: 001_NAB_id_1_Facility_tr_xxx
        except:
            return 'Unknown'
    
    successful_results['dataset_type'] = successful_results['filename'].apply(extract_dataset_type)
    type_summary = {}
    
    for dtype in successful_results['dataset_type'].unique():
        type_data = successful_results[successful_results['dataset_type'] == dtype]
        type_summary[dtype] = {
            'count': len(type_data),
            'avg_auc_roc': type_data['AUC-ROC'].mean() if 'AUC-ROC' in type_data.columns else 0,
            'avg_auc_pr': type_data['AUC-PR'].mean() if 'AUC-PR' in type_data.columns else 0
        }
    
    summary['by_dataset_type'] = type_summary
    
    return summary

def save_results(results, summary, save_dir):
    """保存结果到文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细结果
    results_df = pd.DataFrame(results)
    results_file = os.path.join(save_dir, f'lern_full_benchmark_results_{timestamp}.csv')
    results_df.to_csv(results_file, index=False)
    
    # 保存汇总统计
    summary_file = os.path.join(save_dir, f'lern_full_benchmark_summary_{timestamp}.json')
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
    
    # 保存最新结果的符号链接
    latest_results = os.path.join(save_dir, 'latest_results.csv')
    latest_summary = os.path.join(save_dir, 'latest_summary.json')
    
    if os.path.exists(latest_results):
        os.remove(latest_results)
    if os.path.exists(latest_summary):
        os.remove(latest_summary)
    
    os.symlink(os.path.basename(results_file), latest_results)
    os.symlink(os.path.basename(summary_file), latest_summary)
    
    return results_file, summary_file

def print_summary_report(summary, logger):
    """打印汇总报告"""
    logger.info("\n" + "="*80)
    logger.info("🏆 LERN v2.3 全数据集基准测试报告")
    logger.info("="*80)
    
    logger.info(f"📊 测试概况:")
    logger.info(f"   总数据集: {summary['total_datasets']}")
    logger.info(f"   成功测试: {summary['successful_datasets']}")
    logger.info(f"   成功率: {summary['success_rate']:.2%}")
    logger.info(f"   总运行时间: {summary['total_runtime']:.1f}s ({summary['total_runtime']/3600:.1f}h)")
    logger.info(f"   平均运行时间: {summary['avg_runtime']:.1f}s")
    
    logger.info(f"\n📈 性能指标 (平均值):")
    key_metrics = ['AUC-ROC', 'AUC-PR', 'VUS-ROC', 'VUS-PR', 'Affiliation-F']
    for metric in key_metrics:
        mean_key = f'{metric}_mean'
        std_key = f'{metric}_std'
        if mean_key in summary:
            logger.info(f"   {metric}: {summary[mean_key]:.4f} ± {summary.get(std_key, 0):.4f}")
    
    if 'by_dataset_type' in summary:
        logger.info(f"\n📂 按数据集类型统计:")
        for dtype, stats in summary['by_dataset_type'].items():
            logger.info(f"   {dtype}: {stats['count']}个数据集, "
                       f"平均AUC-ROC: {stats['avg_auc_roc']:.4f}, "
                       f"平均AUC-PR: {stats['avg_auc_pr']:.4f}")

def main():
    parser = argparse.ArgumentParser(description='LERN v2.3 全数据集基准测试')
    parser.add_argument('--dataset_dir', type=str, default='../Datasets/TSB-AD-U/', 
                       help='数据集目录')
    parser.add_argument('--save_dir', type=str, default='./lern_full_benchmark_results/', 
                       help='结果保存目录')
    parser.add_argument('--max_files', type=int, default=None, 
                       help='最大处理文件数量 (用于测试)')
    parser.add_argument('--start_from', type=int, default=0,
                       help='从第几个文件开始处理 (用于断点续传)')
    parser.add_argument('--batch_size', type=int, default=50,
                       help='批次大小，每处理多少个文件保存一次中间结果')
    parser.add_argument('--gpu', type=str, default='0',
                       help='使用的GPU编号')
    args = parser.parse_args()
    
    # 设置GPU
    os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu
    
    # 设置随机种子
    set_seed(2024)
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 设置日志
    logger = setup_logging(args.save_dir)
    
    # LERN超参数配置
    hp_config = {
        'window_size': 50,
        'epochs': 100,
        'lr': 0.001,
        'batch_size': 64,
        'latent_dim': 64,
        'poly_degree': 3,
        'patience': 10
    }
    
    logger.info("🚀 开始LERN v2.3 全数据集基准测试")
    logger.info(f"💻 使用GPU: {args.gpu}")
    logger.info(f"📁 数据集目录: {args.dataset_dir}")
    logger.info(f"💾 结果保存到: {args.save_dir}")
    logger.info(f"🔧 超参数配置: {hp_config}")
    
    # 获取数据集文件列表
    files = get_dataset_files(args.dataset_dir, max_files=args.max_files)
    total_files = len(files)
    
    # 应用start_from参数
    if args.start_from > 0:
        files = files[args.start_from:]
        logger.info(f"📋 从第 {args.start_from} 个文件开始，剩余 {len(files)} 个文件")
    
    logger.info(f"📂 总共需要处理 {len(files)} 个数据集")
    
    # 批量处理
    all_results = []
    start_time = time.time()
    
    try:
        # 使用tqdm显示进度条
        pbar = tqdm(files, desc="处理数据集", unit="dataset")
        
        for i, filename in enumerate(pbar):
            current_idx = args.start_from + i
            pbar.set_description(f"[{current_idx+1}/{args.start_from + len(files)}] {filename[:30]}...")
            
            # 运行单个数据集
            result = run_single_dataset(filename, args.dataset_dir, logger, hp_config)
            all_results.append(result)
            
            # 更新进度条后缀信息
            if result['status'] == 'SUCCESS':
                auc_roc = result.get('AUC-ROC', 0)
                pbar.set_postfix({
                    '成功': len([r for r in all_results if r['status'] == 'SUCCESS']),
                    '失败': len([r for r in all_results if r['status'] == 'FAILED']),
                    'AUC-ROC': f"{auc_roc:.3f}"
                })
            
            # 批次保存中间结果
            if (i + 1) % args.batch_size == 0:
                current_df = pd.DataFrame(all_results)
                temp_file = os.path.join(args.save_dir, f'temp_results_batch_{i//args.batch_size + 1}.csv')
                current_df.to_csv(temp_file, index=False)
                logger.info(f"💾 已保存批次 {i//args.batch_size + 1} 的中间结果 ({i+1}/{len(files)})")
    
    except KeyboardInterrupt:
        logger.warning("⚠️ 用户中断测试，保存当前结果...")
    
    except Exception as e:
        logger.error(f"💥 测试过程中发生异常: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    
    finally:
        # 无论如何都保存结果
        if all_results:
            total_time = time.time() - start_time
            logger.info(f"⏱️ 总测试时间: {total_time:.1f}s ({total_time/3600:.1f}h)")
            
            # 计算汇总统计
            summary = calculate_summary_statistics(pd.DataFrame(all_results))
            summary['total_test_time'] = total_time
            summary['test_timestamp'] = datetime.now().isoformat()
            summary['hp_config'] = hp_config
            
            # 保存结果
            results_file, summary_file = save_results(all_results, summary, args.save_dir)
            
            # 打印报告
            print_summary_report(summary, logger)
            
            logger.info(f"\n📁 结果文件:")
            logger.info(f"   详细结果: {results_file}")
            logger.info(f"   汇总统计: {summary_file}")
            logger.info(f"   最新结果: {os.path.join(args.save_dir, 'latest_results.csv')}")
            
            # 显示最佳性能数据集
            results_df = pd.DataFrame(all_results)
            successful_df = results_df[results_df['status'] == 'SUCCESS']
            
            if len(successful_df) > 0:
                logger.info(f"\n🏅 性能最佳的数据集:")
                
                # AUC-ROC最佳
                best_auc_roc = successful_df.loc[successful_df['AUC-ROC'].idxmax()]
                logger.info(f"   AUC-ROC最佳: {best_auc_roc['filename']} (AUC-ROC: {best_auc_roc['AUC-ROC']:.4f})")
                
                # AUC-PR最佳
                best_auc_pr = successful_df.loc[successful_df['AUC-PR'].idxmax()]
                logger.info(f"   AUC-PR最佳: {best_auc_pr['filename']} (AUC-PR: {best_auc_pr['AUC-PR']:.4f})")
        
        else:
            logger.error("❌ 没有任何测试结果")

if __name__ == "__main__":
    main()