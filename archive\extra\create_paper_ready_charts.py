#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建适合学术论文双栏布局的完美一致图表
专门解决Figure 2的视觉不一致问题
"""

import matplotlib.pyplot as plt
import numpy as np
import os

# 针对学术论文优化的全局设置
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 10
plt.rcParams['figure.dpi'] = 300
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['grid.linewidth'] = 0.6
plt.rcParams['axes.labelweight'] = 'bold'

def create_paper_ready_charts():
    """创建完美适配学术论文双栏布局的图表"""
    
    # 学术论文双栏布局优化参数
    FIGURE_HEIGHT = 4.0      # 适合双栏的紧凑高度
    FIGURE_WIDTH_BASE = 0.32  # 基础宽度比例
    Y_MAX = 0.50            # 统一y轴最大值，便于比较
    Y_TICKS = np.arange(0, 0.51, 0.1)  # 统一y轴刻度
    BAR_WIDTH = 0.7         # 紧凑的柱子宽度
    
    # 字体大小设置（适合论文印刷）
    AXIS_LABEL_SIZE = 11    # 轴标签
    TICK_LABEL_SIZE = 8     # 刻度标签
    VALUE_LABEL_SIZE = 7    # 数值标签
    
    # 统一颜色方案
    HTA_COLOR = '#D32F2F'    # 更深的红色，印刷效果更好
    OTHER_COLOR = '#616161'  # 更深的灰色，对比度更好
    GRID_COLOR = '#E0E0E0'   # 浅灰色网格
    
    # 数据定义
    uni_methods = ['HTA_AD', 'Sub-PCA', 'KShapeAD', 'POI-GPD', 'SampledDCNN', 'MSCRED', 
                   'MSCRED(FT)', 'NormalizingFlow', 'USAD', 'Sub-LOF', 'AutoEncoder', 'STAMP', 
                   'CNN', 'LSTMED', 'IForest', 'TimesNet', 'Donut', 'RobustPCA', 'Telemanom', 
                   'AutoRegression', 'AutoLSTM', 'TranAD', 'FITS', 'Sub-HBOS', 'EFA', 
                   'Sub-KNN', 'Sub-OCSVM', 'Sub-LOF2', 'Sub-IForest2', 'LOF', 'AnomalyTransformer']
    
    uni_values = [0.44, 0.42, 0.40, 0.39, 0.39, 0.39, 0.38, 0.37, 0.36, 0.35, 0.35, 0.34, 
                  0.34, 0.33, 0.32, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 0.26, 0.25, 
                  0.24, 0.24, 0.23, 0.23, 0.22, 0.20, 0.12]
    
    multi_methods = ['HTA_AD', 'LSTMED', 'OmniAnomaly', 'CNN', 'PCA', 'USAD', 'AutoEncoder', 
                     'KMeansAD', 'CBLOF', 'MCD', 'OCSVM', 'Donut', 'RobustPCA', 'DIF', 
                     'EFA', 'FITS', 'ConvTAD', 'Telemanom', 'HBOS', 'TimesNet', 'KNN', 
                     'TranAD', 'LOF', 'AnomalyTransformer']
    
    # 关键修正：确保多变量HTA-AD为0.39
    multi_values = [0.39, 0.31, 0.31, 0.31, 0.31, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 
                    0.24, 0.21, 0.21, 0.21, 0.20, 0.20, 0.19, 0.19, 0.18, 0.18, 0.14, 0.12]
    
    # 计算图表尺寸
    uni_width = len(uni_methods) * FIGURE_WIDTH_BASE
    multi_width = len(multi_methods) * FIGURE_WIDTH_BASE
    
    print(f"📐 优化后图表尺寸:")
    print(f"   单变量: {uni_width:.1f} × {FIGURE_HEIGHT} 英寸")
    print(f"   多变量: {multi_width:.1f} × {FIGURE_HEIGHT} 英寸")
    
    # 通用图表设置函数
    def setup_axis(ax, methods, values, title_suffix):
        """设置轴的通用样式"""
        # 创建柱状图
        colors = [HTA_COLOR if method == 'HTA_AD' else OTHER_COLOR for method in methods]
        bars = ax.bar(range(len(methods)), values, color=colors, alpha=0.9, 
                     width=BAR_WIDTH, edgecolor='white', linewidth=0.8)
        
        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, values)):
            height = bar.get_height()
            color = HTA_COLOR if methods[i] == 'HTA_AD' else '#424242'
            weight = 'bold' if methods[i] == 'HTA_AD' else 'normal'
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.008,
                   f'{value:.2f}', ha='center', va='bottom', 
                   fontsize=VALUE_LABEL_SIZE, fontweight=weight, color=color)
        
        # 设置轴标签和范围
        ax.set_ylabel('VUS-PR Score', fontsize=AXIS_LABEL_SIZE, fontweight='bold')
        ax.set_xlabel('Method', fontsize=AXIS_LABEL_SIZE, fontweight='bold')
        ax.set_ylim(0, Y_MAX)
        ax.set_xlim(-0.5, len(methods) - 0.5)
        
        # 设置刻度
        ax.set_yticks(Y_TICKS)
        ax.set_yticklabels([f'{tick:.1f}' for tick in Y_TICKS], fontsize=TICK_LABEL_SIZE)
        ax.set_xticks(range(len(methods)))
        ax.set_xticklabels(methods, rotation=45, ha='right', fontsize=TICK_LABEL_SIZE)
        
        # 设置网格和背景
        ax.grid(True, alpha=0.4, axis='y', linestyle='-', color=GRID_COLOR)
        ax.set_axisbelow(True)
        ax.set_facecolor('#FAFAFA')
        
        # 设置边框
        for spine in ax.spines.values():
            spine.set_linewidth(1.2)
            spine.set_color('#CCCCCC')
        
        return bars
    
    # === 创建单变量图表 ===
    fig1, ax1 = plt.subplots(figsize=(uni_width, FIGURE_HEIGHT))
    bars1 = setup_axis(ax1, uni_methods, uni_values, 'Univariate')
    
    plt.tight_layout(pad=0.5)
    plt.savefig('figures/bar_chart_tsb-ad-u.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none', pad_inches=0.1)
    plt.close()
    
    # === 创建多变量图表 ===
    fig2, ax2 = plt.subplots(figsize=(multi_width, FIGURE_HEIGHT))
    bars2 = setup_axis(ax2, multi_methods, multi_values, 'Multivariate')
    
    plt.tight_layout(pad=0.5)
    plt.savefig('figures/bar_chart_tsb-ad-m.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none', pad_inches=0.1)
    plt.close()
    
    return uni_width, multi_width

def create_latex_figure_code():
    """生成LaTeX图表代码，便于论文中使用"""
    
    latex_code = """
% 在论文中使用以下LaTeX代码来插入并排图表
\\begin{figure*}[htbp]
    \\centering
    \\begin{subfigure}[b]{0.48\\textwidth}
        \\centering
        \\includegraphics[width=\\textwidth]{figures/bar_chart_tsb-ad-u.png}
        \\caption{Univariate benchmark (TSB-AD-U)}
        \\label{fig:tsb-ad-u}
    \\end{subfigure}
    \\hfill
    \\begin{subfigure}[b]{0.48\\textwidth}
        \\centering
        \\includegraphics[width=\\textwidth]{figures/bar_chart_tsb-ad-m.png}
        \\caption{Multivariate benchmark (TSB-AD-M)}
        \\label{fig:tsb-ad-m}
    \\end{subfigure}
    \\caption{Performance comparison on TSB-AD benchmark datasets. HTA-AD achieves the highest VUS-PR scores of 0.44 (univariate) and 0.39 (multivariate), demonstrating superior anomaly detection capability across both scenarios.}
    \\label{fig:tsb-ad-results}
\\end{figure*}
"""
    
    with open('figures/latex_figure_code.tex', 'w', encoding='utf-8') as f:
        f.write(latex_code.strip())
    
    print("📝 LaTeX代码已生成：figures/latex_figure_code.tex")

if __name__ == "__main__":
    print("🎯 创建学术论文级别的完美一致图表...")
    print("=" * 65)
    
    # 创建输出目录
    os.makedirs('figures', exist_ok=True)
    
    # 生成优化图表
    uni_width, multi_width = create_paper_ready_charts()
    
    # 生成LaTeX代码
    create_latex_figure_code()
    
    print("\n✅ 图表生成完成！")
    print(f"✅ 统一高度: 4.0英寸 (适合双栏布局)")
    print(f"✅ 统一y轴: 0-0.50 (便于性能比较)")
    print(f"✅ HTA-AD多变量: 0.39 (已修正)")
    print(f"✅ 字体优化: 适合学术印刷")
    
    print("\n" + "=" * 65)
    print("🎉 学术论文图表优化完成！")
    print("\n📋 关键特性:")
    print("   🔸 完全一致的高度和y轴范围")
    print("   🔸 优化的字体大小，适合双栏布局")
    print("   🔸 专业的颜色方案，印刷效果佳")
    print("   🔸 统一的网格和边框样式")
    print("   🔸 紧凑的布局，节省版面空间")
    print("\n📁 输出文件:")
    print("   📊 figures/bar_chart_tsb-ad-u.png")
    print("   📊 figures/bar_chart_tsb-ad-m.png") 
    print("   📝 figures/latex_figure_code.tex")
    print("\n💡 现在两个图表在论文中并排显示时将完美一致！")
