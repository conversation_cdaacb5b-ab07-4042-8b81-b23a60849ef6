#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复图表中的小数位数显示，统一保留两位小数
"""

import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体和样式
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 12
plt.rcParams['figure.dpi'] = 300

def create_multivariate_chart():
    """创建多变量基准测试图表，统一保留两位小数"""
    
    # 多变量数据 - 将0.387改为0.39，其他数值保留两位小数
    methods = ['HTA_AD', 'LSTMED', 'OmniAnomaly', 'CNN', 'PCA', 'USAD', 'AutoEncoder', 
               'KMeansAD', 'CBLOF', 'MCD', 'OCSVM', 'Donut', 'RobustPCA', 'DIF', 
               'EFA', 'FITS', 'ConvTAD', 'Telemanom', 'HBOS', 'TimesNet', 'KNN', 
               'TranAD', 'LOF', 'AnomalyTransformer']
    
    # 原始数值，将0.387修改为0.39
    values = [0.39, 0.31, 0.31, 0.31, 0.31, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 
              0.24, 0.21, 0.21, 0.21, 0.20, 0.20, 0.19, 0.19, 0.18, 0.18, 0.14, 0.12]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(14, 6))
    
    # 创建柱状图，HTA-AD用红色突出显示
    colors = ['#E74C3C' if method == 'HTA_AD' else '#7F8C8D' for method in methods]
    bars = ax.bar(methods, values, color=colors, alpha=0.8)
    
    # 在每个柱子上添加数值标签，统一保留两位小数
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                f'{value:.2f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 设置图表样式
    ax.set_ylabel('VUS-PR Score', fontsize=13, fontweight='bold')
    ax.set_xlabel('Method', fontsize=13, fontweight='bold')
    ax.set_ylim(0, 0.45)
    
    # 旋转x轴标签以避免重叠
    plt.xticks(rotation=45, ha='right')
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('figures/bar_chart_tsb-ad-m_fixed.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 多变量图表已生成，HTA-AD数值已修正为0.39")

def create_univariate_chart():
    """创建单变量基准测试图表，统一保留两位小数"""
    
    # 单变量数据
    methods = ['HTA_AD', 'Sub-PCA', 'KShapeAD', 'POI-GPD', 'SampledDCNN', 'MSCRED', 
               'MSCRED (FT)', 'NormalizingFlow', 'USAD', 'Sub-LOF', 'AutoEncoder', 
               'STAMP', 'CNN', 'LSTMED', 'IForest', 'TimesNet', 'Donut', 'RobustPCA', 
               'Telemanom', 'AutoRegression', 'TimesNet', 'AutoLSTM', 'TranAD', 'FITS', 
               'Sub-HBOS', 'EFA', 'Sub-KNN', 'Sub-OCSVM', 'Sub-LOF', 'Sub-IForest', 
               'Donut', 'LOF', 'AnomalyTransformer']
    
    values = [0.44, 0.42, 0.40, 0.39, 0.39, 0.39, 0.38, 0.37, 0.36, 0.35, 0.35, 0.34, 
              0.34, 0.33, 0.32, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 0.26, 0.26, 
              0.25, 0.24, 0.24, 0.23, 0.23, 0.22, 0.20, 0.17, 0.12]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(16, 6))
    
    # 创建柱状图，HTA-AD用红色突出显示
    colors = ['#E74C3C' if method == 'HTA_AD' else '#7F8C8D' for method in methods]
    bars = ax.bar(methods, values, color=colors, alpha=0.8)
    
    # 在每个柱子上添加数值标签，统一保留两位小数
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                f'{value:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    # 设置图表样式
    ax.set_ylabel('VUS-PR Score', fontsize=13, fontweight='bold')
    ax.set_xlabel('Method', fontsize=13, fontweight='bold')
    ax.set_ylim(0, 0.50)
    
    # 旋转x轴标签以避免重叠
    plt.xticks(rotation=45, ha='right')
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('figures/bar_chart_tsb-ad-u_fixed.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 单变量图表已生成，所有数值统一保留两位小数")

if __name__ == "__main__":
    print("🔧 开始修复图表中的小数位数显示...")
    print("=" * 50)
    
    # 创建figures目录（如果不存在）
    import os
    os.makedirs('figures', exist_ok=True)
    
    # 生成修正后的图表
    create_multivariate_chart()
    print()
    create_univariate_chart()
    
    print("\n" + "=" * 50)
    print("✅ 图表修复完成！")
    print("📁 生成的文件:")
    print("   📊 figures/bar_chart_tsb-ad-m_fixed.png (多变量，0.387→0.39)")
    print("   📊 figures/bar_chart_tsb-ad-u_fixed.png (单变量，统一两位小数)")
    print("\n💡 请将这两个文件替换原来的图表文件")
