#!/usr/bin/env python3
"""
测试HTA-AD与TSB-AD基准测试的集成
"""

import numpy as np
import sys
import os
import warnings
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

def generate_test_data(n_samples=1000, anomaly_ratio=0.1):
    """生成测试数据"""
    np.random.seed(42)
    
    # 生成正常数据 (正弦波 + 噪声)
    t = np.linspace(0, 4*np.pi, n_samples)
    data = np.sin(t) + 0.1 * np.random.randn(n_samples)
    
    # 添加异常
    n_anomalies = int(n_samples * anomaly_ratio)
    anomaly_indices = np.random.choice(n_samples, n_anomalies, replace=False)
    
    for idx in anomaly_indices:
        # 添加尖峰异常
        data[idx] += np.random.normal(2, 0.5)
    
    return data, anomaly_indices


def test_tsb_ad_wrapper():
    """测试TSB-AD包装器"""
    print("🧪 测试TSB-AD包装器")
    
    try:
        from TSB_AD.model_wrapper import run_Semisupervise_AD
        print("  ✅ TSB-AD包装器导入成功")
        
        # 生成测试数据
        data, anomaly_indices = generate_test_data(n_samples=500)
        
        # 分割训练和测试数据
        split_point = int(len(data) * 0.7)
        train_data = data[:split_point]
        test_data = data[split_point:]
        
        print(f"  📊 训练数据: {len(train_data)} 点")
        print(f"  📊 测试数据: {len(test_data)} 点")
        
        return train_data, test_data, True
        
    except ImportError as e:
        print(f"  ❌ TSB-AD包装器导入失败: {e}")
        return None, None, False


def test_hta_ad_basic():
    """测试基础HTA-AD"""
    print("\n🧪 测试基础HTA-AD")
    
    try:
        from TSB_AD.model_wrapper import run_HTA_AD
        
        # 生成测试数据
        train_data, test_data, success = test_tsb_ad_wrapper()
        if not success:
            return False
        
        # 运行HTA-AD
        print("  🏋️ 运行HTA-AD...")
        scores = run_HTA_AD(
            train_data, test_data,
            window_size=64,
            epochs=5,  # 减少epoch用于测试
            lr=1e-3,
            batch_size=32,
            latent_dim=16
        )
        
        print(f"  ✅ HTA-AD运行成功")
        print(f"  📊 异常分数形状: {scores.shape}")
        print(f"  📊 分数范围: [{scores.min():.4f}, {scores.max():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"  ❌ HTA-AD测试失败: {e}")
        return False


def test_hta_ad_sae():
    """测试HTA-AD-SAE"""
    print("\n🧪 测试HTA-AD-SAE")
    
    try:
        from TSB_AD.model_wrapper import run_HTA_AD_SAE
        
        # 生成测试数据
        train_data, test_data, success = test_tsb_ad_wrapper()
        if not success:
            return False
        
        # 运行HTA-AD-SAE
        print("  🏋️ 运行HTA-AD-SAE...")
        scores = run_HTA_AD_SAE(
            train_data, test_data,
            window_size=64,
            epochs=5,  # 减少epoch用于测试
            lr=1e-3,
            batch_size=32,
            latent_dim=16,
            sae_hidden_dim=64,
            sae_sparsity_weight=0.01
        )
        
        print(f"  ✅ HTA-AD-SAE运行成功")
        print(f"  📊 异常分数形状: {scores.shape}")
        print(f"  📊 分数范围: [{scores.min():.4f}, {scores.max():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"  ❌ HTA-AD-SAE测试失败: {e}")
        return False


def test_direct_model_usage():
    """测试直接使用模型类"""
    print("\n🧪 测试直接模型使用")
    
    try:
        from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE
        
        # 生成测试数据
        data, anomaly_indices = generate_test_data(n_samples=300)
        
        # 分割数据
        split_point = int(len(data) * 0.7)
        train_data = data[:split_point]
        test_data = data[split_point:]
        
        # 创建模型
        HP = {
            'window_size': 32,
            'epochs': 3,
            'lr': 1e-3,
            'batch_size': 16,
            'latent_dim': 8,
            'tcn_channels': [16, 16],
            'cnn_channels': 8,
            'downsample_stride': 2,
            'gpu': 0
        }
        
        sae_config = {
            'hidden_dim': 32,
            'sparsity_weight': 0.01
        }
        
        print("  🏗️ 创建HTA-AD-SAE模型...")
        model = HTA_AD_SAE(HP=HP, normalize=True, sae_config=sae_config)
        
        print("  🏋️ 训练模型...")
        model.fit(train_data)
        
        print("  🔍 计算异常分数...")
        scores = model.decision_function(test_data)
        
        print(f"  ✅ 直接模型使用成功")
        print(f"  📊 异常分数形状: {scores.shape}")
        print(f"  📊 分数范围: [{scores.min():.4f}, {scores.max():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 直接模型使用失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_benchmark_compatibility():
    """测试基准测试兼容性"""
    print("\n🧪 测试基准测试兼容性")
    
    try:
        # 检查模型是否在支持列表中
        from TSB_AD.model_wrapper import Semisupervise_AD_Pool
        
        hta_ad_in_pool = 'HTA_AD' in Semisupervise_AD_Pool
        hta_ad_sae_in_pool = 'HTA_AD_SAE' in Semisupervise_AD_Pool
        
        print(f"  📋 HTA_AD在支持列表中: {hta_ad_in_pool}")
        print(f"  📋 HTA_AD_SAE在支持列表中: {hta_ad_sae_in_pool}")
        
        if hta_ad_in_pool and hta_ad_sae_in_pool:
            print("  ✅ 基准测试兼容性检查通过")
            return True
        else:
            print("  ⚠️ 部分模型不在支持列表中")
            return False
            
    except Exception as e:
        print(f"  ❌ 基准测试兼容性检查失败: {e}")
        return False


def run_integration_tests():
    """运行所有集成测试"""
    print("🚀 HTA-AD TSB-AD 集成测试")
    print("=" * 60)
    
    test_results = {}
    
    # 测试1: TSB-AD包装器
    success = test_tsb_ad_wrapper()[2]
    test_results['tsb_ad_wrapper'] = success
    
    # 测试2: 基础HTA-AD
    success = test_hta_ad_basic()
    test_results['hta_ad_basic'] = success
    
    # 测试3: HTA-AD-SAE
    success = test_hta_ad_sae()
    test_results['hta_ad_sae'] = success
    
    # 测试4: 直接模型使用
    success = test_direct_model_usage()
    test_results['direct_model'] = success
    
    # 测试5: 基准测试兼容性
    success = test_benchmark_compatibility()
    test_results['benchmark_compatibility'] = success
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 集成测试结果总结")
    print("=" * 60)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有集成测试通过！HTA-AD已成功集成到TSB-AD中。")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
    
    return test_results


if __name__ == '__main__':
    results = run_integration_tests()
