#!/usr/bin/env python3
"""
Two-Stage Training Manager for Paper-Compliant HTA-AD + SAE
Implements the exact training procedure described in the paper
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
from typing import Dict, List, Tuple, Optional
import os
from tqdm import tqdm
import matplotlib.pyplot as plt

from ..models.hta_ad_paper_compliant import HTAADBasic, PostHocSAE


class TwoStageTrainer:
    """
    Two-stage trainer following the paper's methodology:
    Stage 1: Train HTA-AD on reconstruction task
    Stage 2: Collect latent vectors and train SAE post-hoc
    """
    
    def __init__(self, 
                 hta_config: Dict = None,
                 sae_config: Dict = None,
                 device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        
        self.device = device
        
        # Default configurations from paper
        self.hta_config = hta_config or {
            'input_dim': 1,
            'd_model': 32,      # Paper: 32-dimensional latent vector
            'n_heads': 4,
            'n_layers': 2,
            'seq_len': 128,     # Paper: window size 128
            'dropout': 0.1
        }
        
        self.sae_config = sae_config or {
            'latent_dim': 32,           # Paper: input from HTA-AD
            'hidden_dim': 128,          # Paper: 128 interpretable features
            'sparsity_weight': 0.001    # Paper: λ = 0.001
        }
        
        # Initialize models
        self.hta_model = HTAADBasic(**self.hta_config).to(device)
        self.sae_model = PostHocSAE(**self.sae_config).to(device)
        
        # Storage for collected latent vectors
        self.collected_latents = []
        
        print(f"🔧 Two-Stage Trainer initialized")
        print(f"  - Device: {device}")
        print(f"  - HTA-AD: {sum(p.numel() for p in self.hta_model.parameters())} parameters")
        print(f"  - SAE: {sum(p.numel() for p in self.sae_model.parameters())} parameters")
    
    def stage1_train_hta(self, 
                        train_loader: DataLoader,
                        epochs: int = 30,      # Paper: 30 epochs
                        lr: float = 1e-3,      # Paper: 1e-3
                        save_path: str = None) -> Dict[str, List[float]]:
        """
        Stage 1: Train basic HTA-AD model
        """
        print("\n🚀 Stage 1: Training HTA-AD")
        print("=" * 50)
        
        # Setup optimizer
        optimizer = optim.Adam(self.hta_model.parameters(), lr=lr)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
        
        # Training history
        history = {'train_loss': [], 'reconstruction_loss': []}
        
        self.hta_model.train()
        
        for epoch in range(epochs):
            epoch_losses = []
            epoch_recon_losses = []
            
            pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
            
            for batch_idx, batch in enumerate(pbar):
                if isinstance(batch, (list, tuple)):
                    x = batch[0].to(self.device)
                else:
                    x = batch.to(self.device)
                
                # Forward pass
                optimizer.zero_grad()
                outputs = self.hta_model(x)
                losses = self.hta_model.compute_loss(outputs, x)
                
                # Backward pass
                losses['total'].backward()
                optimizer.step()
                
                # Track losses
                epoch_losses.append(losses['total'].item())
                epoch_recon_losses.append(losses['reconstruction'].item())
                
                # Update progress bar
                pbar.set_postfix({
                    'Loss': f"{losses['total'].item():.6f}",
                    'Recon': f"{losses['reconstruction'].item():.6f}"
                })
            
            # Epoch statistics
            avg_loss = np.mean(epoch_losses)
            avg_recon = np.mean(epoch_recon_losses)
            
            history['train_loss'].append(avg_loss)
            history['reconstruction_loss'].append(avg_recon)
            
            # Learning rate scheduling
            scheduler.step(avg_loss)
            
            print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}, Recon = {avg_recon:.6f}")
        
        # Save model if path provided
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            torch.save({
                'model_state_dict': self.hta_model.state_dict(),
                'config': self.hta_config,
                'history': history
            }, save_path)
            print(f"💾 HTA-AD model saved to {save_path}")
        
        print("✅ Stage 1 completed!")
        return history
    
    def collect_latent_vectors(self, 
                              data_loader: DataLoader,
                              max_samples: int = 4_000_000) -> torch.Tensor:  # Paper: 4 million vectors
        """
        Collect latent vectors from trained HTA-AD for SAE training
        """
        print(f"\n📊 Collecting latent vectors (target: {max_samples:,})")
        print("=" * 50)
        
        self.hta_model.eval()
        collected_latents = []
        total_collected = 0
        
        with torch.no_grad():
            pbar = tqdm(data_loader, desc="Collecting latents")
            
            for batch in pbar:
                if total_collected >= max_samples:
                    break
                
                if isinstance(batch, (list, tuple)):
                    x = batch[0].to(self.device)
                else:
                    x = batch.to(self.device)
                
                # Get latent vectors
                outputs = self.hta_model(x)
                latents = outputs['latent_vectors']  # [batch, seq_len, d_model]
                
                # Flatten to individual latent vectors
                batch_size, seq_len, d_model = latents.shape
                latents_flat = latents.view(-1, d_model)  # [batch*seq_len, d_model]
                
                collected_latents.append(latents_flat.cpu())
                total_collected += latents_flat.shape[0]
                
                pbar.set_postfix({'Collected': f"{total_collected:,}"})
        
        # Concatenate all collected latents
        all_latents = torch.cat(collected_latents, dim=0)
        
        # Limit to max_samples if exceeded
        if len(all_latents) > max_samples:
            indices = torch.randperm(len(all_latents))[:max_samples]
            all_latents = all_latents[indices]
        
        print(f"✅ Collected {len(all_latents):,} latent vectors of shape {all_latents.shape}")
        
        self.collected_latents = all_latents
        return all_latents
    
    def stage2_train_sae(self,
                        latent_vectors: torch.Tensor = None,
                        epochs: int = 100,
                        lr: float = 1e-3,
                        batch_size: int = 1024,
                        save_path: str = None) -> Dict[str, List[float]]:
        """
        Stage 2: Train SAE on collected latent vectors
        """
        print(f"\n🧠 Stage 2: Training SAE")
        print("=" * 50)
        
        # Use provided latents or collected ones
        if latent_vectors is None:
            if len(self.collected_latents) == 0:
                raise ValueError("No latent vectors available. Run collect_latent_vectors first.")
            latent_vectors = self.collected_latents
        
        # Create data loader for SAE training
        dataset = TensorDataset(latent_vectors)
        sae_loader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # Setup optimizer
        optimizer = optim.Adam(self.sae_model.parameters(), lr=lr)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        # Training history
        history = {'total_loss': [], 'reconstruction_loss': [], 'sparsity_loss': []}
        
        self.sae_model.train()
        
        for epoch in range(epochs):
            epoch_total = []
            epoch_recon = []
            epoch_sparse = []
            
            pbar = tqdm(sae_loader, desc=f"SAE Epoch {epoch+1}/{epochs}")
            
            for batch in pbar:
                z = batch[0].to(self.device)
                
                # Forward pass
                optimizer.zero_grad()
                losses = self.sae_model.compute_loss(z)
                
                # Backward pass
                losses['total'].backward()
                optimizer.step()
                
                # Track losses
                epoch_total.append(losses['total'].item())
                epoch_recon.append(losses['reconstruction'].item())
                epoch_sparse.append(losses['sparsity'].item())
                
                # Update progress bar
                pbar.set_postfix({
                    'Total': f"{losses['total'].item():.6f}",
                    'Recon': f"{losses['reconstruction'].item():.6f}",
                    'Sparse': f"{losses['sparsity'].item():.6f}"
                })
            
            # Epoch statistics
            avg_total = np.mean(epoch_total)
            avg_recon = np.mean(epoch_recon)
            avg_sparse = np.mean(epoch_sparse)
            
            history['total_loss'].append(avg_total)
            history['reconstruction_loss'].append(avg_recon)
            history['sparsity_loss'].append(avg_sparse)
            
            # Learning rate scheduling
            scheduler.step(avg_total)
            
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}: Total = {avg_total:.6f}, Recon = {avg_recon:.6f}, Sparse = {avg_sparse:.6f}")
        
        # Identify irrelevant features after training
        print("\n🔍 Identifying irrelevant features...")
        self.sae_model.eval()
        with torch.no_grad():
            # Sample some latent vectors for feature analysis
            sample_size = min(10000, len(latent_vectors))
            sample_latents = latent_vectors[:sample_size].to(self.device)
            _, feature_activations = self.sae_model(sample_latents)
            
            # Identify irrelevant features
            self.sae_model.identify_irrelevant_features(feature_activations)
        
        # Save model if path provided
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            torch.save({
                'model_state_dict': self.sae_model.state_dict(),
                'config': self.sae_config,
                'history': history,
                'irrelevant_mask': self.sae_model.irrelevant_mask
            }, save_path)
            print(f"💾 SAE model saved to {save_path}")
        
        print("✅ Stage 2 completed!")
        return history
    
    def full_training_pipeline(self,
                              train_loader: DataLoader,
                              hta_epochs: int = 30,
                              sae_epochs: int = 100,
                              save_dir: str = "models/") -> Dict[str, Dict]:
        """
        Complete two-stage training pipeline
        """
        print("🏗️ Starting Full Two-Stage Training Pipeline")
        print("=" * 60)
        
        # Stage 1: Train HTA-AD
        hta_history = self.stage1_train_hta(
            train_loader, 
            epochs=hta_epochs,
            save_path=os.path.join(save_dir, "hta_ad_basic.pth")
        )
        
        # Collect latent vectors
        latent_vectors = self.collect_latent_vectors(train_loader)
        
        # Stage 2: Train SAE
        sae_history = self.stage2_train_sae(
            latent_vectors,
            epochs=sae_epochs,
            save_path=os.path.join(save_dir, "post_hoc_sae.pth")
        )
        
        print("\n🎉 Full pipeline completed!")
        
        return {
            'hta_history': hta_history,
            'sae_history': sae_history
        }
