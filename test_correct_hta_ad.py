#!/usr/bin/env python3
"""
Test the correct HTA-AD implementation based on backup file
"""

import torch
import sys
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
sys.path.append('.')
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

from core.models.hta_ad_correct import HTAADCorrect, PostHocSAE

try:
    from TSB_AD.evaluation.metrics import get_metrics
    TSB_AD_AVAILABLE = True
except ImportError:
    TSB_AD_AVAILABLE = False
    print("TSB-AD not available, using dummy metrics")

def create_sliding_windows(data, window_size, stride=1):
    """Create sliding windows from time series data"""
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    n_samples, n_features = data.shape
    windows = []
    
    for i in range(0, n_samples - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def test_correct_hta_ad():
    """Test the correct HTA-AD implementation"""
    print("🧪 Testing Correct HTA-AD Implementation")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Test dataset
    dataset_path = "TSB-AD/Datasets/TSB-AD-U/140_MSL_id_1_Sensor_tr_530_1st_630.csv"
    
    if not os.path.exists(dataset_path):
        print("❌ Test dataset not found, using synthetic data")
        # Generate synthetic data
        np.random.seed(42)
        n_points = 1130
        t = np.linspace(0, 10*np.pi, n_points)
        data = np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.randn(n_points)
        labels = np.zeros(n_points)
        # Add some anomalies
        anomaly_indices = [200, 400, 600, 800]
        for idx in anomaly_indices:
            if idx < n_points:
                data[idx-5:idx+5] += 2.0
                labels[idx-5:idx+5] = 1
        
        data = data.reshape(-1, 1)
        train_size = 530
    else:
        # Load real data
        df = pd.read_csv(dataset_path).dropna()
        data = df.iloc[:, 0:-1].values.astype(float)
        labels = df['Label'].astype(int).to_numpy()
        train_size = 530
    
    # Split data
    train_data = data[:train_size]
    test_data = data[train_size:]
    test_labels = labels[train_size:]
    
    print(f"Train data: {train_data.shape}")
    print(f"Test data: {test_data.shape}")
    print(f"Test anomaly ratio: {np.mean(test_labels):.3f}")
    
    # Normalize
    scaler = StandardScaler()
    train_data = scaler.fit_transform(train_data)
    test_data = scaler.transform(test_data)
    
    # Create models with correct architecture
    print("\n🏗️  Creating HTA-AD model (CNN+TCN architecture)...")
    model = HTAADCorrect(
        input_dim=1,
        window_size=100,  # Smaller for testing
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        tcn_kernel_size=3,
        cnn_channels=16,
        downsample_stride=2
    ).to(device)
    
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128,
        sparsity_penalty=1e-3
    ).to(device)
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"SAE parameters: {sum(p.numel() for p in sae.parameters()):,}")
    
    # Create training windows
    train_windows = create_sliding_windows(train_data, 100)
    train_windows = torch.FloatTensor(train_windows).to(device)
    
    print(f"Training windows shape: {train_windows.shape}")
    
    # Train HTA-AD
    print("\n🔧 Training HTA-AD (CNN+TCN)...")
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    model.train()
    
    for epoch in range(20):
        total_loss = 0
        batch_size = 32
        
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch)
            losses = model.compute_loss(outputs, batch)
            loss = losses['total']
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 5 == 0:
            avg_loss = total_loss / (len(train_windows) // batch_size + 1)
            print(f"  Epoch {epoch + 1}/20, Loss: {avg_loss:.4f}")
    
    # Collect latent vectors for SAE training
    print("\n🔧 Collecting latent vectors...")
    model.eval()
    all_latents = []
    
    with torch.no_grad():
        batch_size = 32
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            outputs = model(batch)
            latents = outputs['latent_vectors']
            all_latents.append(latents)
    
    all_latents = torch.cat(all_latents, dim=0)
    print(f"Collected {all_latents.shape[0]} latent vectors")
    
    # Train SAE
    print("\n🔧 Training SAE...")
    sae_optimizer = torch.optim.Adam(sae.parameters(), lr=0.001)
    sae.train()
    
    for epoch in range(15):
        total_loss = 0
        batch_size = 64
        
        for i in range(0, len(all_latents), batch_size):
            batch = all_latents[i:i + batch_size]
            
            sae_optimizer.zero_grad()
            losses = sae.compute_loss(batch)
            loss = losses['total']
            
            loss.backward()
            sae_optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 5 == 0:
            avg_loss = total_loss / (len(all_latents) // batch_size + 1)
            print(f"  SAE Epoch {epoch + 1}/15, Loss: {avg_loss:.4f}")
    
    # Identify irrelevant features (only dead features)
    print("\n🔍 Identifying irrelevant features...")
    sae.identify_irrelevant_features(all_latents, max_irrelevant_ratio=0.25, only_dead_features=True)
    
    # Test anomaly detection
    print("\n📊 Testing anomaly detection...")
    model.eval()
    sae.eval()
    
    # Test on a subset
    window_size = 100
    pad_size = window_size // 2
    padded_data = np.pad(test_data, ((pad_size, pad_size), (0, 0)), mode='edge')
    
    hta_scores = []
    sae_scores = []
    
    with torch.no_grad():
        for i in range(min(200, len(test_data))):  # Test first 200 points
            window = padded_data[i:i + window_size]
            window_tensor = torch.FloatTensor(window).unsqueeze(0).to(device)
            
            # HTA-AD forward pass
            outputs = model(window_tensor)
            reconstruction = outputs['reconstruction']
            latent_vectors = outputs['latent_vectors']
            
            # HTA-AD score (reconstruction error)
            hta_error = torch.mean((window_tensor - reconstruction) ** 2)
            hta_scores.append(hta_error.cpu().item())
            
            # SAE purified reconstruction
            z_purified = sae.purify_latent(latent_vectors)
            purified_reconstruction = model.decode(z_purified)
            sae_error = torch.mean((window_tensor - purified_reconstruction) ** 2)
            sae_scores.append(sae_error.cpu().item())
    
    hta_scores = np.array(hta_scores)
    sae_scores = np.array(sae_scores)
    
    print(f"\n📈 Score Statistics:")
    print(f"HTA-AD scores: mean={hta_scores.mean():.6f}, std={hta_scores.std():.6f}")
    print(f"SAE scores:    mean={sae_scores.mean():.6f}, std={sae_scores.std():.6f}")
    print(f"Score ratio:   {sae_scores.mean() / hta_scores.mean():.3f}")
    
    # Check if scores are reasonable
    if 0.5 < sae_scores.mean() / hta_scores.mean() < 2.0:
        print("✅ SAE scores are in reasonable range compared to HTA-AD!")
    else:
        print("⚠️  SAE scores may need adjustment")
    
    print("\n🎉 Correct HTA-AD implementation test completed!")
    print("Architecture: CNN (downsampling) + TCN (temporal features) + SAE (interpretability)")

if __name__ == "__main__":
    import os
    test_correct_hta_ad()
