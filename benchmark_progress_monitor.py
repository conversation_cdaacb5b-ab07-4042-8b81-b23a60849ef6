#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基准测试进度监控
实时监控基准测试进度并提供中间结果分析
"""

import pandas as pd
import numpy as np
import os
import time
import json
from datetime import datetime
import matplotlib.pyplot as plt

def monitor_progress():
    """监控基准测试进度"""
    print("📊 基准测试进度监控")
    print("=" * 80)
    
    results_file = 'full_tsb_ad_benchmark_results.csv'
    json_file = 'full_tsb_ad_benchmark_results.json'
    
    last_count = 0
    start_time = time.time()
    
    while True:
        try:
            # 检查CSV文件
            if os.path.exists(results_file):
                df = pd.read_csv(results_file)
                current_count = len(df)
                
                if current_count > last_count:
                    elapsed_time = time.time() - start_time
                    avg_time_per_dataset = elapsed_time / current_count if current_count > 0 else 0
                    estimated_total_time = avg_time_per_dataset * 1072
                    remaining_time = estimated_total_time - elapsed_time
                    
                    print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')}")
                    print(f"📈 进度: {current_count}/1072 ({current_count/1072*100:.1f}%)")
                    print(f"⏱️  平均耗时: {avg_time_per_dataset:.1f}s/数据集")
                    print(f"🕐 预计剩余: {remaining_time/3600:.1f}小时")
                    
                    # 简要统计
                    if current_count >= 5:  # 至少有5个结果才统计
                        improvements = df['improvement'].values
                        avg_improvement = np.mean(improvements)
                        positive_count = np.sum(improvements > 0)
                        
                        print(f"📊 当前统计:")
                        print(f"   平均改进: {avg_improvement:+.2f}%")
                        print(f"   有效改进: {positive_count}/{current_count} ({positive_count/current_count*100:.1f}%)")
                        
                        if positive_count > 0:
                            positive_improvements = improvements[improvements > 0]
                            print(f"   有效改进平均: {np.mean(positive_improvements):+.2f}%")
                            print(f"   最大改进: {np.max(improvements):+.2f}%")
                    
                    last_count = current_count
            
            time.sleep(30)  # 每30秒检查一次
            
        except KeyboardInterrupt:
            print("\n⏹️  监控停止")
            break
        except Exception as e:
            print(f"❌ 监控错误: {e}")
            time.sleep(30)

def analyze_intermediate_results():
    """分析中间结果"""
    results_file = 'full_tsb_ad_benchmark_results.csv'
    
    if not os.path.exists(results_file):
        print("❌ 结果文件不存在")
        return
    
    df = pd.read_csv(results_file)
    
    print(f"\n📊 中间结果分析 ({len(df)} 个数据集)")
    print("=" * 80)
    
    # 基本统计
    improvements = df['improvement'].values
    avg_improvement = np.mean(improvements)
    std_improvement = np.std(improvements)
    positive_count = np.sum(improvements > 0)
    
    print(f"📈 改进统计:")
    print(f"   平均改进: {avg_improvement:+.3f}% ± {std_improvement:.3f}%")
    print(f"   有效改进: {positive_count}/{len(df)} ({positive_count/len(df)*100:.1f}%)")
    print(f"   最大改进: {np.max(improvements):+.3f}%")
    print(f"   最小改进: {np.min(improvements):+.3f}%")
    
    # 按数据类型分析
    if 'type' in df.columns:
        print(f"\n📊 按数据类型分析:")
        for data_type in df['type'].unique():
            type_df = df[df['type'] == data_type]
            type_improvements = type_df['improvement'].values
            type_positive = np.sum(type_improvements > 0)
            
            print(f"   {data_type}:")
            print(f"     数据集数: {len(type_df)}")
            print(f"     平均改进: {np.mean(type_improvements):+.3f}%")
            print(f"     有效改进: {type_positive}/{len(type_df)} ({type_positive/len(type_df)*100:.1f}%)")
    
    # Top改进数据集
    print(f"\n🏆 Top 10 改进数据集:")
    top_improvements = df.nlargest(10, 'improvement')
    for _, row in top_improvements.iterrows():
        print(f"   {row['dataset']}: {row['improvement']:+.2f}% ({row['type']})")
    
    # 指标对比
    metrics = ['vus_pr', 'auc_roc', 'auc_pr', 'f1', 'precision', 'recall']
    print(f"\n📋 指标对比:")
    for metric in metrics:
        original_col = f'original_{metric}'
        best_col = f'best_{metric}'
        
        if original_col in df.columns and best_col in df.columns:
            original_avg = df[original_col].mean()
            best_avg = df[best_col].mean()
            improvement = (best_avg - original_avg) / original_avg * 100 if original_avg > 0 else 0
            
            print(f"   {metric.upper()}: {original_avg:.4f} → {best_avg:.4f} ({improvement:+.2f}%)")
    
    return df

def create_progress_visualization(df):
    """创建进度可视化"""
    if len(df) < 5:
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 改进分布直方图
    axes[0, 0].hist(df['improvement'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].axvline(0, color='red', linestyle='--', alpha=0.7)
    axes[0, 0].set_title('Improvement Distribution')
    axes[0, 0].set_xlabel('Improvement (%)')
    axes[0, 0].set_ylabel('Number of Datasets')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 按数据类型的改进对比
    if 'type' in df.columns:
        type_improvements = []
        type_labels = []
        for data_type in df['type'].unique():
            type_df = df[df['type'] == data_type]
            type_improvements.append(type_df['improvement'].values)
            type_labels.append(f"{data_type}\n(n={len(type_df)})")
        
        axes[0, 1].boxplot(type_improvements, labels=type_labels)
        axes[0, 1].axhline(0, color='red', linestyle='--', alpha=0.7)
        axes[0, 1].set_title('Improvement by Data Type')
        axes[0, 1].set_ylabel('Improvement (%)')
        axes[0, 1].grid(True, alpha=0.3)
    
    # 3. VUS-PR对比散点图
    if 'original_vus_pr' in df.columns and 'best_vus_pr' in df.columns:
        axes[1, 0].scatter(df['original_vus_pr'], df['best_vus_pr'], alpha=0.6)
        min_val = min(df['original_vus_pr'].min(), df['best_vus_pr'].min())
        max_val = max(df['original_vus_pr'].max(), df['best_vus_pr'].max())
        axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7)
        axes[1, 0].set_title('VUS-PR: Original vs Enhanced')
        axes[1, 0].set_xlabel('Original VUS-PR')
        axes[1, 0].set_ylabel('Enhanced VUS-PR')
        axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 处理时间分布
    if 'processing_time' in df.columns:
        axes[1, 1].hist(df['processing_time'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[1, 1].set_title('Processing Time Distribution')
        axes[1, 1].set_xlabel('Time (seconds)')
        axes[1, 1].set_ylabel('Number of Datasets')
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('benchmark_progress_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📁 可视化已保存为 'benchmark_progress_analysis.png'")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'monitor':
        monitor_progress()
    else:
        print("📊 基准测试结果分析")
        print("=" * 80)
        
        df = analyze_intermediate_results()
        
        if df is not None and len(df) > 0:
            create_progress_visualization(df)
            
            print(f"\n💡 使用方法:")
            print(f"   python benchmark_progress_monitor.py monitor  # 实时监控")
            print(f"   python benchmark_progress_monitor.py         # 分析当前结果")

if __name__ == "__main__":
    main()
