# Core ML and Data Science Libraries
# Note: PyTorch will be installed separately in setup script based on CUDA version
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
scipy>=1.7.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# TSB-AD specific dependencies
statsmodels>=0.13.0
tensorflow>=2.8.0

# Essential ML libraries for TSB-AD
xgboost>=1.5.0
lightgbm>=3.3.0

# Anomaly detection
pyod>=1.0.0

# Utility libraries
tqdm>=4.62.0
joblib>=1.1.0

# System monitoring
psutil>=5.8.0

# Data processing
openpyxl>=3.0.9

# Additional dependencies that might be needed
h5py>=3.6.0
Pillow>=8.0.0

# For TSB-AD evaluation metrics
tabulate>=0.8.0

# Memory efficient data loading
tables>=3.6.0

# For some TSB-AD models
imbalanced-learn>=0.8.0

# Specific versions for common issues
setuptools>=60.0.0
wheel>=0.37.0

# Note: If you encounter CUDA/PyTorch issues, manually install:
# For CUDA 11.8: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
# For CUDA 11.7: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu117
# For CPU only: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
