#!/usr/bin/env python3
"""
Final test with correct HTA-AD implementation on TSB-AD datasets
Using CNN+TCN architecture as per backup file
"""

import torch
import sys
import numpy as np
import pandas as pd
import os
import time
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import warnings

warnings.filterwarnings('ignore')

# Add paths
sys.path.append('.')
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

from core.models.hta_ad_correct import HTAADCorrect, PostHocSAE

# Import TSB-AD evaluation metrics
try:
    from TSB_AD.evaluation.metrics import get_metrics
    from TSB_AD.utils.slidingWindows import find_length_rank
    TSB_AD_AVAILABLE = True
    print("✅ TSB-AD evaluation metrics loaded successfully")
except ImportError:
    print("❌ TSB-AD evaluation metrics not available")
    TSB_AD_AVAILABLE = False

def parse_dataset_filename(filename):
    """Parse TSB-AD dataset filename to extract train/test split info"""
    parts = filename.split('_')
    train_size = None
    test_size = None
    
    for i, part in enumerate(parts):
        if part == 'tr' and i + 1 < len(parts):
            train_size = int(parts[i + 1])
        elif part == '1st' and i + 1 < len(parts):
            test_size = int(parts[i + 1].split('.')[0])
    
    return train_size, test_size

def load_tsb_ad_dataset(dataset_path):
    """Load TSB-AD dataset with proper train/test split"""
    try:
        df = pd.read_csv(dataset_path)
        data = df.iloc[:, 0].values.reshape(-1, 1)
        labels = df.iloc[:, 1].values
        
        filename = os.path.basename(dataset_path)
        train_size, test_size = parse_dataset_filename(filename)
        
        if train_size is None or test_size is None:
            print(f"Warning: Could not parse sizes from {filename}")
            split_idx = int(0.7 * len(data))
            train_data = data[:split_idx]
            train_labels = labels[:split_idx]
            test_data = data[split_idx:]
            test_labels = labels[split_idx:]
        else:
            train_data = data[:train_size]
            train_labels = labels[:train_size]
            test_data = data[train_size:train_size + test_size]
            test_labels = labels[train_size:train_size + test_size]
        
        return train_data, train_labels, test_data, test_labels
    except Exception as e:
        print(f"Error loading dataset {dataset_path}: {e}")
        return None, None, None, None

def create_sliding_windows(data, window_size, stride=1):
    """Create sliding windows from time series data"""
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    n_samples, n_features = data.shape
    windows = []
    
    for i in range(0, n_samples - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def train_hta_ad_models(train_data, input_dim, device='cpu', epochs=25):
    """Train HTA-AD and SAE models with correct architecture"""
    # Create models with CNN+TCN architecture
    model = HTAADCorrect(
        input_dim=input_dim,
        window_size=100,
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        tcn_kernel_size=3,
        cnn_channels=16,
        downsample_stride=2
    ).to(device)
    
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128,
        sparsity_penalty=1e-3
    ).to(device)
    
    train_windows = create_sliding_windows(train_data, 100)
    train_windows = torch.FloatTensor(train_windows).to(device)
    
    if len(train_windows) == 0:
        return None, None
    
    print(f"    Training on {len(train_windows)} windows...")
    
    # Train HTA-AD
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    model.train()
    
    for epoch in range(epochs):
        total_loss = 0
        batch_size = 32
        
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch)
            losses = model.compute_loss(outputs, batch)
            loss = losses['total']
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 10 == 0:
            avg_loss = total_loss / (len(train_windows) // batch_size + 1)
            print(f"    Epoch {epoch + 1}/{epochs}, Loss: {avg_loss:.4f}")
    
    # Collect latent vectors for SAE training
    print("    Collecting latent vectors for SAE training...")
    model.eval()
    all_latents = []
    
    with torch.no_grad():
        batch_size = 32
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            outputs = model(batch)
            latents = outputs['latent_vectors']
            all_latents.append(latents)
    
    all_latents = torch.cat(all_latents, dim=0)
    print(f"    Collected {all_latents.shape[0]} latent vectors")
    
    # Train SAE
    print("    Training SAE...")
    sae_optimizer = torch.optim.Adam(sae.parameters(), lr=0.001)
    sae.train()
    
    for epoch in range(epochs // 2):
        total_loss = 0
        batch_size = 64
        
        for i in range(0, len(all_latents), batch_size):
            batch = all_latents[i:i + batch_size]
            
            sae_optimizer.zero_grad()
            losses = sae.compute_loss(batch)
            loss = losses['total']
            
            loss.backward()
            sae_optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 5 == 0:
            avg_loss = total_loss / (len(all_latents) // batch_size + 1)
            print(f"    SAE Epoch {epoch + 1}/{epochs // 2}, Loss: {avg_loss:.4f}")
    
    # Identify irrelevant features (only dead features)
    print("    Identifying irrelevant features...")
    sae.identify_irrelevant_features(all_latents, max_irrelevant_ratio=0.25, only_dead_features=True)
    
    return model, sae

def evaluate_models_tsb_ad(model, sae, test_data, test_labels, window_size=100, device='cpu'):
    """Evaluate both HTA-AD and SAE using TSB-AD metrics"""
    model.eval()
    sae.eval()
    
    if len(test_data) < window_size:
        return None
    
    # Point-wise evaluation
    pad_size = window_size // 2
    padded_data = np.pad(test_data, ((pad_size, pad_size), (0, 0)), mode='edge')
    
    hta_scores = []
    sae_scores = []
    
    with torch.no_grad():
        for i in range(len(test_data)):
            window = padded_data[i:i + window_size]
            window_tensor = torch.FloatTensor(window).unsqueeze(0).to(device)
            
            # HTA-AD forward pass
            outputs = model(window_tensor)
            reconstruction = outputs['reconstruction']
            latent_vectors = outputs['latent_vectors']
            
            # HTA-AD score (reconstruction error)
            hta_error = torch.mean((window_tensor - reconstruction) ** 2)
            hta_scores.append(hta_error.cpu().item())
            
            # SAE purified reconstruction
            z_purified = sae.purify_latent(latent_vectors)
            purified_reconstruction = model.decode(z_purified)
            sae_error = torch.mean((window_tensor - purified_reconstruction) ** 2)
            sae_scores.append(sae_error.cpu().item())
    
    hta_scores = np.array(hta_scores)
    sae_scores = np.array(sae_scores)
    
    results = {}
    
    if len(np.unique(test_labels)) > 1 and TSB_AD_AVAILABLE:
        try:
            # TSB-AD official metrics
            slidingWindow = find_length_rank(test_data, rank=1)
            hta_metrics = get_metrics(hta_scores, test_labels, slidingWindow=slidingWindow)
            sae_metrics = get_metrics(sae_scores, test_labels, slidingWindow=slidingWindow)
            
            results['hta_ad_vus_pr'] = hta_metrics.get('VUS-PR', 0)
            results['hta_ad_vus_roc'] = hta_metrics.get('VUS-ROC', 0)
            results['hta_ad_f1'] = hta_metrics.get('Standard-F1', 0)
            results['sae_vus_pr'] = sae_metrics.get('VUS-PR', 0)
            results['sae_vus_roc'] = sae_metrics.get('VUS-ROC', 0)
            results['sae_f1'] = sae_metrics.get('Standard-F1', 0)
            
        except Exception as e:
            print(f"    Warning: TSB-AD metrics calculation failed: {e}")
            results = {
                'hta_ad_vus_pr': 0, 'hta_ad_vus_roc': 0, 'hta_ad_f1': 0,
                'sae_vus_pr': 0, 'sae_vus_roc': 0, 'sae_f1': 0
            }
    else:
        results = {
            'hta_ad_vus_pr': 0, 'hta_ad_vus_roc': 0, 'hta_ad_f1': 0,
            'sae_vus_pr': 0, 'sae_vus_roc': 0, 'sae_f1': 0
        }
    
    results['n_points'] = len(test_labels)
    results['anomaly_ratio'] = np.mean(test_labels)
    results['hta_scores'] = hta_scores
    results['sae_scores'] = sae_scores
    results['test_labels'] = test_labels
    
    return results

def test_single_dataset():
    """Test on a single dataset to verify correct implementation"""
    print("🧪 Testing Correct HTA-AD Implementation on Single Dataset")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Test dataset
    dataset_path = "TSB-AD/Datasets/TSB-AD-U/140_MSL_id_1_Sensor_tr_530_1st_630.csv"
    
    if not os.path.exists(dataset_path):
        print("❌ Dataset not found")
        return
    
    dataset_name = os.path.basename(dataset_path)
    print(f"\n📊 Testing: {dataset_name}")
    print("-" * 50)
    
    # Load dataset
    train_data, train_labels, test_data, test_labels = load_tsb_ad_dataset(dataset_path)
    if train_data is None:
        return
    
    print(f"Train: {train_data.shape[0]} points, {np.mean(train_labels):.3f} anomaly ratio")
    print(f"Test:  {test_data.shape[0]} points, {np.mean(test_labels):.3f} anomaly ratio")
    
    # Normalize data
    scaler = StandardScaler()
    train_data = scaler.fit_transform(train_data)
    test_data = scaler.transform(test_data)
    
    input_dim = train_data.shape[1]
    
    try:
        # Train models
        print("🔧 Training models with CNN+TCN architecture...")
        start_time = time.time()
        model, sae = train_hta_ad_models(train_data, input_dim, device, epochs=20)
        training_time = time.time() - start_time
        
        if model is None or sae is None:
            print("❌ Training failed")
            return
        
        # Evaluate
        print("📈 Evaluating...")
        start_time = time.time()
        results = evaluate_models_tsb_ad(model, sae, test_data, test_labels, device=device)
        eval_time = time.time() - start_time
        
        if results is None:
            print("❌ Evaluation failed")
            return
        
        # Print results
        print(f"✅ Results:")
        print(f"   HTA-AD Metrics:")
        print(f"     - VUS-PR:  {results['hta_ad_vus_pr']:.4f}")
        print(f"     - VUS-ROC: {results['hta_ad_vus_roc']:.4f}")
        print(f"     - F1:      {results['hta_ad_f1']:.4f}")
        print(f"   SAE Metrics:")
        print(f"     - VUS-PR:  {results['sae_vus_pr']:.4f}")
        print(f"     - VUS-ROC: {results['sae_vus_roc']:.4f}")
        print(f"     - F1:      {results['sae_f1']:.4f}")
        print(f"   Performance:")
        print(f"     - Training: {training_time:.1f}s")
        print(f"     - Evaluation: {eval_time:.1f}s")
        
        # Score comparison
        hta_mean = results['hta_scores'].mean()
        sae_mean = results['sae_scores'].mean()
        print(f"   Score Statistics:")
        print(f"     - HTA-AD: {hta_mean:.6f}")
        print(f"     - SAE:    {sae_mean:.6f}")
        print(f"     - Ratio:  {sae_mean/hta_mean:.3f}")
        
        print(f"\n🎉 Test completed successfully!")
        print(f"Architecture: CNN (downsampling) + TCN (temporal features) + SAE (interpretability)")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_dataset()
