#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTA-AD模型深度分析可视化实验
实验一：CNN局部模式总结器可视化 - 展示CNN下采样层如何提取和压缩局部特征
实验二：TCN感受野可视化 - 展示膨胀卷积如何建立长程时间依赖
实验三：重构误差解剖可视化 - 展示模型如何通过重构误差识别异常
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import sys
import os
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
import warnings

# 确保可以导入 TSB_AD 模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_AD, HTA_Model
from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict

warnings.filterwarnings('ignore')

# 设置字体和样式
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def generate_synthetic_data_with_anomaly():
    """生成包含明显异常的合成时间序列数据"""
    np.random.seed(42)
    
    # 基础参数
    total_points = 500
    window_size = 100
    
    # 生成正常的时间序列（多个周期叠加）
    t = np.linspace(0, 10*np.pi, total_points)
    
    # 多层次的周期模式
    base_signal = (0.8 * np.sin(t) + 
                   0.4 * np.sin(3*t + np.pi/4) + 
                   0.2 * np.sin(7*t) + 
                   0.1 * np.random.randn(total_points))
    
    # 添加趋势
    trend = 0.001 * (t - 5*np.pi)**2
    normal_signal = base_signal + trend
    
    # 归一化到 [0, 1]
    normal_signal = (normal_signal - normal_signal.min()) / (normal_signal.max() - normal_signal.min())
    
    # 创建异常信号
    anomaly_signal = normal_signal.copy()
    
    # 异常1：尖峰异常 (位置 300-320)
    anomaly_signal[300:320] += 0.8 * np.exp(-0.5 * ((np.arange(20) - 10)/3)**2)
    
    # 异常2：电平漂移 (位置 400-450)
    anomaly_signal[400:450] += 0.4
    
    # 异常3：相位突变 (位置 200-250)
    anomaly_signal[200:250] = 1.0 - anomaly_signal[200:250]
    
    # 创建标签
    labels = np.zeros(total_points)
    labels[200:250] = 1
    labels[300:320] = 1
    labels[400:450] = 1
    
    return anomaly_signal.reshape(-1, 1), labels, window_size

def create_windows(data, window_size):
    """创建滑动窗口"""
    windows = []
    for i in range(len(data) - window_size + 1):
        windows.append(data[i:i+window_size])
    return np.array(windows)

def experiment_1_cnn_local_pattern_summarizer():
    """Experiment 1: CNN Local Pattern Summarizer Visualization"""
    print("🎯 Experiment 1: CNN Local Pattern Summarizer Visualization")
    
    # 生成数据
    data, labels, window_size = generate_synthetic_data_with_anomaly()
    
    # 选择一个有代表性的窗口（包含异常尖峰）
    target_start = 280
    input_window = data[target_start:target_start+window_size].reshape(1, window_size, 1)
    
    # 创建并训练HTA-AD模型
    model_params = {
        'window_size': window_size,
        'latent_dim': 16,
        'lr': 0.001,
        'epochs': 50,
        'batch_size': 32
    }
    
    hta_model = HTA_AD(HP=model_params)
    
    # 简单训练（使用正常数据）
    normal_data = data[:200]  # 只用前200个正常点训练
    hta_model.fit(normal_data)
    
    # 提取CNN层输出
    hta_model.model.eval()
    with torch.no_grad():
        input_tensor = torch.FloatTensor(input_window).to(hta_model.device)
        input_permuted = input_tensor.permute(0, 2, 1)
        cnn_output = hta_model.model.encoder_cnn(input_permuted)
        cnn_output_np = cnn_output.permute(0, 2, 1).cpu().numpy()[0]
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Experiment 1: CNN Local Pattern Summarizer - Feature Extraction at "First Gate"', fontsize=16, fontweight='bold')
    
    # 子图1：原始输入信号
    axes[0, 0].plot(input_window[0, :, 0], linewidth=2, color='#2E86AB', alpha=0.8)
    axes[0, 0].set_title('Original Input Signal (100 time points)', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('Time Steps')
    axes[0, 0].set_ylabel('Value')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 标注异常区域
    anomaly_start = 20  # 在窗口内的相对位置
    anomaly_end = 40
    axes[0, 0].axvspan(anomaly_start, anomaly_end, alpha=0.3, color='red', label='Anomaly Region')
    axes[0, 0].legend()
    
    # 子图2：CNN下采样后的信号（第一个通道）
    downsampled_len = cnn_output_np.shape[0]
    axes[0, 1].plot(cnn_output_np[:, 0], linewidth=2, color='#A23B72', marker='o', markersize=4)
    axes[0, 1].set_title(f'CNN Downsampled Output - Channel 1 ({downsampled_len} time points)', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('Time Steps (Downsampled)')
    axes[0, 1].set_ylabel('Feature Value')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 子图3：多个CNN通道的响应
    channels_to_show = min(8, cnn_output_np.shape[1])
    for i in range(channels_to_show):
        axes[1, 0].plot(cnn_output_np[:, i], linewidth=1.5, alpha=0.7, label=f'Channel {i+1}')
    axes[1, 0].set_title('Multi-Channel CNN Feature Response', fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('Time Steps (Downsampled)')
    axes[1, 0].set_ylabel('Feature Value')
    axes[1, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 子图4：压缩比和信息保留分析
    original_length = input_window.shape[1]
    compressed_length = downsampled_len
    compression_ratio = original_length / compressed_length
    
    metrics = ['Sequence Length', 'Parameters', 'Computation']
    original_values = [100, 100, 100]  # 归一化基准
    compressed_values = [100/compression_ratio, 100/compression_ratio, 100/(compression_ratio**2)]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    axes[1, 1].bar(x - width/2, original_values, width, label='Original Signal', color='#2E86AB', alpha=0.7)
    axes[1, 1].bar(x + width/2, compressed_values, width, label='CNN Compressed', color='#A23B72', alpha=0.7)
    axes[1, 1].set_title('Compression Efficiency Analysis', fontsize=12, fontweight='bold')
    axes[1, 1].set_ylabel('Relative Value (%)')
    axes[1, 1].set_xticks(x)
    axes[1, 1].set_xticklabels(metrics)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加压缩比文本
    axes[1, 1].text(0.5, 0.95, f'Compression Ratio: {compression_ratio:.1f}x\nSequence Length: {original_length}→{compressed_length}', 
                    transform=axes[1, 1].transAxes, fontsize=10, 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.5),
                    verticalalignment='top', horizontalalignment='center')
    
    plt.tight_layout()
    plt.savefig('experiments/cnn_local_pattern_summarizer.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ CNN Downsampling Effect: {original_length} → {compressed_length} time points (Compression ratio: {compression_ratio:.1f}x)")
    print(f"✅ Feature Channels: {cnn_output_np.shape[1]} channels")
    print(f"✅ Visualization saved to: experiments/cnn_local_pattern_summarizer.png")

def experiment_2_tcn_receptive_field_visualization():
    """Experiment 2: TCN Receptive Field Visualization"""
    print("\n🎯 Experiment 2: TCN Receptive Field Visualization")
    
    # TCN配置参数
    tcn_channels = [32, 32, 32]
    kernel_size = 3
    
    # 计算感受野
    def calculate_receptive_field(num_layers, kernel_size, dilations):
        """计算TCN的理论感受野"""
        receptive_field = 1
        for i in range(num_layers):
            receptive_field += (kernel_size - 1) * dilations[i]
        return receptive_field
    
    dilations = [2**i for i in range(len(tcn_channels))]
    total_receptive_field = calculate_receptive_field(len(tcn_channels), kernel_size, dilations)
    
    # 创建感受野可视化
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Experiment 2: TCN Receptive Field Visualization - Building Spatiotemporal Tunnels', fontsize=16, fontweight='bold')
    
    # 子图1：膨胀卷积示意图
    ax = axes[0, 0]
    sequence_length = 50
    
    # 绘制输入序列
    x_input = np.arange(sequence_length)
    y_input = np.zeros(sequence_length)
    ax.scatter(x_input, y_input, s=30, color='lightblue', alpha=0.7, label='输入序列')
    
    # 绘制不同层的感受野
    colors = ['red', 'green', 'blue']
    layer_names = ['Layer 1 (d=1)', 'Layer 2 (d=2)', 'Layer 3 (d=4)']
    
    center = sequence_length // 2
    for i, (dilation, color, name) in enumerate(zip(dilations, colors, layer_names)):
        y_level = -(i + 1) * 0.5
        
        # 计算当前层的感受野
        layer_receptive_field = 1 + (kernel_size - 1) * dilation
        start = max(0, center - layer_receptive_field // 2)
        end = min(sequence_length, center + layer_receptive_field // 2)
        
        # 绘制感受野连接
        for pos in range(start, end + 1, dilation):
            if pos < sequence_length:
                ax.plot([pos, center], [0, y_level], color=color, alpha=0.6, linewidth=1)
        
        # 绘制层节点
        ax.scatter([center], [y_level], s=100, color=color, label=name, zorder=3)
        
        # 添加膨胀系数标注
        ax.text(center + 2, y_level, f'膨胀={dilation}', fontsize=9, color=color, fontweight='bold')
    
    ax.set_title('膨胀卷积的感受野扩展机制', fontsize=12, fontweight='bold')
    ax.set_xlabel('时间步')
    ax.set_ylabel('网络层')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim(-2, 0.5)
    
    # 子图2：感受野增长曲线
    layers = np.arange(1, len(tcn_channels) + 1)
    receptive_fields = []
    
    for i in range(len(tcn_channels)):
        rf = calculate_receptive_field(i + 1, kernel_size, dilations[:i+1])
        receptive_fields.append(rf)
    
    axes[0, 1].plot(layers, receptive_fields, 'o-', linewidth=3, markersize=8, color='#E76F51', label='TCN感受野')
    
    # 对比：如果使用标准卷积
    standard_rf = [1 + i * (kernel_size - 1) for i in range(1, len(tcn_channels) + 1)]
    axes[0, 1].plot(layers, standard_rf, 's--', linewidth=2, markersize=6, color='gray', alpha=0.7, label='标准卷积')
    
    axes[0, 1].set_title('感受野增长对比：指数 vs 线性', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('网络层数')
    axes[0, 1].set_ylabel('感受野大小')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 添加数值标注
    for i, (layer, rf) in enumerate(zip(layers, receptive_fields)):
        axes[0, 1].annotate(f'{rf}', (layer, rf), textcoords="offset points", xytext=(0,10), ha='center', fontweight='bold')
    
    # 子图3：计算复杂度对比
    methods = ['RNN\n(串行)', '标准CNN\n(小感受野)', 'Transformer\n(注意力)', 'TCN\n(膨胀卷积)']
    complexity = [100, 25, 200, 30]  # 相对计算复杂度
    parallelization = [10, 90, 70, 95]  # 并行化程度
    
    x = np.arange(len(methods))
    width = 0.35
    
    axes[1, 0].bar(x - width/2, complexity, width, label='计算复杂度', color='#F4A261', alpha=0.8)
    axes[1, 0].bar(x + width/2, parallelization, width, label='并行化程度', color='#2A9D8F', alpha=0.8)
    
    axes[1, 0].set_title('不同方法的效率对比', fontsize=12, fontweight='bold')
    axes[1, 0].set_ylabel('相对分数')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(methods)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 子图4：有效感受野热力图
    input_length = 64
    output_pos = input_length // 2  # 中心位置
    
    # 模拟梯度权重（高斯分布，中心高边缘低）
    positions = np.arange(input_length)
    weights = np.exp(-0.5 * ((positions - output_pos) / 8)**2)
    weights = weights / weights.max()
    
    # 创建热力图数据
    heatmap_data = weights.reshape(1, -1)
    
    im = axes[1, 1].imshow(heatmap_data, cmap='YlOrRd', aspect='auto', interpolation='bilinear')
    axes[1, 1].set_title('有效感受野热力图', fontsize=12, fontweight='bold')
    axes[1, 1].set_xlabel('输入位置')
    axes[1, 1].set_ylabel('输出神经元')
    axes[1, 1].set_yticks([])
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=axes[1, 1], fraction=0.046, pad=0.04)
    cbar.set_label('影响权重', rotation=270, labelpad=15)
    
    # 标注理论感受野边界
    start_pos = max(0, output_pos - total_receptive_field // 2)
    end_pos = min(input_length, output_pos + total_receptive_field // 2)
    axes[1, 1].axvline(start_pos, color='red', linestyle='--', alpha=0.8, label=f'理论感受野 ({total_receptive_field})')
    axes[1, 1].axvline(end_pos, color='red', linestyle='--', alpha=0.8)
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig('experiments/tcn_receptive_field_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ TCN层数：{len(tcn_channels)} 层")
    print(f"✅ 膨胀系数：{dilations}")
    print(f"✅ 理论感受野：{total_receptive_field} 个时间步")
    print(f"✅ 可视化已保存至：experiments/tcn_receptive_field_visualization.png")

def experiment_3_reconstruction_error_anatomy():
    """实验三：重构误差解剖可视化"""
    print("\n🎯 实验三：重构误差解剖可视化")
    
    # 生成数据
    data, labels, window_size = generate_synthetic_data_with_anomaly()
    
    # 创建并训练HTA-AD模型
    model_params = {
        'window_size': window_size,
        'latent_dim': 16,
        'lr': 0.001,
        'epochs': 100,
        'batch_size': 32
    }
    
    hta_model = HTA_AD(HP=model_params)
    
    # 训练模型（使用正常数据）
    normal_data = data[:200]
    hta_model.fit(normal_data)
    
    # 选择包含异常的测试窗口
    test_start = 280
    test_window = data[test_start:test_start+window_size]
    test_labels = labels[test_start:test_start+window_size]
    
    # 获取重构结果
    hta_model.model.eval()
    with torch.no_grad():
        input_tensor = torch.FloatTensor(test_window.reshape(1, window_size, 1)).to(hta_model.device)
        reconstructed = hta_model.model(input_tensor)
        reconstruction = reconstructed.cpu().numpy()[0, :, 0]
    
    # 计算重构误差
    reconstruction_error = np.abs(test_window.flatten() - reconstruction)
    
    # 创建可视化
    fig = plt.figure(figsize=(16, 12))
    gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], width_ratios=[2, 1])
    
    fig.suptitle('实验三：重构误差解剖 - 异常检测的"照妖镜"', fontsize=16, fontweight='bold')
    
    # 主图：三条曲线对比
    ax_main = fig.add_subplot(gs[0, :])
    
    time_steps = np.arange(window_size)
    
    # 绘制原始信号
    ax_main.plot(time_steps, test_window.flatten(), linewidth=2.5, color='#2E86AB', label='原始信号 (Ground Truth)', alpha=0.9)
    
    # 绘制重构信号
    ax_main.plot(time_steps, reconstruction, linewidth=2.5, color='#A23B72', label='重构信号 (Reconstruction)', alpha=0.9, linestyle='--')
    
    # 绘制重构误差
    ax_error = ax_main.twinx()
    ax_error.fill_between(time_steps, 0, reconstruction_error, alpha=0.4, color='red', label='重构误差')
    ax_error.plot(time_steps, reconstruction_error, linewidth=2, color='red', alpha=0.8)
    
    # 标注异常区域
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        anomaly_regions = []
        start = None
        for i, is_anomaly in enumerate(anomaly_mask):
            if is_anomaly and start is None:
                start = i
            elif not is_anomaly and start is not None:
                anomaly_regions.append((start, i-1))
                start = None
        if start is not None:
            anomaly_regions.append((start, len(anomaly_mask)-1))
        
        for start, end in anomaly_regions:
            ax_main.axvspan(start, end, alpha=0.2, color='orange', label='异常区域' if start == anomaly_regions[0][0] else "")
    
    ax_main.set_title('异常检测机制解剖：正常vs异常的重构差异', fontsize=14, fontweight='bold')
    ax_main.set_xlabel('时间步')
    ax_main.set_ylabel('信号值', color='#2E86AB')
    ax_error.set_ylabel('重构误差', color='red')
    ax_main.legend(loc='upper left')
    ax_error.legend(loc='upper right')
    ax_main.grid(True, alpha=0.3)
    
    # 子图1：重构误差统计分析
    ax_stats = fig.add_subplot(gs[1, 0])
    
    normal_error = reconstruction_error[~anomaly_mask] if np.any(anomaly_mask) else reconstruction_error
    anomaly_error = reconstruction_error[anomaly_mask] if np.any(anomaly_mask) else []
    
    # 误差分布直方图
    ax_stats.hist(normal_error, bins=20, alpha=0.6, color='green', label=f'正常点误差 (n={len(normal_error)})', density=True)
    if len(anomaly_error) > 0:
        ax_stats.hist(anomaly_error, bins=10, alpha=0.6, color='red', label=f'异常点误差 (n={len(anomaly_error)})', density=True)
    
    ax_stats.axvline(np.mean(normal_error), color='green', linestyle='--', alpha=0.8, label=f'正常均值: {np.mean(normal_error):.4f}')
    if len(anomaly_error) > 0:
        ax_stats.axvline(np.mean(anomaly_error), color='red', linestyle='--', alpha=0.8, label=f'异常均值: {np.mean(anomaly_error):.4f}')
    
    ax_stats.set_title('重构误差分布对比', fontsize=12, fontweight='bold')
    ax_stats.set_xlabel('重构误差')
    ax_stats.set_ylabel('密度')
    ax_stats.legend()
    ax_stats.grid(True, alpha=0.3)
    
    # 子图2：阈值分析
    ax_thresh = fig.add_subplot(gs[1, 1])
    
    # 计算不同阈值下的检测性能
    thresholds = np.linspace(0, np.max(reconstruction_error), 100)
    tpr_scores = []
    fpr_scores = []
    
    for thresh in thresholds:
        predictions = reconstruction_error > thresh
        if np.any(anomaly_mask):
            tp = np.sum(predictions & anomaly_mask)
            fp = np.sum(predictions & ~anomaly_mask)
            tn = np.sum(~predictions & ~anomaly_mask)
            fn = np.sum(~predictions & anomaly_mask)
            
            tpr = tp / (tp + fn) if (tp + fn) > 0 else 0
            fpr = fp / (fp + tn) if (fp + tn) > 0 else 0
        else:
            tpr, fpr = 0, 0
        
        tpr_scores.append(tpr)
        fpr_scores.append(fpr)
    
    ax_thresh.plot(fpr_scores, tpr_scores, linewidth=2, color='purple')
    ax_thresh.plot([0, 1], [0, 1], 'k--', alpha=0.5)
    ax_thresh.set_title('ROC曲线', fontsize=12, fontweight='bold')
    ax_thresh.set_xlabel('假正率 (FPR)')
    ax_thresh.set_ylabel('真正率 (TPR)')
    ax_thresh.grid(True, alpha=0.3)
    
    # 计算AUC
    auc = np.trapz(tpr_scores, fpr_scores)
    ax_thresh.text(0.6, 0.2, f'AUC = {auc:.3f}', fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # 子图3：模型学习到的正常模式可视化
    ax_pattern = fig.add_subplot(gs[2, :])
    
    # 展示模型对不同类型信号的重构能力
    pattern_types = ['正常周期', '正常趋势', '异常尖峰', '异常漂移']
    pattern_colors = ['green', 'blue', 'red', 'orange']
    
    # 创建不同类型的模式片段
    segments = [
        (0, 25, '正常周期'),
        (25, 50, '正常趋势'), 
        (50, 75, '异常尖峰'),
        (75, 100, '异常漂移')
    ]
    
    for i, (start, end, pattern_type) in enumerate(segments):
        segment_original = test_window[start:end].flatten()
        segment_reconstructed = reconstruction[start:end]
        segment_time = time_steps[start:end]
        
        # 计算该段的重构质量
        segment_error = np.mean(np.abs(segment_original - segment_reconstructed))
        
        color = pattern_colors[i]
        alpha = 0.8 if '正常' in pattern_type else 0.6
        
        ax_pattern.plot(segment_time, segment_original, linewidth=2, color=color, alpha=alpha, label=f'{pattern_type} (误差: {segment_error:.4f})')
        ax_pattern.plot(segment_time, segment_reconstructed, linewidth=2, color=color, alpha=0.5, linestyle='--')
    
    ax_pattern.set_title('模型学习的正常模式 vs 异常检测能力', fontsize=12, fontweight='bold')
    ax_pattern.set_xlabel('时间步')
    ax_pattern.set_ylabel('信号值')
    ax_pattern.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax_pattern.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/reconstruction_error_anatomy.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 输出统计信息
    print(f"✅ 平均重构误差 - 正常点: {np.mean(normal_error):.6f}")
    if len(anomaly_error) > 0:
        print(f"✅ 平均重构误差 - 异常点: {np.mean(anomaly_error):.6f}")
        print(f"✅ 异常放大倍数: {np.mean(anomaly_error)/np.mean(normal_error):.2f}x")
    print(f"✅ ROC AUC: {auc:.4f}")
    print(f"✅ 可视化已保存至：experiments/reconstruction_error_anatomy.png")

def run_all_experiments():
    """运行所有三个可视化实验"""
    print("=" * 80)
    print("🚀 HTA-AD模型深度分析可视化实验")
    print("=" * 80)
    
    # 创建输出目录
    os.makedirs('experiments', exist_ok=True)
    
    try:
        # 运行三个实验
        experiment_1_cnn_local_pattern_summarizer()
        experiment_2_tcn_receptive_field_visualization()
        experiment_3_reconstruction_error_anatomy()
        
        print("\n" + "=" * 80)
        print("🎉 所有可视化实验完成！")
        print("📁 可视化文件保存在 experiments/ 目录下：")
        print("   📊 cnn_local_pattern_summarizer.png")
        print("   📊 tcn_receptive_field_visualization.png") 
        print("   📊 reconstruction_error_anatomy.png")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 实验执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_experiments() 
# -*- coding: utf-8 -*-
"""
HTA-AD模型深度分析可视化实验
实验一：CNN局部模式总结器可视化 - 展示CNN下采样层如何提取和压缩局部特征
实验二：TCN感受野可视化 - 展示膨胀卷积如何建立长程时间依赖
实验三：重构误差解剖可视化 - 展示模型如何通过重构误差识别异常
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import sys
import os
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
import warnings

# 确保可以导入 TSB_AD 模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_AD, HTA_Model
from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict

warnings.filterwarnings('ignore')

# 设置字体和样式
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def generate_synthetic_data_with_anomaly():
    """生成包含明显异常的合成时间序列数据"""
    np.random.seed(42)
    
    # 基础参数
    total_points = 500
    window_size = 100
    
    # 生成正常的时间序列（多个周期叠加）
    t = np.linspace(0, 10*np.pi, total_points)
    
    # 多层次的周期模式
    base_signal = (0.8 * np.sin(t) + 
                   0.4 * np.sin(3*t + np.pi/4) + 
                   0.2 * np.sin(7*t) + 
                   0.1 * np.random.randn(total_points))
    
    # 添加趋势
    trend = 0.001 * (t - 5*np.pi)**2
    normal_signal = base_signal + trend
    
    # 归一化到 [0, 1]
    normal_signal = (normal_signal - normal_signal.min()) / (normal_signal.max() - normal_signal.min())
    
    # 创建异常信号
    anomaly_signal = normal_signal.copy()
    
    # 异常1：尖峰异常 (位置 300-320)
    anomaly_signal[300:320] += 0.8 * np.exp(-0.5 * ((np.arange(20) - 10)/3)**2)
    
    # 异常2：电平漂移 (位置 400-450)
    anomaly_signal[400:450] += 0.4
    
    # 异常3：相位突变 (位置 200-250)
    anomaly_signal[200:250] = 1.0 - anomaly_signal[200:250]
    
    # 创建标签
    labels = np.zeros(total_points)
    labels[200:250] = 1
    labels[300:320] = 1
    labels[400:450] = 1
    
    return anomaly_signal.reshape(-1, 1), labels, window_size

def create_windows(data, window_size):
    """创建滑动窗口"""
    windows = []
    for i in range(len(data) - window_size + 1):
        windows.append(data[i:i+window_size])
    return np.array(windows)

def experiment_1_cnn_local_pattern_summarizer():
    """Experiment 1: CNN Local Pattern Summarizer Visualization"""
    print("🎯 Experiment 1: CNN Local Pattern Summarizer Visualization")
    
    # 生成数据
    data, labels, window_size = generate_synthetic_data_with_anomaly()
    
    # 选择一个有代表性的窗口（包含异常尖峰）
    target_start = 280
    input_window = data[target_start:target_start+window_size].reshape(1, window_size, 1)
    
    # 创建并训练HTA-AD模型
    model_params = {
        'window_size': window_size,
        'latent_dim': 16,
        'lr': 0.001,
        'epochs': 50,
        'batch_size': 32
    }
    
    hta_model = HTA_AD(HP=model_params)
    
    # 简单训练（使用正常数据）
    normal_data = data[:200]  # 只用前200个正常点训练
    hta_model.fit(normal_data)
    
    # 提取CNN层输出
    hta_model.model.eval()
    with torch.no_grad():
        input_tensor = torch.FloatTensor(input_window).to(hta_model.device)
        input_permuted = input_tensor.permute(0, 2, 1)
        cnn_output = hta_model.model.encoder_cnn(input_permuted)
        cnn_output_np = cnn_output.permute(0, 2, 1).cpu().numpy()[0]
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Experiment 1: CNN Local Pattern Summarizer - Feature Extraction at "First Gate"', fontsize=16, fontweight='bold')
    
    # 子图1：原始输入信号
    axes[0, 0].plot(input_window[0, :, 0], linewidth=2, color='#2E86AB', alpha=0.8)
    axes[0, 0].set_title('Original Input Signal (100 time points)', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('Time Steps')
    axes[0, 0].set_ylabel('Value')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 标注异常区域
    anomaly_start = 20  # 在窗口内的相对位置
    anomaly_end = 40
    axes[0, 0].axvspan(anomaly_start, anomaly_end, alpha=0.3, color='red', label='Anomaly Region')
    axes[0, 0].legend()
    
    # 子图2：CNN下采样后的信号（第一个通道）
    downsampled_len = cnn_output_np.shape[0]
    axes[0, 1].plot(cnn_output_np[:, 0], linewidth=2, color='#A23B72', marker='o', markersize=4)
    axes[0, 1].set_title(f'CNN Downsampled Output - Channel 1 ({downsampled_len} time points)', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('Time Steps (Downsampled)')
    axes[0, 1].set_ylabel('Feature Value')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 子图3：多个CNN通道的响应
    channels_to_show = min(8, cnn_output_np.shape[1])
    for i in range(channels_to_show):
        axes[1, 0].plot(cnn_output_np[:, i], linewidth=1.5, alpha=0.7, label=f'Channel {i+1}')
    axes[1, 0].set_title('Multi-Channel CNN Feature Response', fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('Time Steps (Downsampled)')
    axes[1, 0].set_ylabel('Feature Value')
    axes[1, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 子图4：压缩比和信息保留分析
    original_length = input_window.shape[1]
    compressed_length = downsampled_len
    compression_ratio = original_length / compressed_length
    
    metrics = ['Sequence Length', 'Parameters', 'Computation']
    original_values = [100, 100, 100]  # 归一化基准
    compressed_values = [100/compression_ratio, 100/compression_ratio, 100/(compression_ratio**2)]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    axes[1, 1].bar(x - width/2, original_values, width, label='Original Signal', color='#2E86AB', alpha=0.7)
    axes[1, 1].bar(x + width/2, compressed_values, width, label='CNN Compressed', color='#A23B72', alpha=0.7)
    axes[1, 1].set_title('Compression Efficiency Analysis', fontsize=12, fontweight='bold')
    axes[1, 1].set_ylabel('Relative Value (%)')
    axes[1, 1].set_xticks(x)
    axes[1, 1].set_xticklabels(metrics)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加压缩比文本
    axes[1, 1].text(0.5, 0.95, f'Compression Ratio: {compression_ratio:.1f}x\nSequence Length: {original_length}→{compressed_length}', 
                    transform=axes[1, 1].transAxes, fontsize=10, 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.5),
                    verticalalignment='top', horizontalalignment='center')
    
    plt.tight_layout()
    plt.savefig('experiments/cnn_local_pattern_summarizer.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ CNN Downsampling Effect: {original_length} → {compressed_length} time points (Compression ratio: {compression_ratio:.1f}x)")
    print(f"✅ Feature Channels: {cnn_output_np.shape[1]} channels")
    print(f"✅ Visualization saved to: experiments/cnn_local_pattern_summarizer.png")

def experiment_2_tcn_receptive_field_visualization():
    """Experiment 2: TCN Receptive Field Visualization"""
    print("\n🎯 Experiment 2: TCN Receptive Field Visualization")
    
    # TCN配置参数
    tcn_channels = [32, 32, 32]
    kernel_size = 3
    
    # 计算感受野
    def calculate_receptive_field(num_layers, kernel_size, dilations):
        """计算TCN的理论感受野"""
        receptive_field = 1
        for i in range(num_layers):
            receptive_field += (kernel_size - 1) * dilations[i]
        return receptive_field
    
    dilations = [2**i for i in range(len(tcn_channels))]
    total_receptive_field = calculate_receptive_field(len(tcn_channels), kernel_size, dilations)
    
    # 创建感受野可视化
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Experiment 2: TCN Receptive Field Visualization - Building Spatiotemporal Tunnels', fontsize=16, fontweight='bold')
    
    # 子图1：膨胀卷积示意图
    ax = axes[0, 0]
    sequence_length = 50
    
    # 绘制输入序列
    x_input = np.arange(sequence_length)
    y_input = np.zeros(sequence_length)
    ax.scatter(x_input, y_input, s=30, color='lightblue', alpha=0.7, label='输入序列')
    
    # 绘制不同层的感受野
    colors = ['red', 'green', 'blue']
    layer_names = ['Layer 1 (d=1)', 'Layer 2 (d=2)', 'Layer 3 (d=4)']
    
    center = sequence_length // 2
    for i, (dilation, color, name) in enumerate(zip(dilations, colors, layer_names)):
        y_level = -(i + 1) * 0.5
        
        # 计算当前层的感受野
        layer_receptive_field = 1 + (kernel_size - 1) * dilation
        start = max(0, center - layer_receptive_field // 2)
        end = min(sequence_length, center + layer_receptive_field // 2)
        
        # 绘制感受野连接
        for pos in range(start, end + 1, dilation):
            if pos < sequence_length:
                ax.plot([pos, center], [0, y_level], color=color, alpha=0.6, linewidth=1)
        
        # 绘制层节点
        ax.scatter([center], [y_level], s=100, color=color, label=name, zorder=3)
        
        # 添加膨胀系数标注
        ax.text(center + 2, y_level, f'膨胀={dilation}', fontsize=9, color=color, fontweight='bold')
    
    ax.set_title('膨胀卷积的感受野扩展机制', fontsize=12, fontweight='bold')
    ax.set_xlabel('时间步')
    ax.set_ylabel('网络层')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim(-2, 0.5)
    
    # 子图2：感受野增长曲线
    layers = np.arange(1, len(tcn_channels) + 1)
    receptive_fields = []
    
    for i in range(len(tcn_channels)):
        rf = calculate_receptive_field(i + 1, kernel_size, dilations[:i+1])
        receptive_fields.append(rf)
    
    axes[0, 1].plot(layers, receptive_fields, 'o-', linewidth=3, markersize=8, color='#E76F51', label='TCN感受野')
    
    # 对比：如果使用标准卷积
    standard_rf = [1 + i * (kernel_size - 1) for i in range(1, len(tcn_channels) + 1)]
    axes[0, 1].plot(layers, standard_rf, 's--', linewidth=2, markersize=6, color='gray', alpha=0.7, label='标准卷积')
    
    axes[0, 1].set_title('感受野增长对比：指数 vs 线性', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('网络层数')
    axes[0, 1].set_ylabel('感受野大小')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 添加数值标注
    for i, (layer, rf) in enumerate(zip(layers, receptive_fields)):
        axes[0, 1].annotate(f'{rf}', (layer, rf), textcoords="offset points", xytext=(0,10), ha='center', fontweight='bold')
    
    # 子图3：计算复杂度对比
    methods = ['RNN\n(串行)', '标准CNN\n(小感受野)', 'Transformer\n(注意力)', 'TCN\n(膨胀卷积)']
    complexity = [100, 25, 200, 30]  # 相对计算复杂度
    parallelization = [10, 90, 70, 95]  # 并行化程度
    
    x = np.arange(len(methods))
    width = 0.35
    
    axes[1, 0].bar(x - width/2, complexity, width, label='计算复杂度', color='#F4A261', alpha=0.8)
    axes[1, 0].bar(x + width/2, parallelization, width, label='并行化程度', color='#2A9D8F', alpha=0.8)
    
    axes[1, 0].set_title('不同方法的效率对比', fontsize=12, fontweight='bold')
    axes[1, 0].set_ylabel('相对分数')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(methods)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 子图4：有效感受野热力图
    input_length = 64
    output_pos = input_length // 2  # 中心位置
    
    # 模拟梯度权重（高斯分布，中心高边缘低）
    positions = np.arange(input_length)
    weights = np.exp(-0.5 * ((positions - output_pos) / 8)**2)
    weights = weights / weights.max()
    
    # 创建热力图数据
    heatmap_data = weights.reshape(1, -1)
    
    im = axes[1, 1].imshow(heatmap_data, cmap='YlOrRd', aspect='auto', interpolation='bilinear')
    axes[1, 1].set_title('有效感受野热力图', fontsize=12, fontweight='bold')
    axes[1, 1].set_xlabel('输入位置')
    axes[1, 1].set_ylabel('输出神经元')
    axes[1, 1].set_yticks([])
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=axes[1, 1], fraction=0.046, pad=0.04)
    cbar.set_label('影响权重', rotation=270, labelpad=15)
    
    # 标注理论感受野边界
    start_pos = max(0, output_pos - total_receptive_field // 2)
    end_pos = min(input_length, output_pos + total_receptive_field // 2)
    axes[1, 1].axvline(start_pos, color='red', linestyle='--', alpha=0.8, label=f'理论感受野 ({total_receptive_field})')
    axes[1, 1].axvline(end_pos, color='red', linestyle='--', alpha=0.8)
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig('experiments/tcn_receptive_field_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ TCN层数：{len(tcn_channels)} 层")
    print(f"✅ 膨胀系数：{dilations}")
    print(f"✅ 理论感受野：{total_receptive_field} 个时间步")
    print(f"✅ 可视化已保存至：experiments/tcn_receptive_field_visualization.png")

def experiment_3_reconstruction_error_anatomy():
    """实验三：重构误差解剖可视化"""
    print("\n🎯 实验三：重构误差解剖可视化")
    
    # 生成数据
    data, labels, window_size = generate_synthetic_data_with_anomaly()
    
    # 创建并训练HTA-AD模型
    model_params = {
        'window_size': window_size,
        'latent_dim': 16,
        'lr': 0.001,
        'epochs': 100,
        'batch_size': 32
    }
    
    hta_model = HTA_AD(HP=model_params)
    
    # 训练模型（使用正常数据）
    normal_data = data[:200]
    hta_model.fit(normal_data)
    
    # 选择包含异常的测试窗口
    test_start = 280
    test_window = data[test_start:test_start+window_size]
    test_labels = labels[test_start:test_start+window_size]
    
    # 获取重构结果
    hta_model.model.eval()
    with torch.no_grad():
        input_tensor = torch.FloatTensor(test_window.reshape(1, window_size, 1)).to(hta_model.device)
        reconstructed = hta_model.model(input_tensor)
        reconstruction = reconstructed.cpu().numpy()[0, :, 0]
    
    # 计算重构误差
    reconstruction_error = np.abs(test_window.flatten() - reconstruction)
    
    # 创建可视化
    fig = plt.figure(figsize=(16, 12))
    gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], width_ratios=[2, 1])
    
    fig.suptitle('实验三：重构误差解剖 - 异常检测的"照妖镜"', fontsize=16, fontweight='bold')
    
    # 主图：三条曲线对比
    ax_main = fig.add_subplot(gs[0, :])
    
    time_steps = np.arange(window_size)
    
    # 绘制原始信号
    ax_main.plot(time_steps, test_window.flatten(), linewidth=2.5, color='#2E86AB', label='原始信号 (Ground Truth)', alpha=0.9)
    
    # 绘制重构信号
    ax_main.plot(time_steps, reconstruction, linewidth=2.5, color='#A23B72', label='重构信号 (Reconstruction)', alpha=0.9, linestyle='--')
    
    # 绘制重构误差
    ax_error = ax_main.twinx()
    ax_error.fill_between(time_steps, 0, reconstruction_error, alpha=0.4, color='red', label='重构误差')
    ax_error.plot(time_steps, reconstruction_error, linewidth=2, color='red', alpha=0.8)
    
    # 标注异常区域
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        anomaly_regions = []
        start = None
        for i, is_anomaly in enumerate(anomaly_mask):
            if is_anomaly and start is None:
                start = i
            elif not is_anomaly and start is not None:
                anomaly_regions.append((start, i-1))
                start = None
        if start is not None:
            anomaly_regions.append((start, len(anomaly_mask)-1))
        
        for start, end in anomaly_regions:
            ax_main.axvspan(start, end, alpha=0.2, color='orange', label='异常区域' if start == anomaly_regions[0][0] else "")
    
    ax_main.set_title('异常检测机制解剖：正常vs异常的重构差异', fontsize=14, fontweight='bold')
    ax_main.set_xlabel('时间步')
    ax_main.set_ylabel('信号值', color='#2E86AB')
    ax_error.set_ylabel('重构误差', color='red')
    ax_main.legend(loc='upper left')
    ax_error.legend(loc='upper right')
    ax_main.grid(True, alpha=0.3)
    
    # 子图1：重构误差统计分析
    ax_stats = fig.add_subplot(gs[1, 0])
    
    normal_error = reconstruction_error[~anomaly_mask] if np.any(anomaly_mask) else reconstruction_error
    anomaly_error = reconstruction_error[anomaly_mask] if np.any(anomaly_mask) else []
    
    # 误差分布直方图
    ax_stats.hist(normal_error, bins=20, alpha=0.6, color='green', label=f'正常点误差 (n={len(normal_error)})', density=True)
    if len(anomaly_error) > 0:
        ax_stats.hist(anomaly_error, bins=10, alpha=0.6, color='red', label=f'异常点误差 (n={len(anomaly_error)})', density=True)
    
    ax_stats.axvline(np.mean(normal_error), color='green', linestyle='--', alpha=0.8, label=f'正常均值: {np.mean(normal_error):.4f}')
    if len(anomaly_error) > 0:
        ax_stats.axvline(np.mean(anomaly_error), color='red', linestyle='--', alpha=0.8, label=f'异常均值: {np.mean(anomaly_error):.4f}')
    
    ax_stats.set_title('重构误差分布对比', fontsize=12, fontweight='bold')
    ax_stats.set_xlabel('重构误差')
    ax_stats.set_ylabel('密度')
    ax_stats.legend()
    ax_stats.grid(True, alpha=0.3)
    
    # 子图2：阈值分析
    ax_thresh = fig.add_subplot(gs[1, 1])
    
    # 计算不同阈值下的检测性能
    thresholds = np.linspace(0, np.max(reconstruction_error), 100)
    tpr_scores = []
    fpr_scores = []
    
    for thresh in thresholds:
        predictions = reconstruction_error > thresh
        if np.any(anomaly_mask):
            tp = np.sum(predictions & anomaly_mask)
            fp = np.sum(predictions & ~anomaly_mask)
            tn = np.sum(~predictions & ~anomaly_mask)
            fn = np.sum(~predictions & anomaly_mask)
            
            tpr = tp / (tp + fn) if (tp + fn) > 0 else 0
            fpr = fp / (fp + tn) if (fp + tn) > 0 else 0
        else:
            tpr, fpr = 0, 0
        
        tpr_scores.append(tpr)
        fpr_scores.append(fpr)
    
    ax_thresh.plot(fpr_scores, tpr_scores, linewidth=2, color='purple')
    ax_thresh.plot([0, 1], [0, 1], 'k--', alpha=0.5)
    ax_thresh.set_title('ROC曲线', fontsize=12, fontweight='bold')
    ax_thresh.set_xlabel('假正率 (FPR)')
    ax_thresh.set_ylabel('真正率 (TPR)')
    ax_thresh.grid(True, alpha=0.3)
    
    # 计算AUC
    auc = np.trapz(tpr_scores, fpr_scores)
    ax_thresh.text(0.6, 0.2, f'AUC = {auc:.3f}', fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # 子图3：模型学习到的正常模式可视化
    ax_pattern = fig.add_subplot(gs[2, :])
    
    # 展示模型对不同类型信号的重构能力
    pattern_types = ['正常周期', '正常趋势', '异常尖峰', '异常漂移']
    pattern_colors = ['green', 'blue', 'red', 'orange']
    
    # 创建不同类型的模式片段
    segments = [
        (0, 25, '正常周期'),
        (25, 50, '正常趋势'), 
        (50, 75, '异常尖峰'),
        (75, 100, '异常漂移')
    ]
    
    for i, (start, end, pattern_type) in enumerate(segments):
        segment_original = test_window[start:end].flatten()
        segment_reconstructed = reconstruction[start:end]
        segment_time = time_steps[start:end]
        
        # 计算该段的重构质量
        segment_error = np.mean(np.abs(segment_original - segment_reconstructed))
        
        color = pattern_colors[i]
        alpha = 0.8 if '正常' in pattern_type else 0.6
        
        ax_pattern.plot(segment_time, segment_original, linewidth=2, color=color, alpha=alpha, label=f'{pattern_type} (误差: {segment_error:.4f})')
        ax_pattern.plot(segment_time, segment_reconstructed, linewidth=2, color=color, alpha=0.5, linestyle='--')
    
    ax_pattern.set_title('模型学习的正常模式 vs 异常检测能力', fontsize=12, fontweight='bold')
    ax_pattern.set_xlabel('时间步')
    ax_pattern.set_ylabel('信号值')
    ax_pattern.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax_pattern.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/reconstruction_error_anatomy.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 输出统计信息
    print(f"✅ 平均重构误差 - 正常点: {np.mean(normal_error):.6f}")
    if len(anomaly_error) > 0:
        print(f"✅ 平均重构误差 - 异常点: {np.mean(anomaly_error):.6f}")
        print(f"✅ 异常放大倍数: {np.mean(anomaly_error)/np.mean(normal_error):.2f}x")
    print(f"✅ ROC AUC: {auc:.4f}")
    print(f"✅ 可视化已保存至：experiments/reconstruction_error_anatomy.png")

def run_all_experiments():
    """运行所有三个可视化实验"""
    print("=" * 80)
    print("🚀 HTA-AD模型深度分析可视化实验")
    print("=" * 80)
    
    # 创建输出目录
    os.makedirs('experiments', exist_ok=True)
    
    try:
        # 运行三个实验
        experiment_1_cnn_local_pattern_summarizer()
        experiment_2_tcn_receptive_field_visualization()
        experiment_3_reconstruction_error_anatomy()
        
        print("\n" + "=" * 80)
        print("🎉 所有可视化实验完成！")
        print("📁 可视化文件保存在 experiments/ 目录下：")
        print("   📊 cnn_local_pattern_summarizer.png")
        print("   📊 tcn_receptive_field_visualization.png") 
        print("   📊 reconstruction_error_anatomy.png")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 实验执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_experiments() 