#!/usr/bin/env python3
"""
Debug SAE training and purification issues
"""

import torch
import sys
import numpy as np
sys.path.append('.')

from core.models.hta_ad_correct import HTAADCorrect, PostHocSAE

def debug_sae_training():
    """Debug SAE training process"""
    print("🔍 Debugging SAE Training Process")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create models
    model = HTAADCorrect(
        input_dim=1,
        window_size=128,
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        tcn_kernel_size=3,
        cnn_channels=16,
        downsample_stride=2
    ).to(device)
    
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128,
        sparsity_penalty=1e-3
    ).to(device)
    
    # Generate some test data
    np.random.seed(42)
    torch.manual_seed(42)
    
    # Create synthetic training data
    n_windows = 100
    test_windows = torch.randn(n_windows, 128, 1).to(device)
    
    print(f"Test windows shape: {test_windows.shape}")
    
    # Train HTA-AD briefly
    print("\n🔧 Training HTA-AD...")
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-5)
    model.train()
    
    for epoch in range(10):
        optimizer.zero_grad()
        outputs = model(test_windows)
        losses = model.compute_loss(outputs, test_windows)
        loss = losses['total']
        loss.backward()
        optimizer.step()
        
        if (epoch + 1) % 5 == 0:
            print(f"  Epoch {epoch + 1}, Loss: {loss.item():.4f}")
    
    # Get latent vectors
    print("\n🔧 Collecting latent vectors...")
    model.eval()
    with torch.no_grad():
        outputs = model(test_windows)
        latent_vectors = outputs['latent_vectors']
    
    print(f"Latent vectors shape: {latent_vectors.shape}")
    print(f"Latent vectors stats: mean={latent_vectors.mean().item():.4f}, std={latent_vectors.std().item():.4f}")
    print(f"Latent vectors range: [{latent_vectors.min().item():.4f}, {latent_vectors.max().item():.4f}]")
    
    # Train SAE with debugging
    print("\n🔧 Training SAE with debugging...")
    sae_optimizer = torch.optim.AdamW(sae.parameters(), lr=1e-3, weight_decay=1e-5)
    sae.train()
    
    for epoch in range(15):
        sae_optimizer.zero_grad()
        losses = sae.compute_loss(latent_vectors)
        loss = losses['total']
        
        loss.backward()
        sae_optimizer.step()
        
        if (epoch + 1) % 5 == 0:
            print(f"  SAE Epoch {epoch + 1}/15:")
            print(f"    Total Loss: {losses['total'].item():.6f}")
            print(f"    Reconstruction Loss: {losses['reconstruction'].item():.6f}")
            print(f"    Sparsity Loss: {losses['sparsity'].item():.6f}")
            
            # Check feature activations
            with torch.no_grad():
                _, features = sae(latent_vectors)
                active_features = torch.sum(features > 0, dim=0)
                print(f"    Active features per sample: {active_features.float().mean().item():.1f}")
                print(f"    Feature activation rate: {(active_features > 0).float().mean().item():.3f}")
    
    # Test feature identification
    print("\n🔍 Testing feature identification...")
    sae.identify_irrelevant_features(latent_vectors, max_irrelevant_ratio=0.25, only_dead_features=True)
    
    # Test purification on a few samples
    print("\n🧪 Testing purification...")
    sae.eval()
    
    test_sample = latent_vectors[:5]  # First 5 samples
    print(f"Test sample shape: {test_sample.shape}")
    print(f"Test sample stats: mean={test_sample.mean().item():.4f}, std={test_sample.std().item():.4f}")
    
    # Test purification with different alpha values
    for alpha in [0.1, 0.3, 0.5, 0.7, 1.0]:
        z_purified = sae.purify_latent(test_sample, alpha=alpha, debug=False)
        purification_magnitude = torch.mean(torch.abs(test_sample - z_purified)).item()
        print(f"  Alpha {alpha}: purification magnitude = {purification_magnitude:.6f}")
    
    # Check if the issue is with identical results
    print("\n🔍 Checking for identical purification results...")
    purified_results = []
    for i in range(10):
        single_sample = latent_vectors[i:i+1]
        z_purified = sae.purify_latent(single_sample, alpha=0.7, debug=False)
        purification_magnitude = torch.mean(torch.abs(single_sample - z_purified)).item()
        purified_results.append(purification_magnitude)
        print(f"  Sample {i}: magnitude = {purification_magnitude:.6f}")
    
    # Check if all results are identical
    unique_results = len(set([f"{x:.6f}" for x in purified_results]))
    print(f"\nUnique purification magnitudes: {unique_results}/10")
    
    if unique_results == 1:
        print("❌ All purification results are identical - there's a bug!")
        
        # Debug the SAE forward pass
        print("\n🔍 Debugging SAE forward pass...")
        with torch.no_grad():
            sample1 = latent_vectors[0:1]
            sample2 = latent_vectors[1:2]
            
            _, f1 = sae(sample1)
            _, f2 = sae(sample2)
            
            print(f"Features 1 stats: mean={f1.mean().item():.6f}, std={f1.std().item():.6f}")
            print(f"Features 2 stats: mean={f2.mean().item():.6f}, std={f2.std().item():.6f}")
            print(f"Features difference: {torch.mean(torch.abs(f1 - f2)).item():.6f}")
            
            # Check irrelevant feature contributions
            if sae.irrelevant_mask is not None:
                f1_irr = f1 * sae.irrelevant_mask.unsqueeze(0)
                f2_irr = f2 * sae.irrelevant_mask.unsqueeze(0)
                
                c1_irr = sae.decoder(f1_irr)
                c2_irr = sae.decoder(f2_irr)
                
                print(f"Irrelevant contribution 1: mean={c1_irr.mean().item():.6f}, std={c1_irr.std().item():.6f}")
                print(f"Irrelevant contribution 2: mean={c2_irr.mean().item():.6f}, std={c2_irr.std().item():.6f}")
                print(f"Irrelevant contributions difference: {torch.mean(torch.abs(c1_irr - c2_irr)).item():.6f}")
    else:
        print("✅ Purification results are different - working correctly!")
    
    print("\n🎉 SAE debugging completed!")

if __name__ == "__main__":
    debug_sae_training()
