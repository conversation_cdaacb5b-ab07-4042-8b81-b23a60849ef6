"""
LLM Exception Explanation Module for HTA-AD

This module provides functionality to explain anomalies detected by the HTA-AD model
using actual Large Language Models (LLMs). It analyzes the model's latent space,
reconstruction errors, and feature attributions, then uses LLMs to generate
human-readable explanations.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import os
import json
import requests
import openai
from typing import Dict, List, Tuple, Optional, Union, Any
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMExplanationModule:
    """
    LLM-based Anomaly Explanation Module for HTA-AD

    This module leverages the structured latent representations from HTA-AD
    and actual Large Language Models to provide human-readable explanations
    for detected anomalies.
    """

    def __init__(self,
                 hta_model: nn.Module,
                 window_size: int,
                 feature_names: Optional[List[str]] = None,
                 domain_knowledge: Optional[Dict[str, Any]] = None,
                 llm_config: Optional[Dict[str, Any]] = None,
                 device: str = "cpu"):
        """
        Initialize the LLM Explanation Module

        Parameters
        ----------
        hta_model : nn.Module
            The trained HTA-AD model
        window_size : int
            Size of the sliding window used in HTA-AD
        feature_names : List[str], optional
            Names of the features in the multivariate time series
        domain_knowledge : Dict[str, Any], optional
            Domain-specific knowledge for better explanations
        llm_config : Dict[str, Any], optional
            Configuration for LLM API (provider, model, API key, etc.)
        device : str, default="cpu"
            Device to run the model on
        """
        self.hta_model = hta_model
        self.window_size = window_size
        self.device = device
        self.feature_names = feature_names or []
        self.domain_knowledge = domain_knowledge or {}

        # LLM Configuration
        self.llm_config = llm_config or {
            'provider': 'openai',  # 'openai', 'anthropic', 'local', 'ollama'
            'model': 'gpt-3.5-turbo',
            'api_key': None,
            'base_url': None,
            'max_tokens': 500,
            'temperature': 0.3
        }

        # Initialize LLM client
        self._initialize_llm_client()

        # Initialize hooks for feature attribution
        self.activation = {}
        self.register_hooks()

        # For storing normal pattern statistics
        self.normal_patterns = {}

    def _initialize_llm_client(self):
        """Initialize the LLM client based on configuration"""
        provider = self.llm_config['provider'].lower()

        if provider == 'openai':
            self._initialize_openai_client()
        elif provider == 'anthropic':
            self._initialize_anthropic_client()
        elif provider == 'ollama':
            self._initialize_ollama_client()
        elif provider == 'local':
            self._initialize_local_client()
        else:
            logger.warning(f"Unknown LLM provider: {provider}. Falling back to template-based explanations.")
            self.llm_client = None

    def _initialize_openai_client(self):
        """Initialize OpenAI client"""
        try:
            api_key = self.llm_config.get('api_key') or os.getenv('OPENAI_API_KEY')
            if not api_key:
                logger.warning("OpenAI API key not found. Set OPENAI_API_KEY environment variable or provide in config.")
                self.llm_client = None
                return

            openai.api_key = api_key
            if self.llm_config.get('base_url'):
                openai.api_base = self.llm_config['base_url']

            self.llm_client = openai
            logger.info("OpenAI client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            self.llm_client = None

    def _initialize_anthropic_client(self):
        """Initialize Anthropic Claude client"""
        try:
            import anthropic
            api_key = self.llm_config.get('api_key') or os.getenv('ANTHROPIC_API_KEY')
            if not api_key:
                logger.warning("Anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")
                self.llm_client = None
                return

            self.llm_client = anthropic.Anthropic(api_key=api_key)
            logger.info("Anthropic client initialized successfully")
        except ImportError:
            logger.error("Anthropic library not installed. Install with: pip install anthropic")
            self.llm_client = None
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic client: {e}")
            self.llm_client = None

    def _initialize_ollama_client(self):
        """Initialize Ollama client for local LLM"""
        try:
            base_url = self.llm_config.get('base_url', 'http://localhost:11434')
            # Test connection to Ollama
            response = requests.get(f"{base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.llm_client = {'type': 'ollama', 'base_url': base_url}
                logger.info("Ollama client initialized successfully")
            else:
                logger.warning("Ollama server not accessible")
                self.llm_client = None
        except Exception as e:
            logger.error(f"Failed to initialize Ollama client: {e}")
            self.llm_client = None

    def _initialize_local_client(self):
        """Initialize local transformer model"""
        try:
            from transformers import pipeline
            model_name = self.llm_config.get('model', 'microsoft/DialoGPT-medium')
            self.llm_client = pipeline('text-generation', model=model_name)
            logger.info(f"Local model {model_name} initialized successfully")
        except ImportError:
            logger.error("Transformers library not installed. Install with: pip install transformers")
            self.llm_client = None
        except Exception as e:
            logger.error(f"Failed to initialize local model: {e}")
            self.llm_client = None
        
    def call_llm(self, prompt: str, system_prompt: str = None) -> str:
        """
        Call the configured LLM with the given prompt

        Parameters
        ----------
        prompt : str
            The user prompt to send to the LLM
        system_prompt : str, optional
            System prompt to set context for the LLM

        Returns
        -------
        str
            LLM response text
        """
        if self.llm_client is None:
            return self._fallback_explanation(prompt)

        try:
            provider = self.llm_config['provider'].lower()

            if provider == 'openai':
                return self._call_openai(prompt, system_prompt)
            elif provider == 'anthropic':
                return self._call_anthropic(prompt, system_prompt)
            elif provider == 'ollama':
                return self._call_ollama(prompt, system_prompt)
            elif provider == 'local':
                return self._call_local(prompt, system_prompt)
            else:
                return self._fallback_explanation(prompt)

        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            return self._fallback_explanation(prompt)

    def _call_openai(self, prompt: str, system_prompt: str = None) -> str:
        """Call OpenAI API"""
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        response = self.llm_client.ChatCompletion.create(
            model=self.llm_config['model'],
            messages=messages,
            max_tokens=self.llm_config['max_tokens'],
            temperature=self.llm_config['temperature']
        )
        return response.choices[0].message.content.strip()

    def _call_anthropic(self, prompt: str, system_prompt: str = None) -> str:
        """Call Anthropic Claude API"""
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\n{prompt}"

        response = self.llm_client.messages.create(
            model=self.llm_config.get('model', 'claude-3-sonnet-20240229'),
            max_tokens=self.llm_config['max_tokens'],
            temperature=self.llm_config['temperature'],
            messages=[{"role": "user", "content": full_prompt}]
        )
        return response.content[0].text.strip()

    def _call_ollama(self, prompt: str, system_prompt: str = None) -> str:
        """Call Ollama local API"""
        base_url = self.llm_client['base_url']

        data = {
            "model": self.llm_config.get('model', 'llama2'),
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": self.llm_config['temperature'],
                "num_predict": self.llm_config['max_tokens']
            }
        }

        if system_prompt:
            data["system"] = system_prompt

        response = requests.post(f"{base_url}/api/generate", json=data, timeout=60)
        response.raise_for_status()
        return response.json()['response'].strip()

    def _call_local(self, prompt: str, system_prompt: str = None) -> str:
        """Call local transformer model"""
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\n{prompt}"

        response = self.llm_client(
            full_prompt,
            max_length=len(full_prompt.split()) + self.llm_config['max_tokens'],
            temperature=self.llm_config['temperature'],
            do_sample=True,
            pad_token_id=self.llm_client.tokenizer.eos_token_id
        )

        generated_text = response[0]['generated_text']
        # Extract only the new generated part
        return generated_text[len(full_prompt):].strip()

    def _fallback_explanation(self, prompt: str) -> str:
        """Fallback explanation when LLM is not available"""
        return "异常检测到，但LLM解释服务当前不可用。请检查配置或网络连接。"

    def register_hooks(self):
        """Register forward hooks to capture activations for feature attribution"""
        def get_activation(name):
            def hook(model, input_tensor, output):
                self.activation[name] = output.detach()
            return hook

        # Register hooks for CNN and TCN layers
        if hasattr(self.hta_model, 'encoder_cnn'):
            self.hta_model.encoder_cnn.register_forward_hook(get_activation('encoder_cnn'))

        if hasattr(self.hta_model, 'encoder_tcn'):
            self.hta_model.encoder_tcn.register_forward_hook(get_activation('encoder_tcn'))
    
    def fit(self, normal_windows: np.ndarray):
        """
        Learn normal patterns from training data
        
        Parameters
        ----------
        normal_windows : np.ndarray
            Windows of normal data, shape (n_windows, window_size, n_features)
        """
        self.hta_model.eval()
        
        # Convert to torch tensor
        normal_tensor = torch.from_numpy(normal_windows).float().to(self.device)
        
        with torch.no_grad():
            # Get reconstructions
            reconstructed = self.hta_model(normal_tensor)
            
            # Calculate reconstruction errors
            errors = torch.mean((reconstructed - normal_tensor) ** 2, dim=2)
            
            # Store statistics for each time point in the window
            for t in range(self.window_size):
                self.normal_patterns[t] = {
                    'mean': normal_tensor[:, t, :].mean(dim=0).cpu().numpy(),
                    'std': normal_tensor[:, t, :].std(dim=0).cpu().numpy(),
                    'error_mean': errors[:, t].mean().item(),
                    'error_std': errors[:, t].std().item(),
                    'q1': np.percentile(normal_tensor[:, t, :].cpu().numpy(), 25, axis=0),
                    'q3': np.percentile(normal_tensor[:, t, :].cpu().numpy(), 75, axis=0)
                }
    
    def explain_anomaly(self, 
                        anomaly_window: np.ndarray, 
                        time_index: int,
                        feature_index: Optional[int] = None) -> Dict[str, Any]:
        """
        Generate explanation for an anomaly
        
        Parameters
        ----------
        anomaly_window : np.ndarray
            Window containing the anomaly, shape (window_size, n_features)
        time_index : int
            Index of the anomaly in the original time series
        feature_index : int, optional
            Index of the feature with anomaly (for multivariate)
            
        Returns
        -------
        Dict[str, Any]
            Explanation dictionary with various analysis results
        """
        self.hta_model.eval()
        
        # Convert to torch tensor
        anomaly_tensor = torch.from_numpy(anomaly_window).float().unsqueeze(0).to(self.device)
        n_features = anomaly_window.shape[1]
        
        with torch.no_grad():
            # Get reconstruction
            reconstructed = self.hta_model(anomaly_tensor)
            
            # Calculate reconstruction errors
            errors = ((reconstructed - anomaly_tensor) ** 2).squeeze(0)
            
            # Get latent representation
            # We need to access the latent vector from the model's forward pass
            # This assumes the model has a structure similar to the one in hta_ad.py
            x_permuted = anomaly_tensor.permute(0, 2, 1)
            encoded_cnn = self.hta_model.encoder_cnn(x_permuted)
            encoded_tcn = self.hta_model.encoder_tcn(encoded_cnn)
            encoded_flat = encoded_tcn.flatten(start_dim=1)
            latent_vec = self.hta_model.fc_encode(encoded_flat)
            
            # Analyze errors to determine anomaly type
            error_per_feature = errors.mean(dim=0).cpu().numpy()
            error_per_timestep = errors.mean(dim=1).cpu().numpy()
            
            # Find the most anomalous time points and features
            top_time_indices = np.argsort(error_per_timestep)[-3:][::-1]
            top_feature_indices = np.argsort(error_per_feature)[-3:][::-1]
            
            # Determine anomaly type
            anomaly_type = self._determine_anomaly_type(errors.cpu().numpy())
            
            # Get feature attribution
            attribution = self._compute_feature_attribution(anomaly_tensor)
            
            # Prepare explanation context
            context = {
                'anomaly_type': anomaly_type,
                'time_index': time_index,
                'window': anomaly_window,
                'reconstruction': reconstructed.squeeze(0).cpu().numpy(),
                'errors': errors.cpu().numpy(),
                'latent_vec': latent_vec.squeeze(0).cpu().numpy(),
                'top_time_indices': top_time_indices,
                'top_feature_indices': top_feature_indices,
                'attribution': attribution,
                'feature_names': self.feature_names if self.feature_names else [f"Feature_{i}" for i in range(n_features)]
            }
            
            # Generate textual explanation
            explanation = self._generate_explanation(context)
            context['explanation'] = explanation
            
            return context
    
    def _determine_anomaly_type(self, errors: np.ndarray) -> str:
        """
        Determine the type of anomaly based on error patterns
        
        Parameters
        ----------
        errors : np.ndarray
            Reconstruction errors, shape (window_size, n_features)
            
        Returns
        -------
        str
            Anomaly type: 'point', 'contextual', 'collective', or 'seasonal'
        """
        # Calculate statistics
        error_per_feature = errors.mean(axis=0)
        error_per_timestep = errors.mean(axis=1)
        
        # Check if multiple features are affected (collective anomaly)
        feature_count = np.sum(error_per_feature > np.mean(error_per_feature) + 2 * np.std(error_per_feature))
        if feature_count > 1:
            return "collective_anomaly"
        
        # Check if the error is concentrated at specific time points (point anomaly)
        time_concentration = np.max(error_per_timestep) / (np.mean(error_per_timestep) + 1e-10)
        if time_concentration > 5:
            return "point_anomaly"
        
        # Check for seasonal patterns in the error
        # This is a simplified check - in practice, you'd use more sophisticated methods
        error_autocorr = np.correlate(error_per_timestep, error_per_timestep, mode='full')
        normalized_autocorr = error_autocorr[len(error_autocorr)//2:] / error_autocorr[len(error_autocorr)//2]
        if np.any(normalized_autocorr[1:min(24, len(normalized_autocorr))] > 0.7):
            return "seasonal_anomaly"
        
        # Default to contextual anomaly
        return "contextual_anomaly"
    
    def _compute_feature_attribution(self, anomaly_tensor: torch.Tensor) -> Dict[str, np.ndarray]:
        """
        Compute feature attribution scores to identify which features contributed most to the anomaly
        
        Parameters
        ----------
        anomaly_tensor : torch.Tensor
            Tensor containing the anomaly window
            
        Returns
        -------
        Dict[str, np.ndarray]
            Attribution scores for different components
        """
        attribution = {}
        
        # CNN filter activations
        if 'encoder_cnn' in self.activation:
            cnn_activations = self.activation['encoder_cnn'].squeeze(0).cpu().numpy()
            attribution['cnn_importance'] = np.mean(np.abs(cnn_activations), axis=0)
        
        # TCN layer activations
        if 'encoder_tcn' in self.activation:
            tcn_activations = self.activation['encoder_tcn'].squeeze(0).cpu().numpy()
            attribution['tcn_importance'] = np.mean(np.abs(tcn_activations), axis=0)
        
        return attribution
    
    def _generate_explanation(self, context: Dict[str, Any]) -> str:
        """
        Generate human-readable explanation using LLM based on the analysis context

        Parameters
        ----------
        context : Dict[str, Any]
            Analysis context with all relevant information

        Returns
        -------
        str
            Human-readable explanation of the anomaly generated by LLM
        """
        # Prepare structured data for LLM
        anomaly_data = self._prepare_anomaly_data_for_llm(context)

        # Create system prompt for the LLM
        system_prompt = self._create_system_prompt()

        # Create user prompt with anomaly data
        user_prompt = self._create_user_prompt(anomaly_data)

        # Call LLM to generate explanation
        explanation = self.call_llm(user_prompt, system_prompt)

        return explanation

    def _prepare_anomaly_data_for_llm(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare anomaly data in a structured format for LLM consumption

        Parameters
        ----------
        context : Dict[str, Any]
            Analysis context with all relevant information

        Returns
        -------
        Dict[str, Any]
            Structured anomaly data for LLM
        """
        anomaly_type = context['anomaly_type']
        time_index = context['time_index']
        window = context['window']
        reconstruction = context['reconstruction']
        errors = context['errors']
        feature_names = context['feature_names']

        # Find the most anomalous feature and time point
        max_error_time = np.unravel_index(np.argmax(errors), errors.shape)[0]
        max_error_feature = np.unravel_index(np.argmax(errors), errors.shape)[1]

        # Calculate statistics
        error_stats = {
            'max_error': float(np.max(errors)),
            'mean_error': float(np.mean(errors)),
            'std_error': float(np.std(errors)),
            'error_distribution': errors.tolist()
        }

        # Get feature-specific information
        feature_info = {}
        for i, fname in enumerate(feature_names):
            actual_values = window[:, i].tolist()
            reconstructed_values = reconstruction[:, i].tolist()
            feature_errors = errors[:, i].tolist()

            feature_info[fname] = {
                'actual_values': actual_values,
                'reconstructed_values': reconstructed_values,
                'errors': feature_errors,
                'max_error': float(np.max(errors[:, i])),
                'mean_actual': float(np.mean(window[:, i])),
                'std_actual': float(np.std(window[:, i])),
                'trend': self._calculate_trend(actual_values),
                'volatility': float(np.std(actual_values))
            }

            # Add normal pattern statistics if available
            if max_error_time in self.normal_patterns:
                normal_stats = self.normal_patterns[max_error_time]
                feature_info[fname]['normal_mean'] = float(normal_stats['mean'][i])
                feature_info[fname]['normal_std'] = float(normal_stats['std'][i])
                feature_info[fname]['normal_q1'] = float(normal_stats['q1'][i])
                feature_info[fname]['normal_q3'] = float(normal_stats['q3'][i])

        # Prepare domain knowledge context
        domain_context = ""
        if self.domain_knowledge:
            domain_context = json.dumps(self.domain_knowledge, ensure_ascii=False, indent=2)

        return {
            'anomaly_type': anomaly_type,
            'time_index': time_index,
            'window_size': self.window_size,
            'most_anomalous_feature': feature_names[max_error_feature],
            'most_anomalous_time': max_error_time,
            'error_statistics': error_stats,
            'feature_information': feature_info,
            'domain_knowledge': domain_context,
            'top_anomalous_features': [feature_names[i] for i in context['top_feature_indices']],
            'top_anomalous_times': context['top_time_indices'].tolist()
        }

    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction of a time series"""
        if len(values) < 2:
            return "stable"

        start_val = np.mean(values[:len(values)//3])
        end_val = np.mean(values[-len(values)//3:])

        change_percent = (end_val - start_val) / (abs(start_val) + 1e-10) * 100

        if change_percent > 5:
            return "increasing"
        elif change_percent < -5:
            return "decreasing"
        else:
            return "stable"

    def _create_system_prompt(self) -> str:
        """Create system prompt for the LLM"""
        return """你是一个专业的时间序列异常检测专家。你的任务是分析时间序列数据中的异常，并为用户提供清晰、准确、有用的解释。

你的专业能力包括：
1. 理解不同类型的时间序列异常（点异常、上下文异常、集体异常、季节性异常）
2. 分析重构误差模式和特征统计信息
3. 结合领域知识提供可能的原因分析
4. 用通俗易懂的语言解释技术概念

请根据提供的异常数据，生成一个结构化的解释，包括：
1. 异常类型和位置
2. 异常的具体表现（数值偏差、模式变化等）
3. 可能的原因分析
4. 建议的后续行动

请用中文回答，语言要专业但易懂，避免过于技术化的术语。"""

    def _create_user_prompt(self, anomaly_data: Dict[str, Any]) -> str:
        """Create user prompt with anomaly data for the LLM"""
        prompt = f"""请分析以下时间序列异常数据并提供详细解释：

## 基本信息
- 异常类型: {anomaly_data['anomaly_type']}
- 时间索引: {anomaly_data['time_index']}
- 窗口大小: {anomaly_data['window_size']}
- 最异常的特征: {anomaly_data['most_anomalous_feature']}
- 最异常的时间点: {anomaly_data['most_anomalous_time']}

## 误差统计
- 最大误差: {anomaly_data['error_statistics']['max_error']:.6f}
- 平均误差: {anomaly_data['error_statistics']['mean_error']:.6f}
- 误差标准差: {anomaly_data['error_statistics']['std_error']:.6f}

## 特征详细信息
"""

        for feature_name, info in anomaly_data['feature_information'].items():
            prompt += f"""
### {feature_name}
- 最大重构误差: {info['max_error']:.6f}
- 实际值均值: {info['mean_actual']:.4f}
- 实际值标准差: {info['std_actual']:.4f}
- 趋势: {info['trend']}
- 波动性: {info['volatility']:.4f}
"""
            if 'normal_mean' in info:
                prompt += f"""- 正常均值: {info['normal_mean']:.4f}
- 正常标准差: {info['normal_std']:.4f}
- 正常范围 (Q1-Q3): [{info['normal_q1']:.4f}, {info['normal_q3']:.4f}]
"""

        prompt += f"""
## 最异常的特征排序
{', '.join(anomaly_data['top_anomalous_features'])}

## 最异常的时间点排序
{anomaly_data['top_anomalous_times']}
"""

        if anomaly_data['domain_knowledge']:
            prompt += f"""
## 领域知识
{anomaly_data['domain_knowledge']}
"""

        prompt += """
请基于以上信息，提供一个全面的异常解释，包括异常的性质、可能原因和建议措施。"""

        return prompt
    
    def _analyze_pattern(self, actual: np.ndarray, reconstructed: np.ndarray) -> str:
        """
        Analyze the pattern difference between actual and reconstructed signals
        
        Parameters
        ----------
        actual : np.ndarray
            Actual signal values
        reconstructed : np.ndarray
            Reconstructed signal values
            
        Returns
        -------
        str
            Description of the pattern difference
        """
        # Calculate trend
        actual_trend = actual[-1] - actual[0]
        recon_trend = reconstructed[-1] - reconstructed[0]
        
        # Calculate volatility
        actual_volatility = np.std(actual)
        recon_volatility = np.std(reconstructed)
        
        # Determine pattern description
        if abs(actual_trend - recon_trend) > 0.5 * abs(recon_trend):
            if actual_trend > recon_trend:
                return "异常上升趋势"
            else:
                return "异常下降趋势"
        elif actual_volatility > 2 * recon_volatility:
            return "异常波动性增加"
        elif actual_volatility < 0.5 * recon_volatility:
            return "异常波动性减少"
        else:
            return "异常的时序模式"
    
    def _get_possible_cause(self, anomaly_type: str, feature_index: Optional[int]) -> str:
        """
        Get possible cause for the anomaly based on domain knowledge
        
        Parameters
        ----------
        anomaly_type : str
            Type of anomaly
        feature_index : int, optional
            Index of the anomalous feature
            
        Returns
        -------
        str
            Possible cause description
        """
        # Check if we have domain knowledge for this feature and anomaly type
        if self.domain_knowledge:
            feature_key = f"feature_{feature_index}" if feature_index is not None else "general"
            if feature_key in self.domain_knowledge and anomaly_type in self.domain_knowledge[feature_key]:
                return self.domain_knowledge[feature_key][anomaly_type]
        
        # Default causes by anomaly type
        default_causes = {
            "point_anomaly": "传感器故障、突发事件或数据记录错误",
            "contextual_anomaly": "系统状态转换、环境条件变化或操作模式切换",
            "collective_anomaly": "系统级故障、级联故障或外部干扰",
            "seasonal_anomaly": "季节性模式中断、计划外维护或系统重置"
        }
        
        return default_causes.get(anomaly_type, "未知原因")
    
    def visualize_anomaly(self, 
                         context: Dict[str, Any], 
                         save_path: Optional[str] = None) -> plt.Figure:
        """
        Visualize the anomaly with its explanation
        
        Parameters
        ----------
        context : Dict[str, Any]
            Anomaly context from explain_anomaly
        save_path : str, optional
            Path to save the visualization
            
        Returns
        -------
        plt.Figure
            Matplotlib figure with the visualization
        """
        window = context['window']
        reconstruction = context['reconstruction']
        errors = context['errors']
        feature_names = context['feature_names']
        explanation = context['explanation']
        
        # Create figure with subplots
        fig, axs = plt.subplots(3, 1, figsize=(12, 10))
        
        # Plot original vs reconstruction for top anomalous feature
        top_feature = context['top_feature_indices'][0]
        feature_name = feature_names[top_feature]
        
        axs[0].plot(window[:, top_feature], 'b-', label='原始信号')
        axs[0].plot(reconstruction[:, top_feature], 'r--', label='重构信号')
        axs[0].set_title(f'特征 {feature_name} 的原始信号与重构信号对比')
        axs[0].set_xlabel('窗口内时间步')
        axs[0].set_ylabel('值')
        axs[0].legend()
        
        # Plot reconstruction error
        im = axs[1].imshow(errors.T, aspect='auto', cmap='hot')
        axs[1].set_title('重构误差热图')
        axs[1].set_xlabel('窗口内时间步')
        axs[1].set_ylabel('特征')
        axs[1].set_yticks(range(len(feature_names)))
        axs[1].set_yticklabels(feature_names)
        plt.colorbar(im, ax=axs[1])
        
        # Display textual explanation
        axs[2].axis('off')
        axs[2].text(0.05, 0.5, explanation, wrap=True, fontsize=12)
        axs[2].set_title('异常解释')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            
        return fig
