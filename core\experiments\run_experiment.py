"""
Run SAE Interpretability Experiment
This script executes the complete interpretability analysis and generates publication-quality figures.
"""

import sys
import os
import subprocess

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = [
        ('numpy', 'numpy'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('sklearn', 'scikit-learn'),
        ('torch', 'torch')
    ]

    missing_packages = []
    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("✅ All dependencies are installed")
    return True

def create_output_directory():
    """Create output directory for figures"""
    output_dir = "interpretability_results"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 Created output directory: {output_dir}")
    return output_dir

def run_experiment():
    """Run the main experiment"""
    print("🚀 Starting SAE Interpretability Experiment...")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        return False
    
    # Create output directory
    output_dir = create_output_directory()
    
    # Change to output directory
    original_dir = os.getcwd()
    os.chdir(output_dir)
    
    try:
        # Copy experiment script to output directory
        import shutil
        script_path = os.path.join(original_dir, "sae_interpretability_experiment.py")
        if os.path.exists(script_path):
            shutil.copy(script_path, ".")
            print("📋 Copied experiment script to output directory")
        
        # Run the experiment
        print("\n🔬 Executing interpretability analysis...")
        from sae_interpretability_experiment import main
        main()
        
        print("\n🎉 Experiment completed successfully!")
        print(f"📊 Results saved in: {os.path.abspath('.')}")
        print("\nGenerated files:")
        print("  📈 interpretability_case_study.png - Comprehensive interpretability analysis")
        print("  📚 feature_dictionary.png - Learned feature patterns")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during experiment: {str(e)}")
        return False
    
    finally:
        # Return to original directory
        os.chdir(original_dir)

def main():
    """Main function"""
    print("🧠 SAE Interpretability Experiment Runner")
    print("=" * 50)
    
    success = run_experiment()
    
    if success:
        print("\n✅ Experiment completed successfully!")
        print("\n📋 Next steps:")
        print("1. Check the generated plots in 'interpretability_results/' directory")
        print("2. Use these figures in your paper")
        print("3. Adjust parameters in sae_interpretability_experiment.py if needed")
    else:
        print("\n❌ Experiment failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
