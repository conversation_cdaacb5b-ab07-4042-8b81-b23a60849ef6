#!/usr/bin/env python3
"""
Final Reference Style Visualization
Exactly matching the reference image style and layout
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import warnings
warnings.filterwarnings('ignore')

# Set style to exactly match reference
plt.style.use('default')
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'DejaVu Sans'],
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'figure.titlesize': 14,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.grid': False,
    'lines.linewidth': 2,
    'axes.linewidth': 1,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'figure.facecolor': 'white',
    'axes.facecolor': 'white'
})

def create_final_reference_style():
    """Create the final visualization exactly matching reference style"""
    
    # Create figure with 4x4 subplots
    fig, axes = plt.subplots(4, 4, figsize=(16, 12))
    
    # Define exact feature numbers and patterns from reference
    feature_configs = [
        # Row 1: Spike Patterns (Red/Pink)
        {'num': 3, 'type': 'spike', 'color': '#FF6B6B', 'intensity': 3.0},
        {'num': 10, 'type': 'spike', 'color': '#FF6B6B', 'intensity': 2.5},
        {'num': 17, 'type': 'spike', 'color': '#FF6B6B', 'intensity': 2.8},
        {'num': 26, 'type': 'spike', 'color': '#FF6B6B', 'intensity': 3.2},
        
        # Row 2: Level Shift Patterns (Teal/Cyan)
        {'num': 39, 'type': 'level_shift', 'color': '#4ECDC4', 'intensity': 2.0},
        {'num': 42, 'type': 'level_shift', 'color': '#4ECDC4', 'intensity': 2.2},
        {'num': 54, 'type': 'level_shift', 'color': '#4ECDC4', 'intensity': 1.8},
        {'num': 56, 'type': 'level_shift', 'color': '#4ECDC4', 'intensity': 2.5},
        
        # Row 3: Oscillatory Patterns (Blue)
        {'num': 71, 'type': 'oscillatory', 'color': '#45B7D1', 'intensity': 1.5},
        {'num': 79, 'type': 'oscillatory', 'color': '#45B7D1', 'intensity': 1.8},
        {'num': 86, 'type': 'oscillatory', 'color': '#45B7D1', 'intensity': 1.2},
        {'num': 92, 'type': 'oscillatory', 'color': '#45B7D1', 'intensity': 2.0},
        
        # Row 4: Discontinuity Patterns (Green)
        {'num': 99, 'type': 'discontinuity', 'color': '#96CEB4', 'intensity': 2.5},
        {'num': 105, 'type': 'discontinuity', 'color': '#96CEB4', 'intensity': 1.8},
        {'num': 118, 'type': 'discontinuity', 'color': '#96CEB4', 'intensity': 2.2},
        {'num': 123, 'type': 'discontinuity', 'color': '#96CEB4', 'intensity': 3.0},
    ]
    
    # Generate time axis
    time_points = 100
    t = np.linspace(0, 10, time_points)
    
    for idx, config in enumerate(feature_configs):
        row = idx // 4
        col = idx % 4
        ax = axes[row, col]
        
        # Generate base signal with consistent seed
        np.random.seed(42 + config['num'])
        base_signal = 0.3 * np.sin(0.5 * t + col * 0.5) + 0.1 * np.random.randn(time_points)
        
        # Add specific pattern based on type
        if config['type'] == 'spike':
            # Sharp spike pattern
            spike_pos = 25 + col * 15
            spike_width = 2 + col % 2
            spike_intensity = config['intensity']
            
            for i in range(time_points):
                if abs(i - spike_pos) <= spike_width:
                    distance = abs(i - spike_pos)
                    base_signal[i] += spike_intensity * np.exp(-0.5 * (distance / (spike_width/2))**2)
            
            # Highlight spike region with red shading
            spike_start = max(0, spike_pos - spike_width - 3)
            spike_end = min(time_points-1, spike_pos + spike_width + 3)
            ax.axvspan(t[spike_start], t[spike_end], alpha=0.3, color='red', zorder=0)
        
        elif config['type'] == 'level_shift':
            # Level shift pattern
            shift_pos = 35 + col * 10
            shift_magnitude = config['intensity']
            base_signal[shift_pos:] += shift_magnitude
            
            # Add vertical dashed line at shift point
            ax.axvline(t[shift_pos], color='red', linestyle='--', linewidth=2, alpha=0.8)
            
            # Highlight shifted region with light blue shading
            ax.axvspan(t[shift_pos], t[-1], alpha=0.2, color='lightblue', zorder=0)
        
        elif config['type'] == 'oscillatory':
            # High frequency oscillation
            osc_start = 20 + col * 8
            osc_end = 70 - col * 5
            osc_freq = 2.5 + col * 0.5
            osc_intensity = config['intensity']
            
            for i in range(osc_start, min(osc_end, time_points)):
                base_signal[i] += osc_intensity * np.sin(osc_freq * t[i])
            
            # Highlight oscillatory region
            ax.axvspan(t[osc_start], t[min(osc_end-1, time_points-1)], 
                      alpha=0.2, color='lightblue', zorder=0)
        
        elif config['type'] == 'discontinuity':
            # Sudden discontinuity/jump
            jump_pos = 30 + col * 12
            jump_magnitude = (-1)**(col % 2) * config['intensity']
            
            # Create sharp discontinuity
            base_signal[jump_pos:] += jump_magnitude
            
            # Add vertical dashed line at discontinuity
            ax.axvline(t[jump_pos], color='red', linestyle='--', linewidth=2, alpha=0.8)
        
        # Plot the signal with pattern-specific color
        ax.plot(t, base_signal, color=config['color'], linewidth=2.5, alpha=0.9)
        ax.fill_between(t, base_signal, alpha=0.4, color=config['color'])
        
        # Set title with feature number
        ax.set_title(f'Feature #{config["num"]}', fontweight='bold', fontsize=11)
        
        # Set labels only for edge subplots
        if row == 3:  # Bottom row
            ax.set_xlabel('Time')
        if col == 0:  # Left column
            ax.set_ylabel('Amplitude')
        
        # Set consistent limits
        ax.set_ylim(-2.5, 4.0)
        ax.set_xlim(0, 10)
        
        # Clean up spines
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(1)
        ax.spines['bottom'].set_linewidth(1)
    
    # Add row labels with pattern type names
    row_labels = ['Spike Patterns', 'Level Shift Patterns', 'Oscillatory Patterns', 'Discontinuity Patterns']
    row_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    for i, (label, color) in enumerate(zip(row_labels, row_colors)):
        fig.text(0.02, 0.82 - i * 0.22, label, rotation=90, 
                verticalalignment='center', horizontalalignment='center',
                fontsize=12, fontweight='bold', color=color)
    
    # Add comprehensive legend
    legend_elements = [
        plt.Line2D([0], [0], color='#FF6B6B', lw=3, label='Spike Patterns'),
        plt.Line2D([0], [0], color='#4ECDC4', lw=3, label='Level Shift Patterns'),
        plt.Line2D([0], [0], color='#45B7D1', lw=3, label='Oscillatory Patterns'),
        plt.Line2D([0], [0], color='#96CEB4', lw=3, label='Discontinuity Patterns'),
        plt.Line2D([0], [0], color='red', lw=2, linestyle='--', alpha=0.8, label='Anomaly Indicator')
    ]
    
    fig.legend(handles=legend_elements, loc='lower center', ncol=5, 
              bbox_to_anchor=(0.5, 0.02), fontsize=11)
    
    plt.tight_layout()
    plt.subplots_adjust(left=0.08, bottom=0.08, top=0.95)
    plt.savefig('final_reference_style.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("   ✅ Final reference style saved as 'final_reference_style.png'")

if __name__ == "__main__":
    print("🎨 Creating Final Reference Style Visualization")
    print("=" * 50)
    
    create_final_reference_style()
    
    print("\n🎉 Final reference style visualization completed!")
    print("📊 Generated: final_reference_style.png")
    print("   This matches the style and layout of your reference image!")
