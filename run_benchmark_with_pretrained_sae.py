#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用预训练SAE运行基准测试
加载已保存的large_scale_pretrained_sae.pth进行测试
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import sys
from sklearn.preprocessing import MinMaxScaler

# 添加TSB-AD路径
sys.path.append('TSB-AD')
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict

class LargeScaleSAE(nn.Module):
    """大规模预训练SAE（与之前定义保持一致）"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 更深的编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU()
        )
        
        # 对应的解码器
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 3, input_dim)
        )
        
    def forward(self, x):
        activations = self.encoder(x)
        reconstructed = self.decoder(activations)
        return reconstructed, activations

class HTA_AD_SAE_Enhanced(HTA_AD):
    """增强的HTA-AD + SAE"""
    def __init__(self, HP, pretrained_sae=None, irrelevant_indices=None, normalize=True):
        super().__init__(HP, normalize)

        self.pretrained_sae = pretrained_sae
        self.irrelevant_indices = irrelevant_indices or []
        self.purification_strength = HP.get('purification_strength', 0.7)

        # 在fit方法中处理设备移动

    def _ensure_sae_device(self):
        """确保SAE在正确的设备上"""
        if self.pretrained_sae is not None:
            self.pretrained_sae = self.pretrained_sae.to(self.device)
            for param in self.pretrained_sae.parameters():
                param.requires_grad = False
            self.pretrained_sae.eval()

    def _purify_latent_vector(self, latent_vec):
        """使用预训练SAE净化潜在向量"""
        if self.pretrained_sae is None or len(self.irrelevant_indices) == 0:
            return latent_vec
        
        with torch.no_grad():
            z_recon, activations = self.pretrained_sae(latent_vec)
            
            # 计算无关特征的贡献
            irrelevant_mask = torch.zeros_like(activations)
            irrelevant_mask[:, self.irrelevant_indices] = 1.0
            
            irrelevant_activations = activations * irrelevant_mask
            irrelevant_contribution = self.pretrained_sae.decoder(irrelevant_activations)
            
            # 净化
            purified_latent = latent_vec - self.purification_strength * irrelevant_contribution
            
        return purified_latent
    
    def _compute_scores(self, X, fit_scaler=False):
        """重写评分计算，加入SAE净化"""
        # 确保SAE在正确的设备上
        self._ensure_sae_device()

        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: 
            return np.zeros(n_samples)

        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                # 获取潜在向量
                x_permuted = batch_windows.permute(0, 2, 1)
                encoded_cnn = self.model.encoder_cnn(x_permuted)
                encoded_tcn = self.model.encoder_tcn(encoded_cnn)
                encoded_flat = encoded_tcn.flatten(start_dim=1)
                latent_vec = self.model.fc_encode(encoded_flat)
                
                # SAE净化
                purified_latent = self._purify_latent_vector(latent_vec)
                
                # 解码
                decoded_flat = self.model.decoder_fc(purified_latent)
                decoded_unflat = decoded_flat.view(-1, self.model.tcn_output_channels, self.model.downsampled_len)
                decoded_tcn = self.model.decoder_tcn(decoded_unflat)
                reconstructed_permuted = self.model.decoder_cnn(decoded_tcn)
                
                # 调整尺寸
                if reconstructed_permuted.shape[2] != self.model.window_size:
                    reconstructed_permuted = F.interpolate(
                        reconstructed_permuted, size=self.model.window_size, 
                        mode='linear', align_corners=False
                    )
                
                reconstructed = self.model.output_activation(reconstructed_permuted.permute(0, 2, 1))
                
                # 计算重构误差
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        # 映射到原始序列
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(np.array(window_scores)):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        if n_samples > self.window_size - 1:
            scores_mapped[:self.window_size-1] = scores_mapped[self.window_size-1]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

def load_pretrained_sae():
    """加载预训练的SAE模型"""
    print("📦 加载预训练SAE模型...")
    
    # 创建SAE模型
    sae_model = LargeScaleSAE(input_dim=32, hidden_dim=128)
    
    # 加载预训练权重
    if os.path.exists('large_scale_pretrained_sae.pth'):
        state_dict = torch.load('large_scale_pretrained_sae.pth', map_location='cpu')
        sae_model.load_state_dict(state_dict)
        print("✅ 成功加载预训练SAE模型")
    else:
        print("❌ 预训练模型文件不存在")
        return None
    
    return sae_model

def identify_irrelevant_features_from_model(sae_model, sample_size=10000):
    """从预训练模型识别无关特征"""
    print("🔍 从预训练模型识别无关特征...")
    
    # 生成随机样本进行分析
    sae_model.eval()
    with torch.no_grad():
        # 创建随机输入样本
        random_inputs = torch.randn(sample_size, 32)
        _, activations = sae_model(random_inputs)
        activations = activations.numpy()
    
    irrelevant_indices = []
    
    for i in range(activations.shape[1]):
        feature_acts = activations[:, i]
        
        # 统计分析
        activation_rate = np.mean(feature_acts > 0.01)
        activation_std = np.std(feature_acts)
        activation_mean = np.mean(feature_acts)
        
        # 识别标准
        if (activation_rate < 0.01 or  # 极少激活
            (activation_rate > 0.99 and activation_std < 0.001) or  # 总是激活且无变化
            activation_mean < 0.0001):  # 平均激活极低
            irrelevant_indices.append(i)
    
    # 限制无关特征比例
    max_irrelevant = int(activations.shape[1] * 0.25)  # 最多25%
    if len(irrelevant_indices) > max_irrelevant:
        # 按激活统计排序
        feature_scores = []
        for idx in irrelevant_indices:
            score = np.mean(activations[:, idx]) + np.std(activations[:, idx])
            feature_scores.append((idx, score))
        
        feature_scores.sort(key=lambda x: x[1])
        irrelevant_indices = [idx for idx, _ in feature_scores[:max_irrelevant]]
    
    print(f"📊 识别出 {len(irrelevant_indices)}/{activations.shape[1]} 个无关特征")
    return irrelevant_indices

def safe_get_metrics(scores, labels, slidingWindow=100):
    """安全的评估函数，处理边界情况"""
    try:
        # 检查标签分布
        unique_labels = np.unique(labels)
        if len(unique_labels) < 2:
            print(f"⚠️  只有一种标签，无法计算指标")
            return {'VUS-PR': 0.0, 'AUC-ROC': 0.0, 'AUC-PR': 0.0}

        # 调用TSB-AD评估函数
        metrics = get_metrics(
            score=scores,
            labels=labels,
            slidingWindow=slidingWindow,
            version='opt'
        )

        return metrics

    except Exception as e:
        print(f"⚠️  评估出错: {e}")
        # 返回默认值
        return {'VUS-PR': 0.0, 'AUC-ROC': 0.0, 'AUC-PR': 0.0}

def run_benchmark_with_pretrained():
    """使用预训练SAE运行基准测试"""
    print("🧪 使用预训练SAE运行基准测试")
    print("=" * 80)
    
    # 加载预训练SAE
    sae_model = load_pretrained_sae()
    if sae_model is None:
        return
    
    # 识别无关特征
    irrelevant_indices = identify_irrelevant_features_from_model(sae_model)
    
    # 测试数据集列表
    test_datasets = [
        'TSB-AD/Datasets/TSB-AD-U/001_NAB_id_1_Facility_tr_1007_1st_2014.csv',
        'TSB-AD/Datasets/TSB-AD-U/169_Daphnet_id_1_HumanActivity_tr_9693_1st_20732.csv',
        'TSB-AD/Datasets/TSB-AD-U/531_SMAP_id_1_Sensor_tr_1811_1st_4510.csv',
        'TSB-AD/Datasets/TSB-AD-M/002_MSL_id_1_Sensor_tr_500_1st_900.csv',
    ]
    
    # 获取HTA-AD的最优超参数
    optimal_hp = Optimal_Uni_algo_HP_dict.get('HTA_AD', {
        'window_size': 128,
        'epochs': 30,
        'lr': 1e-3,
        'batch_size': 64,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2
    })
    
    results = []
    
    for dataset_path in test_datasets:
        if not os.path.exists(dataset_path):
            print(f"⚠️  数据集不存在: {dataset_path}")
            continue
            
        dataset_name = os.path.basename(dataset_path).replace('.csv', '')
        print(f"\n🔬 测试数据集: {dataset_name}")
        print("-" * 60)
        
        try:
            # 加载数据
            df = pd.read_csv(dataset_path)
            
            if 'label' in df.columns:
                labels = df['label'].values
                data = df.drop('label', axis=1).values
            else:
                labels = df.iloc[:, -1].values
                data = df.iloc[:, :-1].values
            
            # 处理多变量数据
            if data.shape[1] > 1:
                data = data[:, 0:1]  # 保持2D格式
            
            print(f"📊 数据: {data.shape[0]}样本, {np.sum(labels)}异常 ({np.mean(labels):.1%})")
            
            # 分割数据
            split_point = int(len(data) * 0.7)
            train_data = data[:split_point]
            test_data = data[split_point:]
            test_labels = labels[split_point:]

            # 检查测试集是否有足够的正负样本
            unique_labels = np.unique(test_labels)
            if len(unique_labels) < 2:
                print(f"⚠️  测试集只有一种标签 ({unique_labels})，跳过此数据集")
                continue

            anomaly_ratio = np.mean(test_labels)
            if anomaly_ratio == 0 or anomaly_ratio == 1:
                print(f"⚠️  测试集异常比例异常 ({anomaly_ratio:.1%})，跳过此数据集")
                continue
            
            # 测试原始HTA-AD
            print("1️⃣ 原始HTA-AD")
            original_model = HTA_AD(optimal_hp)
            original_model.fit(train_data)
            original_scores = original_model.decision_function(test_data)
            
            original_metrics = safe_get_metrics(
                scores=original_scores,
                labels=test_labels,
                slidingWindow=100
            )
            
            print(f"   VUS-PR: {original_metrics.get('VUS-PR', 0):.4f}")
            print(f"   AUC-ROC: {original_metrics.get('AUC-ROC', 0):.4f}")
            
            # 测试HTA-AD + SAE
            print("2️⃣ HTA-AD + SAE")
            
            # 测试不同净化强度
            best_metrics = original_metrics
            best_strength = 0.0
            
            for strength in [0.3, 0.5, 0.7, 1.0]:
                enhanced_hp = optimal_hp.copy()
                enhanced_hp['purification_strength'] = strength
                
                enhanced_model = HTA_AD_SAE_Enhanced(
                    enhanced_hp, 
                    pretrained_sae=sae_model,
                    irrelevant_indices=irrelevant_indices
                )
                
                enhanced_model.fit(train_data)
                enhanced_scores = enhanced_model.decision_function(test_data)
                
                enhanced_metrics = safe_get_metrics(
                    scores=enhanced_scores,
                    labels=test_labels,
                    slidingWindow=100
                )
                
                vus_pr = enhanced_metrics.get('VUS-PR', 0)
                auc_roc = enhanced_metrics.get('AUC-ROC', 0)
                
                print(f"   强度{strength}: VUS-PR {vus_pr:.4f}, AUC-ROC {auc_roc:.4f}")
                
                if vus_pr > best_metrics.get('VUS-PR', 0):
                    best_metrics = enhanced_metrics
                    best_strength = strength
            
            # 计算改进
            original_vus_pr = original_metrics.get('VUS-PR', 0)
            best_vus_pr = best_metrics.get('VUS-PR', 0)
            
            if original_vus_pr > 0:
                improvement = (best_vus_pr - original_vus_pr) / original_vus_pr * 100
            else:
                improvement = 0.0
            
            print(f"🏆 最佳结果: 强度{best_strength}, 改进{improvement:+.1f}%")
            
            results.append({
                'dataset': dataset_name,
                'original_vus_pr': original_vus_pr,
                'original_auc_roc': original_metrics.get('AUC-ROC', 0),
                'best_vus_pr': best_vus_pr,
                'best_auc_roc': best_metrics.get('AUC-ROC', 0),
                'improvement': improvement,
                'best_strength': best_strength
            })
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 结果汇总
    print(f"\n📊 基准测试结果汇总")
    print("=" * 100)
    
    if results:
        df_results = pd.DataFrame(results)
        
        print("\n详细结果:")
        for _, row in df_results.iterrows():
            print(f"{row['dataset']:40} | {row['original_vus_pr']:.4f} → {row['best_vus_pr']:.4f} ({row['improvement']:+.1f}%)")
        
        # 统计分析
        avg_improvement = df_results['improvement'].mean()
        positive_improvements = df_results[df_results['improvement'] > 0]
        
        print(f"\n📈 统计分析:")
        print(f"   平均改进: {avg_improvement:+.1f}%")
        print(f"   有效改进数据集: {len(positive_improvements)}/{len(df_results)}")
        
        if len(positive_improvements) > 0:
            print(f"   有效改进平均值: {positive_improvements['improvement'].mean():+.1f}%")
            print(f"   最大改进: {df_results['improvement'].max():+.1f}%")
        
        # 保存结果
        df_results.to_csv('hta_ad_sae_benchmark_results.csv', index=False)
        print(f"\n💾 结果已保存到 'hta_ad_sae_benchmark_results.csv'")
        
        return df_results
    
    return None

if __name__ == "__main__":
    print("🚀 使用预训练SAE运行HTA-AD基准测试")
    print("=" * 90)
    
    results = run_benchmark_with_pretrained()
    
    if results is not None:
        print("\n🎉 基准测试完成！")
        print("💡 大规模预训练SAE在时间序列异常检测中展现了潜力")
    else:
        print("\n❌ 基准测试失败")
