#!/usr/bin/env python3
"""
Debug HTA-AD latent vector generation
"""

import torch
import sys
import numpy as np
from sklearn.preprocessing import StandardScaler
sys.path.append('.')

from core.models.hta_ad_correct import HTAADCorrect

def debug_hta_ad_latents():
    """Debug HTA-AD latent vector generation"""
    print("🔍 Debugging HTA-AD Latent Vector Generation")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create model
    model = HTAADCorrect(
        input_dim=1,
        window_size=128,
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        tcn_kernel_size=3,
        cnn_channels=16,
        downsample_stride=2
    ).to(device)
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Create test data
    batch_size = 4
    window_size = 128
    input_dim = 1
    
    # Test with random data
    test_input = torch.randn(batch_size, window_size, input_dim).to(device)
    print(f"\nTest input shape: {test_input.shape}")
    print(f"Test input stats: mean={test_input.mean().item():.4f}, std={test_input.std().item():.4f}")
    
    # Test model components step by step
    model.eval()
    with torch.no_grad():
        print("\n🔧 Testing model components:")
        
        # Step 1: Permute input
        x_permuted = test_input.permute(0, 2, 1)
        print(f"1. Permuted input shape: {x_permuted.shape}")
        print(f"   Stats: mean={x_permuted.mean().item():.4f}, std={x_permuted.std().item():.4f}")
        
        # Step 2: CNN encoding
        encoded_cnn = model.encoder_cnn(x_permuted)
        print(f"2. CNN encoded shape: {encoded_cnn.shape}")
        print(f"   Stats: mean={encoded_cnn.mean().item():.4f}, std={encoded_cnn.std().item():.4f}")
        
        # Step 3: TCN encoding
        encoded_tcn = model.encoder_tcn(encoded_cnn)
        print(f"3. TCN encoded shape: {encoded_tcn.shape}")
        print(f"   Stats: mean={encoded_tcn.mean().item():.4f}, std={encoded_tcn.std().item():.4f}")
        
        # Step 4: Flatten
        encoded_flat = encoded_tcn.flatten(start_dim=1)
        print(f"4. Flattened shape: {encoded_flat.shape}")
        print(f"   Stats: mean={encoded_flat.mean().item():.4f}, std={encoded_flat.std().item():.4f}")
        
        # Step 5: Linear encoding
        latent_vec = model.fc_encode(encoded_flat)
        print(f"5. Latent vector shape: {latent_vec.shape}")
        print(f"   Stats: mean={latent_vec.mean().item():.4f}, std={latent_vec.std().item():.4f}")
        
        # Test full forward pass
        print("\n🔧 Testing full forward pass:")
        outputs = model(test_input)
        reconstruction = outputs['reconstruction']
        latent_vectors = outputs['latent_vectors']
        
        print(f"Forward pass latent shape: {latent_vectors.shape}")
        print(f"Forward pass latent stats: mean={latent_vectors.mean().item():.4f}, std={latent_vectors.std().item():.4f}")
        print(f"Reconstruction shape: {reconstruction.shape}")
        print(f"Reconstruction stats: mean={reconstruction.mean().item():.4f}, std={reconstruction.std().item():.4f}")
        
        # Check if latents are all zeros
        if torch.all(latent_vectors == 0):
            print("❌ All latent vectors are zero!")
            
            # Check individual layer weights
            print("\n🔍 Checking layer weights:")
            for name, param in model.named_parameters():
                if param.requires_grad:
                    print(f"  {name}: mean={param.mean().item():.6f}, std={param.std().item():.6f}")
                    if torch.all(param == 0):
                        print(f"    ❌ {name} is all zeros!")
        else:
            print("✅ Latent vectors are non-zero")
    
    # Test with trained model
    print("\n🔧 Testing with brief training:")
    
    # Create some training data
    np.random.seed(42)
    n_samples = 100
    time_series = []
    for i in range(n_samples):
        t = np.linspace(0, 4*np.pi, 128)
        signal = np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.randn(128)
        time_series.append(signal.reshape(-1, 1))
    
    time_series = np.array(time_series)
    scaler = StandardScaler()
    time_series_flat = time_series.reshape(-1, 1)
    time_series_normalized = scaler.fit_transform(time_series_flat).reshape(time_series.shape)
    time_series_tensor = torch.FloatTensor(time_series_normalized).to(device)
    
    print(f"Training data shape: {time_series_tensor.shape}")
    print(f"Training data stats: mean={time_series_tensor.mean().item():.4f}, std={time_series_tensor.std().item():.4f}")
    
    # Brief training
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-5)
    model.train()
    
    for epoch in range(5):
        optimizer.zero_grad()
        outputs = model(time_series_tensor)
        losses = model.compute_loss(outputs, time_series_tensor)
        loss = losses['total']
        loss.backward()
        optimizer.step()
        
        print(f"  Epoch {epoch + 1}, Loss: {loss.item():.4f}")
    
    # Test latents after training
    model.eval()
    with torch.no_grad():
        outputs = model(time_series_tensor[:5])  # Test first 5 samples
        latent_vectors = outputs['latent_vectors']
        
        print(f"\nAfter training:")
        print(f"Latent vectors shape: {latent_vectors.shape}")
        print(f"Latent vectors stats: mean={latent_vectors.mean().item():.4f}, std={latent_vectors.std().item():.4f}")
        
        if torch.all(latent_vectors == 0):
            print("❌ Still all zeros after training!")
        else:
            print("✅ Latent vectors are now non-zero after training")
    
    print("\n🎉 Debug completed!")

if __name__ == "__main__":
    debug_hta_ad_latents()
