import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
import matplotlib.patches as patches

# 设置中文字体和高质量输出
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['grid.alpha'] = 0.3

def generate_hta_ad_structured():
    """生成HTA-AD的规则圆形分布 - 有边界的有序结构"""
    np.random.seed(42)
    points = []
    
    # 在圆形边界内生成有序分布的点
    n_points = 100
    for _ in range(n_points):
        # 使用均匀分布在圆内
        angle = np.random.uniform(0, 2*np.pi)
        # 平方根分布确保圆内均匀
        radius = 18 * np.sqrt(np.random.uniform(0, 1))
        
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        points.append([x, y])
    
    return np.array(points)

def generate_transformer_chaotic():
    """生成Transformer的杂乱无章分布 - 多个分离的混乱聚类"""
    np.random.seed(123)
    points = []
    
    # 创建多个分离的聚类，模拟混乱分布
    cluster_configs = [
        # (center_x, center_y, n_points, spread)
        (-15, 22, 20, 3),     # 左上角聚类
        (8, 20, 18, 2.5),     # 右上角聚类
        (15, 15, 15, 2),      # 右上中聚类
        (-12, 10, 22, 3),     # 左中聚类
        (5, 8, 20, 2.5),      # 中心聚类
        (15, 2, 18, 2),       # 右中聚类
        (-8, -5, 25, 3),      # 左下聚类
        (12, -8, 20, 2.5),    # 右下聚类
        (8, -18, 15, 2),      # 右下角聚类
        (-5, -22, 18, 3),     # 底部聚类
        (0, -28, 12, 2),      # 最底部聚类
    ]
    
    for center_x, center_y, n_points, spread in cluster_configs:
        for _ in range(n_points):
            # 在每个聚类中心周围随机分布
            x = center_x + np.random.normal(0, spread)
            y = center_y + np.random.normal(0, spread)
            points.append([x, y])
    
    return np.array(points)

def create_comparison_plot():
    """创建精确的潜空间对比图"""
    # 生成数据
    hta_points = generate_hta_ad_structured()
    transformer_points = generate_transformer_chaotic()
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # === 左图: HTA-AD 规则结构 ===
    ax1.scatter(hta_points[:, 0], hta_points[:, 1], 
               c='green', alpha=0.7, s=50, edgecolors='darkgreen', linewidth=0.5)
    
    # 添加虚线圆形边界 - 这是关键特征！
    circle = Circle((0, 0), 18, fill=False, linestyle='--', 
                   color='green', linewidth=2, alpha=0.8)
    ax1.add_patch(circle)
    
    ax1.set_xlim(-25, 25)
    ax1.set_ylim(-25, 25)
    ax1.set_xlabel('t-SNE Dimension 1', fontsize=12)
    ax1.set_ylabel('t-SNE Dimension 2', fontsize=12)
    ax1.set_title('(a) HTA-AD Latent Space', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.set_aspect('equal')
    
    # === 右图: Transformer 杂乱分布 ===
    ax2.scatter(transformer_points[:, 0], transformer_points[:, 1], 
               c='red', alpha=0.6, s=40, edgecolors='darkred', linewidth=0.3)
    
    ax2.set_xlim(-25, 25)
    ax2.set_ylim(-35, 25)  # 扩展Y轴范围匹配原图
    ax2.set_xlabel('t-SNE Dimension 1', fontsize=12)
    ax2.set_ylabel('t-SNE Dimension 2', fontsize=12)
    ax2.set_title('(b) Standard Transformer Latent Space', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存高质量图片
    plt.savefig('precise_latent_space_comparison.pdf', 
                dpi=600, bbox_inches='tight', format='pdf')
    plt.savefig('precise_latent_space_comparison.png', 
                dpi=300, bbox_inches='tight', format='png')
    
    plt.show()
    
    print("✅ 精确复制的潜空间对比图已生成！")
    print("📁 保存文件:")
    print("   - precise_latent_space_comparison.pdf (高质量PDF)")
    print("   - precise_latent_space_comparison.png (预览PNG)")
    print("\n🎯 关键特征:")
    print("   - HTA-AD: 规则圆形边界 + 有序分布")
    print("   - Transformer: 杂乱无章的多聚类分布")

if __name__ == "__main__":
    create_comparison_plot()
