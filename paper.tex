\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
\usepackage{cite}
\usepackage{amsmath, amssymb, amsfonts}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{subcaption}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{threeparttable}
\usepackage{hyperref}
\usepackage{algorithmic}

\usepackage{algorithm}
\usepackage{float} % For better figure positioning
\usepackage{tikz}
\usepackage{pgfplots}
\usetikzlibrary{shapes, arrows, positioning, shadows, decorations.pathreplacing}
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\begin{document}
    \title{Interpretable Time Series Anomaly Detection via Sparse Feature
    Disentanglement in Hourglass Autoencoders}

    \author{\IEEEauthorblockN{Author One} \IEEEauthorblockA{\textit{Computer Science Department} \\ \textit{University Name}\\ City, Country \\ <EMAIL>}
    \and \IEEEauthorblockN{Author Two} \IEEEauthorblockA{\textit{Computer Science Department} \\ \textit{University Name}\\ City, Country \\ <EMAIL>}
    }

    \maketitle

    \begin{abstract}
        The ``black box" nature of deep learning models for Time Series Anomaly
        Detection (TSAD) is a critical limitation in high-stakes applications, where
        understanding the rationale behind an alert is essential. This paper
        introduces a framework that establishes interpretability in TSAD through
        sparse feature disentanglement. The core of our method is an interpretability
        module, based on a Sparse Autoencoder (SAE), which decomposes the 32-dimensional
        latent space of a robust Hourglass Temporal Autoencoder (HTA-AD) into 128
        human-understandable features. This decomposition enables precise, evidence-based
        attribution of anomalies to specific temporal patterns (e.g., spikes, level
        shifts). This mechanism serves a dual role: it provides transparent
        explanations for model decisions and enables a self-regularization
        process by purifying the latent space of irrelevant features. Our framework
        is the first to demonstrate that an interpretable model can achieve state-of-the-art
        performance on both univariate (0.44 VUS-PR) and multivariate (0.39 VUS-PR)
        benchmarks with a single, unified architecture. The proposed method provides
        a solution that not only detects anomalies but also explains why a given
        event was flagged as anomalous.
    \end{abstract}

    \begin{IEEEkeywords}
        Time Series Anomaly Detection, Interpretable Machine Learning, Sparse Autoencoder,
        Feature Attribution, Deep Learning, Autoencoder, Convolutional Neural Networks,
        Temporal Convolutional Network
    \end{IEEEkeywords}

    \section{Introduction}
    \label{sec:intro}

    Time Series Anomaly Detection (TSAD) is a critical function for ensuring operational
    stability in high-stakes domains such as industrial manufacturing and IT infrastructure.
    However, the adoption of deep learning models in these environments faces a critical
    barrier: their ``black box" nature. When a model flags an anomaly, it typically
    offers no transparent rationale for its decision. This lack of
    interpretability undermines trust and limits the practical utility of
    otherwise powerful detection systems \cite{theissler2022explainable}. The central
    question of \textit{``Why was this event flagged?"} remains unanswered, motivating
    the need for a new class of interpretable TSAD frameworks.

    To build a foundation for such an interpretable model, one must first
    address prevalent architectural challenges. These challenges include the misalignment
    of model inductive biases with temporal data and the inability of existing architectures
    to generalize across both univariate and multivariate tasks. A dominant
    trend has been the adoption of Transformer-based models, yet their core inductive
    biases are often misaligned with the continuous, ordered nature of time
    series. As illustrated in Figure~\ref{fig:evidence}, this ``structural
    misalignment" can lead to fragmented latent representations that fail to capture
    global temporal patterns \cite{zeng2023effective}. Furthermore, the field suffers
    from a ``specialization curse," where models are effective on either univariate
    or multivariate tasks but fail to generalize across both, a phenomenon
    empirically verified by modern benchmarks \cite{liu2024tsb}.

    % --- t-SNE Figure, keep it here as it supports the intro's argument ---
    \begin{figure}[htpb]
        \centering
        \includegraphics[width=0.9\columnwidth]{
    figures/hta_ad_vs_transformer_latent_space_beautified.pdf}
        
        \caption{Latent space visualization (t-SNE) on a representative periodic
        dataset. This comparison reveals the architectural limitations of a standard
        Transformer, which produces fragmented local trajectories, failing to capture
        the global periodic structure. In contrast, our HTA-AD learns a highly structured,
        discrete orbit.}
        \label{fig:evidence}
    \end{figure}

    This paper introduces a unified framework that confronts these challenges of
    interpretability, architectural suitability, and generalization simultaneously.
    Our approach consists of two core components. The foundation is a lightweight
    \textbf{Hourglass Temporal Autoencoder (HTA-AD)}, whose CNN and TCN-based architecture
    provides appropriate inductive biases for time series data, addressing both
    structural misalignment and the specialization curse. Built upon this, our
    primary contribution is a \textbf{Sparse Autoencoder (SAE) interpretability
    module}. This module decomposes the HTA-AD's latent space into a dictionary
    of human-understandable features. This enables not only precise, evidence-based
    explanations for anomaly detections but also a novel self-regularization
    mechanism through latent space purification.

    Our main contributions are as follows:
    \begin{enumerate}[label=(\arabic{*})]
        \item We propose a new interpretable framework for TSAD, built upon
            a lightweight \textbf{Hourglass Temporal Autoencoder (HTA-AD)}. This
            core component provides appropriate inductive biases for time series
            data, addressing structural misalignment and the specialization curse.

        \item We introduce a novel \textbf{SAE-based purification mechanism} that
            leverages interpretability for self-regularization, enhancing model robustness
            by filtering irrelevant features.

        \item We provide a critical analysis of the Transformer architecture for TSAD,
            demonstrating its ``structural misalignment" and justifying our architectural
            choices, including the adoption of HTA-AD.

        \item We demonstrate through comprehensive experiments that our interpretable
            framework achieves state-of-the-art performance on both univariate and multivariate
            benchmarks with a single, unified architecture.
    \end{enumerate}

    \section{Related Work}
    \label{sec:related_work}

    Our work is positioned at the intersection of three key research areas in TSAD:
    the evolution of reconstruction-based architectures, the challenge of model generalization,
    and the critical need for interpretability.

    \subsection{Architectural Paradigms in TSAD}
    Reconstruction-based models form a dominant paradigm in unsupervised TSAD. The
    historical progression of these models reveals a clear trajectory towards architectures
    with more suitable inductive biases for temporal data. Early approaches
    based on standard Autoencoders (AEs) and Variational Autoencoders (VAEs)\cite{kingma2013auto}
    established the core principle of learning a compressed manifold of normal data.
    Foundational models include USAD \cite{audibert2020usad}, which introduced adversarial
    training, and OmniAnomaly \cite{su2019robust}, which utilized stochastic RNNs
    to enhance robustness.

    A significant limitation of these early models was their failure to explicitly
    model temporal dependencies. This was addressed by integrating sequential
    models, with the LSTM-based Autoencoder\cite{malhotra2015lstmed} being a
    seminal work. However, the sequential nature of RNNs presents computational
    bottlenecks. This has led to the current state-of-the-art, which favors
    hybrid architectures combining Convolutional Neural Networks (CNNs) for
    local pattern extraction and Temporal Convolutional Networks (TCNs) for
    efficient, long-range dependency modeling \cite{bai2018empirical}.

    In parallel, Transformer-based models have been widely adopted. However, a
    growing body of research has critically examined their suitability for time
    series. A landmark study by Zeng et al.~\cite{zeng2023effective} provides a
    forceful critique, arguing that the permutation-invariant nature of self-attention
    inevitably leads to the loss of critical temporal information. This can lead
    to failure modes such as overfitting to anomalies, as noted in Anomaly Transformer
    \cite{xu2022anomaly}. Consequently, many ``X-former" variants achieve
    performance gains by replacing self-attention with mechanisms that possess
    stronger temporal biases, such as the auto-correlation of Autoformer \cite{wu2021autoformer}
    or the frequency-domain analysis of FEDformer \cite{zhou2022fedformer}. This
    body of work provides a strong rationale for our choice of a CNN-TCN
    backbone over a Transformer-based one.

    \subsection{The Challenge of Generalization}
    A critical challenge for TSAD models is generalization across diverse
    datasets, a problem often termed the ``specialization curse". Models designed
    for multivariate series, which learn inter-channel correlations, often fail on
    univariate data, and vice-versa. Several strategies have been proposed to mitigate
    this. PatchTST \cite{nie2023patchtst} champions a \textbf{channel-independent}
    approach, treating each variable as a separate univariate series to learn
    universal temporal patterns. An orthogonal approach, RevIN
    \cite{kim2022revin}, addresses distribution shifts by normalizing the input and
    de-normalizing the output. In contrast, models like Koopa \cite{liu2023koopa}
    argue for the importance of cross-channel correlations and use adaptive mechanisms
    to dynamically choose between channel-independent and channel-mixing operations.
    This active debate highlights the complexity of building a truly universal
    TSAD model.

    \subsection{The Pursuit of Interpretability}
    As model complexity increases, so does the need for transparency. Post-hoc
    methods like TimeSHAP (from OmniXAI \cite{theissler2022explainable}) can
    attribute scores to input features but can be computationally expensive and
    their faithfulness questioned due to unrealistic input perturbations.

    This has motivated a shift towards models that are \textbf{interpretable by
    design}. Early approaches based on attention weights have been shown to be
    unreliable, as ``attention is not explanation" \cite{Jain2019}. A more robust
    paradigm is \textbf{latent space disentanglement}. This has been explored in
    VAEs, for instance, DCdetector \cite{yang2023dcdetector} disentangles the latent
    space into pre-defined temporal components like level, trend, and seasonality.
    While effective for data with clear periodicities, this approach is limited as
    it requires pre-specifying the factors to be disentangled.

    Several other works have also aimed to create interpretable or robust autoencoders.
    For instance, StackVAE-G \cite{li2022stackvae} achieves interpretability by stacking
    VAEs and incorporating graph structures. Others have proposed frameworks
    combining robust training objectives \cite{kieu2022robust} or adversarial
    learning \cite{deng2023adversarial} with explanation mechanisms. While these
    methods represent significant progress, our approach differs fundamentally: instead
    of modifying the training objective or core architecture to \textit{encourage}
    interpretability, we employ a \textbf{post-hoc, modular SAE} to \textbf{directly
    discover and disentangle} data-driven features from a pre-trained encoder's latent
    space. This allows for feature-level attribution without constraining the
    primary reconstruction model.

    Our work advances this frontier by using a Sparse Autoencoder (SAE), a theoretically-grounded
    method for discovering disentangled features from data without pre-specification.
    While SAEs have been used to find interpretable features in images and text,
    their application to the latent space of a deep temporal model for TSAD is novel.
    While recent works such as KAD-Disformer \cite{kad-disformer2024} explore
    disentanglement within specialized Transformer architectures, our framework is
    the first to demonstrate that a post-hoc SAE can provide unsupervised, data-driven
    feature attribution for a general-purpose temporal autoencoder, achieving universal
    state-of-the-art performance.

    % --- 图2：HTA-AD 整体架构图 ---
    \begin{figure*}[htbp]
        \centering
        \includegraphics[width=\textwidth]{
            figures/hta_ad_architecture.pdf
        } % 确保路径正确
        \caption{The overall hourglass architecture of the HTA-AD component. An input
        window $X \in \mathbb{R}^{W \times D}$ is passed through a three-stage process
        of encoding, bottleneck projection, and symmetric decoding to produce the
        reconstructed window $\hat{X}\in \mathbb{R}^{W \times D}$. This unified
        architecture seamlessly handles both univariate ($D=1$) and multivariate
        ($D>1$) time series.}
        \label{fig:model_architecture}
    \end{figure*}

    \section{Methodology}
    \label{sec:methodology}

    Our proposed framework consists of two main components: (1) a core \textbf{Hourglass
    Temporal Autoencoder (HTA-AD)} responsible for efficient time series reconstruction,
    and (2) an \textbf{SAE-based Interpretability Module} that operates on the latent
    space of the autoencoder to provide feature-level explanations and enable representation
    purification. The overall architecture of the HTA-AD component is shown in
    Figure~\ref{fig:model_architecture}, with the interpretability workflow detailed
    in subsequent sections. The framework operates in an unsupervised, reconstruction-based
    manner, assuming that a model trained on normal data will exhibit high
    reconstruction error on anomalous patterns.

    \subsection{The Hourglass Temporal Autoencoder}
    The HTA-AD is an encoder-decoder model meticulously designed for time series
    data. Its defining characteristic is a symmetric ``hourglass" structure that
    balances representation power with computational efficiency.

    \subsubsection{Encoder}
    The encoder transforms an input window $X \in \mathbb{R}^{W \times D}$ into
    a compact latent representation $z \in \mathbb{R}^{32}$ through three stages:
    \begin{align}
          H_{c} & = \text{CNN-Downsampler}(X)                        \\
          H_{t} & = \text{TCN}_{\text{Encoder}}(H_{c})               \\
          z     & = \text{FC}_{\text{encode}}(\text{Flatten}(H_{t}))
    \end{align}
    The process begins with a strided 1D convolution (CNN Downsampler) for local
    pattern extraction and sequence compression. The resulting feature map is
    then processed by a stack of non-causal Temporal Convolutional Network (TCN)
    blocks, which efficiently model long-range dependencies through exponentially
    growing receptive fields. Finally, a fully-connected layer projects the features
    into the 32-dimensional latent vector $z$.

    \subsubsection{Symmetric Decoder}
    The decoder mirrors the encoder's architecture to reconstruct the input window
    $\hat{X}$ from the latent vector $z$. This symmetric design enforces a
    stable mapping from the latent space back to the data space, encouraging the
    model to learn a well-structured representation. The process involves a
    fully-connected layer to expand the latent vector, an inverse TCN module,
    and a transposed convolutional layer (CNN Upsampler) to restore the original
    sequence length.

    \subsection{SAE-based Interpretability Module}
    To address the opacity of the latent vector $z$, we introduce an interpretability
    module based on a Sparse Autoencoder (SAE). This module transforms the
    opaque latent space into a structured and understandable vocabulary of
    temporal patterns. It operates in two phases: unsupervised pre-training for
    feature discovery (Figure~\ref{fig:sae_train}) and inference-time processing
    for attribution and purification (Figure~\ref{fig:sae_inference}).

    \subsubsection{SAE Architecture and Training}
    The SAE takes the 32-dimensional latent vectors $z$ from HTA-AD as input and
    learns a sparse, overcomplete representation through an encoder-decoder structure:
    \begin{align}
          f       & = \text{ReLU}(W_{\text{enc}}\cdot z + b_{\text{enc}}) \\
          \hat{z} & = W_{\text{dec}}\cdot f + b_{\text{dec}}
    \end{align}
    where $f \in \mathbb{R}^{128}$ represents the sparse feature activations. The
    SAE is pre-trained on over 4 million latent vectors from diverse time series,
    minimizing a loss function that combines reconstruction error with an L1
    sparsity penalty on the activations:
    \begin{equation}
          \mathcal{L}_{\text{SAE}}= \|z - \hat{z}\|_{2}^{2}+ \lambda \|f\|_{1}
    \end{equation}
    This process yields a dictionary of 128 interpretable features, each
    specializing in a distinct temporal pattern.

    \subsubsection{Feature Attribution and Latent Space Purification}
    During inference, the trained SAE enables two key capabilities. First, for \textbf{feature
    attribution}, an anomaly's latent vector is passed through the SAE encoder to
    identify which of the 128 features are most strongly activated, providing a
    direct explanation for the detection.

    Second, for \textbf{latent space purification}, these interpretable features
    allow us to identify and remove the contributions of task-irrelevant or noisy
    patterns. \textbf{Task-irrelevant features are identified heuristically
    based on their activation statistics on a large, diverse corpus. Features
    that are either rarely activated (i.e., 'dead' features) or are ubiquitously
    active with low variance are considered uninformative and are masked during
    the purification stage. This process isolates features that are both sparse and
    discriminative.} The purification process is as follows:
    \begin{align}
          f_{\text{irr}}      & = f \odot M_{\text{irr}}             \\
          c_{\text{irr}}      & = \text{SAE-Decoder}(f_{\text{irr}}) \\
          z_{\text{purified}} & = z - \alpha \cdot c_{\text{irr}}
    \end{align}
    where $f$ are the initial feature activations, $M_{\text{irr}}\in \{0,1\}^{128}$
    is a binary mask that isolates irrelevant features, $c_{\text{irr}}$ is
    their reconstructed contribution, and $\alpha$ is a hyperparameter controlling
    the purification strength. \textbf{The motivation for this is twofold. First,
    by removing low-information features, we reduce noise in the latent space,
    forcing the decoder to rely on a more robust subset of core patterns. Second,
    this acts as a form of self-regularization, preventing the model from overfitting
    to spurious correlations present in the training data, thereby improving its
    generalization.} The resulting $z_{\text{purified}}$ is then passed to the HTA-AD
    decoder for the final reconstruction.

    % --- 图6：SAE 训练阶段示意图 ---
    \begin{figure}[htpb]
        \centering
        \includegraphics[width=0.75\columnwidth]{
            figures/SAE_train.pdf
        } % 确保路径正确
        \caption{SAE Training Phase. The SAE learns to reconstruct HTA-AD latent
        vectors with sparsity constraints, discovering interpretable feature
        activations. This phase establishes a sparse dictionary of temporal features
        from over 4 million latent vectors.}
        \label{fig:sae_train}
    \end{figure}

    % --- 图7：SAE 推理/净化阶段示意图 ---
    \begin{figure}[htpb]
        \centering
        \includegraphics[width=\columnwidth]{
            figures/sae_inference.pdf
        } % 确保路径正确
        \caption{SAE Inference Phase with Purification. The process: (1) Extract
        feature activations via SAE encoder. (2) Apply an irrelevant-feature mask.
        (3) Decode the masked activations to reconstruct the irrelevant
        contribution. (4) Subtract this contribution from the original latent vector
        to obtain the purified representation.}
        \label{fig:sae_inference}
    \end{figure}

    \subsection{Training and Anomaly Scoring}
    The HTA-AD component is trained end-to-end by minimizing the Mean Squared Error
    (MSE) between the input window $X$ and the reconstructed window $\hat{X}$.
    The final point-wise anomaly score $A(t)$ is calculated by averaging the
    reconstruction errors of all windows that cover time point $t$, followed by a
    min-max normalization.

    % --- Main Results Figure (Double Column) ---
    \begin{figure*}[htbp]
        \centering
        \includegraphics[width=\textwidth]{figures/combined_tsb_ad_results.png}
        \caption{Performance ranking on TSB-AD benchmark using VUS-PR metric. Our
        framework (highlighted in red) achieves state-of-the-art performance on both
        univariate (0.44) and multivariate (0.39) benchmarks with a single unified
        architecture, demonstrating a successful solution to the specialization
        curse.}
        \label{fig:main_results}
    \end{figure*}

    \section{Experiments}
    \label{sec:experiments}

    We conduct a comprehensive set of experiments to validate our framework. Our
    evaluation is designed to answer four key research questions: (1) Does our interpretable
    framework achieve state-of-the-art performance on diverse benchmarks? (2)
    How does the architectural design of HTA-AD address the limitations of
    Transformers? (3) Can the SAE module provide meaningful, human-understandable
    explanations for anomaly detections? (4) What is the contribution of each component
    in our framework?

    \subsection{Experimental Setup}

    \subsubsection{Datasets and Baselines}
    Our primary evaluation is conducted on \textbf{TSB-AD}, a comprehensive
    public benchmark for time series anomaly detection \cite{liu2024elephant}. For
    baselines, we select a wide array of models, including classical methods (e.g.,
    PCA), canonical deep learning models (e.g., CNN, LSTMED), and recent state-of-the-art
    approaches (e.g., Anomaly Transformer).

    \subsubsection{Evaluation Metrics and Implementation}
    Our evaluation follows the framework established by the TSB-AD benchmark, with
    the \textbf{Volume Under the PR Surface (VUS-PR)} as our primary, robust segment-aware
    metric. All models are implemented in PyTorch and evaluated on a server with
    a single NVIDIA RTX 4090 GPU. To demonstrate its general-purpose capability,
    our framework employs a \textbf{single, fixed hyperparameter configuration} across
    all datasets, detailed in Table~\ref{tab:hyperparameters}.

    % --- New, Compact Interpretability Figure ---
    \begin{figure}[t]
        \centering
        \begin{subfigure}
            [b]{0.49\textwidth}
            \centering
            \includegraphics[width=\textwidth]{
                figures/feature_dictionary.pdf
            } % Use the 4-panel dictionary
            \caption{Representative features from the learned dictionary}
            \label{fig:case_study_dictionary}
        \end{subfigure}
        \hfill
        \begin{subfigure}
            [b]{0.49\textwidth}
            \centering
            \includegraphics[width=\textwidth]{
                figures/anomaly_attribution.pdf
            } % Use the 3-panel attribution example
            \caption{Anomaly attribution example}
            \label{fig:case_study_attribution}
        \end{subfigure}
        \caption{Case Study on Interpretable Anomaly Attribution. (a) Four representative
        temporal patterns from the learned SAE feature dictionary. (b)
        Attribution for a real-world detection event: the system flags a level-shift
        anomaly, correctly attributes it to the 'Level Shift Pattern' category, and
        identifies Feature \#54 as the primary evidence.}
        \label{fig:interpretability_case_study}
    \end{figure}

    % --- 超参数表格 ---
    \begin{table}[h]
        \centering
        \caption{Key Hyperparameter Configuration.}
        \label{tab:hyperparameters}
        \begin{tabular}{@{}llc@{}}
            \toprule \textbf{Component}          & \textbf{Hyperparameter}          & \textbf{Value} \\
            \midrule \multirow{5}{*}{HTA-AD}     & Window Size                      & 128            \\
                                                 & Latent Dimension                 & 32             \\
                                                 & Learning Rate                    & 1e-3           \\
                                                 & Batch Size                       & 64             \\
                                                 & Epochs                           & 30             \\
            \midrule \multirow{3}{*}{SAE Module} & Hidden Dimension                 & 128            \\
                                                 & Sparsity Weight ($\lambda$)      & 0.001          \\
                                                 & Purification Strength ($\alpha$) & 0.7            \\
            \bottomrule
        \end{tabular}
    \end{table}

    \subsection{Main Results: State-of-the-Art Performance}
    \label{ssec:main_results}

    To answer our first research question, we compare the end-to-end performance
    of our framework against all baselines on the complete TSB-AD benchmark. Figure~\ref{fig:main_results}
    summarizes the performance ranking, demonstrating that our framework
    significantly outperforms all baseline methods by achieving the highest VUS-PR
    scores of 0.44 on univariate and 0.39 on multivariate benchmarks. The performance
    gaps are substantial; for instance, it surpasses the next best multivariate method
    by 25\%. Most importantly, our model is the first to achieve top-tier
    performance on both benchmarks simultaneously with a single architecture. This
    demonstrates that the synergy of a sound architecture and an interpretable regularization
    module can lead to superior, universal performance. Detailed performance metrics
    for all models are available in the Appendix (Table~\ref{tab:full_results_appendix}).

    \subsection{Architectural and Interpretability Analysis}
    Having established the framework's superior performance, we now analyze its
    internal mechanics to answer our second and third research questions.

    \subsubsection{Architectural Inductive Bias}
    To validate HTA-AD's architectural design, we first compare its learned representations
    against those of a standard Transformer. As shown in the t-SNE visualization
    in Figure~\ref{fig:evidence} (in Section~\ref{sec:intro}), HTA-AD learns a coherent,
    structured latent space, while the Transformer produces fragmented trajectories.

    To further test this hypothesis, we conduct a \textbf{training set shuffling
    experiment}. As shown in Figure~\ref{fig:shuffling_exp}, HTA-AD's performance
    degrades as temporal order is destroyed, confirming its reliance on
    sequential patterns. In contrast, Anomaly Transformer's performance is
    largely unaffected. This provides strong evidence for our ``structural misalignment"
    claim and justifies our choice of an architecture with appropriate inductive
    biases.

    % --- Shuffling Experiment Figure ---
    \begin{figure}[t]
        \centering
        \includegraphics[width=\columnwidth]{
            figures/shuffling_experiment_Synthetic_Strong_Temporal_Dependencies.png
        }
        \caption{The Training Set Shuffling Experiment. HTA-AD's performance degrades
        gracefully as temporal order is destroyed, while Anomaly Transformer's
        is unaffected, supporting our ``structural misalignment" hypothesis.}
        \label{fig:shuffling_exp}
    \end{figure}

    \begin{figure*}[htbp]
        \centering
        % Panel (a): Quantitative Analysis of Performance vs. Alpha
        \begin{subfigure}
            [b]{\textwidth}
            \centering
            \includegraphics[width=0.9\textwidth]{
                figures/clean_purification_sensitivity.pdf
            } % 确保图片路径正确
            \caption{Quantitative Performance Analysis}
            \label{fig:sensitivity_performance}
        \end{subfigure}

        \vspace{0.5cm} % Add some vertical space between the panels

        % Panel (b): Qualitative Analysis of Feature Activations
        \begin{subfigure}
            [b]{\textwidth}
            \centering
            \includegraphics[width=0.9\textwidth]{
                figures/clean_purification_mechanism.pdf
            } % 确保图片路径正确
            \caption{Qualitative Effect on Feature Activations}
            \label{fig:sensitivity_activations}
        \end{subfigure}

        \caption{Sensitivity analysis of the purification strength $\alpha$. (a)
        The left panel shows that performance (VUS-PR) follows an inverted U-shape,
        peaking at an optimal range around $\alpha=0.7$. The right panel quantifies
        this as a percentage improvement over the baseline ($\alpha=0$). (b) The
        qualitative analysis reveals the mechanism: optimal purification ($\alpha
        =0.7$) effectively suppresses irrelevant features (red) while preserving
        relevant ones (blue). Over-purification ($\alpha=1.0$) begins to
        incorrectly suppress relevant features (orange), explaining the performance
        degradation.}
        \label{fig:sensitivity_analysis}
    \end{figure*}

    \subsubsection{Interpretability via SAE Feature Attribution}
    A core claim of our work is that the SAE module renders the model
    interpretable. This transforms the model from a ``black box" into a
    transparent system. Figure~\ref{fig:interpretability_case_study} presents a case
    study that demonstrates this capability.

    The case study illustrates a complete, evidence-based explanation. Panel (a)
    shows four representative ``words" from our learned 128-feature dictionary. Panel
    (b) shows the framework in action: it detects an anomaly, correctly attributes
    it to the ``Level Shift Pattern" category, and pinpoints Feature \#54 as the
    key driver with a 13.9x higher activation. This process transforms an opaque
    anomaly score into a transparent, human-understandable explanation,
    answering the question of why an event was flagged. This moves beyond traditional
    methods that only provide an anomaly score, offering insights into the types
    of patterns detected, which is invaluable for domain expert validation and system
    debugging.

    \subsubsection{Analysis of Purification Strength
    \texorpdfstring{$\alpha$}{alpha}}
    To investigate the impact of the purification strength hyperparameter $\alpha$,
    we conduct a sensitivity analysis. Figure~\ref{fig:sensitivity_analysis} presents
    a quantitative and qualitative examination of how different values of $\alpha$
    affect performance and feature representation.

    As shown in Figure~\ref{fig:sensitivity_analysis}(a), the VUS-PR score exhibits
    a clear inverted U-shape as $\alpha$ increases from 0 (no purification) to 1.0.
    Performance peaks in an optimal range around $\alpha=0.7$, achieving up to a
    19.6\% improvement over the baseline. This quantitatively validates that a moderate
    level of purification is most effective.

    Figure~\ref{fig:sensitivity_analysis}(b) provides a qualitative explanation
    for this phenomenon at the feature level. With no purification ($\alpha=0.0$),
    both relevant (blue) and irrelevant (red) features are present. At the optimal
    level ($\alpha=0.7$), the irrelevant features are successfully suppressed
    while relevant features remain largely intact. However, during over-purification
    ($\alpha =1.0$), the mechanism begins to incorrectly suppress relevant features
    (orange), leading to a loss of useful information and subsequent performance
    degradation. This analysis confirms that our choice of $\alpha=0.7$ is well-supported
    and that the purification mechanism works as intended: precisely removing
    noise without significantly distorting the core signal.

    \begin{table*}
        [htbp]
        \centering
        \caption{Ablation study of HTA-AD's components on a representative
        subset of 10 datasets from TSB-AD benchmark.}
        \label{tab:ablation}
        \begin{tabular}{llccccc}
            \toprule Dataset                       & Model Variant        & VUS-PR          & $\Delta$ (\%)         & VUS-ROC         & $\Delta$ (\%) & Time (s)      \\
            \midrule \multirow{5}{*}{Univariate}   & Base (Full Model)    & \textbf{0.6194} & -                     & \textbf{0.8088} & -             & 0.58          \\
                                                   & (A) w/o CNN          & 0.6028          & \textcolor{red}{-2.7} & 0.8050          & -0.5          & 0.71          \\
                                                   & (B) w/o TCN          & 0.5871          & \textcolor{red}{-5.2} & 0.7996          & -1.1          & \textbf{0.22} \\
                                                   & (C) w/o Downsampling & 0.6161          & -0.5                  & 0.8070          & -0.2          & 0.78          \\
                                                   & (D) w/o SAE          & 0.6138          & \textcolor{red}{-0.9} & 0.8065          & -0.3          & 0.55          \\
            \midrule \multirow{5}{*}{Multivariate} & Base (Full Model)    & \textbf{0.4238} & -                     & \textbf{0.8404} & -             & 3.56          \\
                                                   & (A) w/o CNN          & 0.4067          & \textcolor{red}{-4.0} & 0.8435          & +0.4          & 4.87          \\
                                                   & (B) w/o TCN          & 0.4181          & \textcolor{red}{-1.3} & 0.8401          & -0.0          & \textbf{1.58} \\
                                                   & (C) w/o Downsampling & 0.4182          & \textcolor{red}{-1.3} & 0.8380          & -0.3          & 5.19          \\
                                                   & (D) w/o SAE          & 0.4195          & \textcolor{red}{-1.0} & 0.8389          & -0.2          & 3.42          \\
            \bottomrule
        \end{tabular}
    \end{table*}

    \subsection{Ablation Study}
    \label{subsec:ablation} Finally, to answer our fourth research question on
    component contributions, we conduct a comprehensive ablation study,
    summarized in Table~\ref{tab:ablation}.

    The results confirm that every component is crucial. The full model consistently
    performs best. Removing the TCN module (B) causes the most significant
    performance drop in the base architecture. Crucially, removing the SAE
    module (D) also leads to a consistent performance degradation (-0.9\% univariate,
    -1.0\% multivariate), quantitatively demonstrating that the interpretability-driven
    purification mechanism provides a tangible performance benefit. This finding
    challenges the traditional assumption that transparency must come at the cost
    of accuracy, showing instead that a better understanding of feature
    contributions can lead to improved model performance.

    \section{Conclusion}
    \label{sec:conclusion}

    This work addressed a fundamental barrier in time series anomaly detection:
    the opaque nature of deep learning models that limits trust and practical
    utility. We demonstrated that this interpretability challenge is coupled with
    architectural issues in popular models and a specialization problem that has
    fragmented research efforts.

    To confront these challenges, we introduced a unified and interpretable
    framework. The core of our contribution is an SAE-based module that decomposes
    the latent space of a robust Hourglass Temporal Autoencoder (HTA-AD) into a dictionary
    of human-understandable features. This mechanism is the first to provide
    clear, evidence-based explanations for anomaly detections in a reconstruction-based
    TSAD model. Furthermore, we showed how this interpretability enables a novel
    self-regularization process—latent space purification—which enhances model robustness
    and performance.

    Our comprehensive experiments on the TSB-AD benchmark confirmed that our framework
    is the first to achieve state-of-the-art performance on both univariate and multivariate
    tasks with a single, unified architecture. This works challenges the notion that
    interpretability and performance are a trade-off, demonstrating instead that
    they can be mutually reinforcing. By providing a solution that not only detects
    anomalies but also explains why, our framework establishes a powerful new
    baseline and lays the foundation for more trustworthy and transparent
    anomaly detection systems.

    While our framework demonstrates strong performance and clear
    interpretability, we acknowledge its limitations. The validation is
    currently conducted on the TSB-AD benchmark, and future work could explore its
    generalization on more diverse, real-world industrial datasets. Additionally,
    the utility of the generated explanations, while qualitatively demonstrated,
    would benefit from formal evaluation through user studies with domain
    experts.

    \section*{Future Work}
    Future research directions include extending the SAE framework to capture
    more complex temporal dependencies, developing interactive visualization tools
    for real-time feature attribution, and investigating the transferability of
    learned feature dictionaries across different application domains.

    \section*{Acknowledgment}
    The authors would like to thank the anonymous reviewers for their valuable
    comments and suggestions.

    \bibliographystyle{IEEEtran}
    \bibliography{IEEEabrv,references}

    % --- 切换到单栏格式，为附录提供更宽敞的布局 ---
    \onecolumn
    \appendix

    \section{Supplementary Materials}
    \label{sec:appendix}

    \subsection{Full Benchmark Results}
    Table~\ref{tab:full_results_appendix} presents the complete performance comparison
    of our framework against all baseline models on the full TSB-AD univariate and
    multivariate benchmarks, ranked by the primary VUS-PR metric. This table provides
    the detailed data supporting the summary presented in the main body.

    % --- 附录中的完整性能对比表 ---
    \begin{table*}
        [h!]
        \centering
        \caption{Full Performance Comparison on the TSB-AD Benchmark (Ranked by VUS-PR).}
        \label{tab:full_results_appendix} \scriptsize % 使用稍大的字号，因为附录空间更充足
        \begin{tabular}{@{}llcccccc@{}}
            \toprule \textbf{Task}                            & \textbf{Method}        & \textbf{VUS-PR}  & \textbf{VUS-ROC} & \textbf{AUC-PR}  & \textbf{AUC-ROC} & \textbf{Standard-F1} & \textbf{R-based-F1} \\
            \midrule \multirow{19}{*}{\textbf{TSB-AD-U}}      & \textbf{HTA-AD (Ours)} & \textbf{0.44}    & \textbf{0.85}    & \textbf{0.41}    & \textbf{0.83}    & \textbf{0.44}        & \textbf{0.46}       \\
                                                              & Sub-PCA                & \underline{0.42} & 0.76             & \underline{0.37} & 0.71             & \underline{0.42}     & \underline{0.41}    \\
                                                              & KShapeAD               & 0.40             & 0.76             & 0.35             & 0.74             & 0.39                 & 0.40                \\
                                                              & POLY                   & 0.39             & 0.76             & 0.31             & 0.73             & 0.37                 & 0.35                \\
                                                              & Series2Graph           & 0.39             & 0.80             & 0.33             & 0.76             & 0.38                 & 0.35                \\
                                                              & MOMENT (FT)            & 0.39             & 0.76             & 0.30             & 0.69             & 0.35                 & 0.35                \\
                                                              & KMeansAD               & 0.37             & 0.76             & 0.32             & 0.74             & 0.37                 & 0.38                \\
                                                              & USAD                   & 0.36             & 0.71             & 0.32             & 0.66             & 0.37                 & 0.40                \\
                                                              & Sub-KNN                & 0.35             & 0.79             & 0.27             & 0.76             & 0.34                 & 0.32                \\
                                                              & MatrixProfile          & 0.35             & 0.76             & 0.26             & 0.73             & 0.33                 & 0.32                \\
                                                              & SAND                   & 0.34             & 0.76             & 0.29             & 0.73             & 0.35                 & 0.36                \\
                                                              & CNN                    & 0.34             & 0.79             & 0.33             & 0.71             & 0.38                 & 0.35                \\
                                                              & LSTMED                 & 0.33             & 0.76             & 0.31             & 0.68             & 0.37                 & 0.34                \\
                                                              & SR                     & 0.32             & \underline{0.81} & 0.32             & \underline{0.81} & 0.38                 & 0.35                \\
                                                              & TimesFM                & 0.30             & 0.74             & 0.28             & 0.67             & 0.34                 & 0.34                \\
                                                              & IForest                & 0.30             & 0.78             & 0.29             & 0.71             & 0.35                 & 0.30                \\
                                                              & OmniAnomaly            & 0.29             & 0.72             & 0.27             & 0.65             & 0.31                 & 0.29                \\
                                                              & Lag-Llama              & 0.27             & 0.72             & 0.25             & 0.65             & 0.30                 & 0.31                \\
                                                              & AnomalyTransformer     & 0.12             & 0.56             & 0.08             & 0.50             & 0.12                 & 0.14                \\
            \midrule[1pt] \multirow{23}{*}{\textbf{TSB-AD-M}} & \textbf{HTA-AD (Ours)} & \textbf{0.39}    & \underline{0.74} & \textbf{0.44}    & \textbf{0.77}    & \textbf{0.48}        & \textbf{0.50}       \\
                                                              & CNN                    & \underline{0.31} & \textbf{0.76}    & \underline{0.32} & \underline{0.73} & \underline{0.37}     & 0.37                \\
                                                              & OmniAnomaly            & \underline{0.31} & 0.69             & 0.27             & 0.65             & 0.32                 & 0.37                \\
                                                              & PCA                    & \underline{0.31} & \underline{0.74} & 0.31             & 0.70             & \underline{0.37}     & 0.29                \\
                                                              & LSTMED                 & \underline{0.31} & \underline{0.74} & 0.31             & 0.70             & 0.36                 & \underline{0.38}    \\
                                                              & USAD                   & 0.30             & 0.68             & 0.26             & 0.64             & 0.31                 & 0.37                \\
                                                              & AutoEncoder            & 0.30             & 0.69             & 0.30             & 0.67             & 0.34                 & 0.28                \\
                                                              & KMeansAD               & 0.29             & 0.73             & 0.25             & 0.69             & 0.31                 & 0.33                \\
                                                              & CBLOF                  & 0.27             & 0.70             & 0.28             & 0.67             & 0.32                 & 0.31                \\
                                                              & MCD                    & 0.27             & 0.69             & 0.27             & 0.65             & 0.33                 & 0.20                \\
                                                              & OCSVM                  & 0.26             & 0.67             & 0.23             & 0.61             & 0.28                 & 0.30                \\
                                                              & Donut                  & 0.26             & 0.71             & 0.20             & 0.64             & 0.28                 & 0.21                \\
                                                              & RobustPCA              & 0.24             & 0.61             & 0.24             & 0.58             & 0.29                 & 0.33                \\
                                                              & FITS                   & 0.21             & 0.66             & 0.15             & 0.58             & 0.22                 & 0.16                \\
                                                              & OFA                    & 0.21             & 0.63             & 0.15             & 0.55             & 0.21                 & 0.17                \\
                                                              & EIF                    & 0.21             & 0.71             & 0.19             & 0.67             & 0.26                 & 0.26                \\
                                                              & COPOD                  & 0.20             & 0.69             & 0.20             & 0.65             & 0.27                 & 0.24                \\
                                                              & IForest                & 0.20             & 0.69             & 0.19             & 0.66             & 0.26                 & 0.24                \\
                                                              & HBOS                   & 0.19             & 0.67             & 0.16             & 0.63             & 0.24                 & 0.24                \\
                                                              & TimesNet               & 0.19             & 0.64             & 0.13             & 0.56             & 0.20                 & 0.17                \\
                                                              & KNN                    & 0.18             & 0.59             & 0.14             & 0.51             & 0.19                 & 0.21                \\
                                                              & TranAD                 & 0.18             & 0.65             & 0.14             & 0.59             & 0.21                 & 0.21                \\
                                                              & LOF                    & 0.14             & 0.60             & 0.10             & 0.53             & 0.15                 & 0.14                \\
                                                              & AnomalyTransformer     & 0.12             & 0.57             & 0.07             & 0.52             & 0.12                 & 0.14                \\
            \bottomrule
        \end{tabular}
    \end{table*}

    \subsection{Detailed Architectural Analysis of HTA-AD}
    This section provides detailed visualizations of the core components of the
    HTA-AD architecture, supporting the design choices discussed in the main body.

    \begin{figure}[h!]
        \centering
        \includegraphics[width=0.7\columnwidth]{
            figures/cnn_feature_analysis.png
        }
        \caption{CNN Feature Extraction Analysis. The left panel shows the 2:1 compression
        of a signal by the CNN downsampler, which preserves essential patterns. The
        right panel demonstrates feature specialization, where different CNN
        channels learn to capture distinct temporal patterns such as pulses and
        trends.}
        \label{fig:cnn_analysis_appendix}
    \end{figure}

    \begin{figure}[h!]
        \centering
        \includegraphics[width=0.6\columnwidth]{figures/TCN_Block.pdf}
        \caption{Detailed structure of a TCN Block. It consists of two dilated non-causal
        convolutional layers with WeightNorm, ReLU, and Dropout. The residual
        connection is crucial for stabilizing training in deep networks.}
        \label{fig:tcn_block_appendix}
    \end{figure}

    \begin{figure}[h!]
        \centering
        \includegraphics[width=0.7\columnwidth]{figures/tcn_receptive_field.png}
        \caption{TCN Receptive Field Growth. Dilated convolutions in TCNs enable
        exponential growth of the receptive field (orange line), achieving 6x
        greater efficiency in capturing long-range dependencies compared to the
        linear growth of standard convolutions (dashed line).}
        \label{fig:tcn_receptive_field_appendix}
    \end{figure}

    \subsection{Detailed Feature and Robustness Analysis}
    This section provides the comprehensive visualizations for the learned SAE
    features and the framework's robustness to data contamination.

    % --- 附录图：详细的特征字典 ---
    \begin{figure}[h!]
        \centering
        \includegraphics[width=0.8\textwidth]{
            figures/feature_dictionary_visualization.png
        }
        \caption{Detailed Feature Dictionary Visualization. The 4x4 grid shows 16
        representative time series patterns that maximally activate different feature
        neurons in our learned 128-feature dictionary. The features are
        systematically organized by their discovered pattern types: (Row 1) Spike
        patterns; (Row 2) Level shift patterns; (Row 3) Oscillatory patterns;
        and (Row 4) Discontinuity patterns. Red shaded regions indicate the anomaly
        locations within the activating segments, demonstrating the model's
        ability to learn diverse and specific temporal signatures without
        explicit supervision.}
        \label{fig:feature_dictionary_detailed}
    \end{figure}

    % --- 附录图：学习到的特征的全面分析 ---
    \begin{figure}[h!]
         
        \centering
         
        \includegraphics[width=0.8\textwidth]{
            figures/sae_comprehensive_analysis.png
        }
         
        \caption{Comprehensive Analysis of Learned SAE Features. This dashboard provides
        a holistic view of the 128 learned features' properties. (a) Latent space
        reconstruction of an anomalous event. (b) Top 15 feature activations, highlighting
        which dictionary items are used to describe the anomaly. (c) An auto-generated
        natural language explanation. (d) The global feature activation rate distribution,
        confirming sparsity. (e) Feature quality analysis, mapping activation
        rates against discriminative power. (f) Attribution of the anomaly to the
        four primary pattern categories.}
          \label{fig:sae_comprehensive_analysis}
    \end{figure}
\end{document}