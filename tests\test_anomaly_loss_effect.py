#!/usr/bin/env python3
"""
测试异常检测损失和伪标签训练的效果
对比论文原始方法 vs 增强方法
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import time
import torch
import warnings
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

def generate_complex_test_data(n_samples=2000):
    """生成复杂的测试数据"""
    np.random.seed(42)
    
    # 生成基础信号：多个周期的复合波
    t = np.linspace(0, 10*np.pi, n_samples)
    data = (np.sin(t) + 0.5*np.sin(3*t) + 0.3*np.sin(7*t) + 
            0.1*np.random.randn(n_samples))
    
    # 创建标签数组
    labels = np.zeros(n_samples)
    anomaly_indices = []
    
    # 1. 点异常 (<PERSON> anomalies)
    for _ in range(30):
        idx = np.random.randint(200, n_samples-200)
        data[idx] += np.random.normal(4, 0.5)
        labels[idx] = 1
        anomaly_indices.append(idx)
    
    # 2. 集体异常 (Collective anomalies)
    for _ in range(8):
        start_idx = np.random.randint(200, n_samples-300)
        length = np.random.randint(30, 80)
        end_idx = start_idx + length
        # 添加趋势变化
        trend = np.linspace(0, 2, length)
        data[start_idx:end_idx] += trend + np.random.normal(0, 0.2, length)
        labels[start_idx:end_idx] = 1
        anomaly_indices.extend(range(start_idx, end_idx))
    
    # 3. 上下文异常 (Contextual anomalies)
    for _ in range(5):
        idx = np.random.randint(200, n_samples-200)
        # 在正常范围内但不符合局部模式
        local_mean = np.mean(data[idx-50:idx+50])
        data[idx] = local_mean + np.random.choice([-1, 1]) * 1.5
        labels[idx] = 1
        anomaly_indices.append(idx)
    
    return data, labels, anomaly_indices


def create_paper_baseline_model():
    """创建论文基线模型（仅重构损失）"""
    import torch.nn as nn
    
    class PaperBaselineModel(nn.Module):
        def __init__(self, input_dim=1, d_model=32, seq_len=128):
            super().__init__()
            self.d_model = d_model
            self.seq_len = seq_len
            
            # 简化的HTA-AD架构
            self.input_projection = nn.Linear(input_dim, d_model)
            self.encoder = nn.TransformerEncoder(
                nn.TransformerEncoderLayer(d_model, nhead=4, batch_first=True),
                num_layers=2
            )
            self.decoder = nn.Linear(d_model, input_dim)
            
        def forward(self, x):
            # x: [batch, seq_len, input_dim]
            x = self.input_projection(x)
            encoded = self.encoder(x)
            reconstructed = self.decoder(encoded)
            return {'reconstruction': reconstructed}
    
    return PaperBaselineModel()


def create_enhanced_model():
    """创建增强模型（重构损失 + 异常检测损失）"""
    import torch.nn as nn
    
    class EnhancedModel(nn.Module):
        def __init__(self, input_dim=1, d_model=32, seq_len=128):
            super().__init__()
            self.d_model = d_model
            self.seq_len = seq_len
            
            # HTA-AD架构
            self.input_projection = nn.Linear(input_dim, d_model)
            self.encoder = nn.TransformerEncoder(
                nn.TransformerEncoderLayer(d_model, nhead=4, batch_first=True),
                num_layers=2
            )
            self.decoder = nn.Linear(d_model, input_dim)
            
            # 异常检测头
            self.anomaly_head = nn.Sequential(
                nn.Linear(d_model, d_model // 2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(d_model // 2, 1)
            )
            
        def forward(self, x):
            # x: [batch, seq_len, input_dim]
            x_proj = self.input_projection(x)
            encoded = self.encoder(x_proj)
            reconstructed = self.decoder(encoded)
            
            # 异常检测
            anomaly_logits = self.anomaly_head(encoded).squeeze(-1)
            anomaly_scores = torch.sigmoid(anomaly_logits)
            
            return {
                'reconstruction': reconstructed,
                'anomaly_scores': anomaly_scores,
                'anomaly_logits': anomaly_logits
            }
    
    return EnhancedModel()


def train_model(model, train_loader, epochs=30, lr=1e-3, use_anomaly_loss=False):
    """训练模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-5)
    
    model.train()
    history = {'total_loss': [], 'recon_loss': [], 'anomaly_loss': []}
    
    for epoch in range(epochs):
        epoch_losses = {'total': 0, 'recon': 0, 'anomaly': 0}
        num_batches = 0
        
        for batch_data in train_loader:
            batch_data = batch_data[0].to(device)  # [batch, seq_len, 1]
            
            optimizer.zero_grad()
            outputs = model(batch_data)
            
            # 重构损失
            recon_loss = torch.nn.MSELoss()(outputs['reconstruction'], batch_data)
            total_loss = recon_loss
            
            # 异常检测损失（如果启用）
            anomaly_loss = torch.tensor(0.0, device=device)
            if use_anomaly_loss and 'anomaly_logits' in outputs:
                # 使用重构误差生成伪标签
                recon_errors = torch.mean((outputs['reconstruction'] - batch_data) ** 2, dim=(1, 2))
                threshold = torch.quantile(recon_errors, 0.9)  # 前10%作为异常
                pseudo_labels = (recon_errors > threshold).float()
                
                # 序列级异常分数
                seq_anomaly_logits = torch.mean(outputs['anomaly_logits'], dim=1)
                anomaly_loss = torch.nn.BCEWithLogitsLoss()(seq_anomaly_logits, pseudo_labels)
                total_loss = total_loss + 0.5 * anomaly_loss  # 权重0.5
            
            total_loss.backward()
            optimizer.step()
            
            epoch_losses['total'] += total_loss.item()
            epoch_losses['recon'] += recon_loss.item()
            epoch_losses['anomaly'] += anomaly_loss.item()
            num_batches += 1
        
        # 记录平均损失
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
            history[f'{key}_loss'].append(epoch_losses[key])
        
        if (epoch + 1) % 10 == 0:
            print(f"  Epoch {epoch+1}/{epochs}: Total={epoch_losses['total']:.4f}, "
                  f"Recon={epoch_losses['recon']:.4f}, Anomaly={epoch_losses['anomaly']:.4f}")
    
    return history


def evaluate_model(model, test_data, test_labels):
    """评估模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()
    
    # 创建滑动窗口
    window_size = 128
    windows = []
    for i in range(len(test_data) - window_size + 1):
        windows.append(test_data[i:i + window_size])
    
    windows = torch.FloatTensor(windows).unsqueeze(-1).to(device)
    
    with torch.no_grad():
        outputs = model(windows)
        
        # 计算重构误差分数
        recon_errors = torch.mean((outputs['reconstruction'] - windows) ** 2, dim=(1, 2))
        
        # 如果有异常检测头，也使用其分数
        if 'anomaly_scores' in outputs:
            anomaly_scores = torch.mean(outputs['anomaly_scores'], dim=1)
            # 结合两种分数
            combined_scores = 0.7 * recon_errors + 0.3 * anomaly_scores
        else:
            combined_scores = recon_errors
    
    # 转换窗口分数为点分数
    point_scores = np.zeros(len(test_data))
    combined_scores = combined_scores.cpu().numpy()
    
    for i, score in enumerate(combined_scores):
        start_idx = i
        end_idx = min(i + window_size, len(test_data))
        point_scores[start_idx:end_idx] = np.maximum(
            point_scores[start_idx:end_idx], score
        )
    
    return point_scores


def run_comparison_experiment():
    """运行对比实验"""
    print("🧪 异常检测损失效果对比实验")
    print("=" * 60)
    
    # 生成数据
    data, labels, anomaly_indices = generate_complex_test_data(2000)
    
    # 分割数据
    split_point = int(len(data) * 0.7)
    train_data = data[:split_point]
    test_data = data[split_point:]
    test_labels = labels[split_point:]
    
    print(f"📊 数据信息:")
    print(f"  - 训练数据: {len(train_data)} 点")
    print(f"  - 测试数据: {len(test_data)} 点")
    print(f"  - 测试异常: {np.sum(test_labels)} 点 ({np.sum(test_labels)/len(test_labels):.1%})")
    
    # 创建数据加载器
    from torch.utils.data import DataLoader, TensorDataset
    
    # 创建训练窗口
    window_size = 128
    train_windows = []
    for i in range(len(train_data) - window_size + 1):
        train_windows.append(train_data[i:i + window_size])
    
    train_dataset = TensorDataset(torch.FloatTensor(train_windows).unsqueeze(-1))
    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    
    results = {}
    
    # 实验1: 论文基线方法（仅重构损失）
    print(f"\n🔬 实验1: 论文基线方法（仅重构损失）")
    print("-" * 40)
    
    model_baseline = create_paper_baseline_model()
    start_time = time.time()
    history_baseline = train_model(model_baseline, train_loader, epochs=30, use_anomaly_loss=False)
    train_time_baseline = time.time() - start_time
    
    scores_baseline = evaluate_model(model_baseline, test_data, test_labels)
    
    results['Paper Baseline'] = {
        'scores': scores_baseline,
        'train_time': train_time_baseline,
        'history': history_baseline
    }
    
    print(f"  ✅ 基线方法完成 (时间: {train_time_baseline:.2f}s)")
    
    # 实验2: 增强方法（重构损失 + 异常检测损失）
    print(f"\n🔬 实验2: 增强方法（重构损失 + 异常检测损失）")
    print("-" * 40)
    
    model_enhanced = create_enhanced_model()
    start_time = time.time()
    history_enhanced = train_model(model_enhanced, train_loader, epochs=30, use_anomaly_loss=True)
    train_time_enhanced = time.time() - start_time
    
    scores_enhanced = evaluate_model(model_enhanced, test_data, test_labels)
    
    results['Enhanced Method'] = {
        'scores': scores_enhanced,
        'train_time': train_time_enhanced,
        'history': history_enhanced
    }
    
    print(f"  ✅ 增强方法完成 (时间: {train_time_enhanced:.2f}s)")
    
    return results, test_data, test_labels, anomaly_indices


def visualize_comparison(results, test_data, test_labels, anomaly_indices):
    """可视化对比结果"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Anomaly Detection Loss Effect Comparison', fontsize=16)
    
    # 1. 原始数据
    ax1 = axes[0, 0]
    ax1.plot(test_data, 'b-', alpha=0.7, label='Test Data')
    test_anomaly_indices = [i for i in anomaly_indices if i >= len(test_data)]
    test_anomaly_indices = [i - (len(test_data) + len(test_data)) for i in anomaly_indices if i >= len(test_data)]
    ax1.scatter(np.where(test_labels == 1)[0], test_data[test_labels == 1], 
               c='red', s=20, label='True Anomalies', alpha=0.8)
    ax1.set_title('Test Data with True Anomalies')
    ax1.set_xlabel('Time Step')
    ax1.set_ylabel('Value')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 训练损失对比
    ax2 = axes[0, 1]
    for method_name, result in results.items():
        if 'history' in result:
            history = result['history']
            ax2.plot(history['total_loss'], label=f'{method_name}', marker='o', markersize=2)
    ax2.set_title('Training Loss Comparison')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_yscale('log')
    
    # 3. 异常分数分布
    ax3 = axes[0, 2]
    for method_name, result in results.items():
        scores = result['scores']
        ax3.hist(scores, bins=30, alpha=0.6, label=method_name, density=True)
    ax3.set_title('Anomaly Score Distribution')
    ax3.set_xlabel('Anomaly Score')
    ax3.set_ylabel('Density')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4-5. 检测结果对比
    method_names = list(results.keys())
    for i, method_name in enumerate(method_names[:2]):
        ax = axes[1, i]
        if method_name in results:
            scores = results[method_name]['scores']
            ax.plot(test_data, 'b-', alpha=0.7, label='Test Data')
            ax.scatter(np.where(test_labels == 1)[0], test_data[test_labels == 1], 
                      c='red', s=20, label='True Anomalies', alpha=0.8)
            
            # 归一化异常分数
            scores_norm = (scores - scores.min()) / (scores.max() - scores.min())
            scores_norm = scores_norm * (test_data.max() - test_data.min()) + test_data.min()
            
            color = 'orange' if i == 0 else 'green'
            ax.plot(scores_norm, color, alpha=0.8, label='Anomaly Scores', linewidth=2)
            
            ax.set_title(f'{method_name} Detection Results')
            ax.set_xlabel('Time Step')
            ax.set_ylabel('Value')
            ax.legend()
            ax.grid(True, alpha=0.3)
    
    # 6. 性能指标对比
    ax6 = axes[1, 2]
    from sklearn.metrics import roc_auc_score, average_precision_score
    
    method_names = []
    auc_scores = []
    ap_scores = []
    train_times = []
    
    for method_name, result in results.items():
        method_names.append(method_name)
        train_times.append(result['train_time'])
        
        try:
            auc = roc_auc_score(test_labels, result['scores'])
            ap = average_precision_score(test_labels, result['scores'])
            auc_scores.append(auc)
            ap_scores.append(ap)
        except:
            auc_scores.append(0.5)
            ap_scores.append(0.5)
    
    x = np.arange(len(method_names))
    width = 0.25
    
    bars1 = ax6.bar(x - width, auc_scores, width, label='AUC', alpha=0.7)
    bars2 = ax6.bar(x, ap_scores, width, label='AP', alpha=0.7)
    bars3 = ax6.bar(x + width, np.array(train_times)/max(train_times), width, label='Norm. Time', alpha=0.7)
    
    ax6.set_xlabel('Method')
    ax6.set_ylabel('Score')
    ax6.set_title('Performance Metrics Comparison')
    ax6.set_xticks(x)
    ax6.set_xticklabels(method_names, rotation=45)
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bars, values in [(bars1, auc_scores), (bars2, ap_scores)]:
        for bar, val in zip(bars, values):
            ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{val:.3f}', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    
    # 保存图片
    import os
    os.makedirs('results/visualizations', exist_ok=True)
    plt.savefig('results/visualizations/anomaly_loss_comparison.png', dpi=300, bbox_inches='tight')
    print(f"\n💾 对比结果已保存到: results/visualizations/anomaly_loss_comparison.png")
    
    plt.show()
    
    return auc_scores, ap_scores


def analyze_results(results, auc_scores, ap_scores):
    """分析实验结果"""
    print("\n" + "=" * 60)
    print("📊 实验结果分析")
    print("=" * 60)
    
    method_names = list(results.keys())
    
    print(f"{'方法':<20} {'AUC':<8} {'AP':<8} {'训练时间(s)':<12} {'分数范围':<15}")
    print("-" * 70)
    
    for i, (method_name, result) in enumerate(results.items()):
        scores = result['scores']
        score_range = f"[{scores.min():.3f}, {scores.max():.3f}]"
        print(f"{method_name:<20} {auc_scores[i]:<8.4f} {ap_scores[i]:<8.4f} "
              f"{result['train_time']:<12.2f} {score_range:<15}")
    
    # 效果分析
    if len(results) >= 2:
        baseline_auc = auc_scores[0]
        enhanced_auc = auc_scores[1]
        improvement = (enhanced_auc - baseline_auc) / baseline_auc * 100
        
        print(f"\n🎯 关键发现:")
        print(f"  - AUC提升: {improvement:+.1f}% ({baseline_auc:.4f} → {enhanced_auc:.4f})")
        
        if improvement > 5:
            print(f"  ✅ 异常检测损失显著提升性能！")
            print(f"  💡 建议: 在论文中加入异常检测损失作为增强方法")
        elif improvement > 1:
            print(f"  ✅ 异常检测损失有一定提升效果")
            print(f"  💡 建议: 可以作为可选的增强技术提及")
        else:
            print(f"  ⚠️ 异常检测损失提升有限")
            print(f"  💡 建议: 保持论文原始方法，或进一步调优")


def main():
    """主函数"""
    print("🔬 异常检测损失和伪标签训练效果测试")
    print("=" * 60)
    
    try:
        # 运行对比实验
        results, test_data, test_labels, anomaly_indices = run_comparison_experiment()
        
        # 可视化结果
        auc_scores, ap_scores = visualize_comparison(results, test_data, test_labels, anomaly_indices)
        
        # 分析结果
        analyze_results(results, auc_scores, ap_scores)
        
        print("\n" + "=" * 60)
        print("📝 实验总结")
        print("=" * 60)
        print("✅ 对比实验完成！")
        print("📊 请查看生成的可视化图表分析两种方法的差异")
        print("💡 根据结果决定是否在论文中加入异常检测损失")
        
    except Exception as e:
        print(f"❌ 实验失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
