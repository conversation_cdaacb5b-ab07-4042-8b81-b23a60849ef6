#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Purification Strength Test
Fast validation of the inverted U-shape hypothesis
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import torch
from sklearn.metrics import average_precision_score
from tqdm import tqdm

# Add TSB-AD path
sys.path.append('TSB-AD')
from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE

def create_quick_dataset():
    """Create a quick synthetic dataset for testing"""
    np.random.seed(42)
    n_samples = 800
    
    # Generate time series with clear patterns
    t = np.linspace(0, 20, n_samples)
    data = np.zeros((n_samples, 2))
    
    # Feature 1: Seasonal pattern
    data[:, 0] = 2 * np.sin(2 * np.pi * 0.1 * t) + 0.5 * np.sin(2 * np.pi * 0.05 * t)
    # Feature 2: Trend + noise
    data[:, 1] = 0.01 * t + np.sin(2 * np.pi * 0.15 * t)
    
    # Add noise
    data += 0.1 * np.random.randn(n_samples, 2)
    
    # Add clear anomalies
    labels = np.zeros(n_samples)
    anomaly_positions = [150, 300, 450, 600, 750]
    
    for pos in anomaly_positions:
        # Level shift anomaly
        end_pos = min(pos + 25, n_samples)
        data[pos:end_pos] += np.array([2.5, 1.8])
        labels[pos:end_pos] = 1
        
        # Spike anomaly
        if pos + 30 < n_samples:
            data[pos + 30] += np.array([4.0, 3.0])
            labels[pos + 30] = 1
    
    return data, labels

def quick_purification_test():
    """Quick test of purification strength sensitivity"""
    print("⚡ Quick Purification Strength Test")
    print("=" * 50)
    
    # Create dataset
    data, labels = create_quick_dataset()
    split_idx = int(0.7 * len(data))
    train_data = data[:split_idx]
    test_data = data[split_idx:]
    test_labels = labels[split_idx:]
    
    print(f"Data: {data.shape}, Anomalies: {np.mean(labels):.1%}")
    
    # Test alpha values
    alpha_values = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
    
    # Base configuration (lightweight for speed)
    base_HP = {
        'window_size': 64,
        'epochs': 15,
        'lr': 1e-3,
        'batch_size': 32,
        'latent_dim': 24,
        'tcn_channels': [24, 24],
        'cnn_channels': 12,
        'downsample_stride': 2,
        'gpu': 0 if torch.cuda.is_available() else -1
    }
    
    base_sae_config = {
        'hidden_dim': 96,
        'sparsity_weight': 0.01
    }
    
    results = []
    
    print("\n🧪 Testing different α values:")
    for alpha in tqdm(alpha_values):
        try:
            # Configure SAE with current alpha
            sae_config = base_sae_config.copy()
            sae_config['purification_strength'] = alpha
            
            # Train model
            model = HTA_AD_SAE(HP=base_HP, normalize=True, sae_config=sae_config)
            model.fit(train_data)
            
            # Evaluate
            scores = model.decision_function(test_data)
            
            if len(np.unique(test_labels)) > 1:
                auc_pr = average_precision_score(test_labels, scores)
            else:
                auc_pr = 0.0
            
            results.append(auc_pr)
            print(f"   α={alpha:.1f}: AUC-PR={auc_pr:.4f}")
            
        except Exception as e:
            print(f"   α={alpha:.1f}: Failed ({e})")
            results.append(0.0)
    
    return alpha_values, results

def create_quick_plot(alpha_values, results):
    """Create a quick visualization of the results"""
    plt.figure(figsize=(10, 6))
    
    # Main plot
    plt.subplot(1, 2, 1)
    plt.plot(alpha_values, results, 'o-', linewidth=3, markersize=8, color='#2E86AB')
    plt.fill_between(alpha_values, results, alpha=0.3, color='#2E86AB')
    
    # Highlight optimal point
    best_idx = np.argmax(results)
    best_alpha = alpha_values[best_idx]
    best_score = results[best_idx]
    
    plt.scatter([best_alpha], [best_score], color='red', s=100, zorder=5)
    plt.annotate(f'Optimal\nα={best_alpha:.1f}\nAUC-PR={best_score:.3f}', 
                xy=(best_alpha, best_score), 
                xytext=(best_alpha + 0.15, best_score + 0.01),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=11, ha='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.8))
    
    plt.xlabel('Purification Strength α', fontsize=12)
    plt.ylabel('AUC-PR Score', fontsize=12)
    plt.title('Purification Strength Sensitivity', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.xlim(-0.05, 1.05)
    
    # Performance improvement plot
    plt.subplot(1, 2, 2)
    baseline = results[0]  # α=0.0
    improvements = [(r - baseline) / baseline * 100 for r in results]
    
    colors = ['red' if imp < 0 else 'green' for imp in improvements]
    bars = plt.bar(alpha_values, improvements, color=colors, alpha=0.7)
    
    # Highlight best
    bars[best_idx].set_color('gold')
    bars[best_idx].set_edgecolor('black')
    bars[best_idx].set_linewidth(2)
    
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    plt.xlabel('Purification Strength α', fontsize=12)
    plt.ylabel('Improvement over Baseline (%)', fontsize=12)
    plt.title('Performance Improvement', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3, axis='y')
    
    # Add value labels
    for bar, imp in zip(bars, improvements):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height >= 0 else -1),
                f'{imp:.1f}%', ha='center', va='bottom' if height >= 0 else 'top', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('quick_purification_test.png', dpi=300, bbox_inches='tight')
    
    return best_alpha, best_score

def analyze_curve_shape(alpha_values, results):
    """Analyze if the curve shows inverted U-shape"""
    print("\n📈 Curve Shape Analysis:")
    
    # Find peak
    peak_idx = np.argmax(results)
    peak_alpha = alpha_values[peak_idx]
    
    # Check if it's inverted U-shape
    is_inverted_u = (peak_idx > 0 and peak_idx < len(results) - 1)
    
    if is_inverted_u:
        print("   ✅ Exhibits inverted U-shape pattern")
        print(f"   📍 Peak at α = {peak_alpha:.1f}")
        
        # Check left and right slopes
        left_slope = results[peak_idx] - results[0] if peak_idx > 0 else 0
        right_slope = results[-1] - results[peak_idx] if peak_idx < len(results) - 1 else 0
        
        print(f"   📈 Left slope (0 to peak): +{left_slope:.3f}")
        print(f"   📉 Right slope (peak to 1.0): {right_slope:.3f}")
        
        if left_slope > 0 and right_slope < 0:
            print("   ✅ Classic inverted U-shape confirmed!")
        else:
            print("   ⚠️ Partial inverted U-shape")
    else:
        if peak_idx == 0:
            print("   📉 Monotonically decreasing")
        elif peak_idx == len(results) - 1:
            print("   📈 Monotonically increasing")
        else:
            print("   🔄 Other pattern")
    
    # Calculate improvement range
    baseline = results[0]
    max_improvement = (max(results) - baseline) / baseline * 100
    final_performance = (results[-1] - baseline) / baseline * 100
    
    print(f"\n📊 Performance Summary:")
    print(f"   Baseline (α=0.0): {baseline:.4f}")
    print(f"   Maximum improvement: +{max_improvement:.1f}%")
    print(f"   Final performance (α=1.0): {final_performance:+.1f}%")
    
    return is_inverted_u, peak_alpha

def main():
    """Main function for quick purification test"""
    print("🚀 Quick Purification Strength Sensitivity Test")
    print("=" * 60)
    
    # Run quick test
    alpha_values, results = quick_purification_test()
    
    # Create visualization
    best_alpha, best_score = create_quick_plot(alpha_values, results)
    
    # Analyze curve shape
    is_inverted_u, peak_alpha = analyze_curve_shape(alpha_values, results)
    
    # Summary
    print(f"\n🎯 Summary:")
    print(f"   Optimal α: {best_alpha:.1f}")
    print(f"   Best AUC-PR: {best_score:.4f}")
    print(f"   Inverted U-shape: {'Yes' if is_inverted_u else 'No'}")
    print(f"   Peak position: α = {peak_alpha:.1f}")
    
    print(f"\n💡 Insights:")
    if is_inverted_u:
        print("   ✅ Moderate purification (α≈0.6-0.8) is optimal")
        print("   ⚠️ Over-purification (α>0.8) hurts performance")
        print("   📝 This validates the hyperparameter choice")
    else:
        print("   📊 Different pattern observed - may need more investigation")
    
    print(f"\n📊 Plot saved: quick_purification_test.png")
    
    # Show plot
    plt.show()

if __name__ == "__main__":
    main()
