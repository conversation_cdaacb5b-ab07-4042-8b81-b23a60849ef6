#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HTA_AD多变量数据集超参数快速测试脚本
用于在少量参数组合上快速测试模型性能
"""

import os
import pandas as pd
import numpy as np
import json
import time
from datetime import datetime
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns

# 导入模型和评估工具
from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.models.HTA_AD_M import HTA_AD_M
from TSB_AD.evaluation.metrics import get_metrics

# 设置随机种子以确保结果可复现
np.random.seed(42)

# 配置参数
CONFIG = {
    # 数据集配置 - 仅使用一个数据集进行快速测试
    'datasets': [
        'Datasets/TSB-AD-U/355_UCR_id_53_HumanActivity_tr_48750_1st_143411.csv',
    ],
    # 训练集比例
    'train_ratio': 0.15,
    # 测试的模型
    'models': ['HTA_AD', 'HTA_AD_M'],
    # 超参数配置 - 仅测试少量组合
    'hyperparameter_sets': [
        # HTA_AD 的基准配置
        {
            'window_size': 128,
            'latent_dim': 32,
            'tcn_channels': [32, 32, 32],
            'cnn_channels': 16,
            'downsample_stride': 2,
            'epochs': 30,
            'lr': 1e-3,
            'batch_size': 64,
            'gpu': 0
        },
        # HTA_AD_M 的基准配置
        {
            'window_size': 128,
            'latent_dim': 32,
            'tcn_channels': [32, 32],
            'cnn_channels': 16,
            'downsample_stride': 2,
            'epochs': 30,
            'lr': 1e-3,
            'batch_size': 64,
            'gpu': 0
        },
        # 更大的窗口大小
        {
            'window_size': 256,
            'latent_dim': 32,
            'tcn_channels': [32, 32, 32],
            'cnn_channels': 16,
            'downsample_stride': 2,
            'epochs': 30,
            'lr': 1e-3,
            'batch_size': 64,
            'gpu': 0
        },
        # 更大的潜在空间维度
        {
            'window_size': 128,
            'latent_dim': 64,
            'tcn_channels': [32, 32, 32],
            'cnn_channels': 16,
            'downsample_stride': 2,
            'epochs': 30,
            'lr': 1e-3,
            'batch_size': 64,
            'gpu': 0
        }
    ],
    # 结果保存路径
    'results_dir': 'quick_hyperparameter_results',
    # 是否保存可视化结果
    'save_plots': True
}

def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def load_dataset(file_path):
    """加载数据集并进行预处理"""
    print(f"加载数据集: {file_path}")
    df = pd.read_csv(file_path).dropna()
    data = df.iloc[:, 0:-1].values.astype(float)
    labels = df['Label'].astype(int).to_numpy()
    
    # 提取数据集名称
    dataset_name = os.path.basename(file_path).split('.')[0]
    
    return data, labels, dataset_name

def run_experiment(model_name, dataset_path, hyperparams):
    """运行单个实验"""
    # 加载数据集
    data, labels, dataset_name = load_dataset(dataset_path)
    
    # 划分训练集和测试集
    train_size = int(len(data) * CONFIG['train_ratio'])
    data_train = data[:train_size]
    data_test = data[train_size:]
    
    start_time = time.time()
    
    try:
        # 选择模型
        if model_name == 'HTA_AD':
            model = HTA_AD(HP=hyperparams)
        elif model_name == 'HTA_AD_M':
            model = HTA_AD_M(HP=hyperparams)
        else:
            raise ValueError(f"不支持的模型: {model_name}")
        
        # 训练模型
        model.fit(data_train)
        
        # 获取测试集的异常分数
        test_scores = model.decision_function(data_test)
        
        # 创建完整的分数数组
        full_scores = np.zeros(len(data))
        full_scores[train_size:] = test_scores
        
        # 计算评估指标
        metrics = get_metrics(full_scores, labels)
        
        # 记录运行时间
        runtime = time.time() - start_time
        
        return {
            'dataset': dataset_name,
            'model': model_name,
            'hyperparameters': hyperparams,
            'metrics': metrics,
            'runtime': runtime,
            'status': 'success'
        }
    
    except Exception as e:
        # 记录错误信息
        return {
            'dataset': dataset_name,
            'model': model_name,
            'hyperparameters': hyperparams,
            'metrics': None,
            'runtime': time.time() - start_time,
            'status': 'error',
            'error_message': str(e)
        }

def visualize_results(results, save_dir):
    """可视化实验结果"""
    # 将结果转换为DataFrame
    rows = []
    for result in results:
        if result['status'] == 'success' and result['metrics'] is not None:
            row = {
                'dataset': result['dataset'],
                'model': result['model'],
                'window_size': result['hyperparameters']['window_size'],
                'latent_dim': result['hyperparameters']['latent_dim'],
                'tcn_channels': str(result['hyperparameters']['tcn_channels']),
                'cnn_channels': result['hyperparameters']['cnn_channels'],
                'downsample_stride': result['hyperparameters']['downsample_stride'],
                'runtime': result['runtime'],
                'config_id': f"{result['model']}_w{result['hyperparameters']['window_size']}_l{result['hyperparameters']['latent_dim']}"
            }
            # 添加指标
            for metric_name, metric_value in result['metrics'].items():
                row[metric_name] = metric_value
            rows.append(row)
    
    if not rows:
        print("没有成功的实验结果可视化")
        return
    
    df = pd.DataFrame(rows)
    
    # 保存结果表格
    df.to_csv(os.path.join(save_dir, 'quick_test_results.csv'), index=False)
    
    # 创建性能比较图
    plt.figure(figsize=(14, 10))
    
    # 选择重要的指标进行可视化
    metrics = ['AUC-ROC', 'AUC-PR', 'Standard-F1', 'Event-based-F1']
    
    # 为每个指标创建子图
    for i, metric in enumerate(metrics):
        plt.subplot(2, 2, i+1)
        
        # 绘制条形图
        sns.barplot(x='config_id', y=metric, data=df)
        plt.title(f'{metric} Comparison')
        plt.xticks(rotation=45)
        plt.tight_layout()
    
    plt.savefig(os.path.join(save_dir, 'metrics_comparison.png'))
    plt.close()
    
    # 创建运行时间比较图
    plt.figure(figsize=(10, 6))
    sns.barplot(x='config_id', y='runtime', data=df)
    plt.title('Runtime Comparison')
    plt.xticks(rotation=45)
    plt.ylabel('Runtime (seconds)')
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'runtime_comparison.png'))
    plt.close()
    
    # 创建性能与运行时间的散点图
    plt.figure(figsize=(12, 8))
    for metric in metrics:
        plt.scatter(df['runtime'], df[metric], label=metric)
        
        # 添加标签
        for i, row in df.iterrows():
            plt.annotate(
                row['config_id'], 
                (row['runtime'], row[metric]),
                fontsize=8
            )
    
    plt.xlabel('Runtime (seconds)')
    plt.ylabel('Metric Value')
    plt.title('Performance vs Runtime')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.savefig(os.path.join(save_dir, 'performance_vs_runtime.png'))
    plt.close()
    
    print(f"可视化结果已保存到 {save_dir}")

def main():
    """主函数"""
    # 创建结果目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(CONFIG['results_dir'], f"quick_test_{timestamp}")
    ensure_dir(results_dir)
    
    # 保存配置
    with open(os.path.join(results_dir, 'config.json'), 'w') as f:
        json.dump(CONFIG, f, indent=4)
    
    # 运行实验
    results = []
    
    # 计算总实验数量
    total_experiments = len(CONFIG['datasets']) * len(CONFIG['models']) * len(CONFIG['hyperparameter_sets'])
    print(f"即将运行 {total_experiments} 个实验...")
    
    for dataset_path in CONFIG['datasets']:
        for model_name in CONFIG['models']:
            # 筛选适合当前模型的超参数集
            for hyperparams in CONFIG['hyperparameter_sets']:
                # 检查超参数是否适合当前模型
                if model_name == 'HTA_AD_M' and len(hyperparams['tcn_channels']) != 2:
                    continue  # 跳过不适合HTA_AD_M的参数配置
                
                print(f"运行 {model_name} 在 {os.path.basename(dataset_path)} 上的实验，参数: {hyperparams}")
                
                # 运行实验
                result = run_experiment(model_name, dataset_path, hyperparams)
                results.append(result)
                
                # 打印实验结果
                if result['status'] == 'success':
                    print(f"实验成功! AUC-ROC: {result['metrics'].get('AUC-ROC', 'N/A')}, 运行时间: {result['runtime']:.2f}秒")
                else:
                    print(f"实验失败: {result['error_message']}")
                
                # 保存中间结果
                with open(os.path.join(results_dir, 'intermediate_results.json'), 'w') as f:
                    json.dump(results, f, indent=4)
    
    # 保存最终结果
    with open(os.path.join(results_dir, 'final_results.json'), 'w') as f:
        json.dump(results, f, indent=4)
    
    # 可视化结果
    if CONFIG['save_plots']:
        visualize_results(results, results_dir)
    
    print(f"快速测试完成，结果已保存到 {results_dir}")

if __name__ == "__main__":
    main() 
 