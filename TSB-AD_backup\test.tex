\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{subcaption}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{multirow}  
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\begin{document}

\title{Breaking the Specialization Curse: A Unified, Lightweight Model for Time Series Anomaly Detection}

\author{\IEEEauthorblockN{Author One}
\IEEEauthorblockA{\textit{Computer Science Department} \\
\textit{University Name}\\
City, Country \\
<EMAIL>}
\and
\IEEEauthorblockN{Author Two}
\IEEEauthorblockA{\textit{Computer Science Department} \\
\textit{University Name}\\
City, Country \\
<EMAIL>}
}

\maketitle

\begin{abstract}
The field of Time Series Anomaly Detection (TSAD) currently faces two critical challenges. First, the dominant research paradigm has gravitated towards complex models, notably Transformer-based architectures, under the assumption that greater complexity yields better performance. Our analysis, however, reveals a fundamental "structural misalignment" between the Transformer's inductive biases and the intrinsic properties of time series, leading to suboptimal reconstruction and a failure to capture global temporal patterns. Second, the field suffers from a persistent "specialization curse," where models excelling at either univariate or multivariate tasks fail to generalize, fragmenting the research landscape.

To confront these challenges head-on, this paper introduces the Hourglass Temporal Autoencoder for Anomaly Detection (HTA-AD), a lightweight yet powerful model with only 0.68M parameters. HTA-AD's meticulously designed hourglass architecture synergizes the locality bias of Convolutional Neural Networks (CNNs) with the temporal ordering bias of non-causal Temporal Convolutional Networks (TCNs), achieving 2:1 compression efficiency and 6× improvement in long-range dependency modeling. This principled design allows it to achieve state-of-the-art (SOTA) performance across the entire large-scale TSB-AD benchmark with a \textbf{single, unified architecture}—the first model in the literature to do so, effectively breaking the "specialization curse." Furthermore, HTA-AD demonstrates remarkable robustness, maintaining high performance even when training data is contaminated with up to 50\% noise, and achieves \textbf{significant error amplification} for anomalies while maintaining precise normal pattern reconstruction. Our work not only contributes an efficient and universal new baseline but, more importantly, provides a tangible and architecturally elegant solution that advocates for a paradigm shift: away from unstructured complexity and towards principled, tailor-made designs for time series data.
\end{abstract}

\begin{IEEEkeywords}
Time Series Anomaly Detection, Deep Learning, Autoencoder, Convolutional Neural Networks, Temporal Convolutional Network, Unified Model
\end{IEEEkeywords}

\section{Introduction}
\label{sec:intro}

Time Series Anomaly Detection (TSAD) is a fundamental technology crucial for ensuring the operational stability of numerous systems across industrial manufacturing, IT infrastructure, and financial security. In the pursuit of higher detection accuracy, the field has fallen into a "complexity trap," increasingly adopting massive, complex models, with Transformer-based architectures leading the charge. The implicit assumption is that the self-attention mechanism, a triumph in Natural Language Processing, can be universally applied to capture complex temporal dependencies. \textbf{This paper argues that this assumption is not only flawed but has led the field astray.}

% --- This figure remains your powerful opening visual evidence ---
\begin{figure}[t]
    \centering
    \includegraphics[width=\columnwidth]{figures/hta_ad_vs_transformer_latent_space_beautified.png}
    \caption{Latent space visualization (t-SNE) on a representative periodic dataset. This comparison reveals the architectural limitations of a standard Transformer, which produces fragmented local trajectories, failing to capture the global periodic structure. In contrast, HTA-AD learns a highly structured, discrete orbit, correctly identifying the signal's recurring states.}
    \label{fig:evidence}
\end{figure}
% --- End of figure ---

We posit that the core architectural design of the Transformer is a suboptimal choice for TSAD due to a "structural misalignment" with the intrinsic properties of time series data. As starkly revealed in Figure~\ref{fig:evidence}, when tasked with learning a highly periodic signal, a standard Transformer produces a latent space of fragmented, disconnected trajectories. While it can capture short-term continuity, its architecture fundamentally fails to model the signal's global periodic structure. In stark contrast, our proposed model, HTA-AD, learns a highly structured, discrete orbit, correctly identifying the recurring states that define the signal's cycle. This powerful visual evidence suggests that the Transformer's core mechanism is ill-suited for tasks where understanding temporal structure is paramount.

Beyond the limitations of specific architectures, the TSAD field faces a second, deeper challenge: the "specialization curse." As empirically demonstrated by the comprehensive TSB-AD benchmark, a performance dichotomy exists wherein models designed for univariate data perform poorly on multivariate tasks, and vice versa. This division has fragmented the research landscape and hindered the development of truly general-purpose, "one-size-fits-all" solutions.

To address these challenges, we propose the Hourglass Temporal Autoencoder for Anomaly Detection (HTA-AD), a lightweight (0.68M) yet powerful model designed with inductive biases tailored for time series data. Its "hourglass" structure efficiently captures local patterns using Convolutional Neural Networks (CNNs) while modeling bidirectional context with non-causal Temporal Convolutional Networks (TCNs). Our proposed HTA-AD addresses these limitations through three key architectural innovations: (1) efficient CNN downsampling that achieves 2:1 compression while preserving multi-scale temporal features, (2) non-causal TCN blocks that provide 6× superior receptive field growth compared to standard convolutions, and (3) a symmetric reconstruction mechanism that demonstrates \textbf{robust error amplification} for anomalies while maintaining high-fidelity normal pattern reconstruction. Our extensive experiments demonstrate that HTA-AD is robust, maintaining high performance even when training data is contaminated with up to 50\% noise. Most critically, HTA-AD is the first model to achieve state-of-the-art (SOTA) performance on both univariate and multivariate benchmarks simultaneously with a single, unified architecture, effectively breaking the specialization curse.

Our main contributions are as follows:
\begin{enumerate}[label=(\arabic*)]
    \item We provide a critical analysis of the Transformer architecture for TSAD, proposing the concept of "structural misalignment" and using visual and empirical evidence to demonstrate its shortcomings.
    \item We propose HTA-AD, an efficient and robust model whose architecture is deliberately designed with the correct inductive biases for learning temporal patterns.
    \item We provide decisive experimental evidence that a single, unified model can achieve SOTA performance on both univariate and multivariate benchmarks, establishing a new, higher standard for universality in the TSAD field and effectively breaking the "specialization curse."
\end{enumerate}

\section{Related Work}
\label{sec:related_work}

Our research is positioned at the intersection of several critical trends in Time Series Anomaly Detection (TSAD): the debate on model complexity, the fundamental role of architectural inductive bias, and the challenge of task specialization.

\subsection{The Complexity Dilemma: From Unstructured to Structured Approaches}

The pursuit of higher performance has led to the dominance of complex deep learning models in TSAD. This trend can be broadly categorized into two main philosophies for modeling dependencies.

\subsubsection{Unstructured Complexity: The Transformer Paradigm}
Transformer-based architectures \cite{zhou2021informer, xu2022anomalytransformer} represent the pinnacle of unstructured, all-to-all dependency modeling. By leveraging self-attention, they attempt to learn relationships between all pairs of points or patches in a sequence. However, this immense expressive power comes at a cost. As we argue in this paper and as supported by recent studies, their core inductive bias is misaligned with time series data, leading to issues like "overfitting on anomalies" \cite{paparrizos2024tsb} where the model becomes too powerful and reconstructs outliers with low error.

\subsubsection{Structured Complexity: The Rise of Graph Neural Networks}
For multivariate time series (MTS), another class of complex models, Graph Neural Networks (GNNs), has gained traction \cite{deng2021graph, jin2024survey}. GNNs introduce a "structured complexity" by explicitly modeling the relationships between variables (e.g., sensors) as a graph. The core mechanism involves passing messages between connected nodes, often guided by a graph attention mechanism \cite{deng2021graph}. Unlike Transformers, GNNs assume a sparse, specific dependency structure, which can be either predefined or learned from the data itself via \textit{structure learning} \cite{deng2021graph}. Recent advancements even focus on integrating hierarchical information from multi-hop neighborhoods to better capture complex dependencies \cite{zhao2024graph}. While powerful, GNNs still represent a highly complex approach focused on inter-variable relationships.

\subsection{The Architectural Heart: Inductive Bias in Time Series Modeling}

The performance of any deep learning model is fundamentally governed by its \textbf{inductive bias}—the set of inherent assumptions it makes about the data. The success of a model hinges on how well its biases align with the properties of the data modality.

\subsubsection{The "Right" Biases for Time Series: Locality and Temporality}
Time series data is defined by two key properties: \textit{locality}, where proximate points are highly correlated, and \textit{temporal ordering}, where the sequence of events is immutable. 
\begin{itemize}
    \item \textbf{CNNs}, with their finite-sized kernels and weight sharing, provide a strong \textit{locality} and \textit{translation invariance} bias, making them excellent at capturing local, shape-based patterns \cite{lecun1998gradient}.
    \item \textbf{TCNs}, through their use of causal or non-causal convolutions, enforce a strict \textit{temporal ordering} bias. Combined with dilated convolutions, they can efficiently model long-range dependencies while respecting the sequential nature of the data \cite{bai2018empirical}.
\end{itemize}
Our proposed HTA-AD is explicitly "tailor-made" for time series, as it synergizes the locality bias of CNNs with the strong temporal ordering bias of TCNs.

\subsubsection{The Transformer's "Structural Misalignment"}
The core self-attention mechanism of the Transformer possesses a \textbf{permutation equivariance} bias \cite{xu2024permutation}. It treats an input sequence as an unordered set of tokens, where the interaction strength is determined by content-based similarity, not by position. This is a powerful bias for tasks like language understanding but is fundamentally at odds with the nature of time series. While \textit{positional encodings} are introduced as a corrective measure, the subsequent self-attention layers still operate on a set-based principle, leading to a loss of crucial temporal information \cite{zeng2023are}. This constitutes a "structural misalignment" that explains its suboptimal performance in TSAD.

\subsection{The Specialization Curse and Evaluation Standards}
The practical challenge of model generalization is highlighted by the "specialization curse," empirically verified by the TSB-AD benchmark, where models excelling at univariate tasks often fail on multivariate ones, and vice versa \cite{paparrizos2024tsb}. This underscores the need for truly universal architectures. Furthermore, our work adheres to modern evaluation standards by using robust, segment-aware metrics like VUS-PR, which provide a more faithful assessment of a model's utility than traditional point-wise scores \cite{paparrizos2024tsb}.

\subsection{Our Positioning}
In summary, the literature reveals a gap for a model that is not only universal but is also built upon sound architectural first principles. HTA-AD is designed to fill this gap. It sidesteps the unstructured complexity of Transformers and the variable-focused complexity of GNNs, instead championing a form of \textbf{structured simplicity} that prioritizes the correct modeling of temporal dynamics.

\section{Methodology}
\label{sec:methodology}

In this section, we elaborate on our proposed \textbf{Hourglass Temporal Autoencoder for Anomaly Detection (HTA-AD)}. HTA-AD is an unsupervised, reconstruction-based model engineered to learn normative time series patterns with high efficiency. Its core design is a symmetric "hourglass" architecture that synergizes convolutional downsampling with temporal context modeling. The fundamental premise is that by learning only normal data, the model will fail to reconstruct anomalous patterns, leading to a significant and detectable reconstruction error.

\subsection{Overall Architecture}
The HTA-AD framework is built upon an encoder-decoder paradigm, as illustrated in Figure~\ref{fig:model_architecture}. Its defining characteristic is the hourglass structure, meticulously designed to balance representation power and computational efficiency for time series analysis. The model comprises three primary stages:
\begin{description}[leftmargin=*, wide]
    \item[Encoder] The encoder compresses an input time series window $X$ into a dense latent representation. It achieves this via a strided 1D convolution ("CNN Downsampler") for sequence compression, followed by a Temporal Convolutional Network (TCN) for capturing temporal dependencies.

    \item[Bottleneck] A fully-connected layer serves as the information bottleneck, projecting the high-dimensional features into a compact latent vector $z$.
    
    \item[Decoder] The decoder symmetrically mirrors the encoder's architecture. It meticulously reconstructs the output window $\hat{X}$ from the latent vector using an inverse TCN and a transposed convolutional layer ("CNN Upsampler").
\end{description}

% --- Figure 1: Overall Architecture Placeholder (Double Column) ---
\begin{figure*}[t] % <-- 将 figure 改为 figure*
    \centering
    % 将宽度单位从 \columnwidth 改为 \textwidth，可以根据需要调整比例
    \includegraphics[width=\textwidth]{figures/hta_ad_architecture.pdf} 
    \caption{The overall hourglass architecture of HTA-AD. An input window $X \in \mathbb{R}^{W \times D}$ is passed through a three-stage process of encoding, bottleneck projection, and symmetric decoding to produce the reconstructed window $\hat{X} \in \mathbb{R}^{W \times D}$. This unified architecture seamlessly handles both univariate ($D=1$) and multivariate ($D>1$) time series without any modification.}
    \label{fig:model_architecture}
\end{figure*} % <-- 将 /figure 改为 /figure*

\subsection{Encoder}
The encoder is designed to distill an input time series window $X \in \mathbb{R}^{W \times D}$ (where $W$ is the window size and $D$ is the feature dimension) into a semantically rich latent vector $z \in \mathbb{R}^{L}$ (where $L$ is the latent dimension). This multi-stage process is formalized as follows:
\begin{align}
    \label{eq:encoder_cnn}
    H_c &= \text{CNN-Downsampler}(X) \\
    \label{eq:encoder_tcn}
    H_t &= \text{TCN}_{\text{Encoder}}(H_c) \\
    \label{eq:encoder_latent}
    z &= \text{FC}_{\text{encode}}(\text{Flatten}(H_t))
\end{align}
where $H_c \in \mathbb{R}^{(W/S) \times D_c}$ is the locally-encoded sequence compressed by stride $S$, and $H_t \in \mathbb{R}^{(W/S) \times D_t}$ is the temporally-encoded sequence. We now detail each component.

\subsubsection{Convolutional Downsampling}
The initial stage of the encoder is a 1D convolutional layer with a stride greater than 1. This strategic choice serves a dual purpose: rapid, local pattern extraction and efficient sequence compression. As the kernel slides across time, it captures local morphological features, while the stride reduces the sequence length, drastically lowering the computational load for subsequent temporal modeling. We employ a GELU activation function post-convolution to enhance non-linear expressiveness.

As illustrated in Figure~\ref{fig:cnn_analysis}, our CNN downsampling effectively compresses complex temporal patterns while preserving essential features. The multi-channel analysis reveals that different CNN filters learn specialized temporal patterns—from high-frequency pulses to envelope modulation—with quantified variance differences demonstrating the architectural benefit of multi-channel feature extraction.

% --- CNN Analysis Figure ---
\begin{figure}[t]
    \centering
    \includegraphics[width=\columnwidth]{figures/cnn_feature_analysis.png}
    \caption{CNN Feature Extraction Analysis. Left: CNN downsampling compresses complex signals from 100 to 50 points (2:1 ratio) while preserving essential patterns. Right: Multi-channel feature diversity demonstrates that different CNN channels learn distinct temporal patterns (pulses, modulation, trends), with variance values indicating feature specialization.}
    \label{fig:cnn_analysis}
\end{figure}

\subsubsection{Non-Causal Temporal Modeling}
The downsampled feature sequence is then processed by a TCN module, composed of a stack of residual TCN blocks (detailed in Figure~\ref{fig:tcn_block}). The power of this module lies in its use of dilated convolutions. By exponentially increasing the dilation factor in successive layers, the TCN attains an expansive receptive field with minimal parameter increase, making it exceptionally effective at modeling long-range dependencies.

Crucially, we utilize a \textbf{non-causal} convolutional configuration, where the kernel is centered on the current time step. This allows the model to leverage a complete, bidirectional context (both past and future information) to infer the value at each point, a capability that is vital for high-fidelity reconstruction.

% --- Figure for TCN Block Structure ---
\begin{figure}[t]
    \centering
    \includegraphics[width=0.9\columnwidth]{figures/TCN Block.pdf}
    \caption{The detailed structure of a TCN Block. It consists of two dilated non-causal convolutional layers with WeightNorm, ReLU, and Dropout. A residual connection from the input to the output helps stabilize training for deeper networks.}
    \label{fig:tcn_block}
\end{figure}

The efficiency advantage of this design is quantified in Figure~\ref{fig:tcn_efficiency}. Through dilated convolutions, TCN achieves exponential receptive field growth, reaching 187 time steps with only 5 layers compared to 31 steps for standard convolutions—a 6× improvement in long-range dependency modeling with identical computational layers.

% --- TCN Efficiency Figure ---
\begin{figure}[t]
    \centering
    \includegraphics[width=\columnwidth]{figures/tcn_receptive_field.png}
    \caption{TCN Receptive Field Growth Comparison. The dilated convolutions in TCN achieve exponential receptive field growth (reaching 187 at layer 5) compared to linear growth in standard convolutions (reaching only 31), demonstrating 6× efficiency in capturing long-range dependencies with the same number of layers.}
    \label{fig:tcn_efficiency}
\end{figure}

\subsubsection{Latent Space Projection}
Finally, the feature map produced by the TCN is flattened and projected into the target latent space by a dense, fully-connected layer, yielding the final latent vector $z$.

\subsection{Symmetric Decoder}
The decoder's objective is to invert the encoding process, accurately reconstructing the original input $X$ from its latent representation $z$. \textbf{A strictly symmetric architecture is employed to ensure a stable and effective mapping from the latent space back to the original data space.} This architectural constraint encourages the model to learn a more meaningful and well-structured latent representation. The decoding process is formalized as a symmetric inversion of the encoder:
\begin{align}
    \label{eq:decoder_fc}
    \hat{H}_t' &= \text{Unflatten}(\text{FC}_{\text{decode}}(z)) \\
    \label{eq:decoder_tcn}
    \hat{H}_c &= \text{TCN}_{\text{Decoder}}(\hat{H}_t') \\
    \label{eq:decoder_cnn}
    \hat{X} &= \sigma(\text{CNN-Upsampler}(\hat{H}_c))
\end{align}
where $\sigma$ denotes the Sigmoid activation function that maps the final output to the normalized range [0, 1]. The decoder mirrors the encoder's components in reverse order: a fully-connected layer to expand the latent vector, an inverse TCN module, and a 1D transposed convolutional layer that upsamples the sequence back to its original length and dimensionality.

\subsection{Training and Anomaly Scoring Framework}

\subsubsection{Data Preprocessing}
The raw time series is first segmented into windows of length $W$ via a sliding window approach. We then apply min-max normalization to the training data, scaling values to the [0, 1] range. \textbf{This scaling is crucial as it aligns the data distribution with the range of the typical final activation function of the decoder (e.g., Sigmoid), facilitating more stable training.}

\subsubsection{Training Objective}
The model is trained end-to-end by minimizing the reconstruction error between the input window $X$ and the reconstructed window $\hat{X}$. We define this error using the Mean Squared Error (MSE) loss function, $\mathcal{L}$:
\begin{equation}
\label{eq:mse}
\mathcal{L}(X, \hat{X}) = \frac{1}{W \times D} \sum_{i=1}^{W}\sum_{j=1}^{D} (X_{ij} - \hat{X}_{ij})^2
\end{equation}
where $X_{ij}$ and $\hat{X}_{ij}$ represent the data points at time step $i$ and feature dimension $j$. We utilize the AdamW optimizer to minimize this objective function.

\subsubsection{Anomaly Score Calculation}
Once trained, the model's reconstruction capability is harnessed to score anomalies in the test set. This is a multi-step process. First, for each sliding window $X_i$ starting at time step $i$, we compute its window-level reconstruction error $e_i$:
\begin{equation}
\label{eq:window_error}
e_i = \mathcal{L}(X_i, \hat{X}_i)
\end{equation}
where $\mathcal{L}$ is the MSE loss from Equation~\ref{eq:mse}.

Since a single time point is covered by multiple overlapping windows, we derive a robust, smoothed score for each point by \textbf{averaging the reconstruction errors of all windows that include it.} The final anomaly score $A(t)$ for each time point $t$ is calculated as:
\begin{equation}
\label{eq:point_score}
A(t) = \frac{1}{|W_t|} \sum_{i \in W_t} e_i
\end{equation}
where $W_t = \{i \mid t-W+1 \le i \le t\}$ is the set of window indices that contain time point $t$. This aggregation mitigates the noise from any single window's score.

Finally, the resulting sequence of point-wise scores $A$ is normalized one last time using min-max scaling, mapping all scores to a consistent [0, 1] range for final thresholding.

% --- Curated Univariate Results Table (Double Column) ---
\begin{table*}[htbp] % <-- 将 table 改为 table*
  \centering
  \caption{Core performance comparison on the univariate benchmark.}
  \label{tab:univariate_core}
  \begin{tabular}{lcccccc}
    \toprule
    Method & VUS-PR & VUS-ROC & AUC-PR & AUC-ROC & Standard-F1 & R-based-F1 \\
    \midrule
    \textbf{HTA\_AD (Ours)} & \textbf{0.44} & \textbf{0.85} & \textbf{0.41} & \textbf{0.83} & \textbf{0.44} & \textbf{0.46} \\
    Sub-PCA & 0.42 & 0.76 & 0.37 & 0.71 & 0.42 & 0.41 \\
    KShapeAD & 0.40 & 0.76 & 0.35 & 0.74 & 0.39 & 0.40 \\
    CNN & 0.34 & 0.79 & 0.33 & 0.71 & 0.38 & 0.35 \\
    LSTMED & 0.33 & 0.76 & 0.31 & 0.68 & 0.37 & 0.34 \\
    Lag-Llama & 0.27 & 0.72 & 0.25 & 0.65 & 0.30 & 0.30 \\
    AnomalyTransformer & 0.12 & 0.56 & 0.08 & 0.50 & 0.12 & 0.14 \\
    \bottomrule
  \end{tabular}
\end{table*} % <-- 将 /table 改为 /table*

% --- Curated Multivariate Results Table (Double Column) ---
\begin{table*}[htbp] % <-- 将 table 改为 table*
  \centering
  \caption{Core performance comparison on the multivariate benchmark.}
  \label{tab:multivariate_core}
  \begin{tabular}{lcccccc}
    \toprule
    Method & VUS-PR & VUS-ROC & AUC-PR & AUC-ROC & Standard-F1 & R-based-F1 \\
    \midrule
    \textbf{HTA\_AD (Ours)} & \textbf{0.39} & 0.74 & \textbf{0.44} & \textbf{0.77} & \textbf{0.48} & \textbf{0.50} \\
    CNN & 0.31 & \textbf{0.76} & 0.32 & 0.73 & 0.37 & 0.37 \\
    OmniAnomaly & 0.31 & 0.69 & 0.27 & 0.65 & 0.32 & 0.37 \\
    PCA & 0.31 & 0.74 & 0.31 & 0.70 & 0.37 & 0.29 \\
    AutoEncoder & 0.30 & 0.69 & 0.30 & 0.67 & 0.34 & 0.28 \\
    TimesNet & 0.19 & 0.64 & 0.13 & 0.56 & 0.20 & 0.17 \\
    AnomalyTransformer & 0.12 & 0.57 & 0.07 & 0.52 & 0.12 & 0.14 \\
    \bottomrule
  \end{tabular}
\end{table*} % <-- 将 /table 改为 /table*


\section{Experiments}
\label{sec:experiments}

This section is dedicated to a comprehensive empirical evaluation of HTA-AD. Our experiments are designed to answer several core questions: (1) Does HTA-AD achieve state-of-the-art (SOTA) performance on both univariate and multivariate benchmarks, thereby breaking the "specialization curse"? (2) Does HTA-AD learn a more meaningful temporal representation compared to Transformer-based models? (3) How robust is HTA-AD to data contamination? (4) What is the contribution of each component in our proposed architecture?

\subsection{Experimental Setup}

\subsubsection{Datasets}
Our primary evaluation is conducted on \textbf{TSB-AD}, a comprehensive and widely-used public benchmark for time series anomaly detection. It contains a large number of both univariate and multivariate real-world datasets, providing a solid foundation for validating the general-purpose capability of our model.

\subsubsection{Baseline Models}
To ensure a thorough comparison, we select a wide array of baseline models. For brevity, we present a representative subset in the main body, while the full comparison is available in the Appendix. The selected baselines cover classical methods (e.g., Sub-PCA), canonical deep learning models (e.g., CNN, LSTMED), and recent SOTA models (e.g., Lag-Llama, TimesNet, Anomaly Transformer).

\subsubsection{Evaluation Metrics}
Following the TSB-AD benchmark's rigorous protocol, our primary performance metric is the \textbf{Volume Under the PR Surface (VUS-PR)}. VUS-PR is a segment-aware metric that robustly evaluates a model's performance across all detection thresholds. We also report other metrics for a holistic view.

\subsubsection{Implementation Details}
HTA-AD is implemented in PyTorch and all experiments were run on a server with a single NVIDIA RTX 4090 GPU. To demonstrate the model's ease of use and robustness to hyperparameter choices, we use a \textbf{single, unified hyperparameter configuration} across all datasets: window size $W=128$, learning rate $lr=1e-3$, and a batch size of 64 for 30 epochs.

\subsection{Main Results: Breaking the Specialization Curse}
\label{ssec:main_results}

We first present the main performance comparison of HTA-AD against a representative subset of baselines on both univariate and multivariate tasks. The results are summarized in Table~\ref{tab:univariate_core} and Table~\ref{tab:multivariate_core}.

In the univariate benchmark (Table~\ref{tab:univariate_core}), HTA-AD achieves the highest VUS-PR score of 0.44, significantly outperforming all other representative models. Similarly, in the more challenging multivariate benchmark (Table~\ref{tab:multivariate_core}), HTA-AD again ranks first with a VUS-PR score of 0.39. These results provide decisive evidence that \textbf{HTA-AD is the first model to achieve SOTA performance on both benchmarks with a single, unified architecture}. The full results, available in Appendix Table~\ref{tab:full_results}, further confirm this conclusion.

\subsection{In-depth Analysis: Architectural Inductive Bias}
\label{ssec:in_depth_analysis}

Having established HTA-AD's superior performance, we now conduct deep-dive analyses to validate our central hypothesis: that the Transformer architecture is fundamentally misaligned with time series data, and HTA-AD's design provides a better inductive bias.

\subsubsection{The Training Set Shuffling Experiment}
To empirically test whether a model truly learns temporal dependencies or merely treats time series as an unordered set of points, we conduct a "training set shuffling" experiment. In this setup, we progressively shuffle the training data along the time axis at different ratios (from 0\% to 100\%) and train the models on this corrupted data. The models are then evaluated on the original, ordered test set.

The results, presented in Figure~\ref{fig:shuffling_exp}, are striking. As the shuffle ratio increases, the performance of HTA-AD degrades gracefully, which is the expected behavior for a model that correctly leverages temporal order. In stark contrast, the performance of Anomaly Transformer either remains flat or counter-intuitively \textit{improves}. This strongly suggests that its attention mechanism fails to capture sequential information and instead operates on set-based representations, a finding consistent with our "structural misalignment" hypothesis. The performance degradation analysis subplot further quantifies this, showing HTA-AD's degradation percentage climbing as expected, while Anomaly Transformer's remains near zero.

% --- Shuffling Experiment Figure (Single Column) ---
\begin{figure}[t]
    \centering
    % 将宽度单位改为 \columnwidth 以适应单栏
    \includegraphics[width=\columnwidth]{figures/shuffling_experiment_Synthetic_Strong_Temporal_Dependencies.png} 
    \caption{The Training Set Shuffling Experiment on a dataset with strong temporal dependencies. The top row shows the absolute VUS-PR and VUS-ROC performance comparison. The bottom-left subplot visualizes the original vs. a shuffled sequence. The bottom-right subplot shows the performance degradation percentage, highlighting the contrasting behavior between HTA-AD and Anomaly Transformer.}
    \label{fig:shuffling_exp}
\end{figure} 


\subsubsection{Visualization of Latent Space and Reconstruction}
The architectural misalignment is best visualized by comparing the learned latent spaces and corresponding reconstruction results (as shown previously in Figure~\ref{fig:evidence}). For a periodic signal, HTA-AD learns a highly structured, discrete orbit, correctly capturing the signal's global periodicity, which leads to a near-perfect reconstruction. The Transformer, however, learns only fragmented local trajectories and completely fails to reconstruct the signal. This provides direct, visual proof of the Transformer's inadequacy for time series reconstruction, stemming from its inability to learn meaningful temporal structures.

\subsubsection{Reconstruction Mechanism Analysis}
To demonstrate HTA-AD's anomaly detection mechanism, we analyze the reconstruction behavior on complex temporal patterns. Figure~\ref{fig:reconstruction_analysis} shows our model's reconstruction process on a representative test sequence. The model achieves remarkably low reconstruction error (0.108) for normal patterns while exhibiting significant error amplification (\textbf{3.3×} on this example) for anomalous regions. This quantitative analysis validates that our architecture successfully learns to reconstruct normal temporal patterns while failing on anomalies, providing the foundation for effective anomaly detection.

% --- Reconstruction Analysis Figure ---
\begin{figure}[t]
    \centering
    \includegraphics[width=\columnwidth]{figures/reconstruction_analysis.png}
    \caption{Reconstruction-based Anomaly Detection Mechanism. The dual-axis plot shows original vs. reconstructed signals (left y-axis) and reconstruction errors (right y-axis). On this representative example, HTA-AD achieves \textbf{3.3× error amplification} for anomalies while maintaining low reconstruction error (0.108) for normal patterns, demonstrating effective anomaly detection capability.}
    \label{fig:reconstruction_analysis}
\end{figure}

% --- Ablation Study Table (Double Column Version) ---
\begin{table*}[htbp] % <-- 将 table 改为 table*
  \centering
  \caption{Ablation study of HTA-AD's components on both univariate and multivariate benchmarks.}
  \label{tab:ablation}
  \begin{tabular}{llccccc}
    \toprule
    Dataset & Model Variant & VUS-PR & $\Delta$ (\%) & VUS-ROC & $\Delta$ (\%) & Time (s) \\
    \midrule
    \multirow{4}{*}{Univariate} & Base (Full Model) & \textbf{0.6194} & - & \textbf{0.8088} & - & 0.58 \\
    & (A) w/o CNN & 0.6028 & \textcolor{red}{-2.7} & 0.8050 & -0.5 & 0.71 \\
    & (B) w/o TCN & 0.5871 & \textcolor{red}{-5.2} & 0.7996 & -1.1 & \textbf{0.22} \\
    & (C) w/o Downsampling & 0.6161 & -0.5 & 0.8070 & -0.2 & 0.78 \\
    \midrule
    \multirow{4}{*}{Multivariate} & Base (Full Model) & \textbf{0.4238} & - & \textbf{0.8404} & - & 3.56 \\
    & (A) w/o CNN & 0.4067 & \textcolor{red}{-4.0} & 0.8435 & +0.4 & 4.87 \\
    & (B) w/o TCN & 0.4181 & \textcolor{red}{-1.3} & 0.8401 & -0.0 & \textbf{1.58} \\
    & (C) w/o Downsampling & 0.4182 & \textcolor{red}{-1.3} & 0.8380 & -0.3 & 5.19 \\
    \bottomrule
  \end{tabular}
\end{table*} % <-- 将 /table 改为 /table*

\subsection{Robustness to Training Data Contamination}

To evaluate HTA-AD's robustness in non-ideal, real-world scenarios, we conduct an experiment where we systematically contaminate the training data with noise.

\subsubsection{Noise Injection Protocol}
We inject \textbf{Gaussian spike noise} into the training data at varying contamination ratios ($r$), from 0\% to 100\%. For each contamination level $r$, we randomly select a subset of $r \times N$ time points from the training data (where $N$ is the total number of training samples). We then add Gaussian noise $\mathcal{N}(0, (\sigma_{\text{signal}} \times 1.5)^2)$ to these selected points, where $\sigma_{\text{signal}}$ is the standard deviation of the original signal. This process simulates realistic data corruption scenarios such as sensor malfunctions or transmission errors. For reproducibility, all random processes in our experiments use a fixed seed.

\subsubsection{Evaluation Protocol}
The model is trained on this contaminated data for 10 epochs (a reduced number to manage computational overhead for this specific analysis) and subsequently evaluated on the original, \textbf{clean test set}. We measure robustness using the VUS-PR score. This approach specifically tests the model's ability to maintain its core detection performance despite the quality degradation of its training data, a crucial capability for real-world deployment.

\subsubsection{Results and Analysis}
We selected four representative datasets with diverse characteristics for this analysis: NAB (real-world sensor data with inherent noise), Exathlon (high-quality data with strong periodicity), MSL (spacecraft telemetry with a stable baseline), and Daphnet (human activity data with variable signal quality). 

As presented in Figure~\ref{fig:robustness_summary}, HTA-AD exhibits remarkable performance stability across all tested datasets.
\begin{itemize}
    \item On datasets like \textbf{Exathlon} and \textbf{MSL}, where the model achieves a high baseline VUS-PR score, this top-tier performance remains almost perfectly stable even when the training data is heavily contaminated.
    \item On the more complex \textbf{NAB} dataset, the model's performance shows some fluctuation but does not exhibit a catastrophic collapse, showcasing resilience.
    \item On \textbf{Daphnet}, while the model's absolute performance is lower, the performance curve is consistently flat, indicating that its capability is not degraded by the noise.
\end{itemize}
In summary, these results demonstrate that HTA-AD is broadly robust to significant training data contamination, a vital characteristic for practical applications where data quality cannot always be guaranteed.

% --- Placeholder for the new Robustness Figure ---
\begin{figure}[t]
    \centering
    % 请将 'figures/robustness_summary_subplots.png' 替换为你的图片文件路径
    \includegraphics[width=\columnwidth]{figures/robustness_summary_subplots.png}
    \caption{Robustness of HTA-AD to spike noise injection in training data. The model demonstrates high performance stability across four datasets with varying characteristics.}
    \label{fig:robustness_summary}
\end{figure}

\subsection{Ablation Study}
To quantify the contribution of each key component in HTA-AD, we conduct a comprehensive ablation study, summarized in Table~\ref{tab:ablation}. The findings are clear: the full \textbf{Base} model achieves the best performance; removing the \textbf{TCN} module (B) causes the most significant performance drop; removing the \textbf{CNN} modules (A) hurts both performance and efficiency; and disabling \textbf{downsampling} (C) drastically increases inference time, validating the efficiency of the "hourglass" structure.



\section{Conclusion}
\label{sec:conclusion}

In this paper, we challenged the prevailing trend of applying increasingly complex architectures, such as the Transformer, to the task of Time Series Anomaly Detection (TSAD). We identified and provided decisive evidence for a "structural misalignment" between the Transformer's core mechanisms and the intrinsic properties of time series data. Furthermore, we highlighted the "specialization curse" that has long fragmented the field into separate univariate and multivariate solutions.

To address these fundamental challenges, we proposed the Hourglass Temporal Autoencoder for Anomaly Detection (HTA-AD), a lightweight, robust, and efficient model. Its architecture, which synergizes Convolutional Neural Networks (CNNs) and non-causal Temporal Convolutional Networks (TCNs), is deliberately engineered with inductive biases tailored for time series. Our extensive experiments on the comprehensive TSB-AD benchmark empirically validated our claims: HTA-AD is the first model to achieve state-of-the-art (SOTA) performance on both univariate and multivariate benchmarks with a single, unified architecture.

Our work advocates for a paradigm shift in TSAD research: away from the pursuit of sheer complexity and towards the development of architecturally elegant models with appropriate inductive biases. By demonstrating that a single, robust model can end the divide between univariate and multivariate approaches, HTA-AD not only serves as a new, powerful baseline but also points towards a more practical and unified future for the field.

Future work could focus on extending HTA-AD to handle significant concept drift in non-stationary series, possibly by integrating online learning mechanisms. Additionally, enhancing the interpretability of the model's decisions remains a valuable direction for research.


\appendix[Full Benchmark Results]
\label{sec:appendix}

Table~\ref{tab:full_results} presents the complete performance comparison of HTA-AD against all baseline models on the full TSB-AD benchmark datasets.

% --- Placeholder for the full results table ---
\begin{table}[h]
  \centering
  \caption{Full performance comparison on the TSB-AD benchmark (Placeholder).}
  \label{tab:full_results}
  \begin{tabular}{lcc}
    \toprule
    Model & Univariate (VUS-PR) & Multivariate (VUS-PR) \\
    \midrule
    \textbf{HTA\_AD (Ours)} & \textbf{0.44} & \textbf{0.39} \\
    Model A & 0.XX & 0.XX \\
    Model B & 0.XX & 0.XX \\
    Model C & 0.XX & 0.XX \\
    ... & ... & ... \\
    \bottomrule
  \end{tabular}
\end{table}

\bibliographystyle{IEEEtran}
\bibliography{IEEEabrv,references}

\end{document}
