# -*- coding: utf-8 -*-
# Lightweight Time Series Anomaly Detector for Multivariate data (LTS_AD_M_NoAttn)
# A simple, effective, and lightweight model for multivariate time series anomaly detection.
# This version does NOT contain an attention mechanism and serves as a baseline.

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import random, argparse, time, os, sys
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.metrics import precision_recall_curve

warnings.filterwarnings('ignore')

# This file is a copy of the original Lightweight_TS_Model_M.py for comparison purposes.
# To avoid conflicts, all major classes and functions have been renamed with a `_NoAttn` suffix.

# --- TSB-AD Imports ---
try:
    from TSB_AD.models.base import BaseDetector
    from TSB_AD.utils.slidingWindows import find_length_rank
    from TSB_AD.evaluation.metrics import get_metrics
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from TSB_AD.models.base import BaseDetector
    from TSB_AD.utils.slidingWindows import find_length_rank
    from TSB_AD.evaluation.metrics import get_metrics

# ----------------------------------------------------
# 1. Multivariate Lightweight Model Definition (No Attention)
# ----------------------------------------------------
class LTS_Model_M_NoAttn(nn.Module):
    """
    Lightweight Convolutional-Recurrent Autoencoder for Multivariate Time Series.
    """
    def __init__(self, window_size=100, input_dim=1, latent_dim=16, rnn_layers=1):
        super(LTS_Model_M_NoAttn, self).__init__()
        self.input_dim = input_dim
        self.window_size = window_size

        # --- Encoder ---
        self.encoder_cnn = nn.Sequential(
            nn.Conv1d(in_channels=input_dim, out_channels=8, kernel_size=7, padding=3, stride=2),
            nn.GELU(),
            nn.Conv1d(in_channels=8, out_channels=16, kernel_size=5, padding=2, stride=2),
            nn.GELU(),
            nn.Conv1d(in_channels=16, out_channels=32, kernel_size=3, padding=1, stride=2),
            nn.GELU(),
        )
        
        self.encoder_output_size = self._get_conv_output_size(window_size)
        
        self.encoder_gru = nn.GRU(
            input_size=32, 
            hidden_size=latent_dim, 
            num_layers=rnn_layers, 
            batch_first=True
        )

        # --- Decoder ---
        self.decoder_gru = nn.GRU(
            input_size=latent_dim, 
            hidden_size=latent_dim, 
            num_layers=rnn_layers, 
            batch_first=True
        )
        
        self.decoder_proj = nn.Linear(latent_dim, self.encoder_output_size * 32)

        self.decoder_cnn = nn.Sequential(
            nn.ConvTranspose1d(in_channels=32, out_channels=16, kernel_size=3, stride=2, padding=1, output_padding=1),
            nn.GELU(),
            nn.ConvTranspose1d(in_channels=16, out_channels=8, kernel_size=5, stride=2, padding=2, output_padding=1),
            nn.GELU(),
            nn.ConvTranspose1d(in_channels=8, out_channels=input_dim, kernel_size=7, stride=2, padding=3, output_padding=1),
        )
        
    def _get_conv_output_size(self, window_size):
        with torch.no_grad():
            x = torch.zeros(1, self.input_dim, window_size)
            x = self.encoder_cnn(x)
            return x.shape[2]

    def forward(self, x):
        batch_size = x.shape[0]
        x = x.permute(0, 2, 1)
        
        encoded_conv = self.encoder_cnn(x)
        encoded_conv_flat = encoded_conv.permute(0, 2, 1)
        
        _, latent_vector = self.encoder_gru(encoded_conv_flat)
        latent_vector = latent_vector.permute(1, 0, 2)
        
        decoder_input_gru = latent_vector.permute(1, 0, 2).repeat(1, self.encoder_output_size, 1)
        decoded_gru_output, _ = self.decoder_gru(decoder_input_gru)
        
        projected_decoder = self.decoder_proj(latent_vector.squeeze(0))
        projected_decoder = projected_decoder.view(batch_size, 32, self.encoder_output_size)
        
        reconstructed = self.decoder_cnn(projected_decoder)

        if reconstructed.shape[2] != self.window_size:
            reconstructed = F.interpolate(reconstructed, size=self.window_size, mode='linear', align_corners=False)

        reconstructed = reconstructed.permute(0, 2, 1)
        return reconstructed

# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class (No Attention)
# ----------------------------------------------------
class LTS_AD_M_NoAttn(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        self.window_size = HP.get('window_size', 100)
        self.epochs = HP.get('epochs', 20)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 16)
        
        self.model = None
        self.ts_scaler = StandardScaler()
        self.score_scaler = MinMaxScaler()
        self.criterion = nn.MSELoss(reduction='none')
        self.training_history = {}

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def fit(self, X, y=None):
        input_dim = X.shape[1]
        X_original_for_scoring = X

        if self.model is None:
            self.model = LTS_Model_M_NoAttn(
                input_dim=input_dim,
                window_size=self.window_size,
                latent_dim=self.latent_dim
            ).to(self.device)

        if self.normalize:
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        if len(windows) == 0:
            self.decision_scores_ = np.zeros(X_original_for_scoring.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        optimizer = optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        self.model.train()
        for epoch in range(self.epochs):
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                reconstructed = self.model(batch_windows)
                loss = torch.mean(self.criterion(reconstructed, batch_windows))
                if not (torch.isnan(loss) or torch.isinf(loss)):
                    loss.backward()
                    optimizer.step()
        
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self

    def decision_function(self, X):
        return self._compute_scores(X, fit_scaler=False)
    
    def _compute_scores(self, X, fit_scaler=False):
        n_samples, n_features = X.shape
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0:
            return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed = self.model(batch_windows)
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        window_scores = np.array(window_scores)

        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(window_scores):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        first_score_idx = self.window_size - 1
        if n_samples > first_score_idx:
             scores_mapped[:first_score_idx] = scores_mapped[first_score_idx]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel() 