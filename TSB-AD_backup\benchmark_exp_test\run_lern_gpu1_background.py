#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LERN v2.3 GPU1后台基准测试启动脚本
在GPU1上运行所有TSB-AD-U数据集的LERN模型测试
"""

import subprocess
import sys
import os
import time
from datetime import datetime

def start_full_benchmark():
    """启动完整的870个数据集基准测试"""
    
    print("🚀 启动LERN v2.3 全数据集基准测试 - GPU1")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📊 数据集规模: 870个")
    print("💻 运行设备: GPU1")
    print("🔧 预计时间: 15-20小时")
    
    # 构建命令
    cmd = [
        sys.executable, "run_lern_full_benchmark.py",
        "--gpu", "1",  # 使用GPU1
        "--batch_size", "25",  # 每25个数据集保存一次
        "--save_dir", "./lern_gpu1_full_results/",
        "--dataset_dir", "../Datasets/TSB-AD-U/"
    ]
    
    print(f"📋 执行命令: {' '.join(cmd)}")
    print("\n🎯 测试即将开始...")
    
    return cmd

def start_small_test():
    """启动小规模测试 (前50个数据集)"""
    
    print("🧪 启动LERN v2.3 小规模测试 - GPU1")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📊 数据集规模: 50个")
    print("💻 运行设备: GPU1")
    print("🔧 预计时间: 1-2小时")
    
    # 构建命令
    cmd = [
        sys.executable, "run_lern_full_benchmark.py",
        "--gpu", "1",  # 使用GPU1
        "--max_files", "50",  # 只测试前50个
        "--batch_size", "1",  # 每10个数据集保存一次
        "--save_dir", "./lern_gpu1_small_results/",
        "--dataset_dir", "../Datasets/TSB-AD-U/"
    ]
    
    print(f"📋 执行命令: {' '.join(cmd)}")
    print("\n🎯 测试即将开始...")
    
    return cmd

def main():
    """主函数"""
    
    # 检查参数
    if len(sys.argv) > 1:
        test_mode = sys.argv[1].lower()
    else:
        test_mode = "small"  # 默认使用small模式
    
    # 根据模式选择命令
    if test_mode == "small":
        cmd = start_small_test()
    elif test_mode == "full":
        cmd = start_full_benchmark()
    else:
        print("❌ 无效模式，请选择 'small' 或 'full'")
        return
    
    # 执行命令
    try:
        print("\n🏃‍♂️ 开始运行...")
        print("📄 日志文件会保存在结果目录中")
        print("-" * 60)
        
        # 运行命令
        result = subprocess.run(cmd, check=False)
        
        if result.returncode == 0:
            print(f"\n✅ 测试成功完成！")
            print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print(f"\n⚠️ 测试结束，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"\n💥 发生异常: {str(e)}")
    
    finally:
        print("\n📊 测试会话结束")
        if test_mode == "small":
            print("📁 结果目录: ./lern_gpu1_small_results/")
        else:
            print("📁 结果目录: ./lern_gpu1_full_results/")

if __name__ == "__main__":
    main()