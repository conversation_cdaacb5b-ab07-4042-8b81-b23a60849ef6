#!/usr/bin/env python3
"""
HTA-AD 综合测试脚本
测试所有核心功能，确保模型正常工作
"""

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import pandas as pd
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加路径
sys.path.append('.')
from core.models.hta_ad_integrated import HTAADComplete, HTAADTrainer


def generate_test_data(n_samples=100, seq_len=50, anomaly_ratio=0.2):
    """生成测试数据"""
    np.random.seed(42)
    torch.manual_seed(42)
    
    sequences = []
    labels = []
    
    for i in range(n_samples):
        # 基础信号
        t = np.linspace(0, 2*np.pi, seq_len)
        signal = np.sin(t) + 0.1 * np.random.randn(seq_len)
        
        # 添加异常
        if np.random.random() < anomaly_ratio:
            anomaly_start = np.random.randint(10, seq_len-10)
            anomaly_end = anomaly_start + 5
            signal[anomaly_start:anomaly_end] += 2.0
            labels.append(1)
        else:
            labels.append(0)
        
        sequences.append(signal)
    
    sequences = np.array(sequences).reshape(-1, seq_len, 1)
    labels = np.array(labels)
    
    return sequences, labels


def test_model_creation():
    """测试1: 模型创建"""
    print("🧪 测试1: 模型创建")
    
    try:
        # 测试基础模型
        model_basic = HTAADComplete(
            input_dim=1,
            d_model=16,
            n_heads=2,
            n_layers=1,
            seq_len=50,
            enable_sae=False
        )
        print("  ✅ 基础模型创建成功")
        
        # 测试SAE集成模型
        model_sae = HTAADComplete(
            input_dim=1,
            d_model=16,
            n_heads=2,
            n_layers=1,
            seq_len=50,
            sae_hidden_dim=32,
            enable_sae=True
        )
        print("  ✅ SAE集成模型创建成功")
        
        # 检查参数数量
        basic_params = sum(p.numel() for p in model_basic.parameters())
        sae_params = sum(p.numel() for p in model_sae.parameters())
        print(f"  📊 基础模型参数: {basic_params:,}")
        print(f"  📊 SAE模型参数: {sae_params:,}")
        
        return True, (model_basic, model_sae)
        
    except Exception as e:
        print(f"  ❌ 模型创建失败: {e}")
        return False, None


def test_forward_pass(models):
    """测试2: 前向传播"""
    print("\n🧪 测试2: 前向传播")
    
    model_basic, model_sae = models
    
    try:
        # 生成测试数据
        batch_size, seq_len = 4, 50
        x = torch.randn(batch_size, seq_len, 1)
        
        # 测试基础模型
        model_basic.eval()
        with torch.no_grad():
            outputs_basic = model_basic(x)
        
        print("  ✅ 基础模型前向传播成功")
        print(f"    - 输出键: {list(outputs_basic.keys())}")
        print(f"    - 异常分数形状: {outputs_basic['anomaly_scores'].shape}")
        print(f"    - 重构形状: {outputs_basic['reconstruction'].shape}")
        
        # 测试SAE模型
        model_sae.eval()
        with torch.no_grad():
            outputs_sae = model_sae(x)
            outputs_sae_interp = model_sae(x, return_interpretability=True)
        
        print("  ✅ SAE模型前向传播成功")
        print(f"    - 基础输出键: {list(outputs_sae.keys())}")
        print(f"    - 可解释性输出键: {list(outputs_sae_interp.keys())}")
        print(f"    - SAE特征形状: {outputs_sae['sae_features'].shape}")
        
        return True, (outputs_basic, outputs_sae, outputs_sae_interp)
        
    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        return False, None


def test_loss_computation(models, outputs):
    """测试3: 损失计算"""
    print("\n🧪 测试3: 损失计算")
    
    model_basic, model_sae = models
    outputs_basic, outputs_sae, outputs_sae_interp = outputs
    
    try:
        batch_size, seq_len = 4, 50
        x = torch.randn(batch_size, seq_len, 1)
        labels = torch.randint(0, 2, (batch_size,)).float()
        
        # 测试基础模型损失
        losses_basic = model_basic.compute_loss(outputs_basic, x, labels)
        print("  ✅ 基础模型损失计算成功")
        print(f"    - 损失项: {list(losses_basic.keys())}")
        print(f"    - 总损失: {losses_basic['total'].item():.4f}")
        
        # 测试SAE模型损失
        losses_sae = model_sae.compute_loss(outputs_sae, x, labels)
        print("  ✅ SAE模型损失计算成功")
        print(f"    - 损失项: {list(losses_sae.keys())}")
        print(f"    - 总损失: {losses_sae['total'].item():.4f}")
        print(f"    - SAE稀疏损失: {losses_sae['sae_sparsity'].item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 损失计算失败: {e}")
        return False


def test_training_basic(model):
    """测试4: 基础训练"""
    print("\n🧪 测试4: 基础训练")
    
    try:
        # 生成训练数据
        sequences, labels = generate_test_data(n_samples=50, seq_len=50)
        
        # 创建数据加载器
        dataset = TensorDataset(torch.FloatTensor(sequences), torch.FloatTensor(labels))
        train_loader = DataLoader(dataset, batch_size=8, shuffle=True)
        
        # 创建训练器
        trainer = HTAADTrainer(model, device='cpu')
        
        # 训练几个epoch
        history = trainer.train(train_loader, epochs=3, lr=1e-3)
        
        print("  ✅ 基础训练成功")
        print(f"    - 训练历史: {len(history['train_loss'])} epochs")
        print(f"    - 最终损失: {history['train_loss'][-1]:.4f}")
        
        return True, history
        
    except Exception as e:
        print(f"  ❌ 基础训练失败: {e}")
        return False, None


def test_anomaly_detection(model):
    """测试5: 异常检测"""
    print("\n🧪 测试5: 异常检测")
    
    try:
        # 生成测试数据
        sequences, true_labels = generate_test_data(n_samples=20, seq_len=50, anomaly_ratio=0.3)
        
        # 异常检测
        model.eval()
        with torch.no_grad():
            sequences_tensor = torch.FloatTensor(sequences)
            results = model.detect_anomalies(sequences_tensor, threshold=0.5)
        
        # 计算指标
        predictions = results['anomaly_predictions'].numpy().astype(int)
        accuracy = np.mean(predictions == true_labels)
        
        print("  ✅ 异常检测成功")
        print(f"    - 检测结果键: {list(results.keys())}")
        print(f"    - 检测到异常: {np.sum(predictions)}/{len(predictions)}")
        print(f"    - 真实异常: {np.sum(true_labels)}/{len(true_labels)}")
        print(f"    - 准确率: {accuracy:.3f}")
        
        return True, results
        
    except Exception as e:
        print(f"  ❌ 异常检测失败: {e}")
        return False, None


def test_interpretability(model):
    """测试6: 可解释性功能"""
    print("\n🧪 测试6: 可解释性功能")
    
    if not model.enable_sae:
        print("  ⚠️  SAE未启用，跳过可解释性测试")
        return True
    
    try:
        # 生成测试数据
        sequences, labels = generate_test_data(n_samples=10, seq_len=50)
        
        # 找到异常样本
        anomaly_indices = np.where(labels == 1)[0]
        if len(anomaly_indices) == 0:
            print("  ⚠️  没有异常样本，跳过解释测试")
            return True
        
        # 异常解释
        sequences_tensor = torch.FloatTensor(sequences)
        explanation = model.explain_anomaly(sequences_tensor, anomaly_indices[0])
        
        print("  ✅ 异常解释成功")
        print(f"    - 解释键: {list(explanation.keys())}")
        print(f"    - 异常分数: {explanation['anomaly_score']:.3f}")
        print(f"    - 重构误差: {explanation['reconstruction_error']:.4f}")
        
        # 特征字典
        feature_dict = model.get_feature_dictionary()
        if 'error' not in feature_dict:
            print("  ✅ 特征字典获取成功")
            print(f"    - 特征字典层数: {len(feature_dict)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 可解释性测试失败: {e}")
        return False


def test_model_save_load(model):
    """测试7: 模型保存和加载"""
    print("\n🧪 测试7: 模型保存和加载")
    
    try:
        # 确保目录存在
        os.makedirs('results/models', exist_ok=True)
        
        # 保存模型
        save_path = 'results/models/test_model.pth'
        model.save_model(save_path)
        print("  ✅ 模型保存成功")
        
        # 加载模型
        loaded_model = HTAADComplete.load_model(save_path, device='cpu')
        print("  ✅ 模型加载成功")
        
        # 验证加载的模型
        test_input = torch.randn(2, 50, 1)
        
        model.eval()
        loaded_model.eval()
        
        with torch.no_grad():
            original_output = model(test_input)
            loaded_output = loaded_model(test_input)
        
        # 检查输出是否一致
        diff = torch.abs(original_output['anomaly_scores'] - loaded_output['anomaly_scores']).max()
        print(f"  📊 输出差异: {diff.item():.6f}")
        
        if diff < 1e-5:
            print("  ✅ 模型加载验证成功")
            return True
        else:
            print("  ⚠️  模型加载后输出有差异")
            return False
        
    except Exception as e:
        print(f"  ❌ 模型保存/加载失败: {e}")
        return False


def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 HTA-AD 综合测试开始")
    print("=" * 60)
    
    test_results = {}
    
    # 测试1: 模型创建
    success, models = test_model_creation()
    test_results['model_creation'] = success
    if not success:
        return test_results
    
    # 测试2: 前向传播
    success, outputs = test_forward_pass(models)
    test_results['forward_pass'] = success
    if not success:
        return test_results
    
    # 测试3: 损失计算
    success = test_loss_computation(models, outputs)
    test_results['loss_computation'] = success
    
    # 测试4: 基础训练 (使用SAE模型)
    success, history = test_training_basic(models[1])
    test_results['basic_training'] = success
    
    # 测试5: 异常检测
    success, detection_results = test_anomaly_detection(models[1])
    test_results['anomaly_detection'] = success
    
    # 测试6: 可解释性功能
    success = test_interpretability(models[1])
    test_results['interpretability'] = success
    
    # 测试7: 模型保存和加载
    success = test_model_save_load(models[1])
    test_results['save_load'] = success
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！HTA-AD模型工作正常。")
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
    
    return test_results


if __name__ == '__main__':
    results = run_comprehensive_test()
