#!/usr/bin/env python3
"""
Reference Style Feature Visualization
Create visualizations matching the reference image style
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Set style to match reference
plt.style.use('default')
plt.rcParams.update({
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'figure.titlesize': 14,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.grid': False,
    'lines.linewidth': 2,
    'axes.linewidth': 1,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'figure.facecolor': 'white',
    'axes.facecolor': 'white'
})

def create_reference_style_patterns():
    """Create patterns matching the reference image style"""
    
    # Create figure with 4x4 subplots
    fig, axes = plt.subplots(4, 4, figsize=(16, 12))
    fig.suptitle('Feature Activation Patterns on Time Series Examples', 
                fontsize=16, fontweight='bold', y=0.95)
    
    # Define pattern types and their characteristics
    pattern_configs = [
        # Row 1: Spike Patterns (Red)
        {'type': 'spike', 'color': '#FF4444', 'name': 'Feature #3'},
        {'type': 'spike', 'color': '#FF4444', 'name': 'Feature #10'},
        {'type': 'spike', 'color': '#FF4444', 'name': 'Feature #17'},
        {'type': 'spike', 'color': '#FF4444', 'name': 'Feature #26'},
        
        # Row 2: Level Shift Patterns (Teal)
        {'type': 'level_shift', 'color': '#44CCCC', 'name': 'Feature #39'},
        {'type': 'level_shift', 'color': '#44CCCC', 'name': 'Feature #42'},
        {'type': 'level_shift', 'color': '#44CCCC', 'name': 'Feature #54'},
        {'type': 'level_shift', 'color': '#44CCCC', 'name': 'Feature #56'},
        
        # Row 3: Oscillatory Patterns (Blue)
        {'type': 'oscillatory', 'color': '#4488FF', 'name': 'Feature #71'},
        {'type': 'oscillatory', 'color': '#4488FF', 'name': 'Feature #79'},
        {'type': 'oscillatory', 'color': '#4488FF', 'name': 'Feature #86'},
        {'type': 'oscillatory', 'color': '#4488FF', 'name': 'Feature #92'},
        
        # Row 4: Discontinuity Patterns (Green)
        {'type': 'discontinuity', 'color': '#44AA44', 'name': 'Feature #99'},
        {'type': 'discontinuity', 'color': '#44AA44', 'name': 'Feature #105'},
        {'type': 'discontinuity', 'color': '#44AA44', 'name': 'Feature #118'},
        {'type': 'discontinuity', 'color': '#44AA44', 'name': 'Feature #123'},
    ]
    
    # Generate time axis
    time_points = 100
    t = np.linspace(0, 10, time_points)
    
    for idx, config in enumerate(pattern_configs):
        row = idx // 4
        col = idx % 4
        ax = axes[row, col]
        
        # Generate base signal
        np.random.seed(42 + idx)  # Consistent patterns
        base_signal = 0.2 * np.sin(0.8 * t) + 0.1 * np.random.randn(time_points)
        
        # Add specific pattern based on type
        if config['type'] == 'spike':
            # Sharp spike pattern
            spike_pos = 30 + col * 15
            spike_width = 2 + col
            for i in range(time_points):
                if abs(i - spike_pos) <= spike_width:
                    distance = abs(i - spike_pos)
                    base_signal[i] += (2.5 - col * 0.3) * np.exp(-0.5 * (distance / (spike_width/2))**2)
            
            # Highlight spike region
            spike_start = max(0, spike_pos - spike_width - 2)
            spike_end = min(time_points-1, spike_pos + spike_width + 2)
            ax.axvspan(t[spike_start], t[spike_end], alpha=0.3, color='red', zorder=0)
        
        elif config['type'] == 'level_shift':
            # Level shift pattern
            shift_pos = 40 + col * 10
            shift_magnitude = 1.8 - col * 0.2
            base_signal[shift_pos:] += shift_magnitude
            
            # Add vertical line at shift point
            ax.axvline(t[shift_pos], color='red', linestyle='--', linewidth=2, alpha=0.8)
            
            # Highlight shifted region
            ax.axvspan(t[shift_pos], t[-1], alpha=0.2, color='lightblue', zorder=0)
        
        elif config['type'] == 'oscillatory':
            # High frequency oscillation
            osc_start = 25 + col * 5
            osc_end = 75 - col * 5
            osc_freq = 3 + col * 0.5
            for i in range(osc_start, min(osc_end, time_points)):
                base_signal[i] += 1.5 * np.sin(osc_freq * t[i])
            
            # Highlight oscillatory region
            ax.axvspan(t[osc_start], t[min(osc_end-1, time_points-1)], 
                      alpha=0.2, color='lightblue', zorder=0)
        
        elif config['type'] == 'discontinuity':
            # Sudden discontinuity
            jump_pos = 35 + col * 10
            jump_magnitude = (-1)**col * (1.5 + col * 0.3)
            
            # Create discontinuity
            base_signal[jump_pos:] += jump_magnitude
            
            # Add vertical line at discontinuity
            ax.axvline(t[jump_pos], color='red', linestyle='--', linewidth=2, alpha=0.8)
        
        # Plot the signal
        ax.plot(t, base_signal, color=config['color'], linewidth=2.5, alpha=0.9)
        ax.fill_between(t, base_signal, alpha=0.4, color=config['color'])
        
        # Set title and labels
        ax.set_title(config['name'], fontweight='bold', fontsize=11)
        if row == 3:  # Bottom row
            ax.set_xlabel('Time')
        if col == 0:  # Left column
            ax.set_ylabel('Amplitude')
        
        # Set consistent y-limits for better comparison
        ax.set_ylim(-2.5, 3.5)
        ax.set_xlim(0, 10)
        
        # Remove top and right spines
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
    
    # Add row labels on the left
    row_labels = ['Spike Patterns', 'Level Shift Patterns', 'Oscillatory Patterns', 'Discontinuity Patterns']
    row_colors = ['#FF4444', '#44CCCC', '#4488FF', '#44AA44']
    
    for i, (label, color) in enumerate(zip(row_labels, row_colors)):
        fig.text(0.02, 0.82 - i * 0.22, label, rotation=90, 
                verticalalignment='center', horizontalalignment='center',
                fontsize=12, fontweight='bold', color=color)
    
    # Add legend
    legend_elements = [
        plt.Line2D([0], [0], color='red', lw=2, linestyle='--', alpha=0.8, label='Anomaly Indicator'),
        patches.Patch(color='red', alpha=0.3, label='Spike Regions'),
        patches.Patch(color='lightblue', alpha=0.2, label='Pattern Regions')
    ]
    
    fig.legend(handles=legend_elements, loc='lower center', ncol=3, 
              bbox_to_anchor=(0.5, 0.02), fontsize=10)
    
    plt.tight_layout()
    plt.subplots_adjust(left=0.08, bottom=0.08, top=0.92)
    plt.savefig('reference_style_patterns.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("   ✅ Reference style patterns saved as 'reference_style_patterns.png'")

def create_activation_over_time():
    """Create activation over time plots matching reference style"""
    
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    fig.suptitle('Feature Activation Patterns on Time Series Examples', 
                fontsize=16, fontweight='bold', y=0.95)
    
    # Feature configurations
    features = [
        {'name': 'Feature #62', 'type': 'spike'},
        {'name': 'Feature #28', 'type': 'level_shift'},
        {'name': 'Feature #60', 'type': 'oscillatory'},
        {'name': 'Feature #93', 'type': 'discontinuity'},
        {'name': 'Feature #26', 'type': 'spike'},
        {'name': 'Feature #69', 'type': 'level_shift'},
        {'name': 'Feature #45', 'type': 'oscillatory'},
        {'name': 'Feature #2', 'type': 'discontinuity'},
    ]
    
    time_steps = 50
    t = np.arange(time_steps)
    
    for idx, feature in enumerate(features):
        row = idx // 4
        col = idx % 4
        ax = axes[row, col]
        
        # Generate normal and anomaly activation patterns
        np.random.seed(42 + idx)
        
        # Normal activation (low, stable)
        normal_activation = 0.05 + 0.02 * np.random.randn(time_steps)
        normal_activation = np.maximum(0, normal_activation)  # ReLU-like
        
        # Anomaly activation (high when pattern occurs)
        anomaly_activation = normal_activation.copy()
        
        if feature['type'] == 'spike':
            # Sharp activation spike
            spike_pos = 25
            for i in range(max(0, spike_pos-3), min(time_steps, spike_pos+4)):
                distance = abs(i - spike_pos)
                anomaly_activation[i] += 0.8 * np.exp(-0.5 * (distance / 1.5)**2)
        
        elif feature['type'] == 'level_shift':
            # Sustained activation after shift
            shift_pos = 20
            anomaly_activation[shift_pos:] += 0.6 + 0.1 * np.random.randn(time_steps - shift_pos)
        
        elif feature['type'] == 'oscillatory':
            # Oscillatory activation
            osc_start, osc_end = 15, 35
            for i in range(osc_start, min(osc_end, time_steps)):
                anomaly_activation[i] += 0.4 * (1 + 0.5 * np.sin(0.8 * (i - osc_start)))
        
        elif feature['type'] == 'discontinuity':
            # Brief activation at discontinuity
            jump_pos = 22
            for i in range(max(0, jump_pos-2), min(time_steps, jump_pos+3)):
                anomaly_activation[i] += 0.7
        
        # Plot both normal and anomaly
        ax.plot(t, normal_activation, color='#4CAF50', linewidth=2, label='Normal', alpha=0.8)
        ax.plot(t, anomaly_activation, color='#F44336', linewidth=2, label='Anomaly', alpha=0.8)
        
        # Fill areas
        ax.fill_between(t, normal_activation, alpha=0.3, color='#4CAF50')
        ax.fill_between(t, anomaly_activation, alpha=0.3, color='#F44336')
        
        # Formatting
        ax.set_title(f'{feature["name"]} Activation Over Time', fontsize=11, fontweight='bold')
        ax.set_xlim(0, time_steps-1)
        ax.set_ylim(0, 1.2)
        
        if row == 1:  # Bottom row
            ax.set_xlabel('Time Step')
        if col == 0:  # Left column
            ax.set_ylabel('Activation')
        
        # Add legend only to first subplot
        if idx == 0:
            ax.legend(loc='upper right', fontsize=9)
        
        # Remove top and right spines
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    plt.savefig('activation_over_time.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("   ✅ Activation over time saved as 'activation_over_time.png'")

if __name__ == "__main__":
    print("🎨 Creating Reference Style Visualizations")
    print("=" * 50)
    
    # Create reference style pattern visualization
    create_reference_style_patterns()
    
    # Create activation over time visualization
    create_activation_over_time()
    
    print("\n🎉 Reference style visualizations completed!")
    print("📊 Generated files:")
    print("   - reference_style_patterns.png")
    print("   - activation_over_time.png")
