#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大规模合成数据生成器
生成几千个点的现实时间序列数据，适合大规模异常检测任务
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.signal import correlate
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

def generate_large_scale_time_series():
    """生成大规模现实时间序列数据"""
    np.random.seed(42)
    
    # 大规模参数设置
    total_length = 5000  # 5000个时间点
    window_size = 100
    
    print(f"🏗️ 生成大规模时间序列数据: {total_length} 个时间点")
    
    # 创建时间轴
    t = np.linspace(0, 20*np.pi, total_length)
    
    # 1. 长期趋势组件（缓慢变化）
    long_trend = 0.2 * np.sin(0.02 * t) + 0.1 * t / total_length
    
    # 2. 季节性组件（多个周期）
    daily_pattern = 0.4 * np.sin(t)  # 主要日周期
    weekly_pattern = 0.2 * np.sin(t/7)  # 周周期
    monthly_pattern = 0.1 * np.sin(t/30)  # 月周期
    
    # 3. 不规则周期性（模拟现实中的不完美周期）
    irregular_period = 0.3 * np.sin(2.1 * t + 0.3 * np.sin(0.01 * t))
    
    # 4. 高频噪声
    high_freq_noise = 0.1 * np.random.normal(0, 1, total_length)
    
    # 5. 低频漂移
    low_freq_drift = 0.15 * np.sin(0.005 * t) * np.random.randn(total_length) * 0.1
    
    # 6. 随机小扰动（模拟测量误差等）
    small_disturbances = np.zeros(total_length)
    n_small_disturbances = 20  # 增加小扰动数量
    for _ in range(n_small_disturbances):
        pos = np.random.randint(100, total_length - 100)
        duration = np.random.randint(3, 15)
        intensity = np.random.uniform(0.05, 0.2)
        small_disturbances[pos:pos+duration] += intensity * np.random.randn(duration)
    
    # 组合所有组件
    base_signal = (long_trend + daily_pattern + weekly_pattern + monthly_pattern + 
                  irregular_period + high_freq_noise + low_freq_drift + small_disturbances)
    
    # 归一化到合理范围
    base_signal = (base_signal - np.mean(base_signal)) / np.std(base_signal)
    base_signal = 0.5 + 0.35 * base_signal  # 缩放到[0.15, 0.85]左右
    
    # 创建标签
    labels = np.zeros(total_length)
    
    # 添加多种类型的异常（更多更复杂）
    anomaly_configs = [
        # 早期异常
        {'start': 800, 'end': 850, 'type': 'gradual_shift', 'intensity': 0.4},
        {'start': 1200, 'end': 1230, 'type': 'spike', 'intensity': 0.6},
        {'start': 1800, 'end': 1850, 'type': 'pattern_break', 'intensity': 0.3},
        
        # 中期异常
        {'start': 2200, 'end': 2280, 'type': 'oscillation', 'intensity': 0.5},
        {'start': 2600, 'end': 2640, 'type': 'drop', 'intensity': 0.4},
        {'start': 3000, 'end': 3100, 'type': 'gradual_shift', 'intensity': 0.6},
        
        # 后期异常
        {'start': 3500, 'end': 3520, 'type': 'spike', 'intensity': 0.8},
        {'start': 3800, 'end': 3900, 'type': 'pattern_break', 'intensity': 0.4},
        {'start': 4200, 'end': 4280, 'type': 'oscillation', 'intensity': 0.6},
        {'start': 4500, 'end': 4550, 'type': 'gradual_shift', 'intensity': 0.5},
    ]
    
    print(f"🚨 添加 {len(anomaly_configs)} 个异常区域")
    
    for anomaly in anomaly_configs:
        start, end = anomaly['start'], anomaly['end']
        anom_type = anomaly['type']
        intensity = anomaly['intensity']
        
        if anom_type == 'gradual_shift':
            # 渐变偏移
            shift_pattern = np.linspace(0, intensity, end - start)
            base_signal[start:end] += shift_pattern
            
        elif anom_type == 'spike':
            # 尖峰异常
            spike_length = end - start
            peak_pos = spike_length // 2
            spike_pattern = np.concatenate([
                np.linspace(0, intensity, peak_pos),
                np.linspace(intensity, 0, spike_length - peak_pos)
            ])
            base_signal[start:end] += spike_pattern
            
        elif anom_type == 'pattern_break':
            # 模式破坏 - 用随机噪声替换正常模式
            base_signal[start:end] = np.mean(base_signal) + intensity * np.random.randn(end - start)
            
        elif anom_type == 'oscillation':
            # 异常振荡
            osc_length = end - start
            osc_freq = 2 * np.pi / 10  # 快速振荡
            osc_t = np.arange(osc_length)
            oscillation = intensity * np.sin(osc_freq * osc_t) * np.exp(-osc_t / (osc_length * 0.5))
            base_signal[start:end] += oscillation
            
        elif anom_type == 'drop':
            # 突然下降
            drop_pattern = np.linspace(0, -intensity, end - start)
            base_signal[start:end] += drop_pattern
        
        labels[start:end] = 1
    
    # 确保信号在合理范围内
    base_signal = np.clip(base_signal, 0, 1.5)
    
    print(f"✅ 数据生成完成: {total_length} 个点，{np.sum(labels)} 个异常点 ({np.sum(labels)/total_length*100:.1f}%)")
    
    return base_signal.reshape(-1, 1), labels, window_size

def analyze_large_scale_data():
    """分析大规模数据"""
    print("\n🔍 生成并分析大规模时间序列数据...")
    
    # 生成大规模数据
    data, labels, window_size = generate_large_scale_time_series()
    data_flat = data.flatten()
    
    # 更合理的数据划分
    train_end = 3000  # 前3000个点用于训练
    test_start = 3500  # 从3500开始测试
    test_end = test_start + window_size
    
    train_data = data_flat[:train_end]
    test_data = data_flat[test_start:test_end]
    test_labels = labels[test_start:test_end]
    
    print(f"\n📊 大规模数据统计信息:")
    print(f"   总长度: {len(data_flat):,} 个时间点")
    print(f"   训练集: 0-{train_end-1:,} ({train_end:,} 个点)")
    print(f"   测试集: {test_start:,}-{test_end-1:,} ({len(test_data)} 个点)")
    print(f"   窗口大小: {window_size}")
    print(f"   总异常点数量: {np.sum(labels):,} ({np.sum(labels)/len(labels)*100:.1f}%)")
    print(f"   测试集中异常点: {np.sum(test_labels)} ({np.sum(test_labels)/len(test_labels)*100:.1f}%)")
    
    # 创建大规模数据可视化
    fig = plt.figure(figsize=(24, 16))
    
    # 1. 完整数据概览（采样显示）
    ax1 = plt.subplot(3, 4, 1)
    # 为了显示清晰，每隔10个点采样一次
    sample_step = 10
    sampled_indices = np.arange(0, len(data_flat), sample_step)
    sampled_data = data_flat[sampled_indices]
    sampled_labels = labels[sampled_indices]
    
    plt.plot(sampled_indices, sampled_data, linewidth=1, color='#2E86AB', alpha=0.7)
    
    # 标记训练集和测试集
    plt.axvspan(0, train_end-1, alpha=0.2, color='green', label='训练集')
    plt.axvspan(test_start, test_end-1, alpha=0.3, color='blue', label='测试集')
    
    # 标记异常区域（采样）
    anomaly_indices = np.where(sampled_labels == 1)[0]
    if len(anomaly_indices) > 0:
        plt.scatter(sampled_indices[anomaly_indices], sampled_data[anomaly_indices], 
                   color='red', s=2, alpha=0.6, label='异常点')
    
    plt.title('大规模数据完整概览（采样显示）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 训练集前1000个点详细视图
    ax2 = plt.subplot(3, 4, 2)
    train_sample = train_data[:1000]
    time_train_sample = np.arange(len(train_sample))
    plt.plot(time_train_sample, train_sample, linewidth=1.5, color='green', alpha=0.8)
    plt.title('训练集样本（前1000点）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 训练集统计
    train_stats = (f'均值: {np.mean(train_data):.3f}\n'
                  f'标准差: {np.std(train_data):.3f}\n'
                  f'最小值: {np.min(train_data):.3f}\n'
                  f'最大值: {np.max(train_data):.3f}')
    plt.text(0.02, 0.98, train_stats, transform=ax2.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8),
            verticalalignment='top')
    
    # 3. 测试集详细视图
    ax3 = plt.subplot(3, 4, 3)
    time_test = np.arange(len(test_data))
    plt.plot(time_test, test_data, linewidth=2, color='blue', alpha=0.8)
    
    # 标记测试集异常
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        plt.scatter(time_test[anomaly_mask], test_data[anomaly_mask], 
                   color='red', s=30, alpha=0.8, label='异常点')
    
    plt.title('测试集窗口', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 4. 长期趋势分析
    ax4 = plt.subplot(3, 4, 4)
    # 使用移动平均展示长期趋势
    window_ma = 100
    moving_avg = np.convolve(data_flat, np.ones(window_ma)/window_ma, mode='valid')
    ma_time = np.arange(len(moving_avg))
    
    plt.plot(ma_time[::20], moving_avg[::20], linewidth=2, color='purple', alpha=0.8)
    plt.title('长期趋势（移动平均）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 5. 周期性分析（训练集采样）
    ax5 = plt.subplot(3, 4, 5)
    train_sample_for_corr = train_data[::5]  # 采样以加速计算
    train_autocorr = correlate(train_sample_for_corr, train_sample_for_corr, mode='full')
    train_autocorr = train_autocorr[train_autocorr.size // 2:]
    train_autocorr = train_autocorr / train_autocorr[0]
    
    lags = np.arange(min(200, len(train_autocorr)))
    plt.plot(lags, train_autocorr[:len(lags)], linewidth=2, color='green')
    plt.title('周期性分析（自相关）', fontsize=14, fontweight='bold')
    plt.xlabel('滞后步数', fontsize=12)
    plt.ylabel('自相关系数', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 6. 异常分布统计
    ax6 = plt.subplot(3, 4, 6)
    anomaly_indices_full = np.where(labels == 1)[0]
    if len(anomaly_indices_full) > 0:
        # 异常区域分布
        anomaly_regions = []
        start = anomaly_indices_full[0]
        for i in range(1, len(anomaly_indices_full)):
            if anomaly_indices_full[i] != anomaly_indices_full[i-1] + 1:
                anomaly_regions.append((start, anomaly_indices_full[i-1]))
                start = anomaly_indices_full[i]
        anomaly_regions.append((start, anomaly_indices_full[-1]))
        
        region_lengths = [end - start + 1 for start, end in anomaly_regions]
        region_positions = [start for start, end in anomaly_regions]
        
        plt.scatter(region_positions, region_lengths, alpha=0.7, color='red', s=50)
        plt.title('异常区域分布', fontsize=14, fontweight='bold')
        plt.xlabel('异常开始位置', fontsize=12)
        plt.ylabel('异常区域长度', fontsize=12)
        plt.grid(True, alpha=0.3)
    
    # 7. 数据复杂度分析
    ax7 = plt.subplot(3, 4, 7)
    # 计算滑动标准差来展示数据复杂度
    window_std = 50
    rolling_std = []
    std_positions = []
    for i in range(0, len(data_flat) - window_std, 100):
        rolling_std.append(np.std(data_flat[i:i+window_std]))
        std_positions.append(i + window_std//2)
    
    plt.plot(std_positions, rolling_std, linewidth=2, color='orange', alpha=0.8)
    plt.title('数据复杂度（滑动标准差）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('滑动标准差', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 8. 频率域分析
    ax8 = plt.subplot(3, 4, 8)
    from scipy.fft import fft, fftfreq
    
    # 对训练数据的采样进行FFT
    train_fft_sample = train_data[::10]  # 采样以加速
    train_fft = fft(train_fft_sample)
    train_freqs = fftfreq(len(train_fft_sample))
    
    pos_mask = train_freqs > 0
    plt.loglog(train_freqs[pos_mask], np.abs(train_fft[pos_mask]), 
              linewidth=2, color='green', alpha=0.8)
    plt.title('频域分析', fontsize=14, fontweight='bold')
    plt.xlabel('频率', fontsize=12)
    plt.ylabel('幅度', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 9-12. 显示几个典型的异常区域
    anomaly_examples = [
        (800, 850, '渐变偏移'),
        (2200, 2280, '异常振荡'),
        (3500, 3520, '尖峰异常'),
        (4200, 4280, '复杂异常')
    ]
    
    for idx, (start, end, anom_type) in enumerate(anomaly_examples):
        ax = plt.subplot(3, 4, 9 + idx)
        if end <= len(data_flat):
            # 显示异常前后的上下文
            context_start = max(0, start - 50)
            context_end = min(len(data_flat), end + 50)
            context_data = data_flat[context_start:context_end]
            context_labels = labels[context_start:context_end]
            context_time = np.arange(len(context_data))
            
            plt.plot(context_time, context_data, linewidth=2, color='#2E86AB', alpha=0.8)
            
            # 标记异常区域
            anomaly_start_rel = start - context_start
            anomaly_end_rel = end - context_start
            plt.axvspan(anomaly_start_rel, anomaly_end_rel, alpha=0.3, color='red')
            
            plt.title(f'{anom_type}示例', fontsize=12, fontweight='bold')
            plt.xlabel('时间步（相对）', fontsize=10)
            plt.ylabel('信号值', fontsize=10)
            plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/large_scale_data_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 详细数据质量评估
    print(f"\n📈 大规模数据质量评估:")
    print(f"   数据规模: {len(data_flat):,} 个时间点")
    print(f"   训练集周期性强度: {np.max(train_autocorr[1:50]):.3f}")
    print(f"   训练集变异系数: {np.std(train_data)/np.mean(train_data):.3f}")
    print(f"   估计信噪比: {np.var(train_data)/np.var(np.diff(train_data)):.2f}")
    
    if len(anomaly_indices_full) > 0:
        anomaly_data = data_flat[anomaly_indices_full]
        print(f"   异常强度范围: {np.min(anomaly_data):.3f} - {np.max(anomaly_data):.3f}")
        print(f"   异常区域数量: {len(anomaly_regions)}")
        print(f"   平均异常区域长度: {np.mean(region_lengths):.1f} 个点")
    
    print(f"   数据复杂度（熵估计）: {-np.sum(np.histogram(train_data, bins=50)[0]/len(train_data) * np.log(np.histogram(train_data, bins=50)[0]/len(train_data) + 1e-10)):.3f}")
    print(f"   内存占用估计: {len(data_flat) * 8 / 1024:.1f} KB")

if __name__ == "__main__":
    print("=" * 80)
    print("🚀 大规模合成数据生成与分析")
    print("=" * 80)
    
    analyze_large_scale_data()
    
    print("\n" + "=" * 80)
    print("✅ 大规模数据分析完成！图像已保存为: experiments/large_scale_data_analysis.png")
    print("=" * 80) 
# -*- coding: utf-8 -*-
"""
大规模合成数据生成器
生成几千个点的现实时间序列数据，适合大规模异常检测任务
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.signal import correlate
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

def generate_large_scale_time_series():
    """生成大规模现实时间序列数据"""
    np.random.seed(42)
    
    # 大规模参数设置
    total_length = 5000  # 5000个时间点
    window_size = 100
    
    print(f"🏗️ 生成大规模时间序列数据: {total_length} 个时间点")
    
    # 创建时间轴
    t = np.linspace(0, 20*np.pi, total_length)
    
    # 1. 长期趋势组件（缓慢变化）
    long_trend = 0.2 * np.sin(0.02 * t) + 0.1 * t / total_length
    
    # 2. 季节性组件（多个周期）
    daily_pattern = 0.4 * np.sin(t)  # 主要日周期
    weekly_pattern = 0.2 * np.sin(t/7)  # 周周期
    monthly_pattern = 0.1 * np.sin(t/30)  # 月周期
    
    # 3. 不规则周期性（模拟现实中的不完美周期）
    irregular_period = 0.3 * np.sin(2.1 * t + 0.3 * np.sin(0.01 * t))
    
    # 4. 高频噪声
    high_freq_noise = 0.1 * np.random.normal(0, 1, total_length)
    
    # 5. 低频漂移
    low_freq_drift = 0.15 * np.sin(0.005 * t) * np.random.randn(total_length) * 0.1
    
    # 6. 随机小扰动（模拟测量误差等）
    small_disturbances = np.zeros(total_length)
    n_small_disturbances = 20  # 增加小扰动数量
    for _ in range(n_small_disturbances):
        pos = np.random.randint(100, total_length - 100)
        duration = np.random.randint(3, 15)
        intensity = np.random.uniform(0.05, 0.2)
        small_disturbances[pos:pos+duration] += intensity * np.random.randn(duration)
    
    # 组合所有组件
    base_signal = (long_trend + daily_pattern + weekly_pattern + monthly_pattern + 
                  irregular_period + high_freq_noise + low_freq_drift + small_disturbances)
    
    # 归一化到合理范围
    base_signal = (base_signal - np.mean(base_signal)) / np.std(base_signal)
    base_signal = 0.5 + 0.35 * base_signal  # 缩放到[0.15, 0.85]左右
    
    # 创建标签
    labels = np.zeros(total_length)
    
    # 添加多种类型的异常（更多更复杂）
    anomaly_configs = [
        # 早期异常
        {'start': 800, 'end': 850, 'type': 'gradual_shift', 'intensity': 0.4},
        {'start': 1200, 'end': 1230, 'type': 'spike', 'intensity': 0.6},
        {'start': 1800, 'end': 1850, 'type': 'pattern_break', 'intensity': 0.3},
        
        # 中期异常
        {'start': 2200, 'end': 2280, 'type': 'oscillation', 'intensity': 0.5},
        {'start': 2600, 'end': 2640, 'type': 'drop', 'intensity': 0.4},
        {'start': 3000, 'end': 3100, 'type': 'gradual_shift', 'intensity': 0.6},
        
        # 后期异常
        {'start': 3500, 'end': 3520, 'type': 'spike', 'intensity': 0.8},
        {'start': 3800, 'end': 3900, 'type': 'pattern_break', 'intensity': 0.4},
        {'start': 4200, 'end': 4280, 'type': 'oscillation', 'intensity': 0.6},
        {'start': 4500, 'end': 4550, 'type': 'gradual_shift', 'intensity': 0.5},
    ]
    
    print(f"🚨 添加 {len(anomaly_configs)} 个异常区域")
    
    for anomaly in anomaly_configs:
        start, end = anomaly['start'], anomaly['end']
        anom_type = anomaly['type']
        intensity = anomaly['intensity']
        
        if anom_type == 'gradual_shift':
            # 渐变偏移
            shift_pattern = np.linspace(0, intensity, end - start)
            base_signal[start:end] += shift_pattern
            
        elif anom_type == 'spike':
            # 尖峰异常
            spike_length = end - start
            peak_pos = spike_length // 2
            spike_pattern = np.concatenate([
                np.linspace(0, intensity, peak_pos),
                np.linspace(intensity, 0, spike_length - peak_pos)
            ])
            base_signal[start:end] += spike_pattern
            
        elif anom_type == 'pattern_break':
            # 模式破坏 - 用随机噪声替换正常模式
            base_signal[start:end] = np.mean(base_signal) + intensity * np.random.randn(end - start)
            
        elif anom_type == 'oscillation':
            # 异常振荡
            osc_length = end - start
            osc_freq = 2 * np.pi / 10  # 快速振荡
            osc_t = np.arange(osc_length)
            oscillation = intensity * np.sin(osc_freq * osc_t) * np.exp(-osc_t / (osc_length * 0.5))
            base_signal[start:end] += oscillation
            
        elif anom_type == 'drop':
            # 突然下降
            drop_pattern = np.linspace(0, -intensity, end - start)
            base_signal[start:end] += drop_pattern
        
        labels[start:end] = 1
    
    # 确保信号在合理范围内
    base_signal = np.clip(base_signal, 0, 1.5)
    
    print(f"✅ 数据生成完成: {total_length} 个点，{np.sum(labels)} 个异常点 ({np.sum(labels)/total_length*100:.1f}%)")
    
    return base_signal.reshape(-1, 1), labels, window_size

def analyze_large_scale_data():
    """分析大规模数据"""
    print("\n🔍 生成并分析大规模时间序列数据...")
    
    # 生成大规模数据
    data, labels, window_size = generate_large_scale_time_series()
    data_flat = data.flatten()
    
    # 更合理的数据划分
    train_end = 3000  # 前3000个点用于训练
    test_start = 3500  # 从3500开始测试
    test_end = test_start + window_size
    
    train_data = data_flat[:train_end]
    test_data = data_flat[test_start:test_end]
    test_labels = labels[test_start:test_end]
    
    print(f"\n📊 大规模数据统计信息:")
    print(f"   总长度: {len(data_flat):,} 个时间点")
    print(f"   训练集: 0-{train_end-1:,} ({train_end:,} 个点)")
    print(f"   测试集: {test_start:,}-{test_end-1:,} ({len(test_data)} 个点)")
    print(f"   窗口大小: {window_size}")
    print(f"   总异常点数量: {np.sum(labels):,} ({np.sum(labels)/len(labels)*100:.1f}%)")
    print(f"   测试集中异常点: {np.sum(test_labels)} ({np.sum(test_labels)/len(test_labels)*100:.1f}%)")
    
    # 创建大规模数据可视化
    fig = plt.figure(figsize=(24, 16))
    
    # 1. 完整数据概览（采样显示）
    ax1 = plt.subplot(3, 4, 1)
    # 为了显示清晰，每隔10个点采样一次
    sample_step = 10
    sampled_indices = np.arange(0, len(data_flat), sample_step)
    sampled_data = data_flat[sampled_indices]
    sampled_labels = labels[sampled_indices]
    
    plt.plot(sampled_indices, sampled_data, linewidth=1, color='#2E86AB', alpha=0.7)
    
    # 标记训练集和测试集
    plt.axvspan(0, train_end-1, alpha=0.2, color='green', label='训练集')
    plt.axvspan(test_start, test_end-1, alpha=0.3, color='blue', label='测试集')
    
    # 标记异常区域（采样）
    anomaly_indices = np.where(sampled_labels == 1)[0]
    if len(anomaly_indices) > 0:
        plt.scatter(sampled_indices[anomaly_indices], sampled_data[anomaly_indices], 
                   color='red', s=2, alpha=0.6, label='异常点')
    
    plt.title('大规模数据完整概览（采样显示）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 训练集前1000个点详细视图
    ax2 = plt.subplot(3, 4, 2)
    train_sample = train_data[:1000]
    time_train_sample = np.arange(len(train_sample))
    plt.plot(time_train_sample, train_sample, linewidth=1.5, color='green', alpha=0.8)
    plt.title('训练集样本（前1000点）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 训练集统计
    train_stats = (f'均值: {np.mean(train_data):.3f}\n'
                  f'标准差: {np.std(train_data):.3f}\n'
                  f'最小值: {np.min(train_data):.3f}\n'
                  f'最大值: {np.max(train_data):.3f}')
    plt.text(0.02, 0.98, train_stats, transform=ax2.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8),
            verticalalignment='top')
    
    # 3. 测试集详细视图
    ax3 = plt.subplot(3, 4, 3)
    time_test = np.arange(len(test_data))
    plt.plot(time_test, test_data, linewidth=2, color='blue', alpha=0.8)
    
    # 标记测试集异常
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        plt.scatter(time_test[anomaly_mask], test_data[anomaly_mask], 
                   color='red', s=30, alpha=0.8, label='异常点')
    
    plt.title('测试集窗口', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 4. 长期趋势分析
    ax4 = plt.subplot(3, 4, 4)
    # 使用移动平均展示长期趋势
    window_ma = 100
    moving_avg = np.convolve(data_flat, np.ones(window_ma)/window_ma, mode='valid')
    ma_time = np.arange(len(moving_avg))
    
    plt.plot(ma_time[::20], moving_avg[::20], linewidth=2, color='purple', alpha=0.8)
    plt.title('长期趋势（移动平均）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 5. 周期性分析（训练集采样）
    ax5 = plt.subplot(3, 4, 5)
    train_sample_for_corr = train_data[::5]  # 采样以加速计算
    train_autocorr = correlate(train_sample_for_corr, train_sample_for_corr, mode='full')
    train_autocorr = train_autocorr[train_autocorr.size // 2:]
    train_autocorr = train_autocorr / train_autocorr[0]
    
    lags = np.arange(min(200, len(train_autocorr)))
    plt.plot(lags, train_autocorr[:len(lags)], linewidth=2, color='green')
    plt.title('周期性分析（自相关）', fontsize=14, fontweight='bold')
    plt.xlabel('滞后步数', fontsize=12)
    plt.ylabel('自相关系数', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 6. 异常分布统计
    ax6 = plt.subplot(3, 4, 6)
    anomaly_indices_full = np.where(labels == 1)[0]
    if len(anomaly_indices_full) > 0:
        # 异常区域分布
        anomaly_regions = []
        start = anomaly_indices_full[0]
        for i in range(1, len(anomaly_indices_full)):
            if anomaly_indices_full[i] != anomaly_indices_full[i-1] + 1:
                anomaly_regions.append((start, anomaly_indices_full[i-1]))
                start = anomaly_indices_full[i]
        anomaly_regions.append((start, anomaly_indices_full[-1]))
        
        region_lengths = [end - start + 1 for start, end in anomaly_regions]
        region_positions = [start for start, end in anomaly_regions]
        
        plt.scatter(region_positions, region_lengths, alpha=0.7, color='red', s=50)
        plt.title('异常区域分布', fontsize=14, fontweight='bold')
        plt.xlabel('异常开始位置', fontsize=12)
        plt.ylabel('异常区域长度', fontsize=12)
        plt.grid(True, alpha=0.3)
    
    # 7. 数据复杂度分析
    ax7 = plt.subplot(3, 4, 7)
    # 计算滑动标准差来展示数据复杂度
    window_std = 50
    rolling_std = []
    std_positions = []
    for i in range(0, len(data_flat) - window_std, 100):
        rolling_std.append(np.std(data_flat[i:i+window_std]))
        std_positions.append(i + window_std//2)
    
    plt.plot(std_positions, rolling_std, linewidth=2, color='orange', alpha=0.8)
    plt.title('数据复杂度（滑动标准差）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('滑动标准差', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 8. 频率域分析
    ax8 = plt.subplot(3, 4, 8)
    from scipy.fft import fft, fftfreq
    
    # 对训练数据的采样进行FFT
    train_fft_sample = train_data[::10]  # 采样以加速
    train_fft = fft(train_fft_sample)
    train_freqs = fftfreq(len(train_fft_sample))
    
    pos_mask = train_freqs > 0
    plt.loglog(train_freqs[pos_mask], np.abs(train_fft[pos_mask]), 
              linewidth=2, color='green', alpha=0.8)
    plt.title('频域分析', fontsize=14, fontweight='bold')
    plt.xlabel('频率', fontsize=12)
    plt.ylabel('幅度', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 9-12. 显示几个典型的异常区域
    anomaly_examples = [
        (800, 850, '渐变偏移'),
        (2200, 2280, '异常振荡'),
        (3500, 3520, '尖峰异常'),
        (4200, 4280, '复杂异常')
    ]
    
    for idx, (start, end, anom_type) in enumerate(anomaly_examples):
        ax = plt.subplot(3, 4, 9 + idx)
        if end <= len(data_flat):
            # 显示异常前后的上下文
            context_start = max(0, start - 50)
            context_end = min(len(data_flat), end + 50)
            context_data = data_flat[context_start:context_end]
            context_labels = labels[context_start:context_end]
            context_time = np.arange(len(context_data))
            
            plt.plot(context_time, context_data, linewidth=2, color='#2E86AB', alpha=0.8)
            
            # 标记异常区域
            anomaly_start_rel = start - context_start
            anomaly_end_rel = end - context_start
            plt.axvspan(anomaly_start_rel, anomaly_end_rel, alpha=0.3, color='red')
            
            plt.title(f'{anom_type}示例', fontsize=12, fontweight='bold')
            plt.xlabel('时间步（相对）', fontsize=10)
            plt.ylabel('信号值', fontsize=10)
            plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/large_scale_data_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 详细数据质量评估
    print(f"\n📈 大规模数据质量评估:")
    print(f"   数据规模: {len(data_flat):,} 个时间点")
    print(f"   训练集周期性强度: {np.max(train_autocorr[1:50]):.3f}")
    print(f"   训练集变异系数: {np.std(train_data)/np.mean(train_data):.3f}")
    print(f"   估计信噪比: {np.var(train_data)/np.var(np.diff(train_data)):.2f}")
    
    if len(anomaly_indices_full) > 0:
        anomaly_data = data_flat[anomaly_indices_full]
        print(f"   异常强度范围: {np.min(anomaly_data):.3f} - {np.max(anomaly_data):.3f}")
        print(f"   异常区域数量: {len(anomaly_regions)}")
        print(f"   平均异常区域长度: {np.mean(region_lengths):.1f} 个点")
    
    print(f"   数据复杂度（熵估计）: {-np.sum(np.histogram(train_data, bins=50)[0]/len(train_data) * np.log(np.histogram(train_data, bins=50)[0]/len(train_data) + 1e-10)):.3f}")
    print(f"   内存占用估计: {len(data_flat) * 8 / 1024:.1f} KB")

if __name__ == "__main__":
    print("=" * 80)
    print("🚀 大规模合成数据生成与分析")
    print("=" * 80)
    
    analyze_large_scale_data()
    
    print("\n" + "=" * 80)
    print("✅ 大规模数据分析完成！图像已保存为: experiments/large_scale_data_analysis.png")
    print("=" * 80) 