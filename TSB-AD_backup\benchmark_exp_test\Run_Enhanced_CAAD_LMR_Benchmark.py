# -*- coding: utf-8 -*-
# Author: Enhanced CAAD-LMR Team
# License: Apache-2.0 License

"""
Enhanced CAAD-LMR Benchmark Integration
增强版CAAD-LMR与TSB-AD Benchmark完整集成
一次性完成训练、测试、评分的完整流程
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import random, argparse, time, os, logging
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import precision_recall_curve, auc
from transformers import (
    AutoModel, AutoTokenizer, 
    LlamaForCausalLM, LlamaTokenizer,
    MistralForCausalLM, AutoConfig
)
import json
import re
from datetime import datetime, timedelta
import warnings
import scipy.stats
from collections import defaultdict
warnings.filterwarnings('ignore')

from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.models.base import BaseDetector
from TSB_AD.utils.utility import zscore

# 设置随机种子
seed = 2024
torch.manual_seed(seed)
torch.cuda.manual_seed(seed)
torch.cuda.manual_seed_all(seed)
np.random.seed(seed)
random.seed(seed)
torch.backends.cudnn.benchmark = False
torch.backends.cudnn.deterministic = True

print("CUDA available: ", torch.cuda.is_available())
print("cuDNN version: ", torch.backends.cudnn.version())

def ensure_device_sync(tensor, target_device):
    """确保tensor在目标设备上"""
    if tensor.device != torch.device(target_device):
        return tensor.to(target_device)
    return tensor

def sync_model_device(model, device):
    """同步模型到指定设备"""
    if torch.cuda.is_available() and device == 'cuda':
        return model.cuda()
    else:
        return model.cpu()

class AdvancedFeatureExtractor:
    """高级特征提取器"""
    
    def __init__(self, window_size=50):
        self.window_size = window_size
        
    def extract_statistical_features(self, window):
        """提取统计特征"""
        if len(window.shape) > 1:
            window = window.flatten()
        
        # 确保window是有效的一维数组
        window = np.asarray(window).flatten()
        
        # 处理空数组或无效数据
        if len(window) == 0:
            return np.zeros(23)  # 返回固定长度的零向量
        
        # 基础统计特征 - 使用安全的计算方式
        try:
            features = [
                float(np.mean(window)),                    # 均值
                float(np.std(window) if len(window) > 1 else 0),                     # 标准差
                float(np.median(window)),                  # 中位数
                float(np.max(window)),                     # 最大值
                float(np.min(window)),                     # 最小值
                float(np.ptp(window)),                     # 峰峰值
                float(scipy.stats.skew(window) if len(window) > 2 else 0),           # 偏度
                float(scipy.stats.kurtosis(window) if len(window) > 2 else 0),       # 峰度
            ]
        except:
            features = [0.0] * 8
        
        # 趋势特征
        try:
            if len(window) > 1:
                x = np.arange(len(window))
                slope, intercept = np.polyfit(x, window, 1)
                features.extend([float(slope), float(intercept)])
            else:
                features.extend([0.0, 0.0])
        except:
            features.extend([0.0, 0.0])
        
        # 变化率特征
        try:
            if len(window) > 1:
                diff = np.diff(window)
                features.extend([
                    float(np.mean(diff)),                  # 平均变化率
                    float(np.std(diff)),                   # 变化率标准差
                    float(np.max(diff)),                   # 最大增量
                    float(np.min(diff)),                   # 最大减量
                ])
            else:
                features.extend([0.0, 0.0, 0.0, 0.0])
        except:
            features.extend([0.0, 0.0, 0.0, 0.0])
        
        # 分位数特征
        try:
            features.extend([
                float(np.percentile(window, 25)),          # Q1
                float(np.percentile(window, 75)),          # Q3
                float(np.percentile(window, 90)),          # P90
                float(np.percentile(window, 95)),          # P95
            ])
        except:
            features.extend([0.0, 0.0, 0.0, 0.0])
        
        # 频域特征
        try:
            if len(window) > 2:
                fft_values = np.fft.fft(window)[:max(1, len(window)//2)]
                features.extend([
                    float(np.mean(np.abs(fft_values))),    # 平均频域强度
                    float(np.std(np.abs(fft_values))),     # 频域强度标准差
                    float(np.max(np.abs(fft_values))),     # 最大频域强度
                ])
            else:
                features.extend([0.0, 0.0, 0.0])
        except:
            features.extend([0.0, 0.0, 0.0])
        
        # 局部异常特征
        try:
            lof = self._calculate_local_outlier_factor(window)
            iso_score = self._calculate_isolation_score(window)
            features.extend([float(lof), float(iso_score)])
        except:
            features.extend([0.0, 0.0])
        
        # 确保返回固定长度的特征向量
        features = features[:23]  # 截断到23个特征
        while len(features) < 23:  # 如果不足23个，用0填充
            features.append(0.0)
        
        return np.array(features, dtype=np.float32)
    
    def _calculate_local_outlier_factor(self, window):
        """计算局部异常因子"""
        if len(window) < 3:
            return 0.0
        
        try:
            mean_val = float(np.mean(window))
            std_val = float(np.std(window))
            
            outliers = np.abs(window - mean_val) > 2 * std_val
            return float(np.sum(outliers) / len(window))
        except:
            return 0.0
    
    def _calculate_isolation_score(self, window):
        """计算隔离分数 - 改用简化的异常检测方法"""
        try:
            if len(window) < 5:
                return 0.0
            
            # 使用简化的异常检测：基于IQR的方法
            q1 = np.percentile(window, 25)
            q3 = np.percentile(window, 75)
            iqr = q3 - q1
            
            if iqr < 1e-8:  # 避免除零
                return 0.0
            
            # 计算超出1.5*IQR范围的点的比例
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            outliers = (window < lower_bound) | (window > upper_bound)
            
            return float(np.mean(outliers))
        except:
            return 0.0
    
    def extract_contextual_features(self, window, focal_idx):
        """提取上下文特征"""
        if len(window.shape) > 1:
            window = window.flatten()
        
        # 确保window是有效的一维数组
        window = np.asarray(window).flatten()
        
        # 处理空数组或无效数据
        if len(window) == 0:
            return np.zeros(7, dtype=np.float32)  # 返回固定长度的零向量
        
        try:
            features = []
            
            # 确保focal_idx在有效范围内
            focal_idx = max(0, min(focal_idx, len(window) - 1))
            
            # 焦点位置特征
            focal_value = float(window[focal_idx])
            window_mean = float(np.mean(window))
            window_std = float(np.std(window))
            
            features.extend([
                focal_value,
                (focal_value - window_mean) / (window_std + 1e-8),
                focal_idx / len(window),
            ])
            
            # 局部上下文
            if focal_idx > 0 and focal_idx < len(window) - 1:
                left_neighbor = float(window[focal_idx - 1])
                right_neighbor = float(window[focal_idx + 1])
                
                features.extend([
                    focal_value - left_neighbor,
                    focal_value - right_neighbor,
                    abs(focal_value - (left_neighbor + right_neighbor) / 2),
                ])
            else:
                features.extend([0.0, 0.0, 0.0])
            
            # 窗口内排名
            rank = float(np.argsort(np.argsort(window))[focal_idx] / len(window))
            features.append(rank)
            
            # 确保返回固定长度的特征向量
            features = features[:7]  # 截断到7个特征
            while len(features) < 7:  # 如果不足7个，用0填充
                features.append(0.0)
            
            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            print(f"上下文特征提取错误: {e}")
            return np.zeros(7, dtype=np.float32)

class HybridAnomalyDetector(nn.Module):
    """混合异常检测器"""
    
    def __init__(self, 
                 llm_feature_dim=768, 
                 statistical_feature_dim=23, 
                 contextual_feature_dim=7,
                 hidden_dims=[512, 256, 128]):
        super().__init__()
        
        # LLM特征编码器 (移除BatchNorm避免设备问题)
        self.llm_encoder = nn.Sequential(
            nn.Linear(llm_feature_dim, hidden_dims[0]),
            nn.LayerNorm(hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dims[0], hidden_dims[1]),
            nn.LayerNorm(hidden_dims[1]),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 统计特征编码器 (移除BatchNorm避免设备问题)
        self.stat_encoder = nn.Sequential(
            nn.Linear(statistical_feature_dim, 128),
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.LayerNorm(64),
            nn.ReLU()
        )
        
        # 上下文特征编码器
        self.context_encoder = nn.Sequential(
            nn.Linear(contextual_feature_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU()
        )
        
        # 注意力机制
        total_feature_dim = hidden_dims[1] + 64 + 16
        self.attention = nn.Sequential(
            nn.Linear(total_feature_dim, total_feature_dim // 4),
            nn.ReLU(),
            nn.Linear(total_feature_dim // 4, 3),
            nn.Softmax(dim=1)
        )
        
        # 特征融合和预测 (移除BatchNorm避免设备问题)
        self.fusion_net = nn.Sequential(
            nn.Linear(total_feature_dim, hidden_dims[2]),
            nn.LayerNorm(hidden_dims[2]),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dims[2], 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
    
    def forward(self, llm_features, stat_features, context_features):
        # 特征编码
        llm_encoded = self.llm_encoder(llm_features)
        stat_encoded = self.stat_encoder(stat_features)
        context_encoded = self.context_encoder(context_features)
        
        # 特征拼接
        combined_features = torch.cat([llm_encoded, stat_encoded, context_encoded], dim=1)
        
        # 注意力权重
        attention_weights = self.attention(combined_features)
        
        # 应用注意力
        weighted_llm = llm_encoded * attention_weights[:, 0:1]
        weighted_stat = stat_encoded * attention_weights[:, 1:2]
        weighted_context = context_encoded * attention_weights[:, 2:3]
        
        weighted_features = torch.cat([weighted_llm, weighted_stat, weighted_context], dim=1)
        
        # 最终预测
        anomaly_score = self.fusion_net(weighted_features)
        return anomaly_score

class MultimodalContextConstructor:
    """多模态情境构建模块"""
    
    def __init__(self, domain="general", window_size=50):
        self.domain = domain
        self.window_size = window_size
        self.feature_extractor = AdvancedFeatureExtractor(window_size)
        
        # 领域模板
        self.domain_templates = {
            "financial": {
                "desc": "financial market indicator",
                "patterns": ["volatility spike", "trend reversal", "market crash"],
                "thresholds": {"high": 3.0, "medium": 2.0, "low": 1.0}
            },
            "server": {
                "desc": "server performance metric", 
                "patterns": ["resource spike", "performance degradation", "system overload"],
                "thresholds": {"high": 2.5, "medium": 1.8, "low": 1.2}
            },
            "general": {
                "desc": "time series measurement",
                "patterns": ["anomalous spike", "sudden drop", "trend change"],
                "thresholds": {"high": 2.5, "medium": 1.8, "low": 1.2}
            }
        }
    
    def generate_enhanced_narration(self, window, focal_idx=None):
        """生成增强的语言描述"""
        if focal_idx is None:
            focal_idx = len(window) // 2
        
        stats = self.feature_extractor.extract_statistical_features(window)
        patterns = self.detect_enhanced_patterns(window, focal_idx)
        domain_info = self.domain_templates.get(self.domain, self.domain_templates["general"])
        
        # 构造描述
        narration = f"Analyzing {domain_info['desc']} with statistical profile: "
        narration += f"mean={stats[0]:.3f}, std={stats[1]:.3f}, skewness={stats[6]:.3f}. "
        
        if len(window.shape) > 1:
            window_flat = window.flatten()
        else:
            window_flat = window
            
        focal_value = window_flat[focal_idx] if focal_idx < len(window_flat) else window_flat[-1]
        relative_position = (focal_value - stats[0]) / (stats[1] + 1e-8)
        
        # 异常程度描述
        if abs(relative_position) > domain_info['thresholds']['high']:
            severity = "severe"
        elif abs(relative_position) > domain_info['thresholds']['medium']:
            severity = "moderate"
        else:
            severity = "mild"
        
        narration += f"The focal point shows {severity} deviation. "
        narration += f"Detected patterns: {', '.join(patterns)}."
        
        return narration
    
    def detect_enhanced_patterns(self, window, focal_idx):
        """检测增强的模式"""
        patterns = []
        
        if len(window.shape) > 1:
            window = window.flatten()
        
        focal_value = window[focal_idx] if focal_idx < len(window) else window[-1]
        local_mean = np.mean(window)
        local_std = np.std(window)
        
        z_score = abs(focal_value - local_mean) / (local_std + 1e-8)
        
        if z_score > 3:
            patterns.append(f"extreme outlier (z={z_score:.1f})")
        elif z_score > 2:
            patterns.append(f"significant outlier (z={z_score:.1f})")
        elif z_score > 1.5:
            patterns.append(f"mild outlier (z={z_score:.1f})")
        
        return patterns if patterns else ["normal variation"]

class LLMReasoningEngine:
    """LLM推理引擎"""
    
    def __init__(self, model_name="sentence-transformers/all-MiniLM-L6-v2", device="cuda", use_multi_gpu=True):
        self.model_name = model_name
        self.use_multi_gpu = use_multi_gpu
        
        # 智能设备选择
        if device == "cuda" and not torch.cuda.is_available():
            print("警告: CUDA不可用，LLM引擎自动切换到CPU")
            self.device = "cpu"
        else:
            self.device = device
        
        print(f"正在加载LLM模型: {model_name} (设备: {self.device})")
        self._load_model()
        
    def _load_model(self):
        """加载LLM模型"""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModel.from_pretrained(self.model_name)
                
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                
            # 使用同步工具确保模型在正确设备上
            self.model = sync_model_device(self.model, self.device)
            print(f"模型已加载到设备: {next(self.model.parameters()).device}")
                
        except Exception as e:
            print(f"警告: 无法加载模型 {self.model_name}: {e}")
            raise RuntimeError(f"无法加载LLM模型: {e}")
    
    def extract_features_batch(self, texts, batch_size=8):
        """批量提取特征"""
        all_features = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            
            inputs = self.tokenizer(
                batch_texts,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            
            # 确保所有输入tensor都在正确的设备上
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                if hasattr(outputs, 'last_hidden_state'):
                    features = outputs.last_hidden_state.mean(dim=1)
                elif hasattr(outputs, 'pooler_output'):
                    features = outputs.pooler_output
                else:
                    features = outputs[0].mean(dim=1)
                
                # 使用同步工具确保输出tensor在正确的设备上
                features = ensure_device_sync(features, self.device)
            
            all_features.append(features)
        
        return torch.cat(all_features, dim=0)

class EnhancedCAAD_LMR_Detector(BaseDetector):
    """增强版CAAD-LMR检测器 - TSB-AD兼容版本"""
    
    def __init__(self, 
                 llm_model_name="sentence-transformers/all-MiniLM-L6-v2",
                 domain="general",
                 window_size=50,
                 device="cuda",
                 use_multi_gpu=True,
                 normalize=True):
        super().__init__()
        
        self.domain = domain
        self.window_size = window_size
        self.normalize = normalize
        
        # 智能设备选择
        if device == "cuda" and not torch.cuda.is_available():
            print("警告: CUDA不可用，自动切换到CPU")
            self.device = "cpu"
        else:
            self.device = device
        
        # 初始化组件
        self.context_constructor = MultimodalContextConstructor(domain, window_size)
        self.llm_engine = LLMReasoningEngine(llm_model_name, device, use_multi_gpu)
        
        # 获取LLM特征维度
        test_text = ["test"]
        with torch.no_grad():
            test_features = self.llm_engine.extract_features_batch(test_text)
            llm_dim = test_features.shape[1]
        
        stat_dim = 23  # 统计特征维度
        context_dim = 7  # 上下文特征维度
        
        self.hybrid_detector = HybridAnomalyDetector(
            llm_feature_dim=llm_dim,
            statistical_feature_dim=stat_dim,
            contextual_feature_dim=context_dim
        ).to(self.device)
        
        # 训练相关
        self.scaler_stat = StandardScaler()
        self.scaler_context = StandardScaler()
        self.is_fitted = False
        
        print(f"增强版CAAD-LMR初始化完成 - 领域: {domain}, LLM特征维度: {llm_dim}")
    
    def _create_windows_with_indices(self, data):
        """创建滑动窗口并记录索引"""
        n_samples, n_features = data.shape
        windows = []
        indices = []
        
        half_window = self.window_size // 2
        
        for i in range(n_samples):
            start = max(0, i - half_window)
            end = min(n_samples, i + half_window + 1)
            
            window = data[start:end]
            
            # 确保所有窗口都有相同的大小
            if len(window) < self.window_size:
                if start == 0:
                    # 在开始处填充
                    padding = np.tile(window[0:1], (self.window_size - len(window), 1))
                    window = np.vstack([padding, window])
                else:
                    # 在结尾处填充
                    padding = np.tile(window[-1:], (self.window_size - len(window), 1))
                    window = np.vstack([window, padding])
            elif len(window) > self.window_size:
                # 截断到指定大小
                window = window[:self.window_size]
            
            # 确保window形状一致
            window = window.reshape(self.window_size, n_features)
            
            windows.append(window)
            indices.append(i - start if start <= i else 0)
        
        # 将windows列表转换为3D numpy数组
        windows_array = np.stack(windows, axis=0)
        
        return windows_array, indices
    
    def fit(self, X, y=None):
        """训练增强模型"""
        n_samples, n_features = X.shape
        
        if self.normalize: 
            X = zscore(X, axis=1, ddof=1)
        
        print(f"训练数据形状: {X.shape}")
        
        # 创建窗口
        windows, focal_indices = self._create_windows_with_indices(X)
        print(f"创建窗口数量: {len(windows)}")
        
        # 提取所有特征
        print("提取训练特征...")
        train_features = self._extract_all_features(windows, focal_indices, is_training=True)
        
        # 生成伪标签
        print("生成伪标签...")
        pseudo_labels = self._generate_pseudo_labels(windows, focal_indices, sample_ratio=0.3)
        
        # 训练混合检测器
        print("训练混合检测器...")
        self._train_hybrid_detector(train_features, pseudo_labels)
        
        self.is_fitted = True
        return self
    
    def _extract_all_features(self, windows, focal_indices, is_training=False):
        """提取所有类型的特征"""
        n_windows = len(windows)
        
        # 批量生成文本描述
        print("生成文本描述...")
        narrations = []
        for i, (window, focal_idx) in enumerate(zip(windows, focal_indices)):
            narration = self.context_constructor.generate_enhanced_narration(window, focal_idx)
            narrations.append(narration)
            
            if (i + 1) % 500 == 0:
                print(f"已生成 {i + 1}/{n_windows} 个描述")
        
        # 批量提取LLM特征
        print("提取LLM特征...")
        llm_features = self.llm_engine.extract_features_batch(narrations)
        
        # 提取统计特征
        print("提取统计特征...")
        stat_features = []
        context_features = []
        
        for window, focal_idx in zip(windows, focal_indices):
            stat_feat = self.context_constructor.feature_extractor.extract_statistical_features(window)
            context_feat = self.context_constructor.feature_extractor.extract_contextual_features(window, focal_idx)
            
            stat_features.append(stat_feat)
            context_features.append(context_feat)
        
        stat_features = np.array(stat_features)
        context_features = np.array(context_features)
        
        # 处理NaN值
        stat_features = np.nan_to_num(stat_features, nan=0.0, posinf=0.0, neginf=0.0)
        context_features = np.nan_to_num(context_features, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 安全的标准化特征 - 处理方差为0的特征
        if is_training:
            # 检查并处理常数特征
            stat_var = np.var(stat_features, axis=0)
            context_var = np.var(context_features, axis=0)
            
            # 对于方差过小的特征，添加微小噪声避免标准化问题
            stat_features = stat_features.copy()
            context_features = context_features.copy()
            
            for i in range(stat_features.shape[1]):
                if stat_var[i] < 1e-10:
                    stat_features[:, i] += np.random.normal(0, 1e-6, stat_features.shape[0])
            
            for i in range(context_features.shape[1]):
                if context_var[i] < 1e-10:
                    context_features[:, i] += np.random.normal(0, 1e-6, context_features.shape[0])
            
            stat_features = self.scaler_stat.fit_transform(stat_features)
            context_features = self.scaler_context.fit_transform(context_features)
        else:
            stat_features = self.scaler_stat.transform(stat_features)
            context_features = self.scaler_context.transform(context_features)
        
        # 再次处理可能的NaN值
        stat_features = np.nan_to_num(stat_features, nan=0.0, posinf=0.0, neginf=0.0)
        context_features = np.nan_to_num(context_features, nan=0.0, posinf=0.0, neginf=0.0)
        
        return {
            'llm': llm_features,
            'statistical': torch.tensor(stat_features, dtype=torch.float32).to(self.device),
            'contextual': torch.tensor(context_features, dtype=torch.float32).to(self.device)
        }
    
    def _generate_pseudo_labels(self, windows, focal_indices, sample_ratio=0.3):
        """生成改进的伪标签"""
        n_samples = len(windows)
        sample_size = int(n_samples * sample_ratio)
        sample_indices = np.random.choice(n_samples, sample_size, replace=False)
        
        pseudo_labels = np.zeros(sample_size)
        
        # 收集全局统计信息
        all_focal_values = []
        for idx in range(n_samples):
            window = windows[idx]
            focal_idx = focal_indices[idx]
            
            if len(window.shape) > 1:
                window_flat = window.flatten()
            else:
                window_flat = window
            
            focal_value = window_flat[focal_idx] if focal_idx < len(window_flat) else window_flat[-1]
            all_focal_values.append(focal_value)
        
        all_focal_values = np.array(all_focal_values)
        global_q1 = np.percentile(all_focal_values, 25)
        global_q3 = np.percentile(all_focal_values, 75)
        global_iqr = global_q3 - global_q1
        
        # 改进的伪标签生成
        for i, idx in enumerate(sample_indices):
            window = windows[idx]
            focal_idx = focal_indices[idx]
            
            if len(window.shape) > 1:
                window_flat = window.flatten()
            else:
                window_flat = window
            
            focal_value = window_flat[focal_idx] if focal_idx < len(window_flat) else window_flat[-1]
            
            # 多种异常检测方法的组合
            scores = []
            
            # 1. 局部Z-score
            local_mean = np.mean(window_flat)
            local_std = np.std(window_flat)
            if local_std > 1e-8:
                local_z = abs(focal_value - local_mean) / local_std
                scores.append(local_z > 2.0)
            
            # 2. 全局IQR方法
            if global_iqr > 1e-8:
                lower_bound = global_q1 - 1.5 * global_iqr
                upper_bound = global_q3 + 1.5 * global_iqr
                scores.append((focal_value < lower_bound) or (focal_value > upper_bound))
            
            # 3. 局部百分位数方法
            if len(window_flat) > 5:
                local_q1 = np.percentile(window_flat, 10)
                local_q3 = np.percentile(window_flat, 90)
                scores.append((focal_value < local_q1) or (focal_value > local_q3))
            
            # 综合决策：如果多数方法认为是异常，则标记为异常
            if len(scores) > 0:
                pseudo_labels[i] = 1.0 if sum(scores) >= len(scores) / 2 else 0.0
            else:
                pseudo_labels[i] = 0.0
        
        # 确保有足够的正样本和负样本
        pos_ratio = np.mean(pseudo_labels)
        if pos_ratio < 0.05:  # 异常样本太少，增加一些
            n_add = int(sample_size * 0.05) - int(np.sum(pseudo_labels))
            if n_add > 0:
                zero_indices = np.where(pseudo_labels == 0)[0]
                if len(zero_indices) >= n_add:
                    add_indices = np.random.choice(zero_indices, n_add, replace=False)
                    pseudo_labels[add_indices] = 1.0
        elif pos_ratio > 0.3:  # 异常样本太多，减少一些
            n_remove = int(np.sum(pseudo_labels)) - int(sample_size * 0.2)
            if n_remove > 0:
                one_indices = np.where(pseudo_labels == 1)[0]
                if len(one_indices) >= n_remove:
                    remove_indices = np.random.choice(one_indices, n_remove, replace=False)
                    pseudo_labels[remove_indices] = 0.0
        
        print(f"伪标签统计: 正样本={int(np.sum(pseudo_labels))}/{len(pseudo_labels)} ({np.mean(pseudo_labels)*100:.1f}%)")
        
        return pseudo_labels
    
    def _train_hybrid_detector(self, features, labels):
        """训练混合检测器"""
        # 采样训练数据
        n_train = len(labels)
        indices = np.arange(n_train)
        
        train_llm = features['llm'][indices]
        train_stat = features['statistical'][indices]
        train_context = features['contextual'][indices]
        train_labels = torch.tensor(labels, dtype=torch.float32).to(self.device)
        
        # 设置优化器 - 使用更小的学习率和学习率调度器
        optimizer = torch.optim.Adam(self.hybrid_detector.parameters(), lr=0.0005, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=20, factor=0.5)
        criterion = nn.BCELoss()
        
        # 训练循环 - 增加训练轮数和调整学习率
        batch_size = 16  # 减小批次大小，更细致的学习
        num_epochs = 100  # 增加训练轮数
        
        for epoch in range(num_epochs):
            total_loss = 0
            num_batches = 0
            
            # 随机打乱数据
            perm = torch.randperm(n_train)
            
            for i in range(0, n_train, batch_size):
                batch_idx = perm[i:i+batch_size]
                
                batch_llm = train_llm[batch_idx]
                batch_stat = train_stat[batch_idx]
                batch_context = train_context[batch_idx]
                batch_labels = train_labels[batch_idx]
                
                optimizer.zero_grad()
                
                # 前向传播
                outputs = self.hybrid_detector(batch_llm, batch_stat, batch_context)
                loss = criterion(outputs.squeeze(), batch_labels)
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.hybrid_detector.parameters(), 1.0)
                optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
            
            avg_loss = total_loss / num_batches
            scheduler.step(avg_loss)  # 学习率调度
            
            if (epoch + 1) % 20 == 0:
                current_lr = optimizer.param_groups[0]['lr']
                print(f"Epoch [{epoch+1}/{num_epochs}], Loss: {avg_loss:.6f}, LR: {current_lr:.6f}")
    
    def decision_function(self, X):
        """预测异常分数"""
        if not self.is_fitted:
            raise RuntimeError("模型尚未训练，请先调用fit()方法")
        
        n_samples, n_features = X.shape
        
        if self.normalize: 
            X = zscore(X, axis=1, ddof=1)
        
        windows, focal_indices = self._create_windows_with_indices(X)
        
        print(f"开始预测 {n_samples} 个样本...")
        
        # 提取特征
        features = self._extract_all_features(windows, focal_indices, is_training=False)
        
        # 批量预测
        scores = []
        batch_size = 64
        
        with torch.no_grad():
            for i in range(0, n_samples, batch_size):
                end_idx = min(i + batch_size, n_samples)
                
                batch_llm = features['llm'][i:end_idx]
                batch_stat = features['statistical'][i:end_idx]
                batch_context = features['contextual'][i:end_idx]
                
                batch_scores = self.hybrid_detector(batch_llm, batch_stat, batch_context)
                scores.extend(batch_scores.squeeze().cpu().numpy())
        
        return np.array(scores)

# 运行函数
def run_Enhanced_CAAD_LMR_Unsupervised(data, **kwargs):
    """运行无监督增强CAAD-LMR"""
    clf = EnhancedCAAD_LMR_Detector(**kwargs)
    clf.fit(data)
    score = clf.decision_function(data)
    score = MinMaxScaler(feature_range=(0,1)).fit_transform(score.reshape(-1,1)).ravel()
    return score

def run_Enhanced_CAAD_LMR_Semisupervised(data_train, data_test, **kwargs):
    """运行半监督增强CAAD-LMR"""
    clf = EnhancedCAAD_LMR_Detector(**kwargs)
    clf.fit(data_train)
    score = clf.decision_function(data_test)
    score = MinMaxScaler(feature_range=(0,1)).fit_transform(score.reshape(-1,1)).ravel()
    return score

# 模型配置
ENHANCED_MODELS = {
    "lightweight": {
        "llm_model_name": "microsoft/DialoGPT-medium",
        "description": "轻量级模型，快速训练",
        "memory_usage": "~6GB"
    },
    "balanced": {
        "llm_model_name": "mistralai/Mistral-7B-Instruct-v0.1",
        "description": "平衡性能，推荐使用", 
        "memory_usage": "~16GB"
    },
    "efficient": {
        "llm_model_name": "sentence-transformers/all-MiniLM-L6-v2",
        "description": "高效特征提取",
        "memory_usage": "~2GB"
    }
}

def benchmark_single_file(filename, dataset_dir, model_config, domain="general"):
    """对单个文件进行完整的benchmark测试"""
    
    print(f"\n{'='*80}")
    print(f"开始处理文件: {filename}")
    print(f"使用模型配置: {model_config['description']}")
    print(f"预计内存使用: {model_config['memory_usage']}")
    print(f"{'='*80}")
    
    # 加载数据
    file_path = os.path.join(dataset_dir, filename)
    df = pd.read_csv(file_path).dropna()
    data = df.iloc[:, 0:-1].values.astype(float)
    label = df['Label'].astype(int).to_numpy()
    
    print(f'数据形状: {data.shape}')
    print(f'标签形状: {label.shape}')
    print(f'异常率: {np.sum(label)/len(label)*100:.2f}%')
    
    # 确定训练集
    slidingWindow = find_length_rank(data, rank=1)
    train_index = filename.split('.')[0].split('_')[-3]
    data_train = data[:int(train_index), :]
    
    print(f'训练集大小: {data_train.shape}')
    print(f'测试集大小: {data.shape}')
    
    # 配置增强模型参数
    enhanced_hp = {
        'llm_model_name': model_config['llm_model_name'],
        'domain': domain,
        'window_size': 50,
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'use_multi_gpu': True,
        'normalize': True
    }
    
    print("\n开始训练和测试...")
    start_time = time.time()
    
    try:
        # 运行检测
        output = run_Enhanced_CAAD_LMR_Semisupervised(data_train, data, **enhanced_hp)
        
        end_time = time.time()
        run_time = end_time - start_time
        
        print(f"运行时间: {run_time:.2f}秒")
        print(f"平均每个样本耗时: {run_time/len(data)*1000:.2f}ms")
        
        # 评估结果
        print("\n开始评估...")
        
        # 使用不同阈值策略
        threshold_strategies = {
            'mean_3std': np.mean(output) + 3*np.std(output),
            'percentile_95': np.percentile(output, 95),
            'percentile_99': np.percentile(output, 99),
            'fixed_0.5': 0.5
        }
        
        results = {}
        
        for strategy_name, threshold in threshold_strategies.items():
            pred = output > threshold
            evaluation_result = get_metrics(output, label, slidingWindow=slidingWindow, pred=pred)
            results[strategy_name] = evaluation_result
            
            print(f'\n{strategy_name} (阈值={threshold:.4f}):')
            for metric, value in evaluation_result.items():
                print(f'  {metric}: {value:.4f}')
        
        # 选择最佳结果
        best_strategy = max(results.keys(), 
                           key=lambda x: results[x].get('F1', 0))
        best_result = results[best_strategy]
        
        print(f'\n最佳策略: {best_strategy}')
        print(f'最佳F1分数: {best_result["F1"]:.4f}')
        
        return {
            'filename': filename,
            'run_time': run_time,
            'best_strategy': best_strategy,
            'all_results': results,
            'anomaly_scores': output,
            'true_labels': label
        }
        
    except Exception as e:
        print(f"处理文件 {filename} 时出错: {str(e)}")
        return {
            'filename': filename,
            'error': str(e),
            'run_time': time.time() - start_time
        }

def run_full_benchmark(dataset_dir, file_list_path, model_type="efficient", domain="general", 
                      results_dir="benchmark_results", max_files=None):
    """运行完整的benchmark测试"""
    
    print("Enhanced CAAD-LMR Full Benchmark")
    print("="*50)
    
    # 创建结果目录
    os.makedirs(results_dir, exist_ok=True)
    
    # 设置日志
    log_file = os.path.join(results_dir, f'enhanced_caad_lmr_{model_type}_{int(time.time())}.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    # 获取模型配置
    if model_type not in ENHANCED_MODELS:
        raise ValueError(f"不支持的模型类型: {model_type}")
    
    model_config = ENHANCED_MODELS[model_type]
    
    # 读取文件列表
    if file_list_path.endswith('.csv'):
        file_df = pd.read_csv(file_list_path)
        if 'file_name' in file_df.columns:
            file_list = file_df['file_name'].values
        else:
            file_list = file_df.iloc[:, 0].values
    else:
        with open(file_list_path, 'r') as f:
            file_list = [line.strip() for line in f.readlines()]
    
    if max_files:
        file_list = file_list[:max_files]
    
    print(f"发现 {len(file_list)} 个文件进行测试")
    
    # 运行benchmark
    all_results = []
    successful_runs = 0
    failed_runs = 0
    
    for i, filename in enumerate(file_list):
        print(f"\n进度: {i+1}/{len(file_list)}")
        
        try:
            result = benchmark_single_file(filename, dataset_dir, model_config, domain)
            all_results.append(result)
            
            if 'error' not in result:
                successful_runs += 1
                logging.info(f"成功处理: {filename}")
            else:
                failed_runs += 1
                logging.error(f"处理失败: {filename} - {result['error']}")
                
        except Exception as e:
            failed_runs += 1
            error_result = {
                'filename': filename,
                'error': str(e),
                'run_time': 0
            }
            all_results.append(error_result)
            logging.error(f"处理失败: {filename} - {str(e)}")
    
    # 生成总结报告
    print(f"\n{'='*80}")
    print("BENCHMARK 总结报告")
    print(f"{'='*80}")
    print(f"总文件数: {len(file_list)}")
    print(f"成功处理: {successful_runs}")
    print(f"处理失败: {failed_runs}")
    print(f"成功率: {successful_runs/len(file_list)*100:.2f}%")
    
    # 统计成功案例的性能
    successful_results = [r for r in all_results if 'error' not in r]
    
    if successful_results:
        # 计算平均性能
        all_f1_scores = []
        all_precision_scores = []
        all_recall_scores = []
        all_run_times = []
        
        for result in successful_results:
            best_result = result['all_results'][result['best_strategy']]
            all_f1_scores.append(best_result.get('F1', 0))
            all_precision_scores.append(best_result.get('Precision', 0))
            all_recall_scores.append(best_result.get('Recall', 0))
            all_run_times.append(result['run_time'])
        
        print(f"\n平均性能指标:")
        print(f"  平均F1分数: {np.mean(all_f1_scores):.4f} ± {np.std(all_f1_scores):.4f}")
        print(f"  平均精确率: {np.mean(all_precision_scores):.4f} ± {np.std(all_precision_scores):.4f}")
        print(f"  平均召回率: {np.mean(all_recall_scores):.4f} ± {np.std(all_recall_scores):.4f}")
        print(f"  平均运行时间: {np.mean(all_run_times):.2f}s ± {np.std(all_run_times):.2f}s")
        
        # 找出最佳和最差案例
        best_idx = np.argmax(all_f1_scores)
        worst_idx = np.argmin(all_f1_scores)
        
        print(f"\n最佳案例: {successful_results[best_idx]['filename']}")
        print(f"  F1分数: {all_f1_scores[best_idx]:.4f}")
        
        print(f"\n最差案例: {successful_results[worst_idx]['filename']}")
        print(f"  F1分数: {all_f1_scores[worst_idx]:.4f}")
    
    # 保存详细结果
    results_summary = {
        'model_type': model_type,
        'model_config': model_config,
        'domain': domain,
        'total_files': len(file_list),
        'successful_runs': successful_runs,
        'failed_runs': failed_runs,
        'results': all_results,
        'timestamp': datetime.now().isoformat()
    }
    
    # 保存到JSON文件
    json_file = os.path.join(results_dir, f'enhanced_caad_lmr_results_{model_type}_{int(time.time())}.json')
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, indent=2, ensure_ascii=False, default=str)
    
    # 保存CSV汇总
    csv_data = []
    for result in successful_results:
        row = {
            'filename': result['filename'],
            'run_time': result['run_time'],
            'best_strategy': result['best_strategy']
        }
        # 添加最佳策略的详细指标
        best_metrics = result['all_results'][result['best_strategy']]
        for metric, value in best_metrics.items():
            row[f'best_{metric}'] = value
        csv_data.append(row)
    
    if csv_data:
        csv_df = pd.DataFrame(csv_data)
        csv_file = os.path.join(results_dir, f'enhanced_caad_lmr_summary_{model_type}_{int(time.time())}.csv')
        csv_df.to_csv(csv_file, index=False)
        print(f"\n结果已保存到: {csv_file}")
    
    print(f"详细结果已保存到: {json_file}")
    print(f"日志文件: {log_file}")
    
    return results_summary

if __name__ == '__main__':
    Start_T = time.time()
    
    parser = argparse.ArgumentParser(description='Enhanced CAAD-LMR Full Benchmark')
    parser.add_argument('--dataset_dir', type=str, default='../Datasets/TSB-AD-U/', 
                       help='数据集目录路径')
    parser.add_argument('--file_list', type=str, default='../Datasets/File_List/TSB-AD-U-Eva.csv',
                       help='文件列表路径')
    parser.add_argument('--model_type', type=str, default='efficient', 
                       choices=['lightweight', 'balanced', 'efficient'],
                       help='模型类型选择')
    parser.add_argument('--domain', type=str, default='general',
                       choices=['financial', 'server', 'network', 'sensor', 'general'],
                       help='应用领域')
    parser.add_argument('--results_dir', type=str, default='benchmark_results',
                       help='结果保存目录')
    parser.add_argument('--max_files', type=int, default=None,
                       help='最大处理文件数（用于测试）')
    parser.add_argument('--single_file', type=str, default=None,
                       help='仅处理单个文件（用于调试）')
    
    args = parser.parse_args()
    
    print("增强版CAAD-LMR Benchmark启动")
    print(f"模型类型: {args.model_type}")
    print(f"应用领域: {args.domain}")
    print(f"数据集目录: {args.dataset_dir}")
    
    if args.single_file:
        # 单文件测试模式
        model_config = ENHANCED_MODELS[args.model_type]
        result = benchmark_single_file(args.single_file, args.dataset_dir, model_config, args.domain)
        print(f"\n单文件测试完成:")
        if 'error' not in result:
            print(f"最佳F1分数: {result['all_results'][result['best_strategy']]['F1']:.4f}")
        else:
            print(f"测试失败: {result['error']}")
    else:
        # 完整benchmark模式
        results_summary = run_full_benchmark(
            dataset_dir=args.dataset_dir,
            file_list_path=args.file_list,
            model_type=args.model_type,
            domain=args.domain,
            results_dir=args.results_dir,
            max_files=args.max_files
        )
        
        print(f"\nBenchmark完成!")
        print(f"总耗时: {time.time() - Start_T:.2f}秒") 