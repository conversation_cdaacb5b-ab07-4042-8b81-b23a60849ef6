#!/usr/bin/env python3
"""
最终的论文规范测试
使用论文中的精确超参数配置
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import time
import warnings
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

def generate_benchmark_data(n_samples=2000):
    """生成基准测试数据"""
    np.random.seed(42)
    
    # 生成复杂的多周期信号
    t = np.linspace(0, 12*np.pi, n_samples)
    data = (np.sin(t) + 0.5*np.sin(3*t) + 0.3*np.sin(7*t) + 
            0.2*np.sin(11*t) + 0.1*np.random.randn(n_samples))
    
    # 创建标签数组
    labels = np.zeros(n_samples)
    anomaly_indices = []
    
    # 1. 尖峰异常 (Spike anomalies)
    for _ in range(25):
        idx = np.random.randint(200, n_samples-200)
        data[idx] += np.random.normal(4, 0.8)
        labels[idx] = 1
        anomaly_indices.append(idx)
    
    # 2. 集体异常 (Collective anomalies)
    for _ in range(10):
        start_idx = np.random.randint(200, n_samples-400)
        length = np.random.randint(50, 120)
        end_idx = start_idx + length
        # 添加趋势和频率变化
        trend = np.linspace(0, 2.5, length)
        freq_change = np.sin(np.linspace(0, 4*np.pi, length)) * 0.8
        data[start_idx:end_idx] += trend + freq_change + np.random.normal(0, 0.3, length)
        labels[start_idx:end_idx] = 1
        anomaly_indices.extend(range(start_idx, end_idx))
    
    # 3. 上下文异常 (Contextual anomalies)
    for _ in range(8):
        idx = np.random.randint(200, n_samples-200)
        # 局部异常模式
        local_pattern = data[idx-30:idx+30]
        local_mean = np.mean(local_pattern)
        data[idx] = local_mean + np.random.choice([-1, 1]) * 2.0
        labels[idx] = 1
        anomaly_indices.append(idx)
    
    return data, labels, anomaly_indices


def run_paper_compliant_benchmark():
    """运行论文规范的基准测试"""
    print("📋 论文规范基准测试")
    print("=" * 60)
    print("📊 使用论文精确超参数:")
    print("  - Window Size: 128")
    print("  - Latent Dimension: 32") 
    print("  - Learning Rate: 1e-3")
    print("  - Batch Size: 64")
    print("  - Epochs: 30")
    print("  - SAE Hidden Dimension: 128")
    print("  - SAE Sparsity Weight (λ): 0.001")
    print("  - Purification Strength (α): 0.7")
    
    # 生成数据
    data, labels, anomaly_indices = generate_benchmark_data(2000)
    
    # 分割数据
    split_point = int(len(data) * 0.7)
    train_data = data[:split_point]
    test_data = data[split_point:]
    test_labels = labels[split_point:]
    
    print(f"\n📊 数据集信息:")
    print(f"  - 总数据点: {len(data)}")
    print(f"  - 训练数据: {len(train_data)} 点")
    print(f"  - 测试数据: {len(test_data)} 点")
    print(f"  - 测试异常: {np.sum(test_labels)} 点 ({np.sum(test_labels)/len(test_labels):.1%})")
    print(f"  - 异常类型: 尖峰异常、集体异常、上下文异常")
    
    # 测试配置 - 严格按照论文
    test_configs = [
        {
            'name': 'HTA-AD (Paper)',
            'function': 'run_HTA_AD',
            'params': {
                'window_size': 128,    # Paper Table
                'epochs': 30,          # Paper Table
                'lr': 1e-3,           # Paper Table
                'batch_size': 64,     # Paper Table
                'latent_dim': 32      # Paper: "32-dimensional latent vector"
            }
        },
        {
            'name': 'HTA-AD-SAE (Paper)',
            'function': 'run_HTA_AD_SAE',
            'params': {
                'window_size': 128,           # Paper Table
                'epochs': 30,                 # Paper Table
                'lr': 1e-3,                  # Paper Table
                'batch_size': 64,            # Paper Table
                'latent_dim': 32,            # Paper: "32-dimensional latent vector"
                'sae_hidden_dim': 128,       # Paper: "128 interpretable features"
                'sae_sparsity_weight': 0.001, # Paper Table: λ = 0.001
                'purification_strength': 0.7  # Paper Table: α = 0.7
            }
        }
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n🧪 测试 {config['name']}")
        print("-" * 40)
        
        try:
            # 导入相应的函数
            if config['function'] == 'run_HTA_AD':
                from TSB_AD.model_wrapper import run_HTA_AD as test_func
            else:
                from TSB_AD.model_wrapper import run_HTA_AD_SAE as test_func
            
            # 记录训练过程
            print(f"  🏋️ 开始训练 {config['name']}...")
            print(f"  📋 参数: {config['params']}")
            
            start_time = time.time()
            
            # 运行模型
            if config['function'] == 'run_HTA_AD':
                scores = test_func(train_data, test_data, **config['params'])
            else:
                scores = test_func(train_data, test_data, **config['params'])
            
            end_time = time.time()
            training_time = end_time - start_time
            
            # 计算评估指标
            from sklearn.metrics import roc_auc_score, average_precision_score, precision_recall_curve
            
            try:
                auc = roc_auc_score(test_labels, scores)
                ap = average_precision_score(test_labels, scores)
                
                # 计算最佳F1分数
                precision, recall, thresholds = precision_recall_curve(test_labels, scores)
                f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
                best_f1 = np.max(f1_scores)
                
            except Exception as e:
                print(f"    ⚠️ 指标计算警告: {e}")
                auc, ap, best_f1 = 0.5, 0.1, 0.1
            
            results[config['name']] = {
                'scores': scores,
                'training_time': training_time,
                'auc': auc,
                'ap': ap,
                'f1': best_f1,
                'config': config['params']
            }
            
            print(f"  ✅ {config['name']} 完成")
            print(f"  ⏱️ 训练时间: {training_time:.2f}s")
            print(f"  📊 AUC: {auc:.4f}")
            print(f"  📊 AP: {ap:.4f}")
            print(f"  📊 Best F1: {best_f1:.4f}")
            
        except Exception as e:
            print(f"  ❌ {config['name']} 失败: {e}")
            import traceback
            traceback.print_exc()
            results[config['name']] = {'error': str(e)}
    
    return results, test_data, test_labels, anomaly_indices


def visualize_paper_results(results, test_data, test_labels, anomaly_indices):
    """可视化论文规范结果"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Paper-Compliant HTA-AD Benchmark Results', fontsize=16)
    
    # 1. 原始数据可视化
    ax1 = axes[0, 0]
    ax1.plot(test_data, 'b-', alpha=0.7, label='Test Data', linewidth=1)
    ax1.scatter(np.where(test_labels == 1)[0], test_data[test_labels == 1], 
               c='red', s=15, label='True Anomalies', alpha=0.8)
    ax1.set_title('Test Data with Ground Truth Anomalies')
    ax1.set_xlabel('Time Step')
    ax1.set_ylabel('Value')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 异常分数分布对比
    ax2 = axes[0, 1]
    colors = ['orange', 'green']
    for i, (method_name, result) in enumerate(results.items()):
        if 'error' not in result:
            scores = result['scores']
            ax2.hist(scores, bins=40, alpha=0.6, label=method_name, 
                    density=True, color=colors[i % len(colors)])
    ax2.set_title('Anomaly Score Distributions')
    ax2.set_xlabel('Anomaly Score')
    ax2.set_ylabel('Density')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 性能指标对比
    ax3 = axes[0, 2]
    method_names = []
    auc_scores = []
    ap_scores = []
    f1_scores = []
    train_times = []
    
    for method_name, result in results.items():
        if 'error' not in result:
            method_names.append(method_name.replace(' (Paper)', ''))
            auc_scores.append(result['auc'])
            ap_scores.append(result['ap'])
            f1_scores.append(result['f1'])
            train_times.append(result['training_time'])
    
    if method_names:
        x = np.arange(len(method_names))
        width = 0.25
        
        bars1 = ax3.bar(x - width, auc_scores, width, label='AUC', alpha=0.8, color='skyblue')
        bars2 = ax3.bar(x, ap_scores, width, label='AP', alpha=0.8, color='lightcoral')
        bars3 = ax3.bar(x + width, f1_scores, width, label='F1', alpha=0.8, color='lightgreen')
        
        ax3.set_xlabel('Method')
        ax3.set_ylabel('Score')
        ax3.set_title('Performance Metrics Comparison')
        ax3.set_xticks(x)
        ax3.set_xticklabels(method_names, rotation=45)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 1)
        
        # 添加数值标签
        for bars, values in [(bars1, auc_scores), (bars2, ap_scores), (bars3, f1_scores)]:
            for bar, val in zip(bars, values):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02, 
                        f'{val:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 4-5. 检测结果可视化
    method_names_full = list(results.keys())
    colors = ['orange', 'green']
    
    for i, method_name in enumerate(method_names_full[:2]):
        ax = axes[1, i]
        if method_name in results and 'error' not in results[method_name]:
            result = results[method_name]
            scores = result['scores']
            
            # 绘制测试数据
            ax.plot(test_data, 'b-', alpha=0.7, label='Test Data', linewidth=1)
            
            # 绘制真实异常
            ax.scatter(np.where(test_labels == 1)[0], test_data[test_labels == 1], 
                      c='red', s=20, label='True Anomalies', alpha=0.8, zorder=5)
            
            # 绘制异常分数（归一化到数据范围）
            if len(scores) > 0:
                scores_norm = (scores - scores.min()) / (scores.max() - scores.min() + 1e-8)
                scores_norm = scores_norm * (test_data.max() - test_data.min()) + test_data.min()
                ax.plot(scores_norm, colors[i], alpha=0.8, label='Anomaly Scores', linewidth=2)
            
            ax.set_title(f'{method_name} Detection Results\nAUC: {result["auc"]:.4f}, AP: {result["ap"]:.4f}')
            ax.set_xlabel('Time Step')
            ax.set_ylabel('Value')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, f'{method_name}\nTraining Failed', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            ax.set_title(f'{method_name} Results')
    
    # 6. 训练时间对比
    ax6 = axes[1, 2]
    if method_names and train_times:
        bars = ax6.bar(method_names, train_times, alpha=0.7, color=['skyblue', 'lightcoral'])
        ax6.set_xlabel('Method')
        ax6.set_ylabel('Training Time (seconds)')
        ax6.set_title('Training Time Comparison')
        ax6.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, time_val in zip(bars, train_times):
            ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                    f'{time_val:.1f}s', ha='center', va='bottom', fontsize=10)
        
        # 添加时间比例信息
        if len(train_times) >= 2:
            ratio = train_times[1] / train_times[0]
            ax6.text(0.5, 0.95, f'SAE/Basic Ratio: {ratio:.2f}x', 
                    transform=ax6.transAxes, ha='center', va='top', 
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    import os
    os.makedirs('results/visualizations', exist_ok=True)
    plt.savefig('results/visualizations/paper_compliant_results.png', dpi=300, bbox_inches='tight')
    print(f"\n💾 论文规范结果已保存到: results/visualizations/paper_compliant_results.png")
    
    plt.show()


def generate_paper_summary(results):
    """生成论文总结报告"""
    print("\n" + "=" * 60)
    print("📋 论文规范基准测试总结报告")
    print("=" * 60)
    
    print(f"{'方法':<20} {'AUC':<8} {'AP':<8} {'F1':<8} {'训练时间':<10} {'状态':<10}")
    print("-" * 75)
    
    valid_results = []
    for method_name, result in results.items():
        if 'error' in result:
            print(f"{method_name:<20} {'N/A':<8} {'N/A':<8} {'N/A':<8} {'N/A':<10} {'失败':<10}")
        else:
            print(f"{method_name:<20} {result['auc']:<8.4f} {result['ap']:<8.4f} "
                  f"{result['f1']:<8.4f} {result['training_time']:<10.2f} {'成功':<10}")
            valid_results.append((method_name, result))
    
    if len(valid_results) >= 2:
        basic_result = valid_results[0][1]
        sae_result = valid_results[1][1]
        
        print(f"\n🎯 关键发现:")
        print(f"  📊 性能对比:")
        print(f"    - AUC: {basic_result['auc']:.4f} → {sae_result['auc']:.4f} "
              f"({((sae_result['auc'] - basic_result['auc']) / basic_result['auc'] * 100):+.1f}%)")
        print(f"    - AP:  {basic_result['ap']:.4f} → {sae_result['ap']:.4f} "
              f"({((sae_result['ap'] - basic_result['ap']) / basic_result['ap'] * 100):+.1f}%)")
        print(f"    - F1:  {basic_result['f1']:.4f} → {sae_result['f1']:.4f} "
              f"({((sae_result['f1'] - basic_result['f1']) / basic_result['f1'] * 100):+.1f}%)")
        
        print(f"  ⏱️ 效率对比:")
        time_ratio = sae_result['training_time'] / basic_result['training_time']
        print(f"    - 训练时间比例: {time_ratio:.2f}x")
        
        if time_ratio < 1:
            print(f"    ✅ SAE版本训练更快 ({(1-time_ratio)*100:.1f}% 时间节省)")
        else:
            print(f"    ⚠️ SAE版本训练更慢 ({(time_ratio-1)*100:.1f}% 额外时间)")
        
        print(f"\n💡 论文建议:")
        if sae_result['auc'] > basic_result['auc']:
            print(f"  ✅ SAE集成显著提升了异常检测性能")
            print(f"  📝 可以在论文中强调SAE的性能优势")
        else:
            print(f"  ⚠️ SAE集成的主要价值在于可解释性，而非性能提升")
            print(f"  📝 论文应重点强调可解释性价值")


def main():
    """主函数"""
    print("📋 HTA-AD 论文规范基准测试")
    print("=" * 60)
    
    try:
        # 运行论文规范基准测试
        results, test_data, test_labels, anomaly_indices = run_paper_compliant_benchmark()
        
        # 可视化结果
        visualize_paper_results(results, test_data, test_labels, anomaly_indices)
        
        # 生成总结报告
        generate_paper_summary(results)
        
        print("\n" + "=" * 60)
        print("📝 测试完成")
        print("=" * 60)
        print("✅ 论文规范基准测试完成！")
        print("📊 所有结果已保存到可视化图表中")
        print("💡 请根据结果调整论文中的性能声明")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
