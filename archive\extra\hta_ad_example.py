#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Example usage of HTA-AD with TSB-AD benchmark

This script demonstrates how to use HTA-AD (Hourglass Temporal Autoencoder for Anomaly Detection)
within the TSB-AD benchmark framework.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import sys
import os

# Add TSB-AD to path
sys.path.insert(0, 'TSB-AD')

def example_basic_usage():
    """Basic usage example of HTA-AD"""
    
    print("📊 HTA-AD Basic Usage Example")
    print("=" * 50)
    
    # Import the model wrapper
    from TSB_AD.model_wrapper import run_Semisupervise_AD
    
    # Create synthetic time series data
    np.random.seed(42)
    n_samples = 1000
    n_features = 3
    
    # Generate normal data with seasonal patterns
    t = np.linspace(0, 20, n_samples)
    data = np.zeros((n_samples, n_features))
    
    for i in range(n_features):
        # Base signal with different frequencies
        base_signal = np.sin(2 * np.pi * (i + 1) * 0.1 * t) + 0.5 * np.sin(2 * np.pi * (i + 1) * 0.05 * t)
        # Add noise
        noise = 0.1 * np.random.randn(n_samples)
        data[:, i] = base_signal + noise
    
    # Add anomalies
    anomaly_indices = [200, 400, 600, 800]
    for idx in anomaly_indices:
        data[idx:idx+20, :] += 2.0 * np.random.randn(20, n_features)
    
    # Split into train/test
    split_point = int(0.7 * n_samples)
    data_train = data[:split_point]
    data_test = data[split_point:]
    
    print(f"Training data shape: {data_train.shape}")
    print(f"Test data shape: {data_test.shape}")
    
    # Run HTA-AD
    print("\n🔄 Running HTA-AD...")
    scores = run_Semisupervise_AD(
        'HTA_AD',
        data_train,
        data_test,
        window_size=64,
        epochs=20,
        lr=1e-3,
        batch_size=32,
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        cnn_channels=16
    )
    
    print(f"✅ Anomaly detection completed!")
    print(f"   Anomaly scores shape: {scores.shape}")
    print(f"   Score range: [{scores.min():.4f}, {scores.max():.4f}]")
    print(f"   Mean score: {scores.mean():.4f}")
    
    return data_test, scores

def example_hyperparameter_tuning():
    """Example of hyperparameter tuning for HTA-AD"""
    
    print("\n\n🔧 HTA-AD Hyperparameter Tuning Example")
    print("=" * 50)
    
    from TSB_AD.model_wrapper import run_Semisupervise_AD
    
    # Create test data
    np.random.seed(123)
    n_samples = 800
    n_features = 2
    
    t = np.linspace(0, 10, n_samples)
    data = np.column_stack([
        np.sin(2 * np.pi * 0.1 * t) + 0.1 * np.random.randn(n_samples),
        np.cos(2 * np.pi * 0.15 * t) + 0.1 * np.random.randn(n_samples)
    ])
    
    # Add anomalies
    data[200:220] += 3.0
    data[500:510] -= 2.5
    
    split_point = int(0.6 * n_samples)
    data_train = data[:split_point]
    data_test = data[split_point:]
    
    # Test different hyperparameter configurations
    configs = [
        {
            'name': 'Small Model',
            'window_size': 32,
            'latent_dim': 16,
            'tcn_channels': [16, 16],
            'cnn_channels': 8,
            'epochs': 15
        },
        {
            'name': 'Medium Model',
            'window_size': 64,
            'latent_dim': 32,
            'tcn_channels': [32, 32, 32],
            'cnn_channels': 16,
            'epochs': 20
        },
        {
            'name': 'Large Model',
            'window_size': 128,
            'latent_dim': 64,
            'tcn_channels': [64, 64, 32],
            'cnn_channels': 32,
            'epochs': 25
        }
    ]
    
    results = {}
    
    for config in configs:
        print(f"\n🧪 Testing {config['name']}...")
        
        try:
            scores = run_Semisupervise_AD(
                'HTA_AD',
                data_train,
                data_test,
                window_size=config['window_size'],
                latent_dim=config['latent_dim'],
                tcn_channels=config['tcn_channels'],
                cnn_channels=config['cnn_channels'],
                epochs=config['epochs'],
                lr=1e-3,
                batch_size=16
            )
            
            results[config['name']] = {
                'scores': scores,
                'mean_score': scores.mean(),
                'std_score': scores.std(),
                'max_score': scores.max()
            }
            
            print(f"   ✅ Mean score: {scores.mean():.4f}")
            print(f"   📊 Std score: {scores.std():.4f}")
            print(f"   🔝 Max score: {scores.max():.4f}")
            
        except Exception as e:
            print(f"   ❌ Failed: {str(e)}")
    
    # Compare results
    print(f"\n📈 Hyperparameter Comparison:")
    print("-" * 40)
    for name, result in results.items():
        print(f"{name:12} | Mean: {result['mean_score']:.4f} | Std: {result['std_score']:.4f} | Max: {result['max_score']:.4f}")
    
    return results

def example_with_real_dataset():
    """Example using HTA-AD with a more realistic dataset structure"""
    
    print("\n\n📁 HTA-AD with Realistic Dataset Example")
    print("=" * 50)
    
    from TSB_AD.model_wrapper import run_Semisupervise_AD
    
    # Simulate a realistic multivariate time series (e.g., sensor data)
    np.random.seed(456)
    n_samples = 1200
    n_features = 5  # Multiple sensors
    
    # Create realistic patterns
    t = np.linspace(0, 30, n_samples)
    data = np.zeros((n_samples, n_features))
    
    # Simulate different types of sensors
    sensor_patterns = [
        lambda t: 20 + 5 * np.sin(2 * np.pi * 0.1 * t) + np.random.normal(0, 0.5, len(t)),  # Temperature
        lambda t: 50 + 10 * np.cos(2 * np.pi * 0.05 * t) + np.random.normal(0, 1, len(t)),   # Humidity
        lambda t: 1000 + 100 * np.sin(2 * np.pi * 0.2 * t) + np.random.normal(0, 10, len(t)), # Pressure
        lambda t: 0.5 + 0.2 * np.sin(2 * np.pi * 0.15 * t) + np.random.normal(0, 0.05, len(t)), # Flow rate
        lambda t: 220 + 10 * np.sin(2 * np.pi * 0.08 * t) + np.random.normal(0, 2, len(t))    # Voltage
    ]
    
    for i, pattern_func in enumerate(sensor_patterns):
        data[:, i] = pattern_func(t)
    
    # Add realistic anomalies (equipment failures, sensor malfunctions)
    anomaly_events = [
        (300, 320, [0, 1]),      # Temperature and humidity spike
        (600, 610, [2]),         # Pressure drop
        (900, 930, [3, 4]),      # Flow and voltage anomaly
        (1100, 1105, [0, 2, 4])  # Multiple sensor failure
    ]
    
    for start, end, sensors in anomaly_events:
        for sensor in sensors:
            if sensor < n_features:
                # Different types of anomalies
                if sensor == 0:  # Temperature spike
                    data[start:end, sensor] += 15
                elif sensor == 1:  # Humidity drop
                    data[start:end, sensor] -= 20
                elif sensor == 2:  # Pressure anomaly
                    data[start:end, sensor] *= 0.7
                elif sensor == 3:  # Flow rate spike
                    data[start:end, sensor] += 0.5
                elif sensor == 4:  # Voltage fluctuation
                    data[start:end, sensor] += 30 * np.random.randn(end - start)
    
    # Split data
    split_point = int(0.75 * n_samples)
    data_train = data[:split_point]
    data_test = data[split_point:]
    
    print(f"Dataset info:")
    print(f"  - Total samples: {n_samples}")
    print(f"  - Features (sensors): {n_features}")
    print(f"  - Training samples: {data_train.shape[0]}")
    print(f"  - Test samples: {data_test.shape[0]}")
    print(f"  - Anomaly events in test: {len([e for e in anomaly_events if e[0] >= split_point])}")
    
    # Run HTA-AD with optimized parameters for multivariate data
    print(f"\n🔄 Running HTA-AD on realistic dataset...")
    scores = run_Semisupervise_AD(
        'HTA_AD',
        data_train,
        data_test,
        window_size=96,      # Larger window for complex patterns
        epochs=30,           # More epochs for better learning
        lr=5e-4,            # Lower learning rate for stability
        batch_size=64,       # Larger batch size
        latent_dim=48,       # Higher latent dimension for complex data
        tcn_channels=[64, 48, 32],  # Deeper network
        cnn_channels=24,
        downsample_stride=2
    )
    
    print(f"✅ Analysis completed!")
    print(f"   Anomaly scores computed: {scores.shape}")
    print(f"   Score statistics:")
    print(f"     - Min: {scores.min():.4f}")
    print(f"     - Max: {scores.max():.4f}")
    print(f"     - Mean: {scores.mean():.4f}")
    print(f"     - Std: {scores.std():.4f}")
    
    # Analyze anomaly detection performance
    test_anomaly_indices = []
    for start, end, sensors in anomaly_events:
        if start >= split_point:
            test_start = start - split_point
            test_end = min(end - split_point, len(scores))
            test_anomaly_indices.extend(range(test_start, test_end))
    
    if test_anomaly_indices:
        anomaly_scores = scores[test_anomaly_indices]
        normal_indices = [i for i in range(len(scores)) if i not in test_anomaly_indices]
        normal_scores = scores[normal_indices]
        
        print(f"\n📊 Anomaly Detection Performance:")
        print(f"   - Mean normal score: {normal_scores.mean():.4f}")
        print(f"   - Mean anomaly score: {anomaly_scores.mean():.4f}")
        print(f"   - Anomaly/Normal ratio: {anomaly_scores.mean() / normal_scores.mean():.2f}")
    
    return data_test, scores

if __name__ == "__main__":
    print("🚀 HTA-AD Examples with TSB-AD")
    print("=" * 60)
    
    # Run examples
    try:
        # Basic usage
        data1, scores1 = example_basic_usage()
        
        # Hyperparameter tuning
        results = example_hyperparameter_tuning()
        
        # Realistic dataset
        data2, scores2 = example_with_real_dataset()
        
        print(f"\n🎉 All examples completed successfully!")
        print(f"💡 HTA-AD is ready for use in TSB-AD benchmark evaluations.")
        
    except Exception as e:
        print(f"\n❌ Example failed: {str(e)}")
        import traceback
        traceback.print_exc()
