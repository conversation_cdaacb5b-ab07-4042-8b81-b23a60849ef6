#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HTA_AD_M多变量数据集测试脚本
专门用于在多变量数据集上测试HTA_AD_M模型的性能
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import time
from datetime import datetime
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入模型和评估工具
from TSB_AD.models.HTA_AD_M import HTA_AD_M
from TSB_AD.models.HTA_AD_M_VAE import HTA_AD_M_VAE
from TSB_AD.evaluation.metrics import get_metrics

# --- 尝试导入可选的可视化工具 ---
try:
    from visualize_results import plot_detection_results
except ImportError:
    plot_detection_results = None
    print("警告: 'visualize_results.py' 未找到，将跳过生成单个检测图。")

# 设置随机种子以确保结果可复现
np.random.seed(42)

# 配置参数
CONFIG = {
    # 多变量数据集配置 - 选择几个不同类型的数据集进行测试
    'datasets': [
        'Datasets/TSB-AD-M/001_Genesis_id_1_Sensor_tr_4055_1st_15538.csv',  # 传感器数据
        'Datasets/TSB-AD-M/018_Daphnet_id_1_HumanActivity_tr_9693_1st_20732.csv',  # 人类活动数据
        'Datasets/TSB-AD-M/057_SMD_id_1_Facility_tr_4529_1st_4629.csv',  # 设施数据
        'Datasets/TSB-AD-M/079_LTDB_id_1_Medical_tr_3618_1st_3718.csv'   # 医疗数据
    ],
    # 模型配置
    'models': [
        {
            'name': 'HTA_AD_M',
            'class': HTA_AD_M,
            'hyperparams': {
                'window_size': 128,
                'latent_dim': 32,
                'tcn_channels': [32, 32],
                'cnn_channels': 16,
                'downsample_stride': 2,
                'epochs': 30,
                'lr': 1e-3,
                'batch_size': 64,
                'gpu': 0
            }
        },
        {
            'name': 'HTA_AD_M_VAE',
            'class': HTA_AD_M_VAE,
            'hyperparams': {
                'window_size': 128,
                'latent_dim': 32,
                'tcn_channels': [32, 32],
                'cnn_channels': 16,
                'downsample_stride': 2,
                'beta': 1.0,  # VAE的KL散度权重
                'epochs': 30,
                'lr': 1e-3,
                'batch_size': 64,
                'gpu': 0
            }
        }
    ],
    # 超参数变体（仅对HTA_AD_M_VAE模型测试不同的beta值）
    'beta_variants': [0.1, 0.5, 1.0, 2.0, 5.0],
    # 结果保存路径
    'results_dir': 'multivariate_test_results',
    # 是否保存可视化结果
    'save_plots': True
}

def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def load_dataset(file_path):
    """加载数据集并进行预处理"""
    print(f"加载数据集: {file_path}")
    df = pd.read_csv(file_path).dropna()
    data = df.iloc[:, 0:-1].values.astype(float)
    labels = df['Label'].astype(int).to_numpy()
    
    # 提取数据集名称和类型
    dataset_name = os.path.basename(file_path).split('.')[0]
    dataset_type = dataset_name.split('_')[3]  # 提取数据类型（如Sensor, Medical等）
    
    print(f"数据集 {dataset_name} 加载完成。形状: {data.shape}, 变量数: {data.shape[1]}, 异常比例: {np.mean(labels):.2%}")
    
    return data, labels, dataset_name, dataset_type

def extract_train_size_from_filename(filename):
    """从文件名中提取训练集大小"""
    parts = os.path.basename(filename).split('_')
    try:
        # 查找 'tr' 部分的索引
        tr_index = parts.index('tr')
        # 下一个部分应该是训练集大小
        if tr_index + 1 < len(parts):
            train_size_str = parts[tr_index + 1]
            return int(train_size_str)
    except (ValueError, IndexError):
        # 如果 'tr' 不在列表中或它是最后一个元素，则忽略
        pass

    # 如果上述方法失败，尝试旧的逻辑以兼容 'tr4055' 这样的格式
    for part in parts:
        if part.startswith('tr') and len(part) > 2:
            try:
                return int(part[2:])
            except ValueError:
                print(f"警告: 无法从部分 '{part}' 中提取训练集大小")
                continue

    # 如果无法从文件名提取，则使用默认的15%
    print(f"警告: 无法从文件名 '{os.path.basename(filename)}' 中提取训练集大小，将使用默认值15%")
    return None

def run_experiment(model_config, dataset_path, save_dir, beta=None):
    """运行单个实验"""
    # 加载数据集
    data, labels, dataset_name, dataset_type = load_dataset(dataset_path)
    
    # 从文件名中提取训练集大小
    train_size = extract_train_size_from_filename(dataset_path)
    if train_size is None:
        train_size = int(len(data) * 0.15)  # 默认使用15%作为训练集
    else:
        # 确保训练集大小不超过数据集总大小
        train_size = min(train_size, len(data) - 1)
    
    print(f"使用训练集大小: {train_size} ({train_size/len(data):.2%})")
    
    data_train = data[:train_size]
    data_test = data[train_size:]
    
    start_time = time.time()
    
    try:
        # 复制超参数，以便可以修改
        hyperparams = model_config['hyperparams'].copy()
        
        # 如果提供了beta值，则更新超参数
        if beta is not None and model_config['name'] == 'HTA_AD_M_VAE':
            hyperparams['beta'] = beta
            model_name = f"{model_config['name']}_beta{beta}"
        else:
            model_name = model_config['name']
        
        # 实例化模型
        model = model_config['class'](HP=hyperparams)
        
        # 训练模型
        model.fit(data_train)
        
        # 获取测试集的异常分数
        test_scores = model.decision_function(data_test)
        
        # 创建完整的分数数组
        full_scores = np.zeros(len(data))
        full_scores[train_size:] = test_scores
        
        # 计算评估指标
        metrics = get_metrics(full_scores, labels)
        
        # 记录运行时间
        runtime = time.time() - start_time
        
        # --- 如果配置了保存，则生成单个检测图 ---
        if CONFIG['save_plots'] and plot_detection_results:
            print("🎨 正在生成单个检测图...")
            plots_dir = os.path.join(save_dir, "detection_plots")
            ensure_dir(plots_dir)
            plot_detection_results(
                data=data,
                label=labels,
                score=full_scores,
                model_name=model_name,
                filename=dataset_name,
                train_size=train_size,
                save_dir=plots_dir
            )
        
        return {
            'dataset': dataset_name,
            'dataset_type': dataset_type,
            'n_variables': data.shape[1],
            'model': model_name,
            'hyperparameters': hyperparams,
            'metrics': metrics,
            'runtime': runtime,
            'status': 'success'
        }
    
    except Exception as e:
        # 记录错误信息
        import traceback
        error_details = traceback.format_exc()
        
        return {
            'dataset': dataset_name,
            'dataset_type': dataset_type,
            'n_variables': data.shape[1],
            'model': model_name if 'model_name' in locals() else model_config['name'],
            'hyperparameters': hyperparams if 'hyperparams' in locals() else model_config['hyperparams'],
            'metrics': None,
            'runtime': time.time() - start_time,
            'status': 'error',
            'error_message': str(e),
            'error_details': error_details
        }

def visualize_results(results, save_dir):
    """可视化实验结果"""
    # 将结果转换为DataFrame
    rows = []
    for result in results:
        if result['status'] == 'success' and result['metrics'] is not None:
            row = {
                'dataset': result['dataset'],
                'dataset_type': result['dataset_type'],
                'n_variables': result['n_variables'],
                'model': result['model'],
                'window_size': result['hyperparameters']['window_size'],
                'latent_dim': result['hyperparameters']['latent_dim'],
                'tcn_channels': str(result['hyperparameters']['tcn_channels']),
                'cnn_channels': result['hyperparameters']['cnn_channels'],
                'downsample_stride': result['hyperparameters']['downsample_stride'],
                'runtime': result['runtime']
            }
            
            # 添加beta参数（如果存在）
            if 'beta' in result['hyperparameters']:
                row['beta'] = result['hyperparameters']['beta']
            
            # 添加指标
            for metric_name, metric_value in result['metrics'].items():
                row[metric_name] = metric_value
            rows.append(row)
    
    if not rows:
        print("没有成功的实验结果可视化")
        return
    
    df = pd.DataFrame(rows)
    
    # 保存结果表格
    df.to_csv(os.path.join(save_dir, 'multivariate_test_results.csv'), index=False)
    
    # 按数据集类型分组可视化
    for dataset_type in df['dataset_type'].unique():
        subset_by_type = df[df['dataset_type'] == dataset_type]
        
        # 创建模型性能比较图
        plt.figure(figsize=(15, 10))
        
        # 选择重要的指标进行可视化
        metrics = ['AUC-ROC', 'AUC-PR', 'Standard-F1', 'Event-based-F1']
        
        for i, metric in enumerate(metrics):
            plt.subplot(2, 2, i+1)
            
            # 按数据集和模型分组
            pivot = subset_by_type.pivot_table(
                index='dataset', 
                columns='model',
                values=metric,
                aggfunc='mean'
            )
            
            # 绘制热图
            sns.heatmap(pivot, annot=True, fmt=".3f", cmap="YlGnBu", cbar=True)
            plt.title(f'{metric} - {dataset_type}')
            plt.ylabel('Dataset')
            plt.xlabel('Model')
            plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f'{dataset_type}_model_comparison.png'))
        plt.close()
    
    # 创建模型性能对比条形图
    plt.figure(figsize=(16, 10))
    
    # 按模型分组，计算平均性能
    model_perf = df.groupby('model')[metrics].mean().reset_index()
    
    # 绘制条形图
    for i, metric in enumerate(metrics):
        plt.subplot(2, 2, i+1)
        sns.barplot(x='model', y=metric, data=model_perf)
        plt.title(f'Average {metric} by Model')
        plt.xticks(rotation=45)
        plt.grid(True, linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'model_performance_comparison.png'))
    plt.close()
    
    # 如果有VAE模型，创建beta参数影响图
    if 'beta' in df.columns:
        vae_results = df[df['model'].str.contains('VAE')]
        
        if not vae_results.empty:
            plt.figure(figsize=(15, 10))
            
            for i, metric in enumerate(metrics):
                plt.subplot(2, 2, i+1)
                
                # 按beta值和数据集类型分组
                sns.lineplot(x='beta', y=metric, hue='dataset_type', data=vae_results, marker='o')
                
                plt.title(f'Effect of Beta on {metric}')
                plt.xlabel('Beta Value')
                plt.ylabel(metric)
                plt.grid(True, linestyle='--', alpha=0.7)
            
            plt.tight_layout()
            plt.savefig(os.path.join(save_dir, 'beta_parameter_effect.png'))
            plt.close()
    
    # 创建运行时间比较图
    plt.figure(figsize=(12, 6))
    sns.barplot(x='model', y='runtime', data=df.groupby('model')['runtime'].mean().reset_index())
    plt.title('Average Runtime by Model')
    plt.xlabel('Model')
    plt.ylabel('Runtime (seconds)')
    plt.xticks(rotation=45)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'model_runtime_comparison.png'))
    plt.close()
    
    print(f"可视化结果已保存到 {save_dir}")

def main():
    """主函数"""
    # 创建结果目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(CONFIG['results_dir'], f"multivariate_test_{timestamp}")
    ensure_dir(results_dir)
    
    # 保存配置（将类对象转换为字符串）
    config_to_save = {
        'datasets': CONFIG['datasets'],
        'models': [{
            'name': model['name'],
            'class': model['class'].__name__,
            'hyperparams': model['hyperparams'].copy()
        } for model in CONFIG['models']],
        'beta_variants': CONFIG['beta_variants'],
        'results_dir': CONFIG['results_dir'],
        'save_plots': CONFIG['save_plots']
    }
    
    with open(os.path.join(results_dir, 'config.json'), 'w') as f:
        json.dump(config_to_save, f, indent=4)
    
    # 运行实验
    results = []
    
    # 计算总实验数量
    base_experiments = len(CONFIG['datasets']) * len(CONFIG['models'])
    vae_beta_experiments = sum(1 for model in CONFIG['models'] if model['name'] == 'HTA_AD_M_VAE') * len(CONFIG['datasets']) * len(CONFIG['beta_variants'])
    total_experiments = base_experiments + vae_beta_experiments
    
    print(f"即将运行 {total_experiments} 个实验...")
    
    # 运行基本模型实验
    for dataset_path in CONFIG['datasets']:
        for model_config in CONFIG['models']:
            print(f"--- 运行 {model_config['name']} 在 {os.path.basename(dataset_path)} 上的实验 ---")
            
            # 运行实验
            result = run_experiment(model_config, dataset_path, results_dir)
            results.append(result)
            
            # 打印实验结果
            if result['status'] == 'success':
                print(f"实验成功! AUC-ROC: {result['metrics'].get('AUC-ROC', 'N/A'):.4f}, 运行时间: {result['runtime']:.2f}秒")
            else:
                print(f"实验失败: {result['error_message']}")
                print(f"详细错误: {result.get('error_details', '无详细信息')}")
            
            # 保存中间结果
            with open(os.path.join(results_dir, 'intermediate_results.json'), 'w') as f:
                json.dump(results, f, indent=4)
    
    # 对VAE模型运行beta参数实验
    for dataset_path in CONFIG['datasets']:
        for model_config in CONFIG['models']:
            if model_config['name'] == 'HTA_AD_M_VAE':
                for beta in CONFIG['beta_variants']:
                    print(f"--- 运行 {model_config['name']} (beta={beta}) 在 {os.path.basename(dataset_path)} 上的实验 ---")
                    
                    # 运行实验
                    result = run_experiment(model_config, dataset_path, results_dir, beta=beta)
                    results.append(result)
                    
                    # 打印实验结果
                    if result['status'] == 'success':
                        print(f"实验成功! AUC-ROC: {result['metrics'].get('AUC-ROC', 'N/A'):.4f}, 运行时间: {result['runtime']:.2f}秒")
                    else:
                        print(f"实验失败: {result['error_message']}")
                        print(f"详细错误: {result.get('error_details', '无详细信息')}")
                    
                    # 保存中间结果
                    with open(os.path.join(results_dir, 'intermediate_results.json'), 'w') as f:
                        json.dump(results, f, indent=4)
    
    # 保存最终结果
    with open(os.path.join(results_dir, 'final_results.json'), 'w') as f:
        json.dump(results, f, indent=4)
    
    # 可视化结果
    if CONFIG['save_plots']:
        visualize_results(results, results_dir)
    
    print(f"多变量数据集测试完成，结果已保存到 {results_dir}")

if __name__ == "__main__":
    main() 