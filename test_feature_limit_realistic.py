#!/usr/bin/env python3
"""
Test feature limit with realistic training scenario
"""

import torch
import sys
import numpy as np
sys.path.append('.')
from core.models.hta_ad_paper_compliant import HTAADBasic, PostHocSAE

print('🧪 Testing feature limits with realistic training scenario...')

# Create models
model = HTAADBasic(input_dim=1, d_model=32, seq_len=100)
sae = PostHocSAE(latent_dim=32, hidden_dim=128)

# Generate realistic training data (with some patterns)
np.random.seed(42)
torch.manual_seed(42)

# Create synthetic time series with patterns
n_samples = 1000
time_series = []
for i in range(n_samples):
    # Mix of sine waves, trends, and noise
    t = np.linspace(0, 4*np.pi, 100)
    signal = (np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.randn(100) + 
              0.01*i)  # slight trend
    time_series.append(signal.reshape(-1, 1))

time_series = np.array(time_series)
print(f'Generated time series shape: {time_series.shape}')

# Convert to tensor
time_series_tensor = torch.FloatTensor(time_series)

# Train HTA-AD briefly to get realistic latent vectors
print('\n🔧 Training HTA-AD briefly...')
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
model.train()

for epoch in range(10):  # Just a few epochs
    optimizer.zero_grad()
    outputs = model(time_series_tensor)
    losses = model.compute_loss(outputs, time_series_tensor)
    loss = losses['total']
    loss.backward()
    optimizer.step()
    
    if (epoch + 1) % 5 == 0:
        print(f'  Epoch {epoch + 1}, Loss: {loss.item():.4f}')

# Get latent vectors
model.eval()
with torch.no_grad():
    outputs = model(time_series_tensor)
    latent_vectors = outputs['latent_vectors']

print(f'Latent vectors shape: {latent_vectors.shape}')

# Now test SAE feature identification with different limits
print('\n📊 Testing SAE feature identification:')

print('\n1. With 25% limit (default):')
mask1 = sae.identify_irrelevant_features(latent_vectors, max_irrelevant_ratio=0.25)

print('\n2. With 10% limit:')
mask2 = sae.identify_irrelevant_features(latent_vectors, max_irrelevant_ratio=0.10)

print('\n3. With 50% limit:')
mask3 = sae.identify_irrelevant_features(latent_vectors, max_irrelevant_ratio=0.50)

print('\n4. With no limit (100%):')
mask4 = sae.identify_irrelevant_features(latent_vectors, max_irrelevant_ratio=1.0)

print('\n✅ Realistic feature identification test completed!')
print(f'   - 25% limit: {torch.sum(mask1).item():.0f} features')
print(f'   - 10% limit: {torch.sum(mask2).item():.0f} features') 
print(f'   - 50% limit: {torch.sum(mask3).item():.0f} features')
print(f'   - No limit:  {torch.sum(mask4).item():.0f} features')
