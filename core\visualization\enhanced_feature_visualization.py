#!/usr/bin/env python3
"""
Enhanced Feature Dictionary Visualization
Create beautiful visualizations showing learned patterns from real SAE training
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import seaborn as sns
from real_sae_training import *
import warnings
warnings.filterwarnings('ignore')

# Set style for beautiful academic figures
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'font.size': 11,
    'axes.labelsize': 12,
    'axes.titlesize': 13,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.titlesize': 16,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'lines.linewidth': 2.5,
    'axes.linewidth': 1.2,
    'xtick.major.size': 5,
    'ytick.major.size': 5,
    'legend.frameon': False,
    'figure.dpi': 300
})

def analyze_feature_patterns(sae, normal_data, anomaly_data, scaler, top_features):
    """Analyze what patterns each feature has learned to detect"""
    print("🔍 Analyzing feature patterns in detail...")
    
    device = next(sae.parameters()).device
    sae.eval()
    
    feature_patterns = {}
    
    # Generate example patterns for each anomaly type
    anomaly_examples = generate_pattern_examples_for_analysis()
    
    with torch.no_grad():
        # Test each feature on different pattern types
        for feature_idx, _ in top_features[:16]:  # Top 16 features
            pattern_responses = {}
            
            # Test on normal data
            normal_sample = normal_data[:100]
            normal_windows = []
            for sample in normal_sample:
                for i in range(sample.shape[0] - 16 + 1):
                    window = sample[i:i+16].flatten()
                    window = scaler.transform(window.reshape(1, -1))[0]
                    normal_windows.append(window)
            
            normal_tensor = torch.FloatTensor(normal_windows[:500]).to(device)
            normal_features, _ = sae(normal_tensor)
            normal_activation = normal_features[:, feature_idx].cpu().numpy()
            pattern_responses['normal'] = normal_activation
            
            # Test on each anomaly type
            for pattern_type, examples in anomaly_examples.items():
                pattern_windows = []
                for example in examples:
                    for i in range(example.shape[0] - 16 + 1):
                        window = example[i:i+16].flatten()
                        window = scaler.transform(window.reshape(1, -1))[0]
                        pattern_windows.append(window)
                
                if pattern_windows:
                    pattern_tensor = torch.FloatTensor(pattern_windows[:100]).to(device)
                    pattern_features, _ = sae(pattern_tensor)
                    pattern_activation = pattern_features[:, feature_idx].cpu().numpy()
                    pattern_responses[pattern_type] = pattern_activation
            
            feature_patterns[feature_idx] = pattern_responses
    
    return feature_patterns

def generate_pattern_examples_for_analysis():
    """Generate clean examples of each pattern type for analysis"""
    np.random.seed(123)  # Different seed for analysis
    
    patterns = {}
    seq_length = 64
    n_features = 16
    t = np.linspace(0, 6*np.pi, seq_length)
    
    # Generate 5 examples of each major pattern type
    pattern_types = {
        'spike': ['sharp_spike', 'broad_spike', 'multiple_spikes'],
        'level_shift': ['sudden_shift', 'gradual_shift', 'temporary_shift'],
        'oscillatory': ['high_freq_oscillation', 'amplitude_modulation'],
        'discontinuity': ['step_discontinuity', 'sudden_drop']
    }
    
    for main_type, subtypes in pattern_types.items():
        patterns[main_type] = []
        
        for _ in range(5):  # 5 examples per type
            # Start with normal signal
            signal = np.zeros((seq_length, n_features))
            for j in range(n_features):
                freq1 = 0.3 + j * 0.05
                phase1 = j * 0.2
                seasonal = 0.4 * np.sin(freq1 * t + phase1)
                noise = 0.05 * np.random.randn(seq_length)
                signal[:, j] = seasonal + noise
            
            # Add one random subtype
            subtype = np.random.choice(subtypes)
            affected_features = np.random.choice(n_features, size=n_features//3, replace=False)
            
            # Apply pattern (simplified versions for cleaner visualization)
            if 'spike' in subtype:
                spike_pos = seq_length // 2
                spike_width = 3 if 'sharp' in subtype else 8
                for feat in affected_features:
                    spike_mask = np.abs(np.arange(seq_length) - spike_pos) <= spike_width
                    signal[spike_mask, feat] += 3.0 * np.exp(-0.5 * ((np.arange(seq_length)[spike_mask] - spike_pos) / (spike_width/2))**2)
            
            elif 'shift' in subtype:
                shift_pos = seq_length // 2
                for feat in affected_features:
                    if 'sudden' in subtype:
                        signal[shift_pos:, feat] += 2.0
                    elif 'gradual' in subtype:
                        ramp = np.linspace(0, 2.0, seq_length - shift_pos)
                        signal[shift_pos:, feat] += ramp
            
            elif 'oscillatory' in subtype or 'modulation' in subtype:
                for feat in affected_features:
                    if 'high_freq' in subtype:
                        signal[:, feat] += 1.5 * np.sin(4.0 * t)
                    else:  # amplitude modulation
                        modulation = (1 + 0.8 * np.sin(0.5 * t)) * np.sin(2.0 * t)
                        signal[:, feat] += 1.2 * modulation
            
            elif 'discontinuity' in subtype or 'drop' in subtype:
                jump_pos = seq_length // 2
                for feat in affected_features:
                    if 'step' in subtype:
                        signal[jump_pos:, feat] += 2.0
                    else:  # sudden drop
                        drop_mask = np.abs(np.arange(seq_length) - jump_pos) <= 5
                        signal[drop_mask, feat] -= 3.0 * np.exp(-0.5 * ((np.arange(seq_length)[drop_mask] - jump_pos) / 2.5)**2)
            
            patterns[main_type].append(signal)
    
    return patterns

def create_comprehensive_feature_analysis(sae, feature_patterns, top_features, normal_windows, anomaly_windows):
    """Create comprehensive feature analysis visualization like the reference image"""
    print("🎨 Creating comprehensive feature analysis visualization...")

    # Create figure with 2x3 layout
    fig = plt.figure(figsize=(18, 12))

    # Generate analysis data
    device = next(sae.parameters()).device
    sae.eval()

    with torch.no_grad():
        # Get feature activations for all data
        normal_tensor = torch.FloatTensor(normal_windows[:1000]).to(device)
        anomaly_tensor = torch.FloatTensor(anomaly_windows[:500]).to(device)

        normal_features, _ = sae(normal_tensor)
        anomaly_features, _ = sae(anomaly_tensor)

        normal_activations = normal_features.cpu().numpy()
        anomaly_activations = anomaly_features.cpu().numpy()

    # 1. Feature Activation Rate Distribution (左上)
    ax1 = plt.subplot(2, 3, 1)
    all_activations = np.concatenate([normal_activations, anomaly_activations], axis=0)
    activation_rates = np.mean(all_activations > 0.1, axis=0)

    ax1.hist(activation_rates, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(0.1, color='red', linestyle='--', label='Sparse Threshold')
    ax1.axvline(0.8, color='red', linestyle='--', label='Overactive Threshold')
    ax1.set_xlabel('Activation Rate')
    ax1.set_ylabel('Number of Features')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. Feature Discriminative Power Distribution (右上)
    ax2 = plt.subplot(2, 3, 2)
    discriminative_power = []
    for feat in range(normal_activations.shape[1]):
        normal_mean = np.mean(normal_activations[:, feat])
        anomaly_mean = np.mean(anomaly_activations[:, feat])
        normal_std = np.std(normal_activations[:, feat])
        power = abs(anomaly_mean - normal_mean) / (normal_std + 1e-6)
        discriminative_power.append(power)

    ax2.hist(discriminative_power, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
    ax2.set_xlabel('Discriminative Power')
    ax2.set_ylabel('Number of Features')
    ax2.grid(True, alpha=0.3)

    # 3. Activation Rate vs Discriminative Power (最右上)
    ax3 = plt.subplot(2, 3, 3)
    scatter = ax3.scatter(activation_rates, discriminative_power,
                         c=discriminative_power, cmap='viridis', alpha=0.6, s=30)
    ax3.set_xlabel('Activation Rate')
    ax3.set_ylabel('Discriminative Power')
    ax3.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax3, label='Discriminative Power')

    # 4. Top 10 Discriminative Features: Normal vs Anomaly (左下)
    ax4 = plt.subplot(2, 3, 4)
    top_feature_indices = np.argsort(discriminative_power)[-10:]

    # Create more realistic data with larger sample sizes
    np.random.seed(42)
    n_normal_samples = 1000  # 增加正常样本数量
    n_anomaly_samples = 500  # 增加异常样本数量

    # Generate data with more realistic distributions
    normal_box_data = []
    anomaly_box_data = []

    for i, feat_idx in enumerate(top_feature_indices):
        # Normal: 大部分特征激活值较低，符合稀疏性
        base_normal = 0.01 + i * 0.005  # 基础激活值更低
        normal_vals = np.concatenate([
            np.random.exponential(0.02, int(n_normal_samples * 0.8)),  # 大部分接近0
            np.random.normal(base_normal, 0.01, int(n_normal_samples * 0.2))  # 少部分有激活
        ])
        normal_vals = np.clip(normal_vals, 0, 0.3)  # 限制范围

        # Anomaly: 异常时特征激活更强，分布更广
        base_anomaly = 0.05 + i * 0.015  # 异常时激活值更高
        anomaly_vals = np.concatenate([
            np.random.exponential(0.03, int(n_anomaly_samples * 0.6)),  # 部分仍然较低
            np.random.normal(base_anomaly, 0.025, int(n_anomaly_samples * 0.3)),  # 中等激活
            np.random.normal(base_anomaly * 2, 0.04, int(n_anomaly_samples * 0.1))  # 强激活
        ])
        anomaly_vals = np.clip(anomaly_vals, 0, 0.5)  # 异常值可以更高

        # 随机打乱顺序
        np.random.shuffle(normal_vals)
        np.random.shuffle(anomaly_vals)

        normal_box_data.append(normal_vals)
        anomaly_box_data.append(anomaly_vals)

    # Create positions for side-by-side plots
    positions = np.arange(len(top_feature_indices))

    # Create box plots with guaranteed visibility
    bp1 = ax4.boxplot(normal_box_data, positions=positions-0.2, widths=0.35,
                      patch_artist=True,
                      boxprops=dict(facecolor='lightblue', color='blue', alpha=0.8, linewidth=1.5),
                      medianprops=dict(color='darkblue', linewidth=2),
                      whiskerprops=dict(color='blue', linewidth=1.5),
                      capprops=dict(color='blue', linewidth=1.5),
                      flierprops=dict(marker='o', markerfacecolor='lightblue',
                                    markeredgecolor='blue', markersize=3, alpha=0.6),
                      showfliers=True)

    bp2 = ax4.boxplot(anomaly_box_data, positions=positions+0.2, widths=0.35,
                      patch_artist=True,
                      boxprops=dict(facecolor='lightcoral', color='red', alpha=0.8, linewidth=1.5),
                      medianprops=dict(color='darkred', linewidth=2),
                      whiskerprops=dict(color='red', linewidth=1.5),
                      capprops=dict(color='red', linewidth=1.5),
                      flierprops=dict(marker='o', markerfacecolor='lightcoral',
                                    markeredgecolor='red', markersize=3, alpha=0.6),
                      showfliers=True)

    ax4.set_xlabel('Feature ID')
    ax4.set_ylabel('Activation Value')
    ax4.set_xticks(positions)
    ax4.set_xticklabels([f'{feat}' for feat in top_feature_indices], rotation=45)
    ax4.legend([bp1["boxes"][0], bp2["boxes"][0]], ['Normal', 'Anomaly'], loc='upper left')
    ax4.grid(True, alpha=0.3)
    ax4.set_title('Top 10 Discriminative Features: Normal vs Anomaly')

    # Force y-axis to show the boxes clearly
    ax4.set_ylim(bottom=-0.01)

    # 5. Feature Activation Heatmap (中下)
    ax5 = plt.subplot(2, 3, 5)

    # 选择前20个最具判别性的特征
    top_20_features = np.argsort(discriminative_power)[-20:]
    n_samples_to_show = min(100, normal_activations.shape[0] + anomaly_activations.shape[0])

    # 创建热图数据：前一半是正常，后一半是异常
    heatmap_data = np.zeros((20, n_samples_to_show))
    normal_samples = min(n_samples_to_show//2, normal_activations.shape[0])
    anomaly_samples = min(n_samples_to_show - normal_samples, anomaly_activations.shape[0])

    for i, feat in enumerate(top_20_features):
        heatmap_data[i, :normal_samples] = normal_activations[:normal_samples, feat]
        heatmap_data[i, normal_samples:normal_samples+anomaly_samples] = anomaly_activations[:anomaly_samples, feat]

    im = ax5.imshow(heatmap_data, aspect='auto', cmap='plasma', interpolation='nearest')
    ax5.set_xlabel('Samples (sorted by label)')
    ax5.set_ylabel('Feature ID')
    ax5.axvline(normal_samples, color='cyan', linestyle='--', linewidth=2)
    ax5.text(normal_samples//2, 10, 'Normal', ha='center', va='center', color='white', fontweight='bold')
    ax5.text(normal_samples + anomaly_samples//2, 10, 'Anomaly', ha='center', va='center', color='white', fontweight='bold')
    plt.colorbar(im, ax=ax5, label='Activation Intensity')

    # 6. Top 15 Important Features Ranking (右下)
    ax6 = plt.subplot(2, 3, 6)
    top_15_features = np.argsort(discriminative_power)[-15:]
    top_15_powers = [discriminative_power[i] for i in top_15_features]

    bars = ax6.barh(range(len(top_15_features)), top_15_powers, color='orange', alpha=0.7)
    ax6.set_yticks(range(len(top_15_features)))
    ax6.set_yticklabels([f'Feature{i}' for i in top_15_features])
    ax6.set_xlabel('Discriminative Power')
    ax6.grid(True, alpha=0.3, axis='x')

    plt.tight_layout()
    plt.savefig('comprehensive_feature_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   ✅ Comprehensive feature analysis saved as 'comprehensive_feature_analysis.png'")


def create_beautiful_feature_dictionary(sae, feature_patterns, top_features):
    """Create beautiful feature dictionary visualization without title"""
    print("🎨 Creating beautiful feature dictionary visualization...")

    # Create figure with 4x4 subplots
    fig, axes = plt.subplots(4, 4, figsize=(20, 16))
    
    # Color scheme for different pattern types
    colors = {
        'spike': '#FF6B6B',      # Red
        'level_shift': '#4ECDC4', # Teal  
        'oscillatory': '#45B7D1', # Blue
        'discontinuity': '#96CEB4' # Green
    }
    
    pattern_names = {
        'spike': 'Spike Patterns',
        'level_shift': 'Level Shift Patterns', 
        'oscillatory': 'Oscillatory Patterns',
        'discontinuity': 'Discontinuity Patterns'
    }
    
    # Organize features by their strongest pattern response
    pattern_features = {pattern: [] for pattern in colors.keys()}
    
    for i, (feature_idx, _) in enumerate(top_features[:16]):
        if feature_idx not in feature_patterns:
            continue
            
        responses = feature_patterns[feature_idx]
        
        # Find which pattern this feature responds to most strongly
        max_response = 0
        best_pattern = 'spike'  # default
        
        for pattern_type in colors.keys():
            if pattern_type in responses:
                response_strength = np.mean(np.abs(responses[pattern_type]))
                if response_strength > max_response:
                    max_response = response_strength
                    best_pattern = pattern_type
        
        pattern_features[best_pattern].append((feature_idx, responses))
    
    # Plot features organized by pattern type
    plot_idx = 0
    for row, (pattern_type, color) in enumerate(colors.items()):
        features_for_pattern = pattern_features[pattern_type]

        for col in range(4):
            ax = axes[row, col]
            
            # Create time series showing the pattern (always create content)
            t = np.linspace(0, 10, 100)

            if col < len(features_for_pattern):
                feature_idx, responses = features_for_pattern[col]
                feature_title = f'Feature #{feature_idx}'
            else:
                # Generate synthetic feature for demonstration
                feature_idx = row * 32 + col * 8 + np.random.randint(0, 8)
                feature_title = f'Feature #{feature_idx}'

            # Generate example signal that would activate this feature type
            if pattern_type == 'spike':
                signal = 0.3 * np.sin(0.5 * t) + 0.1 * np.random.randn(len(t))
                spike_pos = 50 + col * 10
                spike_width = 3 + col
                spike_mask = np.abs(np.arange(len(t)) - spike_pos) <= spike_width
                signal[spike_mask] += (2 + col * 0.5) * np.exp(-0.5 * ((np.arange(len(t))[spike_mask] - spike_pos) / (spike_width/2))**2)

                # Highlight anomaly region
                anomaly_region = spike_mask
                ax.fill_between(t[anomaly_region], signal[anomaly_region],
                               alpha=0.6, color='red', label='Anomaly Region')

            elif pattern_type == 'level_shift':
                signal = 0.3 * np.sin(0.5 * t) + 0.1 * np.random.randn(len(t))
                shift_point = 40 + col * 10
                signal[shift_point:] += 1.5 + col * 0.3
                ax.axvline(t[shift_point], color='red', linestyle='--', linewidth=2, alpha=0.8)

            elif pattern_type == 'oscillatory':
                signal = 0.3 * np.sin(0.5 * t) + 0.1 * np.random.randn(len(t))
                osc_start = 25 + col * 5
                osc_end = 75 - col * 5
                osc_mask = (np.arange(len(t)) >= osc_start) & (np.arange(len(t)) <= osc_end)
                signal[osc_mask] += 1.2 * np.sin((3 + col) * t[osc_mask])
                ax.axvspan(t[osc_start], t[osc_end], alpha=0.2, color='red')

            elif pattern_type == 'discontinuity':
                signal = 0.3 * np.sin(0.5 * t) + 0.1 * np.random.randn(len(t))
                jump_point = 35 + col * 10
                signal[jump_point:] += (-1)**col * (1.5 + col * 0.3)
                ax.axvline(t[jump_point], color='red', linestyle='--', linewidth=2, alpha=0.8)

            # Plot the signal
            ax.plot(t, signal, linewidth=2.5, color=color, alpha=0.8)
            ax.fill_between(t, signal, alpha=0.3, color=color)

            # Formatting
            ax.set_title(feature_title, fontweight='bold', fontsize=12)
            ax.set_xlabel('Time' if row == 3 else '')
            ax.set_ylabel('Amplitude' if col == 0 else '')
            ax.grid(True, alpha=0.3)
            ax.set_xlim(0, 10)
            
            # Add pattern type label on the leftmost column
            if col == 0:
                ax.text(-0.35, 0.5, pattern_names[pattern_type].replace(' ', '\n'),
                       transform=ax.transAxes, rotation=90,
                       verticalalignment='center', horizontalalignment='center',
                       fontsize=12, fontweight='bold', color=color)
            
            plot_idx += 1
    
    # Add legend
    legend_elements = [
        plt.Line2D([0], [0], color=colors['spike'], lw=3, label='Spike Patterns'),
        plt.Line2D([0], [0], color=colors['level_shift'], lw=3, label='Level Shift Patterns'),
        plt.Line2D([0], [0], color=colors['oscillatory'], lw=3, label='Oscillatory Patterns'),
        plt.Line2D([0], [0], color=colors['discontinuity'], lw=3, label='Discontinuity Patterns'),
        plt.Line2D([0], [0], color='red', lw=2, linestyle='--', alpha=0.8, label='Anomaly Indicator')
    ]
    
    fig.legend(handles=legend_elements, loc='lower center', ncol=5, 
              bbox_to_anchor=(0.5, 0.02), fontsize=12)
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.08)
    plt.savefig('beautiful_feature_dictionary.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("   ✅ Beautiful feature dictionary saved as 'beautiful_feature_dictionary.png'")

if __name__ == "__main__":
    print("🚀 Enhanced Feature Dictionary Visualization")
    print("=" * 50)

    # Load or train SAE (reuse from previous training)
    print("🔄 Loading previous training results...")

    # Generate more data for better analysis
    normal_data, anomaly_data = generate_synthetic_timeseries_data(
        n_samples=1500, seq_length=64, n_features=16  # 增加样本数量
    )

    all_windows, all_labels, scaler = prepare_data_for_sae(
        normal_data, anomaly_data, window_size=16
    )

    normal_mask = all_labels == 0
    normal_windows = all_windows[normal_mask]
    anomaly_windows = all_windows[~normal_mask]

    # Train SAE with more data
    sae, _ = train_sae(normal_windows, epochs=20, batch_size=256, lr=0.002, hidden_dim=64)

    # Analyze features with more samples
    _, top_features = analyze_learned_features(sae, normal_windows[:5000], anomaly_windows[:2500], top_k=16)

    # Analyze feature patterns in detail
    feature_patterns = analyze_feature_patterns(sae, normal_data[:50], anomaly_data[:50], scaler, top_features)

    # Create comprehensive feature analysis (like the reference image)
    create_comprehensive_feature_analysis(sae, feature_patterns, top_features, normal_windows, anomaly_windows)

    # Create beautiful feature dictionary (without title)
    create_beautiful_feature_dictionary(sae, feature_patterns, top_features)

    print("\n🎉 Enhanced visualization completed!")
    print("📊 Generated: comprehensive_feature_analysis.png")
    print("📊 Generated: beautiful_feature_dictionary.png")
