#!/usr/bin/env python3
"""
验证TCN感受野计算的不同方法
"""

def calculate_rf_iterative(kernel_size, num_layers):
    """逐层累加方法（代码中的方法）"""
    dilations = [2**i for i in range(num_layers)]
    rf = kernel_size  # 第一层
    
    print(f"逐层累加方法 (k={kernel_size}):")
    print(f"Layer 1: RF = {rf} (dilation=1)")
    
    for i in range(1, num_layers):
        dilation = dilations[i]
        rf_increment = (kernel_size - 1) * dilation
        rf += rf_increment
        print(f"Layer {i+1}: RF = {rf} (dilation={dilation}, +{rf_increment})")
    
    return rf

def calculate_rf_formula(kernel_size, num_layers):
    """公式方法（论文中的方法）"""
    rf = 1 + (kernel_size - 1) * (2**num_layers - 1)
    print(f"\n公式方法 (k={kernel_size}):")
    print(f"RF = 1 + (k-1)(2^L - 1)")
    print(f"RF = 1 + ({kernel_size}-1)(2^{num_layers} - 1)")
    print(f"RF = 1 + {kernel_size-1} × {2**num_layers - 1} = {rf}")
    return rf

def verify_standard_conv(kernel_size, num_layers):
    """验证标准卷积的感受野"""
    rf = 1 + (kernel_size - 1) * num_layers
    print(f"\n标准卷积 (k={kernel_size}):")
    print(f"RF = 1 + (k-1) × L = 1 + {kernel_size-1} × {num_layers} = {rf}")
    return rf

if __name__ == "__main__":
    print("=" * 60)
    print("TCN感受野计算验证")
    print("=" * 60)
    
    # 测试不同的核大小
    test_cases = [
        (3, 5),  # 论文中的参数
        (7, 5),  # 代码中的参数
    ]
    
    for kernel_size, num_layers in test_cases:
        print(f"\n🔍 测试: kernel_size={kernel_size}, layers={num_layers}")
        print("-" * 50)
        
        rf_iter = calculate_rf_iterative(kernel_size, num_layers)
        rf_formula = calculate_rf_formula(kernel_size, num_layers)
        rf_std = verify_standard_conv(kernel_size, num_layers)
        
        print(f"\n结果对比:")
        print(f"  逐层累加: {rf_iter}")
        print(f"  公式计算: {rf_formula}")
        print(f"  标准卷积: {rf_std}")
        print(f"  TCN优势: {rf_iter/rf_std:.1f}x")
        
        if rf_iter == rf_formula:
            print("  ✅ 两种方法结果一致")
        else:
            print("  ❌ 两种方法结果不一致")
    
    print("\n" + "=" * 60)
    print("结论:")
    print("1. 论文中k=3, L=5 → RF=63是正确的")
    print("2. 代码中k=7, L=5 → RF=187也是正确的")
    print("3. 差异来源于不同的核大小设置")
    print("=" * 60)
