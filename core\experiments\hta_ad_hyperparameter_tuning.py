#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTA-AD Hyperparameter Tuning Script
Demonstrates different hyperparameter optimization strategies
"""

import numpy as np
import pandas as pd
import sys
import itertools
from sklearn.model_selection import ParameterGrid
from sklearn.metrics import roc_auc_score, average_precision_score
import torch

# Add TSB-AD path
sys.path.append('TSB-AD')
from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE

def create_synthetic_data(n_samples=1000, n_features=1, anomaly_ratio=0.05):
    """Create synthetic time series data for hyperparameter tuning"""
    np.random.seed(42)
    
    # Generate normal data with seasonal patterns
    t = np.linspace(0, 20, n_samples)
    data = np.zeros((n_samples, n_features))
    
    for i in range(n_features):
        # Base signal with different frequencies
        base_signal = (np.sin(2 * np.pi * (i + 1) * 0.1 * t) + 
                      0.5 * np.sin(2 * np.pi * (i + 1) * 0.05 * t))
        # Add noise
        noise = 0.1 * np.random.randn(n_samples)
        data[:, i] = base_signal + noise
    
    # Add anomalies
    labels = np.zeros(n_samples)
    n_anomalies = int(n_samples * anomaly_ratio)
    anomaly_indices = np.random.choice(n_samples, n_anomalies, replace=False)
    
    for idx in anomaly_indices:
        # Add different types of anomalies
        anomaly_type = np.random.choice(['spike', 'level_shift'])
        
        if anomaly_type == 'spike':
            data[idx] += np.random.normal(0, 2, n_features)
        elif anomaly_type == 'level_shift':
            end_idx = min(idx + 20, n_samples)
            data[idx:end_idx] += np.random.normal(1.5, 0.5, (end_idx - idx, n_features))
        
        labels[idx:min(idx + 20, n_samples)] = 1
    
    return data, labels

def evaluate_model(model, train_data, test_data, test_labels):
    """Evaluate model performance"""
    try:
        model.fit(train_data)
        scores = model.decision_function(test_data)
        
        if len(np.unique(test_labels)) > 1:
            auc_roc = roc_auc_score(test_labels, scores)
            auc_pr = average_precision_score(test_labels, scores)
        else:
            auc_roc = auc_pr = 0.0
        
        return {
            'AUC-ROC': auc_roc,
            'AUC-PR': auc_pr,
            'success': True
        }
    except Exception as e:
        print(f"   ❌ Model evaluation failed: {e}")
        return {
            'AUC-ROC': 0.0,
            'AUC-PR': 0.0,
            'success': False
        }

def grid_search_tuning():
    """Grid search hyperparameter tuning"""
    print("🔍 Grid Search Hyperparameter Tuning")
    print("=" * 50)
    
    # Create data
    data, labels = create_synthetic_data(n_samples=800, n_features=1)
    split_idx = int(0.7 * len(data))
    train_data = data[:split_idx]
    test_data = data[split_idx:]
    test_labels = labels[split_idx:]
    
    print(f"Data: {data.shape}, Train: {len(train_data)}, Test: {len(test_data)}")
    
    # Define parameter grid
    param_grid = {
        'window_size': [64, 128],
        'epochs': [10, 20],
        'lr': [1e-3, 5e-3],
        'batch_size': [32, 64],
        'latent_dim': [16, 32],
        'tcn_channels': [[16, 16], [32, 32, 32]],
        'cnn_channels': [8, 16]
    }
    
    print(f"Parameter combinations: {len(list(ParameterGrid(param_grid)))}")
    
    best_score = 0
    best_params = None
    results = []
    
    for i, params in enumerate(ParameterGrid(param_grid)):
        print(f"\n🧪 Testing combination {i+1}:")
        print(f"   {params}")
        
        # Create HP dict
        HP = {
            'gpu': 0 if torch.cuda.is_available() else -1,
            'downsample_stride': 2,
            **params
        }
        
        # Test model
        model = HTA_AD(HP=HP, normalize=True)
        result = evaluate_model(model, train_data, test_data, test_labels)
        
        if result['success']:
            auc_roc = result['AUC-ROC']
            print(f"   ✅ AUC-ROC: {auc_roc:.4f}, AUC-PR: {result['AUC-PR']:.4f}")
            
            if auc_roc > best_score:
                best_score = auc_roc
                best_params = params.copy()
            
            results.append({
                'params': params,
                'auc_roc': auc_roc,
                'auc_pr': result['AUC-PR']
            })
    
    print(f"\n🏆 Best Parameters (AUC-ROC: {best_score:.4f}):")
    for key, value in best_params.items():
        print(f"   {key}: {value}")
    
    return best_params, results

def adaptive_tuning():
    """Adaptive hyperparameter tuning based on data characteristics"""
    print("\n🎯 Adaptive Hyperparameter Tuning")
    print("=" * 50)
    
    # Test different data scenarios
    scenarios = [
        {'name': 'Small Univariate', 'n_samples': 500, 'n_features': 1},
        {'name': 'Large Univariate', 'n_samples': 2000, 'n_features': 1},
        {'name': 'Small Multivariate', 'n_samples': 500, 'n_features': 3},
        {'name': 'Large Multivariate', 'n_samples': 2000, 'n_features': 3}
    ]
    
    adaptive_results = {}
    
    for scenario in scenarios:
        print(f"\n📊 Scenario: {scenario['name']}")
        
        # Create data
        data, labels = create_synthetic_data(
            n_samples=scenario['n_samples'],
            n_features=scenario['n_features']
        )
        
        split_idx = int(0.7 * len(data))
        train_data = data[:split_idx]
        test_data = data[split_idx:]
        test_labels = labels[split_idx:]
        
        # Adaptive parameter selection
        n_samples, n_features = data.shape
        
        # Base configuration
        HP = {
            'window_size': min(128, n_samples // 10),
            'latent_dim': max(16, min(64, n_features * 8)),
            'batch_size': min(64, n_samples // 20),
            'epochs': 20 if n_samples > 1000 else 30,
            'lr': 1e-3,
            'downsample_stride': 2,
            'gpu': 0 if torch.cuda.is_available() else -1
        }
        
        # Adjust based on data type
        if n_features > 1:  # Multivariate
            HP['cnn_channels'] = min(32, n_features * 4)
            HP['tcn_channels'] = [32, 32, 32]
        else:  # Univariate
            HP['cnn_channels'] = 16
            HP['tcn_channels'] = [16, 32, 32]
        
        print(f"   Adaptive HP: {HP}")
        
        # Test model
        model = HTA_AD(HP=HP, normalize=True)
        result = evaluate_model(model, train_data, test_data, test_labels)
        
        if result['success']:
            print(f"   ✅ AUC-ROC: {result['AUC-ROC']:.4f}, AUC-PR: {result['AUC-PR']:.4f}")
            adaptive_results[scenario['name']] = {
                'HP': HP,
                'performance': result
            }
        else:
            print(f"   ❌ Failed")
    
    return adaptive_results

def sae_hyperparameter_tuning():
    """SAE-specific hyperparameter tuning"""
    print("\n🧠 SAE Hyperparameter Tuning")
    print("=" * 50)
    
    # Create multivariate data for SAE testing
    data, labels = create_synthetic_data(n_samples=1000, n_features=2)
    split_idx = int(0.7 * len(data))
    train_data = data[:split_idx]
    test_data = data[split_idx:]
    test_labels = labels[split_idx:]
    
    # Base HTA-AD configuration
    base_HP = {
        'window_size': 64,
        'epochs': 15,
        'lr': 1e-3,
        'batch_size': 32,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2,
        'gpu': 0 if torch.cuda.is_available() else -1
    }
    
    # SAE parameter grid
    sae_params = {
        'hidden_dim': [64, 128, 256],
        'sparsity_weight': [0.001, 0.01, 0.1],
        'purification_strength': [0.3, 0.5, 0.7]
    }
    
    print(f"SAE combinations: {len(list(ParameterGrid(sae_params)))}")
    
    best_sae_score = 0
    best_sae_config = None
    
    for i, sae_config in enumerate(ParameterGrid(sae_params)):
        print(f"\n🧪 SAE Config {i+1}: {sae_config}")
        
        # Test HTA-AD with SAE
        model = HTA_AD_SAE(HP=base_HP, normalize=True, sae_config=sae_config)
        result = evaluate_model(model, train_data, test_data, test_labels)
        
        if result['success']:
            auc_roc = result['AUC-ROC']
            print(f"   ✅ AUC-ROC: {auc_roc:.4f}, AUC-PR: {result['AUC-PR']:.4f}")
            
            if auc_roc > best_sae_score:
                best_sae_score = auc_roc
                best_sae_config = sae_config.copy()
    
    print(f"\n🏆 Best SAE Config (AUC-ROC: {best_sae_score:.4f}):")
    for key, value in best_sae_config.items():
        print(f"   {key}: {value}")
    
    return best_sae_config

def performance_vs_efficiency_analysis():
    """Analyze performance vs computational efficiency trade-offs"""
    print("\n⚡ Performance vs Efficiency Analysis")
    print("=" * 50)
    
    # Create test data
    data, labels = create_synthetic_data(n_samples=1000, n_features=1)
    split_idx = int(0.7 * len(data))
    train_data = data[:split_idx]
    test_data = data[split_idx:]
    test_labels = labels[split_idx:]
    
    # Different efficiency configurations
    configs = {
        'Fast': {
            'window_size': 32,
            'epochs': 10,
            'batch_size': 64,
            'latent_dim': 16,
            'tcn_channels': [16, 16],
            'cnn_channels': 8
        },
        'Balanced': {
            'window_size': 64,
            'epochs': 20,
            'batch_size': 32,
            'latent_dim': 32,
            'tcn_channels': [32, 32, 32],
            'cnn_channels': 16
        },
        'High-Performance': {
            'window_size': 128,
            'epochs': 50,
            'batch_size': 64,
            'latent_dim': 64,
            'tcn_channels': [64, 64, 64, 64],
            'cnn_channels': 32
        }
    }
    
    results = {}
    
    for config_name, config in configs.items():
        print(f"\n🔧 Testing {config_name} Configuration:")
        
        HP = {
            'lr': 1e-3,
            'downsample_stride': 2,
            'gpu': 0 if torch.cuda.is_available() else -1,
            **config
        }
        
        import time
        start_time = time.time()
        
        model = HTA_AD(HP=HP, normalize=True)
        result = evaluate_model(model, train_data, test_data, test_labels)
        
        training_time = time.time() - start_time
        
        if result['success']:
            print(f"   ✅ AUC-ROC: {result['AUC-ROC']:.4f}")
            print(f"   ⏱️ Training Time: {training_time:.2f}s")
            
            results[config_name] = {
                'performance': result['AUC-ROC'],
                'time': training_time,
                'config': config
            }
    
    # Print summary
    print(f"\n📊 Performance vs Efficiency Summary:")
    for name, result in results.items():
        efficiency = result['performance'] / result['time'] if result['time'] > 0 else 0
        print(f"   {name}: Performance={result['performance']:.4f}, "
              f"Time={result['time']:.2f}s, Efficiency={efficiency:.4f}")
    
    return results

def main():
    """Main function to run all hyperparameter tuning experiments"""
    print("🚀 HTA-AD Hyperparameter Tuning Suite")
    print("=" * 80)
    
    # Check PyTorch availability
    print(f"🔧 PyTorch: {torch.__version__}")
    print(f"🔧 CUDA: {torch.cuda.is_available()}")
    
    # Run different tuning strategies
    try:
        # 1. Grid search tuning
        best_params, grid_results = grid_search_tuning()
        
        # 2. Adaptive tuning
        adaptive_results = adaptive_tuning()
        
        # 3. SAE hyperparameter tuning
        best_sae_config = sae_hyperparameter_tuning()
        
        # 4. Performance vs efficiency analysis
        efficiency_results = performance_vs_efficiency_analysis()
        
        # Summary
        print("\n" + "=" * 80)
        print("📋 Hyperparameter Tuning Summary")
        print("=" * 80)
        
        print("\n🏆 Best Grid Search Parameters:")
        for key, value in best_params.items():
            print(f"   {key}: {value}")
        
        print("\n🧠 Best SAE Configuration:")
        for key, value in best_sae_config.items():
            print(f"   {key}: {value}")
        
        print("\n💡 Recommendations:")
        print("   1. Use adaptive parameter selection for different data types")
        print("   2. Balance performance and efficiency based on your requirements")
        print("   3. Consider SAE enhancement for interpretability needs")
        print("   4. Fine-tune sparsity_weight and purification_strength for SAE")
        
    except Exception as e:
        print(f"❌ Tuning failed: {e}")

if __name__ == "__main__":
    main()
