#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SAE Pretraining Script for HTA-AD
Supports both univariate and multivariate time series data
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import os
import glob
import sys
from sklearn.preprocessing import MinMaxScaler
from torch.utils.data import DataLoader, TensorDataset
import argparse
from tqdm import tqdm

# Add TSB-AD path
sys.path.append('TSB-AD')
from TSB_AD.models.HTA_AD import HTA_Model
from TSB_AD.models.HTA_AD_SAE import SparseAutoencoder

def create_sliding_windows(data, window_size, stride=1):
    """Create sliding windows from time series data"""
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    windows = []
    for i in range(0, len(data) - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def load_datasets(data_dir, max_datasets=None, data_type='both'):
    """Load datasets for SAE pretraining
    
    Args:
        data_dir: Directory containing TSB-AD datasets
        max_datasets: Maximum number of datasets to load (None for all)
        data_type: 'univariate', 'multivariate', or 'both'
    """
    print(f"🔍 Loading datasets from {data_dir}")
    
    # Find dataset files
    univariate_pattern = os.path.join(data_dir, "TSB-AD-U", "*.csv")
    multivariate_pattern = os.path.join(data_dir, "TSB-AD-M", "*.csv")
    
    dataset_files = []
    
    if data_type in ['univariate', 'both']:
        univariate_files = glob.glob(univariate_pattern)
        dataset_files.extend([(f, 'univariate') for f in univariate_files])
        print(f"   Found {len(univariate_files)} univariate datasets")
    
    if data_type in ['multivariate', 'both']:
        multivariate_files = glob.glob(multivariate_pattern)
        dataset_files.extend([(f, 'multivariate') for f in multivariate_files])
        print(f"   Found {len(multivariate_files)} multivariate datasets")
    
    if max_datasets:
        dataset_files = dataset_files[:max_datasets]
        print(f"   Limited to {len(dataset_files)} datasets")
    
    return dataset_files

def extract_latent_vectors(dataset_files, window_size=128, batch_size=64):
    """Extract latent vectors from datasets using HTA-AD encoder"""
    print(f"🔧 Extracting latent vectors...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    all_latent_vectors = []
    successful_datasets = 0
    
    # HTA-AD configuration
    hta_config = {
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'tcn_kernel_size': 3,
        'cnn_channels': 16,
        'downsample_stride': 2
    }
    
    for i, (dataset_path, dataset_type) in enumerate(tqdm(dataset_files, desc="Processing datasets")):
        try:
            # Load dataset
            df = pd.read_csv(dataset_path)
            
            # Extract data (assume last column is label)
            if 'label' in df.columns:
                data = df.drop('label', axis=1).values
            else:
                data = df.iloc[:, :-1].values
            
            # Handle univariate case
            if dataset_type == 'univariate' and data.shape[1] > 1:
                data = data[:, 0:1]
            
            # Skip if too short
            if len(data) < window_size * 2:
                continue
            
            # Normalize data
            scaler = MinMaxScaler()
            data_normalized = scaler.fit_transform(data)
            
            # Create windows
            windows = create_sliding_windows(data_normalized, window_size, stride=window_size//4)
            
            if len(windows) < 10:  # Skip datasets with too few windows
                continue
            
            # Initialize HTA-AD model for this dataset
            input_dim = data_normalized.shape[1]
            hta_model = HTA_Model(
                input_dim=input_dim,
                window_size=window_size,
                **hta_config
            ).to(device)
            
            # Quick training on this dataset (just to get meaningful latent vectors)
            hta_model.train()
            optimizer = optim.Adam(hta_model.parameters(), lr=1e-3)
            criterion = nn.MSELoss()
            
            # Mini training (5 epochs)
            windows_tensor = torch.FloatTensor(windows).to(device)
            dataset = TensorDataset(windows_tensor)
            loader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
            
            for epoch in range(5):
                for batch_windows, in loader:
                    optimizer.zero_grad()
                    reconstructed = hta_model(batch_windows)
                    loss = criterion(reconstructed, batch_windows)
                    loss.backward()
                    optimizer.step()
            
            # Extract latent vectors
            hta_model.eval()
            with torch.no_grad():
                for batch_windows, in DataLoader(dataset, batch_size=batch_size, shuffle=False):
                    # Encode to latent space
                    x_permuted = batch_windows.permute(0, 2, 1)
                    encoded_cnn = hta_model.encoder_cnn(x_permuted)
                    encoded_tcn = hta_model.encoder_tcn(encoded_cnn)
                    encoded_flat = encoded_tcn.flatten(start_dim=1)
                    latent_vec = hta_model.fc_encode(encoded_flat)
                    
                    all_latent_vectors.append(latent_vec.cpu().numpy())
            
            successful_datasets += 1
            
        except Exception as e:
            print(f"   ⚠️ Failed to process {os.path.basename(dataset_path)}: {e}")
            continue
    
    if len(all_latent_vectors) == 0:
        print("❌ No latent vectors extracted!")
        return None
    
    # Combine all latent vectors
    latent_vectors = np.vstack(all_latent_vectors)
    print(f"✅ Extracted {len(latent_vectors)} latent vectors from {successful_datasets} datasets")
    print(f"   Shape: {latent_vectors.shape}")
    
    return latent_vectors

def train_sae(latent_vectors, hidden_dim=128, sparsity_weight=0.01, epochs=50, lr=1e-3, batch_size=256):
    """Train Sparse Autoencoder on latent vectors"""
    print(f"🚀 Training Sparse Autoencoder...")
    print(f"   Input dim: {latent_vectors.shape[1]}")
    print(f"   Hidden dim: {hidden_dim}")
    print(f"   Sparsity weight: {sparsity_weight}")
    print(f"   Epochs: {epochs}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Normalize latent vectors
    scaler = MinMaxScaler(feature_range=(-1, 1))
    latent_normalized = scaler.fit_transform(latent_vectors)
    
    print(f"   Data range: [{latent_normalized.min():.4f}, {latent_normalized.max():.4f}]")
    
    # Create SAE model
    input_dim = latent_vectors.shape[1]
    sae_model = SparseAutoencoder(
        input_dim=input_dim,
        hidden_dim=hidden_dim,
        sparsity_weight=sparsity_weight
    ).to(device)
    
    # Training setup
    optimizer = optim.Adam(sae_model.parameters(), lr=lr)
    
    # Prepare data
    tensor_data = torch.FloatTensor(latent_normalized).to(device)
    dataset = TensorDataset(tensor_data)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    # Training loop
    sae_model.train()
    losses = []
    
    for epoch in tqdm(range(epochs), desc="Training SAE"):
        epoch_loss = 0
        num_batches = 0
        
        for batch_data, in dataloader:
            optimizer.zero_grad()
            
            reconstruction, features = sae_model(batch_data)
            loss = sae_model.loss_function(batch_data, reconstruction, features)
            
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            num_batches += 1
        
        avg_loss = epoch_loss / num_batches
        losses.append(avg_loss)
        
        if (epoch + 1) % 10 == 0:
            print(f"   Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
    
    print("✅ SAE training completed")
    
    return sae_model, scaler, losses

def analyze_features(sae_model, scaler, latent_vectors, threshold=0.1):
    """Analyze learned features and identify irrelevant ones"""
    print("🔍 Analyzing learned features...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    sae_model.eval()
    
    # Normalize data
    latent_normalized = scaler.transform(latent_vectors)
    tensor_data = torch.FloatTensor(latent_normalized).to(device)
    
    # Get feature activations
    with torch.no_grad():
        _, activations = sae_model(tensor_data)
        activations = activations.cpu().numpy()
    
    # Analyze each feature
    feature_stats = []
    for i in range(activations.shape[1]):
        feature_activations = activations[:, i]
        
        stats = {
            'feature_id': i,
            'mean_activation': np.mean(feature_activations),
            'std_activation': np.std(feature_activations),
            'activation_rate': np.mean(feature_activations > 0.1),
            'max_activation': np.max(feature_activations)
        }
        feature_stats.append(stats)
    
    # Identify irrelevant features (low activation rate and low variance)
    irrelevant_indices = []
    for i, stats in enumerate(feature_stats):
        if (stats['activation_rate'] < threshold and 
            stats['std_activation'] < threshold):
            irrelevant_indices.append(i)
    
    print(f"   Total features: {len(feature_stats)}")
    print(f"   Irrelevant features: {len(irrelevant_indices)} ({len(irrelevant_indices)/len(feature_stats)*100:.1f}%)")
    
    return feature_stats, irrelevant_indices

def save_pretrained_sae(sae_model, scaler, irrelevant_indices, save_path, config):
    """Save pretrained SAE model"""
    print(f"💾 Saving pretrained SAE to {save_path}")
    
    checkpoint = {
        'model_state_dict': sae_model.state_dict(),
        'input_dim': sae_model.input_dim,
        'hidden_dim': sae_model.hidden_dim,
        'sparsity_weight': sae_model.sparsity_weight,
        'scaler': scaler,
        'irrelevant_indices': irrelevant_indices,
        'config': config
    }
    
    torch.save(checkpoint, save_path)
    print("✅ Model saved successfully")

def main():
    parser = argparse.ArgumentParser(description='SAE Pretraining for HTA-AD')
    parser.add_argument('--data_dir', type=str, default='TSB-AD/Datasets', 
                       help='Directory containing TSB-AD datasets')
    parser.add_argument('--data_type', type=str, default='both', 
                       choices=['univariate', 'multivariate', 'both'],
                       help='Type of datasets to use')
    parser.add_argument('--max_datasets', type=int, default=None,
                       help='Maximum number of datasets to use')
    parser.add_argument('--window_size', type=int, default=128,
                       help='Window size for time series')
    parser.add_argument('--hidden_dim', type=int, default=128,
                       help='SAE hidden dimension')
    parser.add_argument('--sparsity_weight', type=float, default=0.01,
                       help='Sparsity regularization weight')
    parser.add_argument('--epochs', type=int, default=50,
                       help='Training epochs')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='Learning rate')
    parser.add_argument('--batch_size', type=int, default=256,
                       help='Batch size')
    parser.add_argument('--save_path', type=str, default='pretrained_sae.pth',
                       help='Path to save pretrained model')
    
    args = parser.parse_args()
    
    print("🚀 SAE Pretraining for HTA-AD")
    print("=" * 60)
    print(f"Data directory: {args.data_dir}")
    print(f"Data type: {args.data_type}")
    print(f"Max datasets: {args.max_datasets}")
    print(f"Window size: {args.window_size}")
    print(f"Hidden dim: {args.hidden_dim}")
    print(f"Sparsity weight: {args.sparsity_weight}")
    print("=" * 60)
    
    # Step 1: Load datasets
    dataset_files = load_datasets(args.data_dir, args.max_datasets, args.data_type)
    
    if not dataset_files:
        print("❌ No datasets found!")
        return
    
    # Step 2: Extract latent vectors
    latent_vectors = extract_latent_vectors(dataset_files, args.window_size)
    
    if latent_vectors is None:
        print("❌ Failed to extract latent vectors!")
        return
    
    # Step 3: Train SAE
    sae_model, scaler, losses = train_sae(
        latent_vectors,
        hidden_dim=args.hidden_dim,
        sparsity_weight=args.sparsity_weight,
        epochs=args.epochs,
        lr=args.lr,
        batch_size=args.batch_size
    )
    
    # Step 4: Analyze features
    feature_stats, irrelevant_indices = analyze_features(sae_model, scaler, latent_vectors)
    
    # Step 5: Save model
    config = {
        'data_type': args.data_type,
        'window_size': args.window_size,
        'hidden_dim': args.hidden_dim,
        'sparsity_weight': args.sparsity_weight,
        'epochs': args.epochs,
        'lr': args.lr,
        'num_datasets': len(dataset_files),
        'num_samples': len(latent_vectors)
    }
    
    save_pretrained_sae(sae_model, scaler, irrelevant_indices, args.save_path, config)
    
    print("\n🎉 SAE Pretraining Completed!")
    print(f"📁 Model saved to: {args.save_path}")
    print(f"📊 Training samples: {len(latent_vectors)}")
    print(f"🔧 Irrelevant features: {len(irrelevant_indices)}/{args.hidden_dim}")
    print("\nNow you can use the pretrained SAE with HTA_AD_SAE!")

if __name__ == "__main__":
    main()
