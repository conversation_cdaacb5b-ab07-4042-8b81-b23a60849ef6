#!/usr/bin/env python3
"""
测试核心模型修复效果
"""

import numpy as np
import sys
import time
import warnings
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

def test_core_model_loading():
    """测试核心模型加载"""
    print("🔍 测试核心模型加载")
    
    try:
        from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE
        
        # 创建模型配置
        HP = {
            'window_size': 32,
            'epochs': 5,
            'lr': 1e-3,
            'batch_size': 16,
            'latent_dim': 8,
            'tcn_channels': [16, 16],
            'cnn_channels': 8,
            'downsample_stride': 2,
            'gpu': 0
        }
        
        sae_config = {
            'hidden_dim': 32,
            'sparsity_weight': 0.01
        }
        
        print("  🏗️ 创建HTA-AD-SAE模型...")
        model = HTA_AD_SAE(HP=HP, normalize=True, sae_config=sae_config)
        
        # 检查是否使用了核心模型
        if hasattr(model, 'use_core') and model.use_core:
            print("  ✅ 成功使用核心模型")
            return True, model
        else:
            print("  ⚠️ 使用fallback模型")
            return False, model
            
    except Exception as e:
        print(f"  ❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def test_training_performance():
    """测试训练性能对比"""
    print("\n🏋️ 测试训练性能对比")
    
    # 生成测试数据
    np.random.seed(42)
    train_data = np.sin(np.linspace(0, 4*np.pi, 300)) + 0.1 * np.random.randn(300)
    test_data = np.sin(np.linspace(4*np.pi, 8*np.pi, 100)) + 0.1 * np.random.randn(100)
    
    results = {}
    
    # 测试基础HTA-AD
    print("\n  📊 测试基础HTA-AD")
    try:
        from TSB_AD.model_wrapper import run_HTA_AD
        
        start_time = time.time()
        scores_basic = run_HTA_AD(
            train_data, test_data,
            window_size=32, epochs=5, lr=1e-3, batch_size=16, latent_dim=8
        )
        end_time = time.time()
        
        results['HTA-AD'] = {
            'time': end_time - start_time,
            'scores_range': (scores_basic.min(), scores_basic.max()),
            'scores_mean': scores_basic.mean()
        }
        
        print(f"    ⏱️ 训练时间: {results['HTA-AD']['time']:.2f}s")
        print(f"    📊 分数范围: [{results['HTA-AD']['scores_range'][0]:.4f}, {results['HTA-AD']['scores_range'][1]:.4f}]")
        
    except Exception as e:
        print(f"    ❌ 基础HTA-AD测试失败: {e}")
        results['HTA-AD'] = {'error': str(e)}
    
    # 测试HTA-AD-SAE
    print("\n  📊 测试HTA-AD-SAE")
    try:
        from TSB_AD.model_wrapper import run_HTA_AD_SAE
        
        start_time = time.time()
        scores_sae = run_HTA_AD_SAE(
            train_data, test_data,
            window_size=32, epochs=5, lr=1e-3, batch_size=16, latent_dim=8,
            sae_hidden_dim=32, sae_sparsity_weight=0.01
        )
        end_time = time.time()
        
        results['HTA-AD-SAE'] = {
            'time': end_time - start_time,
            'scores_range': (scores_sae.min(), scores_sae.max()),
            'scores_mean': scores_sae.mean()
        }
        
        print(f"    ⏱️ 训练时间: {results['HTA-AD-SAE']['time']:.2f}s")
        print(f"    📊 分数范围: [{results['HTA-AD-SAE']['scores_range'][0]:.4f}, {results['HTA-AD-SAE']['scores_range'][1]:.4f}]")
        
    except Exception as e:
        print(f"    ❌ HTA-AD-SAE测试失败: {e}")
        import traceback
        traceback.print_exc()
        results['HTA-AD-SAE'] = {'error': str(e)}
    
    return results


def analyze_results(results):
    """分析结果"""
    print("\n" + "=" * 50)
    print("🎯 性能分析结果")
    print("=" * 50)
    
    if 'error' not in results.get('HTA-AD', {}) and 'error' not in results.get('HTA-AD-SAE', {}):
        basic_time = results['HTA-AD']['time']
        sae_time = results['HTA-AD-SAE']['time']
        
        print(f"⏱️ 训练时间对比:")
        print(f"  HTA-AD:     {basic_time:.2f}s")
        print(f"  HTA-AD-SAE: {sae_time:.2f}s")
        print(f"  时间比例:   {sae_time/basic_time:.2f}x")
        
        if sae_time > basic_time:
            print("  ✅ 正常：SAE版本训练时间更长")
        else:
            print("  ⚠️ 异常：SAE版本训练时间更短")
        
        print(f"\n📊 异常分数对比:")
        print(f"  HTA-AD 平均分数:     {results['HTA-AD']['scores_mean']:.4f}")
        print(f"  HTA-AD-SAE 平均分数: {results['HTA-AD-SAE']['scores_mean']:.4f}")
        
    else:
        print("❌ 部分测试失败，无法进行完整对比")
        for model, result in results.items():
            if 'error' in result:
                print(f"  {model}: {result['error']}")


def main():
    """主函数"""
    print("🚀 核心模型修复验证")
    print("=" * 50)
    
    # 测试1: 核心模型加载
    core_loaded, model = test_core_model_loading()
    
    # 测试2: 性能对比
    results = test_training_performance()
    
    # 分析结果
    analyze_results(results)
    
    # 总结
    print("\n" + "=" * 50)
    print("📝 修复验证总结")
    print("=" * 50)
    
    if core_loaded:
        print("✅ 核心模型加载成功")
    else:
        print("❌ 核心模型加载失败，仍使用fallback")
    
    if 'error' not in results.get('HTA-AD-SAE', {}):
        sae_time = results['HTA-AD-SAE']['time']
        basic_time = results.get('HTA-AD', {}).get('time', 0)
        
        if basic_time > 0 and sae_time > basic_time:
            print("✅ SAE版本训练时间正常（比基础版本长）")
        elif basic_time > 0:
            print("⚠️ SAE版本训练时间异常（比基础版本短）")
        else:
            print("⚠️ 无法对比训练时间")
    else:
        print("❌ SAE版本测试失败")


if __name__ == '__main__':
    main()
