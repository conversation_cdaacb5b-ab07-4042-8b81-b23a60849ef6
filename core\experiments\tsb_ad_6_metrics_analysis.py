#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TSB-AD 6个核心指标分析
专门分析VUS-PR, VUS-ROC, AUC-PR, AUC-ROC, Standard-F1, R-based-F1
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

def load_and_analyze_results():
    """加载并分析基准测试结果"""
    results_file = 'full_tsb_ad_benchmark_results.csv'
    
    if not os.path.exists(results_file):
        print("❌ 结果文件不存在")
        return None
    
    df = pd.read_csv(results_file)
    print(f"📊 加载了 {len(df)} 个数据集的结果")
    
    return df

def analyze_6_metrics(df):
    """分析6个核心指标"""
    print("\n📋 TSB-AD 6个核心指标分析")
    print("=" * 100)
    
    # 6个核心指标
    metrics = ['vus_pr', 'vus_roc', 'auc_pr', 'auc_roc', 'standard_f1', 'r_based_f1']
    metric_names = ['VUS-PR', 'VUS-ROC', 'AUC-PR', 'AUC-ROC', 'Standard-F1', 'R-based-F1']
    
    results_summary = []
    
    print(f"{'指标':<15} {'原始平均':<12} {'增强平均':<12} {'绝对改进':<12} {'相对改进':<12} {'有效改进率':<12}")
    print("-" * 100)
    
    for metric, metric_name in zip(metrics, metric_names):
        original_col = f'original_{metric}'
        best_col = f'best_{metric}'
        
        if original_col in df.columns and best_col in df.columns:
            original_values = df[original_col].values
            best_values = df[best_col].values
            
            # 计算统计量
            original_mean = np.mean(original_values)
            best_mean = np.mean(best_values)
            absolute_improvement = best_mean - original_mean
            relative_improvement = (absolute_improvement / original_mean * 100) if original_mean > 0 else 0
            
            # 计算有效改进率
            improvements = best_values - original_values
            positive_improvements = np.sum(improvements > 0)
            improvement_rate = positive_improvements / len(improvements) * 100
            
            print(f"{metric_name:<15} {original_mean:<12.4f} {best_mean:<12.4f} {absolute_improvement:<+12.4f} {relative_improvement:<+12.2f}% {improvement_rate:<12.1f}%")
            
            results_summary.append({
                'metric': metric_name,
                'original_mean': original_mean,
                'best_mean': best_mean,
                'absolute_improvement': absolute_improvement,
                'relative_improvement': relative_improvement,
                'improvement_rate': improvement_rate,
                'original_std': np.std(original_values),
                'best_std': np.std(best_values)
            })
    
    return results_summary

def analyze_by_dataset_type(df):
    """按数据集类型分析"""
    print(f"\n📊 按数据集类型分析")
    print("=" * 80)
    
    if 'type' not in df.columns:
        print("❌ 没有数据集类型信息")
        return
    
    metrics = ['vus_pr', 'vus_roc', 'auc_pr', 'auc_roc', 'standard_f1', 'r_based_f1']
    metric_names = ['VUS-PR', 'VUS-ROC', 'AUC-PR', 'AUC-ROC', 'Standard-F1', 'R-based-F1']
    
    for data_type in df['type'].unique():
        type_df = df[df['type'] == data_type]
        print(f"\n🔍 {data_type.upper()} 数据集 (n={len(type_df)})")
        print("-" * 60)
        
        print(f"{'指标':<15} {'原始':<10} {'增强':<10} {'改进':<10} {'有效率':<10}")
        print("-" * 60)
        
        for metric, metric_name in zip(metrics, metric_names):
            original_col = f'original_{metric}'
            best_col = f'best_{metric}'
            
            if original_col in type_df.columns and best_col in type_df.columns:
                original_values = type_df[original_col].values
                best_values = type_df[best_col].values
                
                original_mean = np.mean(original_values)
                best_mean = np.mean(best_values)
                relative_improvement = ((best_mean - original_mean) / original_mean * 100) if original_mean > 0 else 0
                
                improvements = best_values - original_values
                positive_improvements = np.sum(improvements > 0)
                improvement_rate = positive_improvements / len(improvements) * 100
                
                print(f"{metric_name:<15} {original_mean:<10.4f} {best_mean:<10.4f} {relative_improvement:<+10.2f}% {improvement_rate:<10.1f}%")

def find_best_improvements(df, top_n=10):
    """找出改进最好的数据集"""
    print(f"\n🏆 Top {top_n} 改进数据集 (按VUS-PR改进排序)")
    print("=" * 120)
    
    if 'original_vus_pr' not in df.columns or 'best_vus_pr' not in df.columns:
        print("❌ 缺少VUS-PR数据")
        return
    
    # 计算VUS-PR改进
    df['vus_pr_improvement'] = ((df['best_vus_pr'] - df['original_vus_pr']) / df['original_vus_pr'] * 100)
    
    # 排序并取前N个
    top_improvements = df.nlargest(top_n, 'vus_pr_improvement')
    
    print(f"{'数据集':<40} {'类型':<12} {'原始VUS-PR':<12} {'增强VUS-PR':<12} {'改进':<10}")
    print("-" * 120)
    
    for _, row in top_improvements.iterrows():
        dataset_name = row['dataset'][:37] + "..." if len(row['dataset']) > 40 else row['dataset']
        print(f"{dataset_name:<40} {row['type']:<12} {row['original_vus_pr']:<12.4f} {row['best_vus_pr']:<12.4f} {row['vus_pr_improvement']:<+10.2f}%")

def create_comprehensive_visualization(df, results_summary):
    """创建全面的可视化"""
    print(f"\n📈 创建可视化图表...")
    
    # 设置图表样式
    plt.style.use('default')
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 6个指标的改进对比 (2x3 子图)
    metrics = ['vus_pr', 'vus_roc', 'auc_pr', 'auc_roc', 'standard_f1', 'r_based_f1']
    metric_names = ['VUS-PR', 'VUS-ROC', 'AUC-PR', 'AUC-ROC', 'Standard-F1', 'R-based-F1']
    
    for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = plt.subplot(3, 4, i+1)
        
        original_col = f'original_{metric}'
        best_col = f'best_{metric}'
        
        if original_col in df.columns and best_col in df.columns:
            original_values = df[original_col].values
            best_values = df[best_col].values
            
            # 散点图：原始 vs 增强
            ax.scatter(original_values, best_values, alpha=0.6, s=20)
            
            # 对角线
            min_val = min(np.min(original_values), np.min(best_values))
            max_val = max(np.max(original_values), np.max(best_values))
            ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, linewidth=1)
            
            ax.set_xlabel(f'Original {metric_name}')
            ax.set_ylabel(f'Enhanced {metric_name}')
            ax.set_title(f'{metric_name} Comparison')
            ax.grid(True, alpha=0.3)
    
    # 7. 改进分布直方图
    ax7 = plt.subplot(3, 4, 7)
    if 'improvement' in df.columns:
        ax7.hist(df['improvement'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax7.axvline(0, color='red', linestyle='--', alpha=0.7)
        ax7.set_xlabel('VUS-PR Improvement (%)')
        ax7.set_ylabel('Number of Datasets')
        ax7.set_title('Overall Improvement Distribution')
        ax7.grid(True, alpha=0.3)
    
    # 8. 按数据类型的改进对比
    ax8 = plt.subplot(3, 4, 8)
    if 'type' in df.columns:
        type_improvements = []
        type_labels = []
        for data_type in df['type'].unique():
            type_df = df[df['type'] == data_type]
            if 'improvement' in type_df.columns:
                type_improvements.append(type_df['improvement'].values)
                type_labels.append(f"{data_type}\n(n={len(type_df)})")
        
        if type_improvements:
            ax8.boxplot(type_improvements, labels=type_labels)
            ax8.axhline(0, color='red', linestyle='--', alpha=0.7)
            ax8.set_ylabel('VUS-PR Improvement (%)')
            ax8.set_title('Improvement by Data Type')
            ax8.grid(True, alpha=0.3)
    
    # 9. 指标改进对比条形图
    ax9 = plt.subplot(3, 4, 9)
    if results_summary:
        metric_names_short = [r['metric'] for r in results_summary]
        improvements = [r['relative_improvement'] for r in results_summary]
        
        colors = ['green' if imp > 0 else 'red' for imp in improvements]
        bars = ax9.bar(range(len(metric_names_short)), improvements, color=colors, alpha=0.7)
        
        ax9.set_xticks(range(len(metric_names_short)))
        ax9.set_xticklabels(metric_names_short, rotation=45, ha='right')
        ax9.set_ylabel('Relative Improvement (%)')
        ax9.set_title('Metric Improvements')
        ax9.axhline(0, color='black', linestyle='-', alpha=0.3)
        ax9.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, imp in zip(bars, improvements):
            height = bar.get_height()
            ax9.text(bar.get_x() + bar.get_width()/2., height + (0.1 if height >= 0 else -0.3),
                    f'{imp:+.2f}%', ha='center', va='bottom' if height >= 0 else 'top', fontsize=8)
    
    # 10. 有效改进率
    ax10 = plt.subplot(3, 4, 10)
    if results_summary:
        improvement_rates = [r['improvement_rate'] for r in results_summary]
        bars = ax10.bar(range(len(metric_names_short)), improvement_rates, color='orange', alpha=0.7)
        
        ax10.set_xticks(range(len(metric_names_short)))
        ax10.set_xticklabels(metric_names_short, rotation=45, ha='right')
        ax10.set_ylabel('Improvement Rate (%)')
        ax10.set_title('Effective Improvement Rate')
        ax10.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, rate in zip(bars, improvement_rates):
            height = bar.get_height()
            ax10.text(bar.get_x() + bar.get_width()/2., height + 1,
                     f'{rate:.1f}%', ha='center', va='bottom', fontsize=8)
    
    # 11. 处理时间分析
    ax11 = plt.subplot(3, 4, 11)
    if 'processing_time' in df.columns:
        ax11.hist(df['processing_time'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        ax11.set_xlabel('Processing Time (seconds)')
        ax11.set_ylabel('Number of Datasets')
        ax11.set_title('Processing Time Distribution')
        ax11.grid(True, alpha=0.3)
    
    # 12. 数据集规模分析
    ax12 = plt.subplot(3, 4, 12)
    if 'test_samples' in df.columns and 'improvement' in df.columns:
        ax12.scatter(df['test_samples'], df['improvement'], alpha=0.6, s=20)
        ax12.set_xlabel('Test Samples')
        ax12.set_ylabel('VUS-PR Improvement (%)')
        ax12.set_title('Improvement vs Dataset Size')
        ax12.axhline(0, color='red', linestyle='--', alpha=0.7)
        ax12.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('tsb_ad_6_metrics_comprehensive_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📁 综合分析图表已保存为 'tsb_ad_6_metrics_comprehensive_analysis.png'")

def generate_summary_report(df, results_summary):
    """生成总结报告"""
    print(f"\n📋 TSB-AD基准测试总结报告")
    print("=" * 100)
    
    total_datasets = len(df)
    
    # 基本统计
    print(f"📊 测试规模:")
    print(f"   总数据集数: {total_datasets}")
    
    if 'type' in df.columns:
        for data_type in df['type'].unique():
            count = len(df[df['type'] == data_type])
            print(f"   {data_type}: {count} ({count/total_datasets*100:.1f}%)")
    
    # 整体改进情况
    if 'improvement' in df.columns:
        improvements = df['improvement'].values
        positive_count = np.sum(improvements > 0)
        
        print(f"\n📈 整体改进情况 (基于VUS-PR):")
        print(f"   平均改进: {np.mean(improvements):+.3f}%")
        print(f"   标准差: {np.std(improvements):.3f}%")
        print(f"   有效改进数据集: {positive_count}/{total_datasets} ({positive_count/total_datasets*100:.1f}%)")
        print(f"   最大改进: {np.max(improvements):+.3f}%")
        print(f"   最小改进: {np.min(improvements):+.3f}%")
    
    # 6个指标的改进总结
    if results_summary:
        print(f"\n📋 6个核心指标改进总结:")
        for result in results_summary:
            print(f"   {result['metric']}: {result['relative_improvement']:+.3f}% (有效率: {result['improvement_rate']:.1f}%)")
    
    # 最佳表现指标
    if results_summary:
        best_metric = max(results_summary, key=lambda x: x['relative_improvement'])
        worst_metric = min(results_summary, key=lambda x: x['relative_improvement'])
        
        print(f"\n🏆 最佳改进指标: {best_metric['metric']} ({best_metric['relative_improvement']:+.3f}%)")
        print(f"⚠️  最差改进指标: {worst_metric['metric']} ({worst_metric['relative_improvement']:+.3f}%)")

def main():
    """主函数"""
    print("🎯 TSB-AD 6个核心指标分析")
    print("=" * 100)
    print("📋 分析指标: VUS-PR, VUS-ROC, AUC-PR, AUC-ROC, Standard-F1, R-based-F1")
    print("=" * 100)
    
    # 加载结果
    df = load_and_analyze_results()
    if df is None:
        return
    
    # 分析6个指标
    results_summary = analyze_6_metrics(df)
    
    # 按数据集类型分析
    analyze_by_dataset_type(df)
    
    # 找出最佳改进
    find_best_improvements(df)
    
    # 创建可视化
    if results_summary:
        create_comprehensive_visualization(df, results_summary)
    
    # 生成总结报告
    generate_summary_report(df, results_summary)
    
    print(f"\n🎉 分析完成！")
    print(f"💡 HTA-AD + SAE在TSB-AD基准测试中的表现已全面分析")

if __name__ == "__main__":
    main()
