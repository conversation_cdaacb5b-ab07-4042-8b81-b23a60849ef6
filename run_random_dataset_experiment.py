#!/usr/bin/env python3
"""
Random Dataset Experiment: TSB-AD-U and TSB-AD-M
Test HTA-AD with SAE pre-training on randomly selected datasets
"""

import torch
import sys
import numpy as np
import pandas as pd
import os
import time
import random
from sklearn.preprocessing import StandardScaler
from torch.utils.data import DataLoader, TensorDataset
import warnings
import glob

warnings.filterwarnings('ignore')

# Add paths
sys.path.append('.')
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

from core.models.hta_ad_correct import HTAADCorrect, PostHocSAE
from core.training.sae_pretrainer import SAEPretrainer

# Import TSB-AD evaluation metrics
try:
    from TSB_AD.evaluation.metrics import get_metrics
    from TSB_AD.utils.slidingWindows import find_length_rank
    TSB_AD_AVAILABLE = True
    print("✅ TSB-AD evaluation metrics loaded successfully")
except ImportError:
    print("❌ TSB-AD evaluation metrics not available")
    TSB_AD_AVAILABLE = False

def get_available_datasets():
    """Get all available datasets from TSB-AD-U and TSB-AD-M"""
    tsb_u_path = "TSB-AD/Datasets/TSB-AD-U"
    tsb_m_path = "TSB-AD/Datasets/TSB-AD-M"
    
    tsb_u_datasets = []
    tsb_m_datasets = []
    
    if os.path.exists(tsb_u_path):
        tsb_u_datasets = [f for f in os.listdir(tsb_u_path) if f.endswith('.csv') and not f.endswith('copy.csv')]
    
    if os.path.exists(tsb_m_path):
        tsb_m_datasets = [f for f in os.listdir(tsb_m_path) if f.endswith('.csv') and not f.endswith('copy.csv')]
    
    return tsb_u_datasets, tsb_m_datasets

def select_random_datasets(datasets, n=10):
    """Randomly select n datasets from the list"""
    if len(datasets) < n:
        print(f"Warning: Only {len(datasets)} datasets available, selecting all")
        return datasets
    return random.sample(datasets, n)

def parse_dataset_filename(filename):
    """Parse TSB-AD dataset filename to extract train/test split info"""
    parts = filename.split('_')
    train_size = None
    test_size = None
    
    for i, part in enumerate(parts):
        if part == 'tr' and i + 1 < len(parts):
            train_size = int(parts[i + 1])
        elif part == '1st' and i + 1 < len(parts):
            test_size = int(parts[i + 1].split('.')[0])
    
    return train_size, test_size

def load_tsb_ad_dataset(dataset_path):
    """Load TSB-AD dataset with proper train/test split"""
    try:
        df = pd.read_csv(dataset_path)
        
        # Handle different column formats
        if df.shape[1] == 2:
            # Univariate: [Data, Label]
            data = df.iloc[:, 0].values.reshape(-1, 1)
            labels = df.iloc[:, 1].values
        else:
            # Multivariate: [Feature1, Feature2, ..., Label]
            data = df.iloc[:, :-1].values
            labels = df.iloc[:, -1].values
        
        filename = os.path.basename(dataset_path)
        train_size, test_size = parse_dataset_filename(filename)
        
        if train_size is None or test_size is None:
            # Fallback to 70-30 split
            split_idx = int(0.7 * len(data))
            train_data = data[:split_idx]
            train_labels = labels[:split_idx]
            test_data = data[split_idx:]
            test_labels = labels[split_idx:]
        else:
            train_data = data[:train_size]
            train_labels = labels[:train_size]
            test_data = data[train_size:train_size + test_size]
            test_labels = labels[train_size:train_size + test_size]
        
        return train_data, train_labels, test_data, test_labels
    except Exception as e:
        print(f"Error loading dataset {dataset_path}: {e}")
        return None, None, None, None

def create_sliding_windows(data, window_size, stride=1):
    """Create sliding windows from time series data"""
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    n_samples, n_features = data.shape
    windows = []
    
    for i in range(0, n_samples - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def train_hta_ad_with_sae(train_data, input_dim, device='cpu', epochs=25):
    """Train HTA-AD and SAE models"""
    # Create HTA-AD model
    model = HTAADCorrect(
        input_dim=input_dim,
        window_size=128,
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        tcn_kernel_size=3,
        cnn_channels=16,
        downsample_stride=2
    ).to(device)
    
    # Create SAE model
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128,
        sparsity_penalty=1e-3
    ).to(device)
    
    # Create training windows
    train_windows = create_sliding_windows(train_data, 128)
    train_windows = torch.FloatTensor(train_windows).to(device)
    
    if len(train_windows) == 0:
        return None, None
    
    # Train HTA-AD
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-5)
    model.train()
    
    for epoch in range(epochs):
        total_loss = 0
        batch_size = 64
        
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch)
            losses = model.compute_loss(outputs, batch)
            loss = losses['total']
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
    
    # SAE pre-training
    pretrainer_config = {
        'learning_rate': 1e-3,
        'weight_decay': 1e-5,
        'batch_size': 64,
        'epochs': 25,
        'sparsity_penalty': 1e-3,
        'patience': 10,
        'min_delta': 1e-6,
        'save_checkpoints': False,
        'log_interval': 25  # Only log at the end
    }
    
    pretrainer = SAEPretrainer(sae, config=pretrainer_config)
    
    # Create data loader for latent collection
    dataset = TensorDataset(train_windows)
    data_loader = DataLoader(dataset, batch_size=64, shuffle=False)
    
    # Collect latent vectors
    latent_vectors = pretrainer.collect_latent_vectors(
        model, data_loader, max_samples=3000
    )
    
    # Pre-train SAE
    pretrainer.pretrain(latent_vectors)
    
    return model, sae

def evaluate_models(model, sae, test_data, test_labels, device='cpu'):
    """Evaluate both HTA-AD and SAE models"""
    model.eval()
    sae.eval()
    
    window_size = 128
    if len(test_data) < window_size:
        return None
    
    # Point-wise evaluation
    pad_size = window_size // 2
    padded_data = np.pad(test_data, ((pad_size, pad_size), (0, 0)), mode='edge')
    
    hta_scores = []
    sae_scores = []
    
    with torch.no_grad():
        for i in range(len(test_data)):
            window = padded_data[i:i + window_size]
            window_tensor = torch.FloatTensor(window).unsqueeze(0).to(device)
            
            # HTA-AD forward pass
            outputs = model(window_tensor)
            reconstruction = outputs['reconstruction']
            latent_vectors = outputs['latent_vectors']
            
            # HTA-AD score
            hta_error = torch.mean((window_tensor - reconstruction) ** 2)
            hta_scores.append(hta_error.cpu().item())
            
            # SAE purified reconstruction
            z_purified = sae.purify_latent(latent_vectors, debug=False)
            purified_reconstruction = model.decode(z_purified)
            sae_error = torch.mean((window_tensor - purified_reconstruction) ** 2)
            sae_scores.append(sae_error.cpu().item())
    
    hta_scores = np.array(hta_scores)
    sae_scores = np.array(sae_scores)
    
    results = {}
    
    # TSB-AD evaluation if available
    if len(np.unique(test_labels)) > 1 and TSB_AD_AVAILABLE:
        try:
            slidingWindow = find_length_rank(test_data, rank=1)
            hta_metrics = get_metrics(hta_scores, test_labels, slidingWindow=slidingWindow)
            sae_metrics = get_metrics(sae_scores, test_labels, slidingWindow=slidingWindow)
            
            results['hta_ad_vus_pr'] = hta_metrics.get('VUS-PR', 0)
            results['hta_ad_vus_roc'] = hta_metrics.get('VUS-ROC', 0)
            results['hta_ad_f1'] = hta_metrics.get('Standard-F1', 0)
            results['sae_vus_pr'] = sae_metrics.get('VUS-PR', 0)
            results['sae_vus_roc'] = sae_metrics.get('VUS-ROC', 0)
            results['sae_f1'] = sae_metrics.get('Standard-F1', 0)
            
        except Exception as e:
            results = {
                'hta_ad_vus_pr': 0, 'hta_ad_vus_roc': 0, 'hta_ad_f1': 0,
                'sae_vus_pr': 0, 'sae_vus_roc': 0, 'sae_f1': 0
            }
    else:
        results = {
            'hta_ad_vus_pr': 0, 'hta_ad_vus_roc': 0, 'hta_ad_f1': 0,
            'sae_vus_pr': 0, 'sae_vus_roc': 0, 'sae_f1': 0
        }
    
    results['hta_scores'] = hta_scores
    results['sae_scores'] = sae_scores
    results['test_labels'] = test_labels
    
    return results

def run_experiment_on_datasets(dataset_type, datasets, base_path):
    """Run experiment on a list of datasets"""
    results_summary = []
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    for i, dataset_name in enumerate(datasets):
        dataset_path = os.path.join(base_path, dataset_name)
        
        if not os.path.exists(dataset_path):
            print(f"❌ Dataset not found: {dataset_path}")
            continue
        
        print(f"\n📊 [{i+1}/10] {dataset_type}: {dataset_name}")
        print("-" * 60)
        
        # Load dataset
        train_data, train_labels, test_data, test_labels = load_tsb_ad_dataset(dataset_path)
        if train_data is None:
            continue
        
        print(f"  Train: {train_data.shape[0]} points, {train_data.shape[1]} features, {np.mean(train_labels):.3f} anomaly ratio")
        print(f"  Test:  {test_data.shape[0]} points, {np.mean(test_labels):.3f} anomaly ratio")
        
        # Skip if no anomalies in test set
        if np.sum(test_labels) == 0:
            print("  ⚠️  Skipping: No anomalies in test set")
            continue
        
        # Skip if training data is all zeros
        if np.all(train_data == 0):
            print("  ⚠️  Skipping: Training data is all zeros")
            continue
        
        # Normalize data
        scaler = StandardScaler()
        train_data = scaler.fit_transform(train_data)
        test_data = scaler.transform(test_data)
        
        input_dim = train_data.shape[1]
        
        try:
            # Train models
            print("  🔧 Training models...")
            start_time = time.time()
            model, sae = train_hta_ad_with_sae(train_data, input_dim, device, epochs=20)
            training_time = time.time() - start_time
            
            if model is None or sae is None:
                print("  ❌ Training failed")
                continue
            
            # Evaluate
            print("  📈 Evaluating...")
            start_time = time.time()
            results = evaluate_models(model, sae, test_data, test_labels, device)
            eval_time = time.time() - start_time
            
            if results is None:
                print("  ❌ Evaluation failed")
                continue
            
            # Print results
            print(f"  ✅ Results:")
            print(f"     HTA-AD: VUS-PR={results['hta_ad_vus_pr']:.4f}, VUS-ROC={results['hta_ad_vus_roc']:.4f}, F1={results['hta_ad_f1']:.4f}")
            print(f"     SAE:    VUS-PR={results['sae_vus_pr']:.4f}, VUS-ROC={results['sae_vus_roc']:.4f}, F1={results['sae_f1']:.4f}")
            print(f"     Time: {training_time:.1f}s train, {eval_time:.1f}s eval")
            
            # Store results
            results_summary.append({
                'dataset_type': dataset_type,
                'dataset': dataset_name,
                'dataset_id': i + 1,
                'input_dim': input_dim,
                'train_points': train_data.shape[0],
                'test_points': test_data.shape[0],
                'anomaly_ratio': np.mean(test_labels),
                'hta_ad_vus_pr': results['hta_ad_vus_pr'],
                'hta_ad_vus_roc': results['hta_ad_vus_roc'],
                'hta_ad_f1': results['hta_ad_f1'],
                'sae_vus_pr': results['sae_vus_pr'],
                'sae_vus_roc': results['sae_vus_roc'],
                'sae_f1': results['sae_f1'],
                'training_time': training_time,
                'eval_time': eval_time,
                'hta_score_mean': results['hta_scores'].mean(),
                'sae_score_mean': results['sae_scores'].mean(),
                'score_ratio': results['sae_scores'].mean() / results['hta_scores'].mean()
            })
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            continue
    
    return results_summary

def main():
    """Main experiment function"""
    print("🚀 Random Dataset Experiment: TSB-AD-U and TSB-AD-M")
    print("=" * 70)
    
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Get available datasets
    tsb_u_datasets, tsb_m_datasets = get_available_datasets()
    
    print(f"Available datasets:")
    print(f"  TSB-AD-U: {len(tsb_u_datasets)} datasets")
    print(f"  TSB-AD-M: {len(tsb_m_datasets)} datasets")
    
    # Select random datasets
    selected_u = select_random_datasets(tsb_u_datasets, 10)
    selected_m = select_random_datasets(tsb_m_datasets, 10)
    
    print(f"\nSelected datasets:")
    print(f"  TSB-AD-U: {len(selected_u)} datasets")
    print(f"  TSB-AD-M: {len(selected_m)} datasets")
    
    all_results = []
    
    # Run experiments on TSB-AD-U
    print(f"\n🔬 Running experiments on TSB-AD-U datasets...")
    u_results = run_experiment_on_datasets("TSB-AD-U", selected_u, "TSB-AD/Datasets/TSB-AD-U")
    all_results.extend(u_results)
    
    # Run experiments on TSB-AD-M
    print(f"\n🔬 Running experiments on TSB-AD-M datasets...")
    m_results = run_experiment_on_datasets("TSB-AD-M", selected_m, "TSB-AD/Datasets/TSB-AD-M")
    all_results.extend(m_results)
    
    # Process and save results
    if not all_results:
        print("\n❌ No datasets were successfully processed.")
        return
    
    results_df = pd.DataFrame(all_results)
    
    # Print summary
    print("\n" + "=" * 70)
    print("📈 EXPERIMENT RESULTS SUMMARY")
    print("=" * 70)
    
    # Group by dataset type
    for dataset_type in ['TSB-AD-U', 'TSB-AD-M']:
        type_results = results_df[results_df['dataset_type'] == dataset_type]
        if len(type_results) == 0:
            continue
        
        print(f"\n📊 {dataset_type} Results ({len(type_results)} datasets):")
        print(f"   HTA-AD Average:")
        print(f"     - VUS-PR:  {type_results['hta_ad_vus_pr'].mean():.4f} ± {type_results['hta_ad_vus_pr'].std():.4f}")
        print(f"     - VUS-ROC: {type_results['hta_ad_vus_roc'].mean():.4f} ± {type_results['hta_ad_vus_roc'].std():.4f}")
        print(f"     - F1:      {type_results['hta_ad_f1'].mean():.4f} ± {type_results['hta_ad_f1'].std():.4f}")
        
        print(f"   SAE Average:")
        print(f"     - VUS-PR:  {type_results['sae_vus_pr'].mean():.4f} ± {type_results['sae_vus_pr'].std():.4f}")
        print(f"     - VUS-ROC: {type_results['sae_vus_roc'].mean():.4f} ± {type_results['sae_vus_roc'].std():.4f}")
        print(f"     - F1:      {type_results['sae_f1'].mean():.4f} ± {type_results['sae_f1'].std():.4f}")
        
        # Calculate improvements
        vus_pr_improvement = ((type_results['sae_vus_pr'] - type_results['hta_ad_vus_pr']) / 
                             (type_results['hta_ad_vus_pr'] + 1e-8) * 100).mean()
        vus_roc_improvement = ((type_results['sae_vus_roc'] - type_results['hta_ad_vus_roc']) / 
                              (type_results['hta_ad_vus_roc'] + 1e-8) * 100).mean()
        
        print(f"   SAE Improvements:")
        print(f"     - VUS-PR:  {vus_pr_improvement:+.2f}%")
        print(f"     - VUS-ROC: {vus_roc_improvement:+.2f}%")
    
    # Overall summary
    print(f"\n📊 Overall Results ({len(results_df)} datasets):")
    print(f"   HTA-AD Average:")
    print(f"     - VUS-PR:  {results_df['hta_ad_vus_pr'].mean():.4f} ± {results_df['hta_ad_vus_pr'].std():.4f}")
    print(f"     - VUS-ROC: {results_df['hta_ad_vus_roc'].mean():.4f} ± {results_df['hta_ad_vus_roc'].std():.4f}")
    print(f"     - F1:      {results_df['hta_ad_f1'].mean():.4f} ± {results_df['hta_ad_f1'].std():.4f}")
    
    print(f"   SAE Average:")
    print(f"     - VUS-PR:  {results_df['sae_vus_pr'].mean():.4f} ± {results_df['sae_vus_pr'].std():.4f}")
    print(f"     - VUS-ROC: {results_df['sae_vus_roc'].mean():.4f} ± {results_df['sae_vus_roc'].std():.4f}")
    print(f"     - F1:      {results_df['sae_f1'].mean():.4f} ± {results_df['sae_f1'].std():.4f}")
    
    # Save results
    os.makedirs('results', exist_ok=True)
    results_df.to_csv('results/random_dataset_experiment_results.csv', index=False)
    print(f"\n💾 Results saved to results/random_dataset_experiment_results.csv")
    
    print(f"\n🎉 Random dataset experiment completed!")
    print(f"   - Total datasets tested: {len(results_df)}")
    print(f"   - Average training time: {results_df['training_time'].mean():.1f}s")
    print(f"   - Average evaluation time: {results_df['eval_time'].mean():.1f}s")

if __name__ == "__main__":
    main()
