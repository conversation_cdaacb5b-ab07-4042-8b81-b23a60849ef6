#!/usr/bin/env python3
"""
Test HTA-AD with SAE Pre-training
Complete pipeline: HTA-AD training -> SAE pre-training -> Evaluation
"""

import torch
import sys
import numpy as np
import pandas as pd
import os
import time
from sklearn.preprocessing import StandardScaler
from torch.utils.data import DataLoader, TensorDataset
import warnings

warnings.filterwarnings('ignore')

# Add paths
sys.path.append('.')
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

from core.models.hta_ad_correct import HTAADCorrect, PostHocSAE
from core.training.sae_pretrainer import SAEPretrainer

# Import TSB-AD evaluation metrics
try:
    from TSB_AD.evaluation.metrics import get_metrics
    from TSB_AD.utils.slidingWindows import find_length_rank
    TSB_AD_AVAILABLE = True
    print("✅ TSB-AD evaluation metrics loaded successfully")
except ImportError:
    print("❌ TSB-AD evaluation metrics not available")
    TSB_AD_AVAILABLE = False

def create_sliding_windows(data, window_size, stride=1):
    """Create sliding windows from time series data"""
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    n_samples, n_features = data.shape
    windows = []
    
    for i in range(0, n_samples - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def load_tsb_ad_dataset(dataset_path):
    """Load TSB-AD dataset with proper train/test split"""
    try:
        df = pd.read_csv(dataset_path)
        print(f"   Loaded CSV shape: {df.shape}")
        print(f"   CSV columns: {df.columns.tolist()}")
        print(f"   First few rows:\n{df.head()}")

        data = df.iloc[:, 0].values.reshape(-1, 1)
        labels = df.iloc[:, 1].values

        print(f"   Raw data stats: mean={data.mean():.4f}, std={data.std():.4f}")
        print(f"   Raw data range: [{data.min():.4f}, {data.max():.4f}]")
        
        # Parse filename for train/test split
        filename = os.path.basename(dataset_path)
        parts = filename.split('_')
        train_size = None
        
        for i, part in enumerate(parts):
            if part == 'tr' and i + 1 < len(parts):
                train_size = int(parts[i + 1])
                break
        
        if train_size is None:
            train_size = int(0.7 * len(data))
        
        train_data = data[:train_size]
        train_labels = labels[:train_size]
        test_data = data[train_size:]
        test_labels = labels[train_size:]
        
        return train_data, train_labels, test_data, test_labels
    except Exception as e:
        print(f"Error loading dataset {dataset_path}: {e}")
        return None, None, None, None

def train_hta_ad_model(train_data, input_dim, device='cpu'):
    """Train HTA-AD model with correct settings"""
    print("🔧 Training HTA-AD model...")
    
    # Create HTA-AD model
    model = HTAADCorrect(
        input_dim=input_dim,
        window_size=128,
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        tcn_kernel_size=3,
        cnn_channels=16,
        downsample_stride=2
    ).to(device)
    
    # Create training windows
    train_windows = create_sliding_windows(train_data, 128)
    train_windows = torch.FloatTensor(train_windows).to(device)
    
    if len(train_windows) == 0:
        return None
    
    print(f"  Training on {len(train_windows)} windows...")
    
    # Training setup
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-5)
    model.train()
    
    # Training loop
    epochs = 30
    for epoch in range(epochs):
        total_loss = 0
        batch_size = 64
        
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch)
            losses = model.compute_loss(outputs, batch)
            loss = losses['total']
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 10 == 0:
            avg_loss = total_loss / (len(train_windows) // batch_size + 1)
            print(f"  Epoch {epoch + 1}/{epochs}, Loss: {avg_loss:.4f}")
    
    print("✅ HTA-AD training completed")
    return model, train_windows

def pretrain_sae(model, train_windows, device='cpu'):
    """Pre-train SAE on HTA-AD latent vectors"""
    print("\n🚀 Starting SAE pre-training...")

    # Debug: Check train_windows
    print(f"   Train windows shape: {train_windows.shape}")
    print(f"   Train windows stats: mean={train_windows.mean().item():.4f}, std={train_windows.std().item():.4f}")

    # Create SAE model
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128,
        sparsity_penalty=1e-3
    ).to(device)

    # Create SAE pre-trainer
    pretrainer_config = {
        'learning_rate': 1e-3,
        'weight_decay': 1e-5,
        'batch_size': 64,
        'epochs': 50,
        'sparsity_penalty': 1e-3,
        'patience': 10,
        'min_delta': 1e-6,
        'save_checkpoints': False,  # Disable checkpoints for testing
        'log_interval': 10
    }

    pretrainer = SAEPretrainer(sae, config=pretrainer_config)

    # Create data loader for latent collection
    dataset = TensorDataset(train_windows)
    data_loader = DataLoader(dataset, batch_size=64, shuffle=False)

    # Debug: Test model on a small batch first
    print("   Testing model on first batch...")
    with torch.no_grad():
        test_batch = train_windows[:5]
        print(f"   Test batch shape: {test_batch.shape}")
        print(f"   Test batch stats: mean={test_batch.mean().item():.4f}, std={test_batch.std().item():.4f}")

        model.eval()
        test_outputs = model(test_batch)
        test_latents = test_outputs['latent_vectors']
        print(f"   Test latents shape: {test_latents.shape}")
        print(f"   Test latents stats: mean={test_latents.mean().item():.4f}, std={test_latents.std().item():.4f}")

        if torch.all(test_latents == 0):
            print("❌ Model produces zero latents! Aborting SAE pre-training.")
            return None, None

    # Collect latent vectors
    latent_vectors = pretrainer.collect_latent_vectors(
        model, data_loader, max_samples=5000
    )

    # Check if latent vectors are valid
    if latent_vectors is None or torch.all(latent_vectors == 0):
        print("❌ Warning: All latent vectors are zero! Model may not be trained properly.")
        return None, None
    
    # Pre-train SAE
    training_results = pretrainer.pretrain(latent_vectors)
    
    # Save pre-trained model
    os.makedirs('results/models', exist_ok=True)
    save_path = 'results/models/pretrained_sae.pt'
    pretrainer.save_pretrained_model(save_path)
    
    print("✅ SAE pre-training completed")
    return sae, training_results

def evaluate_models(model, sae, test_data, test_labels, device='cpu'):
    """Evaluate both HTA-AD and SAE models"""
    print("\n📊 Evaluating models...")
    
    model.eval()
    sae.eval()
    
    window_size = 128
    if len(test_data) < window_size:
        return None
    
    # Point-wise evaluation
    pad_size = window_size // 2
    padded_data = np.pad(test_data, ((pad_size, pad_size), (0, 0)), mode='edge')
    
    hta_scores = []
    sae_scores = []
    
    with torch.no_grad():
        for i in range(len(test_data)):
            window = padded_data[i:i + window_size]
            window_tensor = torch.FloatTensor(window).unsqueeze(0).to(device)
            
            # HTA-AD forward pass
            outputs = model(window_tensor)
            reconstruction = outputs['reconstruction']
            latent_vectors = outputs['latent_vectors']
            
            # HTA-AD score
            hta_error = torch.mean((window_tensor - reconstruction) ** 2)
            hta_scores.append(hta_error.cpu().item())
            
            # SAE purified reconstruction
            debug_mode = i < 5  # Only debug first 5 samples
            z_purified = sae.purify_latent(latent_vectors, debug=debug_mode)
            purified_reconstruction = model.decode(z_purified)
            sae_error = torch.mean((window_tensor - purified_reconstruction) ** 2)
            sae_scores.append(sae_error.cpu().item())
    
    hta_scores = np.array(hta_scores)
    sae_scores = np.array(sae_scores)
    
    results = {}
    
    # TSB-AD evaluation if available
    if len(np.unique(test_labels)) > 1 and TSB_AD_AVAILABLE:
        try:
            slidingWindow = find_length_rank(test_data, rank=1)
            hta_metrics = get_metrics(hta_scores, test_labels, slidingWindow=slidingWindow)
            sae_metrics = get_metrics(sae_scores, test_labels, slidingWindow=slidingWindow)
            
            results['hta_ad_vus_pr'] = hta_metrics.get('VUS-PR', 0)
            results['hta_ad_vus_roc'] = hta_metrics.get('VUS-ROC', 0)
            results['hta_ad_f1'] = hta_metrics.get('Standard-F1', 0)
            results['sae_vus_pr'] = sae_metrics.get('VUS-PR', 0)
            results['sae_vus_roc'] = sae_metrics.get('VUS-ROC', 0)
            results['sae_f1'] = sae_metrics.get('Standard-F1', 0)
            
        except Exception as e:
            print(f"  Warning: TSB-AD metrics calculation failed: {e}")
            results = {
                'hta_ad_vus_pr': 0, 'hta_ad_vus_roc': 0, 'hta_ad_f1': 0,
                'sae_vus_pr': 0, 'sae_vus_roc': 0, 'sae_f1': 0
            }
    else:
        results = {
            'hta_ad_vus_pr': 0, 'hta_ad_vus_roc': 0, 'hta_ad_f1': 0,
            'sae_vus_pr': 0, 'sae_vus_roc': 0, 'sae_f1': 0
        }
    
    results['hta_scores'] = hta_scores
    results['sae_scores'] = sae_scores
    results['test_labels'] = test_labels
    
    return results

def test_with_sae_pretraining():
    """Complete test with SAE pre-training"""
    print("🧪 Testing HTA-AD with SAE Pre-training")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Test dataset (use a different one with non-zero training data)
    dataset_path = "TSB-AD/Datasets/TSB-AD-U/002_NAB_id_2_WebService_tr_1500_1st_4106.csv"
    
    if not os.path.exists(dataset_path):
        print("❌ Test dataset not found")
        return
    
    dataset_name = os.path.basename(dataset_path)
    print(f"\n📊 Testing: {dataset_name}")
    print("-" * 50)
    
    # Load dataset
    train_data, train_labels, test_data, test_labels = load_tsb_ad_dataset(dataset_path)
    if train_data is None:
        return
    
    print(f"Train: {train_data.shape[0]} points, {np.mean(train_labels):.3f} anomaly ratio")
    print(f"Test:  {test_data.shape[0]} points, {np.mean(test_labels):.3f} anomaly ratio")

    # Debug: Check raw data
    print(f"Raw train data stats: mean={train_data.mean():.4f}, std={train_data.std():.4f}")
    print(f"Raw train data range: [{train_data.min():.4f}, {train_data.max():.4f}]")

    # Normalize data
    scaler = StandardScaler()
    train_data = scaler.fit_transform(train_data)
    test_data = scaler.transform(test_data)

    # Debug: Check normalized data
    print(f"Normalized train data stats: mean={train_data.mean():.4f}, std={train_data.std():.4f}")
    print(f"Normalized train data range: [{train_data.min():.4f}, {train_data.max():.4f}]")
    
    input_dim = train_data.shape[1]
    
    try:
        # Step 1: Train HTA-AD
        start_time = time.time()
        model, train_windows = train_hta_ad_model(train_data, input_dim, device)
        hta_training_time = time.time() - start_time
        
        if model is None:
            print("❌ HTA-AD training failed")
            return
        
        # Step 2: Pre-train SAE
        start_time = time.time()
        sae, sae_results = pretrain_sae(model, train_windows, device)
        sae_training_time = time.time() - start_time
        
        # Step 3: Evaluate
        start_time = time.time()
        results = evaluate_models(model, sae, test_data, test_labels, device)
        eval_time = time.time() - start_time
        
        if results is None:
            print("❌ Evaluation failed")
            return
        
        # Print results
        print(f"\n✅ Results:")
        print(f"   HTA-AD (CNN+TCN):")
        print(f"     - VUS-PR:  {results['hta_ad_vus_pr']:.4f}")
        print(f"     - VUS-ROC: {results['hta_ad_vus_roc']:.4f}")
        print(f"     - F1:      {results['hta_ad_f1']:.4f}")
        print(f"   SAE (Pre-trained + Dead Features Purified):")
        print(f"     - VUS-PR:  {results['sae_vus_pr']:.4f}")
        print(f"     - VUS-ROC: {results['sae_vus_roc']:.4f}")
        print(f"     - F1:      {results['sae_f1']:.4f}")
        
        # Performance comparison
        vus_pr_improvement = ((results['sae_vus_pr'] - results['hta_ad_vus_pr']) / 
                             (results['hta_ad_vus_pr'] + 1e-8) * 100)
        vus_roc_improvement = ((results['sae_vus_roc'] - results['hta_ad_vus_roc']) / 
                              (results['hta_ad_vus_roc'] + 1e-8) * 100)
        
        print(f"\n🚀 SAE Pre-training Benefits:")
        print(f"     - VUS-PR improvement:  {vus_pr_improvement:+.2f}%")
        print(f"     - VUS-ROC improvement: {vus_roc_improvement:+.2f}%")
        
        print(f"\n⏱️  Timing:")
        print(f"     - HTA-AD training: {hta_training_time:.1f}s")
        print(f"     - SAE pre-training: {sae_training_time:.1f}s")
        print(f"     - Evaluation: {eval_time:.1f}s")
        print(f"     - Total: {hta_training_time + sae_training_time + eval_time:.1f}s")
        
        # Score statistics
        hta_mean = results['hta_scores'].mean()
        sae_mean = results['sae_scores'].mean()
        score_diff = np.abs(results['sae_scores'] - results['hta_scores']).mean()
        
        print(f"\n📈 Score Statistics:")
        print(f"     - HTA-AD: {hta_mean:.6f}")
        print(f"     - SAE:    {sae_mean:.6f}")
        print(f"     - Ratio:  {sae_mean/hta_mean:.3f}")
        print(f"     - Avg difference: {score_diff:.6f}")
        
        print(f"\n🎉 Test with SAE pre-training completed successfully!")
        print(f"   - Architecture: CNN+TCN + Pre-trained SAE")
        print(f"   - SAE training epochs: {sae_results['epochs_trained']}")
        print(f"   - Final SAE loss: {sae_results['final_train_loss']:.6f}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_with_sae_pretraining()
