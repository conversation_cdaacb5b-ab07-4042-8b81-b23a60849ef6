#!/usr/bin/env python3
"""
Verify that the generated visualizations are correct
"""

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import numpy as np
import os

def check_image_exists_and_size(filename):
    """Check if image exists and get its properties"""
    if not os.path.exists(filename):
        print(f"❌ {filename} does not exist!")
        return False
    
    try:
        img = mpimg.imread(filename)
        print(f"✅ {filename} exists - Shape: {img.shape}")
        return True
    except Exception as e:
        print(f"❌ Error reading {filename}: {e}")
        return False

def main():
    """Verify all generated visualizations"""
    print("🔍 Verifying Generated Visualizations")
    print("=" * 50)
    
    # Check comprehensive feature analysis
    print("\n1. Comprehensive Feature Analysis:")
    check_image_exists_and_size('comprehensive_feature_analysis.png')
    
    # Check beautiful feature dictionary  
    print("\n2. Beautiful Feature Dictionary:")
    check_image_exists_and_size('beautiful_feature_dictionary.png')
    
    # List all PNG files in current directory
    print("\n📊 All PNG files in current directory:")
    png_files = [f for f in os.listdir('.') if f.endswith('.png')]
    for png_file in sorted(png_files):
        size_mb = os.path.getsize(png_file) / (1024 * 1024)
        print(f"   • {png_file} ({size_mb:.2f} MB)")
    
    print(f"\n✅ Total PNG files: {len(png_files)}")
    
    # Verify the images have reasonable content
    print("\n🎨 Image Content Verification:")
    
    if os.path.exists('comprehensive_feature_analysis.png'):
        img = mpimg.imread('comprehensive_feature_analysis.png')
        print(f"   • Comprehensive analysis: {img.shape[0]}x{img.shape[1]} pixels")
        print(f"   • Should show 6 subplots (2x3 grid)")
        
    if os.path.exists('beautiful_feature_dictionary.png'):
        img = mpimg.imread('beautiful_feature_dictionary.png')
        print(f"   • Feature dictionary: {img.shape[0]}x{img.shape[1]} pixels")
        print(f"   • Should show 16 subplots (4x4 grid)")

if __name__ == "__main__":
    main()
