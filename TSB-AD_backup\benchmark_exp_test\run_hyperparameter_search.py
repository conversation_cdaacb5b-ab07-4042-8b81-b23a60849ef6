#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HTA_AD_M 模型超参数搜索脚本
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import time
from datetime import datetime
from sklearn.model_selection import ParameterGrid

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from TSB_AD.models.HTA_AD_M import HTA_AD_M
from TSB_AD.evaluation.metrics import get_metrics

# 设置随机种子
np.random.seed(42)

# --- 配置 ---
CONFIG = {
    # 为加快搜索速度，选择部分有代表性的数据集
    'datasets': [
        'Datasets/TSB-AD-M/001_Genesis_id_1_Sensor_tr_4055_1st_15538.csv',
        'Datasets/TSB-AD-M/018_Daphnet_id_1_HumanActivity_tr_9693_1st_20732.csv',
        'Datasets/TSB-AD-M/057_SMD_id_1_Facility_tr_4529_1st_4629.csv'
    ],
    'model_class': HTA_AD_M,
    'model_name': 'HTA_AD_M',
    'results_dir': 'hyperparam_search_results',
    
    # 固定的基础超参数
    'base_hyperparams': {
        'tcn_channels': [32, 32],
        'epochs': 30,  # 为加快搜索速度，保持较低的epochs
        'lr': 1e-3,
        'batch_size': 64,
        'gpu': 0
    },

    # --- 超参数搜索空间 ---
    'search_space': {
        'window_size': [64, 128],
        'latent_dim': [16, 32, 64],
        'cnn_channels': [8, 16, 32],
        'downsample_stride': [2, 4]
    }
}

def ensure_dir(directory):
    if not os.path.exists(directory):
        os.makedirs(directory)

def load_dataset(file_path):
    print(f"  加载数据集: {os.path.basename(file_path)}")
    df = pd.read_csv(file_path).dropna()
    data = df.iloc[:, 0:-1].values.astype(float)
    labels = df['Label'].astype(int).to_numpy()
    dataset_name = os.path.basename(file_path)
    return data, labels, dataset_name

def extract_train_size_from_filename(filename):
    parts = os.path.basename(filename).split('_')
    try:
        tr_index = parts.index('tr')
        if tr_index + 1 < len(parts):
            return int(parts[tr_index + 1])
    except (ValueError, IndexError):
        pass
    for part in parts:
        if part.startswith('tr') and len(part) > 2:
            try:
                return int(part[2:])
            except ValueError:
                continue
    print(f"警告: 无法从 '{os.path.basename(filename)}' 提取训练集大小，使用默认值25%")
    return None

def run_single_experiment(params, dataset_path):
    data, labels, dataset_name = load_dataset(dataset_path)

    train_size = extract_train_size_from_filename(dataset_path)
    if train_size is None:
        train_size = int(len(data) * 0.25)
    train_size = min(train_size, len(data) - 1)
    
    data_train = data[:train_size]
    data_test = data[train_size:]

    start_time = time.time()
    
    try:
        model = CONFIG['model_class'](HP=params)
        model.fit(data_train)
        scores = model.decision_function(data_test)
        
        full_scores = np.zeros(len(data))
        full_scores[train_size:] = scores
        
        metrics = get_metrics(full_scores, labels)
        runtime = time.time() - start_time
        
        result = {'status': 'success', 'runtime': runtime}
        result.update(metrics)
        return result

    except Exception as e:
        import traceback
        return {
            'status': 'error',
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'runtime': time.time() - start_time
        }

def main():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(CONFIG['results_dir'], f"search_{timestamp}")
    ensure_dir(results_dir)

    param_grid = list(ParameterGrid(CONFIG['search_space']))
    all_results = []
    
    print(f"🚀 开始为 {CONFIG['model_name']} 进行超参数搜索")
    print(f"  总参数组合数: {len(param_grid)}")
    print(f"  总数据集数: {len(CONFIG['datasets'])}")
    print(f"  预计实验次数: {len(param_grid) * len(CONFIG['datasets'])}")
    print("-" * 50)

    for i, params in enumerate(param_grid):
        current_params = CONFIG['base_hyperparams'].copy()
        current_params.update(params)
        
        print(f"\n[{i+1}/{len(param_grid)}] 测试参数: {params}")
        
        dataset_metrics = []
        for dataset_path in CONFIG['datasets']:
            exp_result = run_single_experiment(current_params, dataset_path)
            
            if exp_result['status'] == 'success':
                metric_values = {k: v for k, v in exp_result.items() if k not in ['status']}
                dataset_metrics.append(metric_values)
                print(f"    ✅ 成功于 {os.path.basename(dataset_path)} | AUC-ROC: {exp_result['AUC-ROC']:.4f}, F1: {exp_result['Standard-F1']:.4f}")
            else:
                print(f"    ❌ 失败于 {os.path.basename(dataset_path)}: {exp_result['error_message']}")

        if dataset_metrics:
            df_metrics = pd.DataFrame(dataset_metrics)
            avg_metrics = df_metrics.mean().to_dict()
            
            result_summary = {
                'params': params,
                'avg_metrics': avg_metrics
            }
            all_results.append(result_summary)

    if not all_results:
        print("\n所有实验均未成功完成。")
        return

    summary_rows = []
    for res in all_results:
        row = res['params'].copy()
        for metric, value in res['avg_metrics'].items():
            row[f"avg_{metric}"] = value
        summary_rows.append(row)
        
    summary_df = pd.DataFrame(summary_rows)
    summary_df = summary_df.sort_values(by='avg_Standard-F1', ascending=False)

    summary_path = os.path.join(results_dir, 'hyperparameter_search_summary.csv')
    summary_df.to_csv(summary_path, index=False, float_format='%.4f')

    full_results_path = os.path.join(results_dir, 'full_search_results.json')
    with open(full_results_path, 'w') as f:
        json.dump(all_results, f, indent=4)
        
    print("-" * 50)
    print(f"🎉 超参数搜索完成!")
    print(f"🏆 基于 avg_Standard-F1 的最佳参数组合:")
    print(summary_df.iloc[0])
    print(f"\n完整的汇总结果已保存至: {summary_path}")
    print(f"详细结果 (JSON) 已保存至: {full_results_path}")

if __name__ == "__main__":
    main() 