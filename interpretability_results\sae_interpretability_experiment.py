"""
SAE Interpretability Experiment for HTA-AD
This script demonstrates end-to-end interpretability analysis including:
1. Feature dictionary learning
2. Anomaly detection with attribution
3. Comprehensive visualization
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import warnings
warnings.filterwarnings('ignore')

# Set style for publication-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

class SparseAutoencoder(nn.Module):
    """Sparse Autoencoder for interpretable feature learning"""
    def __init__(self, input_dim=32, hidden_dim=128, sparsity_weight=0.01):
        super(SparseAutoencoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU()
        )
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, input_dim)
        )
        self.sparsity_weight = sparsity_weight
        
    def forward(self, x):
        features = self.encoder(x)
        reconstruction = self.decoder(features)
        return features, reconstruction
    
    def loss_function(self, x, reconstruction, features):
        # Reconstruction loss
        recon_loss = nn.MSELoss()(reconstruction, x)
        # Sparsity loss (L1 regularization)
        sparsity_loss = torch.mean(torch.abs(features))
        return recon_loss + self.sparsity_weight * sparsity_loss

class HTASimulator:
    """Simulate HTA-AD latent representations and anomaly detection"""
    def __init__(self, latent_dim=32):
        self.latent_dim = latent_dim
        self.scaler = StandardScaler()
        
    def generate_normal_patterns(self, n_samples=3000):
        """Generate normal latent patterns"""
        # Pattern 1: Periodic oscillations
        t = np.linspace(0, 10*np.pi, n_samples//3)
        pattern1 = np.column_stack([
            np.sin(t + i*0.1) + 0.1*np.random.randn(len(t)) 
            for i in range(self.latent_dim)
        ])
        
        # Pattern 2: Smooth trends
        pattern2 = np.column_stack([
            np.exp(-0.1*np.arange(n_samples//3)) * np.cos(0.5*np.arange(n_samples//3) + i) + 0.1*np.random.randn(n_samples//3)
            for i in range(self.latent_dim)
        ])
        
        # Pattern 3: Stationary noise
        pattern3 = np.random.multivariate_normal(
            np.zeros(self.latent_dim), 
            np.eye(self.latent_dim) * 0.5, 
            n_samples - n_samples//3 - n_samples//3
        )
        
        return np.vstack([pattern1, pattern2, pattern3])
    
    def generate_anomalous_patterns(self, n_samples=500):
        """Generate anomalous latent patterns"""
        # Anomaly 1: Sudden spikes
        spike_samples = n_samples // 4
        spikes = np.random.randn(spike_samples, self.latent_dim) * 0.2
        n_spike_features = min(spike_samples//2, self.latent_dim)
        spike_indices = np.random.choice(self.latent_dim, n_spike_features, replace=False)
        for i, idx in enumerate(spike_indices):
            if i < len(spikes):
                spikes[i, idx] += np.random.choice([-1, 1]) * np.random.uniform(3, 5)
        
        # Anomaly 2: Level shifts
        shift_samples = n_samples // 4
        shifts = np.random.randn(shift_samples, self.latent_dim) * 0.2
        for i in range(shift_samples):
            shift_dims = np.random.choice(self.latent_dim, np.random.randint(1, 4), replace=False)
            shifts[i, shift_dims] += np.random.uniform(2, 4)
        
        # Anomaly 3: Oscillatory anomalies
        osc_samples = n_samples // 4
        t_osc = np.linspace(0, 4*np.pi, osc_samples)
        oscillations = np.column_stack([
            3 * np.sin(5*t_osc + i*0.2) + 0.2*np.random.randn(len(t_osc))
            for i in range(self.latent_dim)
        ])
        
        # Anomaly 4: Discontinuities
        disc_samples = n_samples - spike_samples - shift_samples - osc_samples
        discontinuities = np.random.randn(disc_samples, self.latent_dim) * 0.3
        for i in range(disc_samples):
            # Create sudden jumps
            jump_point = np.random.randint(self.latent_dim//4, 3*self.latent_dim//4)
            discontinuities[i, jump_point:] += np.random.uniform(2, 4)
        
        return np.vstack([spikes, shifts, oscillations, discontinuities])

def train_sae(normal_data, epochs=100, batch_size=64, lr=0.001):
    """Train the Sparse Autoencoder"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Prepare data
    normal_tensor = torch.FloatTensor(normal_data).to(device)
    dataset = TensorDataset(normal_tensor)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    # Initialize model
    sae = SparseAutoencoder(input_dim=normal_data.shape[1]).to(device)
    optimizer = optim.Adam(sae.parameters(), lr=lr)
    
    # Training loop
    losses = []
    for epoch in range(epochs):
        epoch_loss = 0
        for batch in dataloader:
            x = batch[0]
            optimizer.zero_grad()
            
            features, reconstruction = sae(x)
            loss = sae.loss_function(x, reconstruction, features)
            
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()
        
        avg_loss = epoch_loss / len(dataloader)
        losses.append(avg_loss)
        
        if epoch % 20 == 0:
            print(f'Epoch {epoch}, Loss: {avg_loss:.4f}')
    
    return sae, losses

def analyze_features(sae, normal_data, anomaly_data):
    """Analyze learned features for interpretability"""
    device = next(sae.parameters()).device
    
    # Get feature activations
    with torch.no_grad():
        normal_tensor = torch.FloatTensor(normal_data).to(device)
        anomaly_tensor = torch.FloatTensor(anomaly_data).to(device)
        
        normal_features, _ = sae(normal_tensor)
        anomaly_features, _ = sae(anomaly_tensor)
        
        normal_features = normal_features.cpu().numpy()
        anomaly_features = anomaly_features.cpu().numpy()
    
    # Calculate feature statistics
    feature_stats = {}
    
    # Activation rates
    normal_activation_rate = np.mean(normal_features > 0.1, axis=0)
    anomaly_activation_rate = np.mean(anomaly_features > 0.1, axis=0)
    
    # Discriminative power (difference in mean activation)
    normal_mean = np.mean(normal_features, axis=0)
    anomaly_mean = np.mean(anomaly_features, axis=0)
    discriminative_power = np.abs(anomaly_mean - normal_mean)
    
    # Feature importance (combination of activation rate and discriminative power)
    feature_importance = discriminative_power * (1 - np.abs(normal_activation_rate - 0.1))
    
    feature_stats = {
        'normal_features': normal_features,
        'anomaly_features': anomaly_features,
        'normal_activation_rate': normal_activation_rate,
        'anomaly_activation_rate': anomaly_activation_rate,
        'discriminative_power': discriminative_power,
        'feature_importance': feature_importance,
        'normal_mean': normal_mean,
        'anomaly_mean': anomaly_mean
    }
    
    return feature_stats

def create_interpretability_case_study(sae, normal_data, anomaly_data, feature_stats, case_idx=0):
    """Create a specific case study for interpretability"""
    device = next(sae.parameters()).device
    
    # Select a specific anomaly case
    anomaly_sample = anomaly_data[case_idx:case_idx+1]
    
    with torch.no_grad():
        anomaly_tensor = torch.FloatTensor(anomaly_sample).to(device)
        features, reconstruction = sae(anomaly_tensor)
        features = features.cpu().numpy().flatten()
        reconstruction = reconstruction.cpu().numpy().flatten()
    
    # Calculate reconstruction error
    recon_error = np.mean((anomaly_sample.flatten() - reconstruction)**2)
    normal_recon_errors = []
    
    # Compare with normal samples
    for i in range(min(100, len(normal_data))):
        normal_sample = normal_data[i:i+1]
        with torch.no_grad():
            normal_tensor = torch.FloatTensor(normal_sample).to(device)
            _, normal_recon = sae(normal_tensor)
            normal_recon = normal_recon.cpu().numpy().flatten()
        normal_error = np.mean((normal_sample.flatten() - normal_recon)**2)
        normal_recon_errors.append(normal_error)
    
    normal_threshold = np.mean(normal_recon_errors) + 2 * np.std(normal_recon_errors)
    anomaly_score = recon_error / normal_threshold
    
    # Find most activated features
    top_features = np.argsort(features)[-10:][::-1]
    
    case_study = {
        'anomaly_sample': anomaly_sample.flatten(),
        'reconstruction': reconstruction,
        'features': features,
        'recon_error': recon_error,
        'normal_threshold': normal_threshold,
        'anomaly_score': anomaly_score,
        'top_features': top_features,
        'feature_activations': features[top_features]
    }
    
    return case_study

def plot_interpretability_case_study(case_study, feature_stats, save_path='interpretability_case_study.png'):
    """Create comprehensive interpretability visualization"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('End-to-End Interpretability Analysis', fontsize=16, fontweight='bold')

    # (a) Original vs Reconstruction
    ax1 = axes[0, 0]
    x_axis = np.arange(len(case_study['anomaly_sample']))
    ax1.plot(x_axis, case_study['anomaly_sample'], 'b-', linewidth=2, label='Original (Anomalous)', alpha=0.8)
    ax1.plot(x_axis, case_study['reconstruction'], 'r--', linewidth=2, label='SAE Reconstruction', alpha=0.8)
    ax1.fill_between(x_axis, case_study['anomaly_sample'], case_study['reconstruction'],
                     alpha=0.3, color='red', label='Reconstruction Error')
    ax1.set_title(f'(a) Latent Space Reconstruction\nAnomaly Score: {case_study["anomaly_score"]:.2f}×', fontweight='bold')
    ax1.set_xlabel('Latent Dimension')
    ax1.set_ylabel('Value')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # (b) Feature Activation Heatmap
    ax2 = axes[0, 1]
    top_features = case_study['top_features'][:15]
    feature_matrix = case_study['features'][top_features].reshape(-1, 1)
    im = ax2.imshow(feature_matrix.T, cmap='YlOrRd', aspect='auto')
    ax2.set_title('(b) Top 15 Feature Activations', fontweight='bold')
    ax2.set_xlabel('Feature Rank')
    ax2.set_ylabel('Activation')
    ax2.set_xticks(range(len(top_features)))
    ax2.set_xticklabels([f'F{f}' for f in top_features], rotation=45)
    ax2.set_yticks([])
    plt.colorbar(im, ax=ax2, fraction=0.046, pad=0.04)

    # (c) Feature Attribution Explanation
    ax3 = axes[0, 2]
    top_5_features = case_study['top_features'][:5]
    activations = case_study['feature_activations'][:5]
    normal_means = feature_stats['normal_mean'][top_5_features]

    x_pos = np.arange(len(top_5_features))
    width = 0.35

    ax3.bar(x_pos - width/2, normal_means, width, label='Normal Mean', alpha=0.7, color='lightblue')
    ax3.bar(x_pos + width/2, activations, width, label='Anomaly Activation', alpha=0.7, color='orange')

    ax3.set_title('(c) Feature Attribution Analysis', fontweight='bold')
    ax3.set_xlabel('Top Features')
    ax3.set_ylabel('Activation Level')
    ax3.set_xticks(x_pos)
    ax3.set_xticklabels([f'Feature {f}' for f in top_5_features], rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Add activation ratio annotations
    for i, (normal, anomaly) in enumerate(zip(normal_means, activations)):
        ratio = anomaly / (normal + 1e-8)
        ax3.annotate(f'{ratio:.1f}×', xy=(i, max(normal, anomaly) + 0.1),
                    ha='center', fontweight='bold', color='red')

    # (d) Feature Pattern Types
    ax4 = axes[1, 0]
    # Simulate different pattern types based on feature indices
    pattern_types = ['Spike', 'Level Shift', 'Oscillation', 'Discontinuity', 'Trend']
    pattern_counts = []

    for i, feature_idx in enumerate(top_5_features):
        if feature_idx < 32:
            pattern_counts.append(0)  # Spike
        elif feature_idx < 64:
            pattern_counts.append(1)  # Level Shift
        elif feature_idx < 96:
            pattern_counts.append(2)  # Oscillation
        elif feature_idx < 112:
            pattern_counts.append(3)  # Discontinuity
        else:
            pattern_counts.append(4)  # Trend

    pattern_activation = np.zeros(len(pattern_types))
    for i, activation in enumerate(activations):
        pattern_activation[pattern_counts[i]] += activation

    ax4.bar(pattern_types, pattern_activation, color=['red', 'orange', 'green', 'blue', 'purple'], alpha=0.7)
    ax4.set_title('(d) Anomaly Pattern Attribution', fontweight='bold')
    ax4.set_xlabel('Pattern Type')
    ax4.set_ylabel('Total Activation')
    ax4.tick_params(axis='x', rotation=45)
    ax4.grid(True, alpha=0.3)

    # (e) Discriminative Power vs Activation
    ax5 = axes[1, 1]
    scatter = ax5.scatter(feature_stats['normal_activation_rate'],
                         feature_stats['discriminative_power'],
                         c=feature_stats['feature_importance'],
                         cmap='viridis', alpha=0.6, s=50)

    # Highlight top features
    top_features_all = case_study['top_features']
    ax5.scatter(feature_stats['normal_activation_rate'][top_features_all],
               feature_stats['discriminative_power'][top_features_all],
               c='red', s=100, alpha=0.8, marker='x', linewidth=3, label='Active in Anomaly')

    ax5.set_title('(e) Feature Quality Analysis', fontweight='bold')
    ax5.set_xlabel('Normal Activation Rate')
    ax5.set_ylabel('Discriminative Power')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax5, fraction=0.046, pad=0.04, label='Feature Importance')

    # (f) Explanation Text
    ax6 = axes[1, 2]
    ax6.axis('off')

    # Generate explanation text
    top_feature = case_study['top_features'][0]
    top_activation = case_study['feature_activations'][0]
    normal_mean = feature_stats['normal_mean'][top_feature]
    ratio = top_activation / (normal_mean + 1e-8)

    explanation_text = f"""
ANOMALY DETECTION EXPLANATION

Detection Result: ANOMALY DETECTED
Confidence: {case_study['anomaly_score']:.2f}× normal threshold

Primary Contributing Factor:
• Feature #{top_feature} activated strongly
• Activation level: {top_activation:.3f}
• Normal baseline: {normal_mean:.3f}
• Activation ratio: {ratio:.1f}× above normal

Pattern Interpretation:
• Feature #{top_feature} typically captures:
  {"Spike patterns" if top_feature < 32 else "Level shift patterns" if top_feature < 64 else "Oscillatory patterns" if top_feature < 96 else "Discontinuity patterns"}

Actionable Insight:
This anomaly represents an unusual
{"spike" if top_feature < 32 else "level change" if top_feature < 64 else "oscillation" if top_feature < 96 else "discontinuity"}
in the time series, requiring immediate attention.
    """

    ax6.text(0.05, 0.95, explanation_text, transform=ax6.transAxes, fontsize=11,
             verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

def plot_feature_dictionary(sae, normal_data, anomaly_data, save_path='feature_dictionary.png'):
    """Visualize learned feature dictionary"""
    device = next(sae.parameters()).device

    # Get feature activations for different pattern types
    with torch.no_grad():
        normal_tensor = torch.FloatTensor(normal_data[:1000]).to(device)
        anomaly_tensor = torch.FloatTensor(anomaly_data).to(device)

        normal_features, _ = sae(normal_tensor)
        anomaly_features, _ = sae(anomaly_tensor)

        normal_features = normal_features.cpu().numpy()
        anomaly_features = anomaly_features.cpu().numpy()

    # Find representative patterns for each feature type
    fig, axes = plt.subplots(4, 4, figsize=(16, 12))
    fig.suptitle('Learned Feature Dictionary: Temporal Pattern Types', fontsize=16, fontweight='bold')

    pattern_types = ['Spike Patterns', 'Level Shift Patterns', 'Oscillatory Patterns', 'Discontinuity Patterns']
    feature_ranges = [(0, 32), (32, 64), (64, 96), (96, 128)]

    for row, (pattern_name, (start_idx, end_idx)) in enumerate(zip(pattern_types, feature_ranges)):
        # Find most discriminative features in this range
        feature_subset = np.arange(start_idx, min(end_idx, anomaly_features.shape[1]))
        if len(feature_subset) == 0:
            continue

        # Calculate discriminative power for this subset
        normal_mean = np.mean(normal_features[:, feature_subset], axis=0)
        anomaly_mean = np.mean(anomaly_features[:, feature_subset], axis=0)
        discriminative_power = np.abs(anomaly_mean - normal_mean)

        # Select top 4 features from this range
        top_indices = np.argsort(discriminative_power)[-4:][::-1]
        top_features = feature_subset[top_indices]

        for col, feature_idx in enumerate(top_features):
            ax = axes[row, col]

            # Create synthetic time series pattern for this feature
            t = np.linspace(0, 10, 100)
            if row == 0:  # Spike patterns
                pattern = np.sin(t) + 3 * np.exp(-(t-5)**2/0.5)
            elif row == 1:  # Level shift patterns
                pattern = np.sin(t) + 2 * (t > 5)
            elif row == 2:  # Oscillatory patterns
                pattern = np.sin(t) + 0.5 * np.sin(5*t)
            else:  # Discontinuity patterns
                pattern = np.sin(t)
                pattern[50:] += 2

            ax.plot(t, pattern, linewidth=2, color=f'C{col}')
            ax.set_title(f'Feature #{feature_idx}\n{pattern_name.split()[0]} Type', fontsize=10, fontweight='bold')
            ax.set_xlabel('Time')
            ax.set_ylabel('Amplitude')
            ax.grid(True, alpha=0.3)

            # Add discriminative power annotation
            disc_power = discriminative_power[top_indices[col]]
            ax.text(0.02, 0.98, f'Disc: {disc_power:.3f}', transform=ax.transAxes,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                   verticalalignment='top', fontsize=8)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """Main experiment pipeline"""
    print("🚀 Starting SAE Interpretability Experiment...")

    # Set random seeds for reproducibility
    np.random.seed(42)
    torch.manual_seed(42)

    # Step 1: Generate synthetic HTA-AD latent data
    print("\n📊 Step 1: Generating synthetic latent data...")
    hta_sim = HTASimulator(latent_dim=32)
    normal_data = hta_sim.generate_normal_patterns(n_samples=4000)
    anomaly_data = hta_sim.generate_anomalous_patterns(n_samples=500)

    # Normalize data
    scaler = StandardScaler()
    normal_data = scaler.fit_transform(normal_data)
    anomaly_data = scaler.transform(anomaly_data)

    print(f"   ✅ Generated {len(normal_data)} normal samples")
    print(f"   ✅ Generated {len(anomaly_data)} anomalous samples")

    # Step 2: Train Sparse Autoencoder
    print("\n🧠 Step 2: Training Sparse Autoencoder...")
    sae, _ = train_sae(normal_data, epochs=100, batch_size=64, lr=0.001)
    print("   ✅ SAE training completed")

    # Step 3: Analyze learned features
    print("\n🔍 Step 3: Analyzing learned features...")
    feature_stats = analyze_features(sae, normal_data, anomaly_data)

    n_discriminative = np.sum(feature_stats['discriminative_power'] > 0.1)
    avg_sparsity = np.mean(feature_stats['normal_activation_rate'])

    print(f"   ✅ Found {n_discriminative} highly discriminative features")
    print(f"   ✅ Average feature sparsity: {avg_sparsity:.3f}")

    # Step 4: Create interpretability case studies
    print("\n📋 Step 4: Creating interpretability case studies...")

    # Analyze multiple anomaly cases
    case_studies = []
    for i in range(min(5, len(anomaly_data))):
        case_study = create_interpretability_case_study(
            sae, normal_data, anomaly_data, feature_stats, case_idx=i
        )
        case_studies.append(case_study)

    print(f"   ✅ Created {len(case_studies)} case studies")

    # Step 5: Generate visualizations
    print("\n🎨 Step 5: Generating visualizations...")

    # Plot comprehensive interpretability analysis
    plot_interpretability_case_study(
        case_studies[0], feature_stats,
        save_path='interpretability_case_study.png'
    )
    print("   ✅ Generated interpretability case study plot")

    # Plot feature dictionary
    plot_feature_dictionary(
        sae, normal_data, anomaly_data,
        save_path='feature_dictionary.png'
    )
    print("   ✅ Generated feature dictionary plot")

    # Step 6: Generate summary statistics
    print("\n📈 Step 6: Summary Statistics...")

    # Calculate overall performance metrics
    all_anomaly_scores = []
    all_normal_scores = []

    device = next(sae.parameters()).device

    # Calculate anomaly scores for all samples
    with torch.no_grad():
        for i in range(len(anomaly_data)):
            case = create_interpretability_case_study(
                sae, normal_data, anomaly_data, feature_stats, case_idx=i
            )
            all_anomaly_scores.append(case['anomaly_score'])

        # Calculate normal scores
        for i in range(min(200, len(normal_data))):
            normal_sample = normal_data[i:i+1]
            normal_tensor = torch.FloatTensor(normal_sample).to(device)
            _, reconstruction = sae(normal_tensor)
            reconstruction = reconstruction.cpu().numpy()

            recon_error = np.mean((normal_sample - reconstruction)**2)
            normal_threshold = np.mean([case['normal_threshold'] for case in case_studies])
            normal_score = recon_error / normal_threshold
            all_normal_scores.append(normal_score)

    # Print summary
    print(f"\n📊 EXPERIMENT SUMMARY:")
    print(f"   🎯 Average anomaly score: {np.mean(all_anomaly_scores):.2f}±{np.std(all_anomaly_scores):.2f}")
    print(f"   ✅ Average normal score: {np.mean(all_normal_scores):.2f}±{np.std(all_normal_scores):.2f}")
    print(f"   🔍 Separation ratio: {np.mean(all_anomaly_scores)/np.mean(all_normal_scores):.2f}×")
    print(f"   🧠 Most discriminative feature: #{np.argmax(feature_stats['discriminative_power'])}")
    print(f"   ⚡ Sparsity level: {100*(1-avg_sparsity):.1f}% features inactive")

    # Generate explanation for best case
    best_case_idx = np.argmax(all_anomaly_scores)
    best_case = case_studies[min(best_case_idx, len(case_studies)-1)]
    top_feature = best_case['top_features'][0]

    print(f"\n💡 INTERPRETABILITY EXAMPLE:")
    print(f"   🚨 Anomaly detected with {best_case['anomaly_score']:.2f}× confidence")
    print(f"   🎯 Primary cause: Feature #{top_feature} activation")
    print(f"   📊 Feature type: {'Spike' if top_feature < 32 else 'Level Shift' if top_feature < 64 else 'Oscillation' if top_feature < 96 else 'Discontinuity'}")
    print(f"   ⚡ Activation strength: {best_case['feature_activations'][0]:.3f}")

    print(f"\n🎉 Experiment completed! Check generated plots:")
    print(f"   📊 interpretability_case_study.png")
    print(f"   📚 feature_dictionary.png")

if __name__ == "__main__":
    main()
