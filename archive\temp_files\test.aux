\relax 
\@writefile{toc}{\contentsline {section}{\numberline {I}Introduction}{1}{}\protected@file@percent }
\newlabel{sec:intro}{{I}{1}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces Latent space visualization (t-SNE) on a representative periodic dataset. This comparison reveals the architectural limitations of a standard Transformer, which produces fragmented local trajectories, failing to capture the global periodic structure. In contrast, HTA-AD learns a highly structured, discrete orbit, correctly identifying the signal's recurring states.}}{1}{}\protected@file@percent }
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{fig:evidence}{{1}{1}{}{}{}}
\citation{zhou2021informer,xu2022anomalytransformer}
\citation{paparrizos2024tsb}
\citation{deng2021graph,jin2024survey}
\citation{deng2021graph}
\citation{deng2021graph}
\citation{zhao2024graph}
\citation{lecun1998gradient}
\citation{bai2018empirical}
\citation{xu2024permutation}
\citation{zeng2023are}
\@writefile{toc}{\contentsline {section}{\numberline {II}Related Work}{2}{}\protected@file@percent }
\newlabel{sec:related_work}{{II}{2}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-A}}The Complexity Dilemma: From Unstructured to Structured Approaches}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {II-A}1}Unstructured Complexity: The Transformer Paradigm}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {II-A}2}Structured Complexity: The Rise of Graph Neural Networks}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-B}}The Architectural Heart: Inductive Bias in Time Series Modeling}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {II-B}1}The "Right" Biases for Time Series: Locality and Temporality}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {II-B}2}The Transformer's "Structural Misalignment"}{2}{}\protected@file@percent }
\citation{paparrizos2024tsb}
\citation{paparrizos2024tsb}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-C}}The Specialization Curse and Evaluation Standards}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-D}}Our Positioning}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {III}Methodology}{3}{}\protected@file@percent }
\newlabel{sec:methodology}{{III}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}Overall Architecture}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}Encoder}{3}{}\protected@file@percent }
\newlabel{eq:encoder_cnn}{{1}{3}{}{}{}}
\newlabel{eq:encoder_tcn}{{2}{3}{}{}{}}
\newlabel{eq:encoder_latent}{{3}{3}{}{}{}}
\newlabel{eq:receptive_field}{{4}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}1}Convolutional Downsampling}{3}{}\protected@file@percent }
\newlabel{eq:cnn_downsampling}{{6}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}2}Non-Causal Temporal Modeling}{4}{}\protected@file@percent }
\newlabel{eq:tcn_conv}{{7}{4}{}{}{}}
\newlabel{eq:tcn_block}{{8}{4}{}{}{}}
\newlabel{eq:tcn_residual}{{9}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}3}Latent Space Projection}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}Symmetric Decoder}{4}{}\protected@file@percent }
\newlabel{eq:decoder_fc}{{10}{4}{}{}{}}
\newlabel{eq:decoder_tcn}{{11}{4}{}{}{}}
\newlabel{eq:decoder_cnn}{{12}{4}{}{}{}}
\newlabel{eq:cnn_upsampling}{{13}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-D}}Training and Anomaly Scoring Framework}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-D}1}Data Preprocessing}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-D}2}Training Objective}{4}{}\protected@file@percent }
\newlabel{eq:mse}{{14}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-D}3}Anomaly Score Calculation}{4}{}\protected@file@percent }
\newlabel{eq:window_error}{{15}{4}{}{}{}}
\newlabel{eq:point_score}{{16}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-E}}Sparse Autoencoder for Interpretable Feature Learning}{5}{}\protected@file@percent }
\newlabel{subsec:sae_framework}{{\mbox  {III-E}}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-E}1}SAE Architecture and Training}{5}{}\protected@file@percent }
\newlabel{eq:sae_encoder}{{17}{5}{}{}{}}
\newlabel{eq:sae_decoder}{{18}{5}{}{}{}}
\newlabel{eq:sae_loss}{{19}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-E}2}Feature Attribution and Purification}{5}{}\protected@file@percent }
\newlabel{eq:sae_encode}{{20}{5}{}{}{}}
\newlabel{eq:mask_irrelevant}{{21}{5}{}{}{}}
\newlabel{eq:decode_irrelevant}{{22}{5}{}{}{}}
\newlabel{eq:purification}{{23}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-F}}Computational Complexity Analysis}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {IV}Experiments}{5}{}\protected@file@percent }
\newlabel{sec:experiments}{{IV}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-A}}Experimental Setup}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}1}Datasets}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}2}Baseline Models}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}3}Evaluation Metrics}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}4}Statistical Analysis}{5}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces Performance comparison on TSB-AD univariate benchmark.}}{6}{}\protected@file@percent }
\newlabel{tab:univariate_core}{{I}{6}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {II}{\ignorespaces Performance comparison on TSB-AD multivariate benchmark.}}{6}{}\protected@file@percent }
\newlabel{tab:multivariate_core}{{II}{6}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}5}Implementation Details}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-B}}Main Results: Breaking the Specialization Curse}{6}{}\protected@file@percent }
\newlabel{ssec:main_results}{{\mbox  {IV-B}}{6}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-C}}In-depth Analysis: Architectural Inductive Bias}{6}{}\protected@file@percent }
\newlabel{ssec:in_depth_analysis}{{\mbox  {IV-C}}{6}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}1}The Training Set Shuffling Experiment}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}2}Visualization of Latent Space and Reconstruction}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}3}Reconstruction Mechanism Analysis}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-D}}Interpretability Analysis: SAE Feature Attribution}{7}{}\protected@file@percent }
\newlabel{subsec:interpretability}{{\mbox  {IV-D}}{7}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-D}1}Feature Discovery and Characterization}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-D}2}Anomaly Attribution Process}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-D}3}Latent Space Purification}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-E}}Ablation Study}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {V}Discussion}{7}{}\protected@file@percent }
\newlabel{sec:discussion}{{V}{7}{}{}{}}
\@writefile{toc}{\contentsline {section}{\numberline {VI}Conclusion}{7}{}\protected@file@percent }
\newlabel{sec:conclusion}{{VI}{7}{}{}{}}
\bibstyle{IEEEtran}
\bibdata{IEEEabrv,references}
\@writefile{lot}{\contentsline {table}{\numberline {III}{\ignorespaces Ablation study of HTA-AD's components on a representative subset of 10 datasets from TSB-AD benchmark.}}{8}{}\protected@file@percent }
\newlabel{tab:ablation}{{III}{8}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces The overall hourglass architecture of HTA-AD. An input window $X \in \mathbb  {R}^{W \times D}$ is passed through a three-stage process of encoding, bottleneck projection, and symmetric decoding to produce the reconstructed window $\hat  {X} \in \mathbb  {R}^{W \times D}$. This unified architecture seamlessly handles both univariate ($D=1$) and multivariate ($D>1$) time series without any modification.}}{9}{}\protected@file@percent }
\newlabel{fig:model_architecture}{{2}{9}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces CNN Feature Extraction Analysis. Left: CNN downsampling compresses complex signals from 100 to 50 points (2:1 ratio) while preserving essential patterns. Right: Multi-channel feature diversity demonstrates that different CNN channels learn distinct temporal patterns (pulses, modulation, trends), with variance values indicating feature specialization.}}{10}{}\protected@file@percent }
\newlabel{fig:cnn_analysis}{{3}{10}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces The detailed structure of a TCN Block. It consists of two dilated non-causal convolutional layers with WeightNorm, ReLU, and Dropout. A residual connection from the input to the output helps stabilize training for deeper networks.}}{10}{}\protected@file@percent }
\newlabel{fig:tcn_block}{{4}{10}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces TCN Receptive Field Growth Comparison. The dilated convolutions in TCN achieve exponential receptive field growth (reaching 187 at layer 5) compared to linear growth in standard convolutions (reaching only 31), demonstrating 6× efficiency in capturing long-range dependencies with the same number of layers.}}{10}{}\protected@file@percent }
\newlabel{fig:tcn_efficiency}{{5}{10}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {6}{\ignorespaces SAE Training Phase. The SAE learns to reconstruct HTA-AD latent vectors with sparsity constraints, discovering interpretable feature activations through the loss function $\mathcal  {L} = ||z - \hat  {z}||_2^2 + \lambda ||f||_1$. This phase establishes a sparse dictionary of temporal features from over 4 million latent vectors.}}{10}{}\protected@file@percent }
\newlabel{fig:sae_train}{{6}{10}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7}{\ignorespaces SAE Inference Phase with Purification. The process: (1) Extract feature activations via SAE encoder, (2) Apply irrelevant mask to identify unwanted features, (3) Decode irrelevant activations to get their contribution, (4) Subtract $\alpha \times $ irrelevant contribution from original latent vector to obtain purified representation for anomaly detection.}}{11}{}\protected@file@percent }
\newlabel{fig:sae_inference}{{7}{11}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {8}{\ignorespaces Performance ranking on TSB-AD benchmark using VUS-PR metric. HTA-AD (highlighted in red) achieves state-of-the-art performance on both univariate (0.44) and multivariate (0.39) benchmarks with a single unified architecture, demonstrating the first successful solution to the specialization curse in time series anomaly detection. Both charts use identical height and y-axis scaling for fair visual comparison.}}{12}{}\protected@file@percent }
\newlabel{fig:main_results}{{8}{12}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {9}{\ignorespaces The Training Set Shuffling Experiment on a dataset with strong temporal dependencies. The top row shows the absolute VUS-PR and VUS-ROC performance comparison. The bottom-left subplot visualizes the original vs. a shuffled sequence. The bottom-right subplot shows the performance degradation percentage, highlighting the contrasting behavior between HTA-AD and Anomaly Transformer.}}{12}{}\protected@file@percent }
\newlabel{fig:shuffling_exp}{{9}{12}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {10}{\ignorespaces Reconstruction-based Anomaly Detection Mechanism. The dual-axis plot shows original vs. reconstructed signals (left y-axis) and reconstruction errors (right y-axis). On this representative example, HTA-AD achieves \textbf  {3.3× error amplification} for anomalies while maintaining low reconstruction error (0.108) for normal patterns, demonstrating effective anomaly detection capability. This example illustrates the typical behavior observed across multiple test cases.}}{12}{}\protected@file@percent }
\newlabel{fig:reconstruction_analysis}{{10}{12}{}{}{}}
