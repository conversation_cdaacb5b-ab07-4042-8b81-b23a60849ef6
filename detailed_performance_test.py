#!/usr/bin/env python3
"""
详细的性能测试，验证SAE版本的训练时间和性能
"""

import numpy as np
import sys
import time
import warnings
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

def run_detailed_benchmark():
    """运行详细的基准测试"""
    print("🚀 详细性能基准测试")
    print("=" * 60)
    
    # 生成更大的测试数据集
    np.random.seed(42)
    n_samples = 1000
    
    # 生成复杂的时间序列数据
    t = np.linspace(0, 8*np.pi, n_samples)
    data = np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.randn(n_samples)
    
    # 添加异常
    anomaly_indices = []
    for _ in range(30):
        idx = np.random.randint(100, n_samples-100)
        data[idx] += np.random.normal(3, 0.5)
        anomaly_indices.append(idx)
    
    # 分割数据
    split_point = int(len(data) * 0.7)
    train_data = data[:split_point]
    test_data = data[split_point:]
    
    print(f"📊 数据集信息:")
    print(f"  - 训练数据: {len(train_data)} 点")
    print(f"  - 测试数据: {len(test_data)} 点")
    print(f"  - 异常点数: {len(anomaly_indices)}")
    
    # 测试配置
    test_configs = [
        {
            'name': 'HTA-AD (基础)',
            'function': 'run_HTA_AD',
            'params': {
                'window_size': 64,
                'epochs': 10,
                'lr': 1e-3,
                'batch_size': 32,
                'latent_dim': 16
            }
        },
        {
            'name': 'HTA-AD-SAE (核心)',
            'function': 'run_HTA_AD_SAE',
            'params': {
                'window_size': 64,
                'epochs': 10,
                'lr': 1e-3,
                'batch_size': 32,
                'latent_dim': 16,
                'sae_hidden_dim': 64,
                'sae_sparsity_weight': 0.01
            }
        }
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n🧪 测试 {config['name']}")
        print("-" * 40)
        
        try:
            # 导入相应的函数
            if config['function'] == 'run_HTA_AD':
                from TSB_AD.model_wrapper import run_HTA_AD as test_func
            else:
                from TSB_AD.model_wrapper import run_HTA_AD_SAE as test_func
            
            # 记录详细的训练过程
            print(f"  🏋️ 开始训练 {config['name']}...")
            start_time = time.time()
            
            # 运行模型
            if config['function'] == 'run_HTA_AD':
                scores = test_func(train_data, test_data, **config['params'])
            else:
                scores = test_func(train_data, test_data, **config['params'])
            
            end_time = time.time()
            training_time = end_time - start_time
            
            # 计算统计信息
            results[config['name']] = {
                'training_time': training_time,
                'scores_mean': np.mean(scores),
                'scores_std': np.std(scores),
                'scores_min': np.min(scores),
                'scores_max': np.max(scores),
                'scores_range': np.max(scores) - np.min(scores)
            }
            
            print(f"  ✅ {config['name']} 完成")
            print(f"  ⏱️ 训练时间: {training_time:.2f}s")
            print(f"  📊 分数统计:")
            print(f"    - 平均值: {results[config['name']]['scores_mean']:.4f}")
            print(f"    - 标准差: {results[config['name']]['scores_std']:.4f}")
            print(f"    - 范围: [{results[config['name']]['scores_min']:.4f}, {results[config['name']]['scores_max']:.4f}]")
            
        except Exception as e:
            print(f"  ❌ {config['name']} 失败: {e}")
            import traceback
            traceback.print_exc()
            results[config['name']] = {'error': str(e)}
    
    return results


def analyze_detailed_results(results):
    """分析详细结果"""
    print("\n" + "=" * 60)
    print("🎯 详细性能分析")
    print("=" * 60)
    
    # 检查是否有有效结果
    valid_results = {k: v for k, v in results.items() if 'error' not in v}
    
    if len(valid_results) < 2:
        print("❌ 没有足够的有效结果进行对比")
        return
    
    basic_key = 'HTA-AD (基础)'
    sae_key = 'HTA-AD-SAE (核心)'
    
    if basic_key in valid_results and sae_key in valid_results:
        basic = valid_results[basic_key]
        sae = valid_results[sae_key]
        
        print("⏱️ 训练时间对比:")
        print(f"  {basic_key:20}: {basic['training_time']:8.2f}s")
        print(f"  {sae_key:20}: {sae['training_time']:8.2f}s")
        print(f"  时间比例 (SAE/基础):        {sae['training_time']/basic['training_time']:8.2f}x")
        
        if sae['training_time'] > basic['training_time']:
            print("  ✅ 正常：SAE版本训练时间更长")
        else:
            print("  ⚠️ 异常：SAE版本训练时间更短")
            print("     可能原因：")
            print("     1. SAE模型收敛更快")
            print("     2. 批处理效率更高")
            print("     3. 模型复杂度实际更低")
        
        print(f"\n📊 异常分数对比:")
        print(f"  {basic_key:20}:")
        print(f"    平均值: {basic['scores_mean']:8.4f}")
        print(f"    标准差: {basic['scores_std']:8.4f}")
        print(f"    范围:   {basic['scores_range']:8.4f}")
        
        print(f"  {sae_key:20}:")
        print(f"    平均值: {sae['scores_mean']:8.4f}")
        print(f"    标准差: {sae['scores_std']:8.4f}")
        print(f"    范围:   {sae['scores_range']:8.4f}")
        
        # 分析分数差异
        score_ratio = sae['scores_mean'] / basic['scores_mean']
        print(f"  分数比例 (SAE/基础):        {score_ratio:8.2f}x")
        
        if abs(score_ratio - 1.0) < 0.5:
            print("  ✅ 分数范围合理")
        else:
            print("  ⚠️ 分数差异较大，可能需要调整参数")


def main():
    """主函数"""
    print("🔬 HTA-AD 详细性能分析")
    print("=" * 60)
    
    try:
        # 运行详细基准测试
        results = run_detailed_benchmark()
        
        # 分析结果
        analyze_detailed_results(results)
        
        print("\n" + "=" * 60)
        print("📝 总结")
        print("=" * 60)
        
        valid_count = len([r for r in results.values() if 'error' not in r])
        total_count = len(results)
        
        print(f"✅ 成功测试: {valid_count}/{total_count}")
        
        if valid_count == total_count:
            print("🎉 所有测试成功完成！")
            
            # 给出建议
            if 'HTA-AD-SAE (核心)' in results and 'error' not in results['HTA-AD-SAE (核心)']:
                sae_time = results['HTA-AD-SAE (核心)']['training_time']
                basic_time = results.get('HTA-AD (基础)', {}).get('training_time', 0)
                
                if basic_time > 0 and sae_time < basic_time:
                    print("\n💡 建议:")
                    print("  - SAE版本训练时间较短可能是正常的，因为：")
                    print("    1. 核心模型优化了训练流程")
                    print("    2. SAE可能帮助模型更快收敛")
                    print("    3. 批处理和内存使用更高效")
                    print("  - 重点关注检测性能而非训练时间")
        else:
            print("⚠️ 部分测试失败，需要进一步调试")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
