\documentclass{article}
\usepackage{graphicx}
\usepackage{subcaption}
\usepackage{float}

\begin{document}

\title{Interpretability Framework Example}
\author{HTA-AD}
\maketitle

\section{Option 1: Side-by-Side Layout}

\begin{figure}[H]
\centering
\begin{subfigure}{0.48\textwidth}
    \includegraphics[width=\textwidth]{figures/feature_dictionary.pdf}
    \caption{Feature Dictionary}
    \label{fig:feature_dict_sub}
\end{subfigure}
\hfill
\begin{subfigure}{0.48\textwidth}
    \includegraphics[width=\textwidth]{figures/anomaly_attribution.pdf}
    \caption{Anomaly Attribution}
    \label{fig:anomaly_attr_sub}
\end{subfigure}
\caption{SAE-based Interpretable Anomaly Detection Framework. (a) The learned feature dictionary shows four distinct temporal pattern types discovered through unsupervised learning. (b) Real-world anomaly attribution example demonstrating the three-stage attribution process from detection to pattern classification to feature identification.}
\label{fig:interpretability_framework}
\end{figure}

\section{Option 2: Stacked Layout}

\begin{figure}[H]
\centering
\begin{subfigure}{\textwidth}
    \includegraphics[width=\textwidth]{figures/feature_dictionary.pdf}
    \caption{Learned Feature Dictionary}
    \label{fig:feature_dict_stack}
\end{subfigure}

\vspace{0.5cm}

\begin{subfigure}{\textwidth}
    \includegraphics[width=\textwidth]{figures/anomaly_attribution.pdf}
    \caption{Anomaly Attribution Example}
    \label{fig:anomaly_attr_stack}
\end{subfigure}
\caption{Interpretability Framework Components. The SAE framework enables both feature discovery and anomaly attribution through systematic decomposition of the latent space.}
\label{fig:interpretability_stacked}
\end{figure}

\section{Option 3: Individual Figures}

\begin{figure}[H]
\centering
\includegraphics[width=\textwidth]{figures/feature_dictionary.pdf}
\caption{Learned Feature Dictionary from SAE Training. The sparse autoencoder discovers four distinct temporal pattern types through unsupervised learning on over 4 million latent vectors: spike patterns (top-left), level shift patterns (top-right), oscillatory patterns (bottom-left), and discontinuity patterns (bottom-right).}
\label{fig:feature_dictionary_individual}
\end{figure}

\begin{figure}[H]
\centering
\includegraphics[width=\textwidth]{figures/anomaly_attribution.pdf}
\caption{Real-world Anomaly Attribution Example. The three-stage attribution process: detected anomaly with level shift behavior (left), pattern attribution scores showing level shift dominance (center), and key feature activations with Feature \#54 exhibiting strongest response (right).}
\label{fig:anomaly_attribution_individual}
\end{figure}

\end{document}
