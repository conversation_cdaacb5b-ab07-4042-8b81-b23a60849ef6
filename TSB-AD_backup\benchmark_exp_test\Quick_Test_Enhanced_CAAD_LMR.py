# -*- coding: utf-8 -*-
# Enhanced CAAD-LMR Quick Test Script
# 增强版CAAD-LMR快速测试脚本

import pandas as pd
import numpy as np
import torch
import time
import argparse
try:
    from Run_Enhanced_CAAD_LMR_Benchmark import (
        run_Enhanced_CAAD_LMR_Semisupervised,
        ENHANCED_MODELS,
        benchmark_single_file
    )
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保Run_Enhanced_CAAD_LMR_Benchmark.py文件存在且可访问")
    exit(1)
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.utils.slidingWindows import find_length_rank

def quick_test_single_file():
    """快速测试单个文件"""
    
    print("Enhanced CAAD-LMR 快速测试")
    print("="*50)
    
    # 测试配置
    dataset_dir = "../Datasets/TSB-AD-U/"
    test_file = "001_NAB_id_1_Facility_tr_1007_1st_2014.csv"
    model_type = "efficient"  # 使用最轻量的模型
    
    print(f"测试文件: {test_file}")
    print(f"模型配置: {ENHANCED_MODELS[model_type]['description']}")
    print(f"预计内存: {ENHANCED_MODELS[model_type]['memory_usage']}")
    
    try:
        # 运行单文件测试
        result = benchmark_single_file(
            filename=test_file,
            dataset_dir=dataset_dir,
            model_config=ENHANCED_MODELS[model_type],
            domain="general"
        )
        
        if 'error' in result:
            print(f"❌ 测试失败: {result['error']}")
            return False
        else:
            print(f"✅ 测试成功!")
            print(f"运行时间: {result['run_time']:.2f}秒")
            print(f"最佳策略: {result['best_strategy']}")
            
            best_metrics = result['all_results'][result['best_strategy']]
            print(f"最佳F1分数: {best_metrics['F1']:.4f}")
            print(f"精确率: {best_metrics['Precision']:.4f}")
            print(f"召回率: {best_metrics['Recall']:.4f}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False

def quick_test_synthetic_data():
    """使用合成数据进行快速测试"""
    
    print("\n使用合成数据进行快速测试")
    print("-" * 30)
    
    # 生成合成时间序列数据
    np.random.seed(42)
    
    # 正常数据
    n_samples = 1000
    n_features = 1
    
    # 基础趋势
    t = np.linspace(0, 4*np.pi, n_samples)
    normal_data = np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.normal(0, 1, n_samples)
    
    # 添加异常点
    anomaly_indices = [200, 300, 500, 700, 800]
    labels = np.zeros(n_samples, dtype=int)
    
    for idx in anomaly_indices:
        if idx < n_samples:
            normal_data[idx] += np.random.choice([-1, 1]) * np.random.uniform(3, 5)
            labels[idx] = 1
    
    # 转换为所需格式
    data = normal_data.reshape(-1, 1)
    
    print(f"合成数据形状: {data.shape}")
    print(f"异常点数量: {np.sum(labels)}")
    print(f"异常率: {np.sum(labels)/len(labels)*100:.2f}%")
    
    # 分割训练测试集
    train_size = int(0.7 * n_samples)
    data_train = data[:train_size]
    
    # 配置模型
    model_config = ENHANCED_MODELS["efficient"]
    enhanced_hp = {
        'llm_model_name': model_config['llm_model_name'],
        'domain': 'general',
        'window_size': 20,  # 减小窗口大小加速测试
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'use_multi_gpu': True,
        'normalize': True
    }
    
    print("开始训练和预测...")
    start_time = time.time()
    
    try:
        # 运行检测
        scores = run_Enhanced_CAAD_LMR_Semisupervised(data_train, data, **enhanced_hp)
        
        end_time = time.time()
        run_time = end_time - start_time
        
        print(f"✅ 合成数据测试成功!")
        print(f"运行时间: {run_time:.2f}秒")
        print(f"异常分数范围: [{np.min(scores):.4f}, {np.max(scores):.4f}]")
        
        # 简单评估
        threshold = np.percentile(scores, 95)
        pred = scores > threshold
        
        # 计算基本指标
        tp = np.sum((pred == 1) & (labels == 1))
        fp = np.sum((pred == 1) & (labels == 0))
        fn = np.sum((pred == 0) & (labels == 1))
        tn = np.sum((pred == 0) & (labels == 0))
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        print(f"基本评估结果:")
        print(f"  精确率: {precision:.4f}")
        print(f"  召回率: {recall:.4f}")
        print(f"  F1分数: {f1:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 合成数据测试失败: {str(e)}")
        return False

def environment_check():
    """环境检查"""
    
    print("环境检查")
    print("-" * 20)
    
    # 检查PyTorch
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"CUDA设备数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  设备 {i}: {torch.cuda.get_device_name(i)}")
            print(f"  显存: {torch.cuda.get_device_properties(i).total_memory / 1e9:.1f} GB")
    
    # 检查依赖包
    try:
        import transformers
        print(f"Transformers版本: {transformers.__version__}")
    except ImportError:
        print("❌ Transformers未安装")
        return False
    
    try:
        import sklearn
        print(f"Scikit-learn版本: {sklearn.__version__}")
    except ImportError:
        print("❌ Scikit-learn未安装")
        return False
    
    try:
        import scipy
        print(f"Scipy版本: {scipy.__version__}")
    except ImportError:
        print("❌ Scipy未安装")
        return False
    
    print("✅ 环境检查通过")
    return True

def main():
    """主测试函数"""
    
    parser = argparse.ArgumentParser(description='Enhanced CAAD-LMR Quick Test')
    parser.add_argument('--test_real_data', action='store_true', 
                       help='测试真实数据文件')
    parser.add_argument('--test_synthetic', action='store_true',
                       help='测试合成数据')
    parser.add_argument('--check_env', action='store_true',
                       help='仅进行环境检查')
    
    args = parser.parse_args()
    
    # 如果没有指定参数，则运行所有测试
    if not (args.test_real_data or args.test_synthetic or args.check_env):
        args.test_real_data = True
        args.test_synthetic = True
        args.check_env = True
    
    print("Enhanced CAAD-LMR 快速测试工具")
    print("=" * 60)
    
    overall_success = True
    
    # 环境检查
    if args.check_env:
        if not environment_check():
            overall_success = False
    
    # 合成数据测试
    if args.test_synthetic:
        if not quick_test_synthetic_data():
            overall_success = False
    
    # 真实数据测试
    if args.test_real_data:
        if not quick_test_single_file():
            overall_success = False
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 所有测试通过! Enhanced CAAD-LMR工作正常")
        print("\n可以使用以下命令运行完整benchmark:")
        print("python Run_Enhanced_CAAD_LMR_Benchmark.py --model_type efficient --max_files 5")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    return overall_success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1) 