#!/usr/bin/env python3
"""
Test HTA-AD Paper Compliant Implementation on TSB-AD Dataset
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import sys
import os
import time
import warnings
from sklearn.metrics import roc_auc_score, average_precision_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

warnings.filterwarnings('ignore')

# Add paths
sys.path.append('.')
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

from core.models.hta_ad_paper_compliant import HTAADBasic, PostHocSAE

# Import TSB-AD evaluation metrics
try:
    from TSB_AD.evaluation.metrics import get_metrics
    TSB_AD_AVAILABLE = True
except ImportError:
    print("Warning: TSB-AD evaluation metrics not available, using basic metrics")
    TSB_AD_AVAILABLE = False

def parse_dataset_filename(filename):
    """
    Parse TSB-AD dataset filename to extract train/test split info
    Example: 001_NAB_id_1_Facility_tr_1007_1st_2014.csv
    Returns: train_size, test_size
    """
    parts = filename.split('_')
    train_size = None
    test_size = None

    for i, part in enumerate(parts):
        if part == 'tr' and i + 1 < len(parts):
            train_size = int(parts[i + 1])
        elif part == '1st' and i + 1 < len(parts):
            test_size = int(parts[i + 1].split('.')[0])  # Remove .csv extension

    return train_size, test_size

def load_tsb_ad_dataset(dataset_path, max_samples=None):
    """
    Load a TSB-AD dataset with proper train/test split based on filename
    Args:
        dataset_path: path to the CSV file
        max_samples: maximum number of samples to load (for testing)
    Returns:
        train_data, train_labels, test_data, test_labels
    """
    try:
        df = pd.read_csv(dataset_path)

        # TSB-AD format: first column is data, second column is label
        data = df.iloc[:, 0].values.reshape(-1, 1)  # Make it 2D
        labels = df.iloc[:, 1].values

        # Parse filename to get train/test split
        filename = os.path.basename(dataset_path)
        train_size, test_size = parse_dataset_filename(filename)

        if train_size is None or test_size is None:
            print(f"Warning: Could not parse train/test sizes from filename {filename}")
            # Fallback to 70/30 split
            split_idx = int(0.7 * len(data))
            train_data = data[:split_idx]
            train_labels = labels[:split_idx]
            test_data = data[split_idx:]
            test_labels = labels[split_idx:]
        else:
            # Use the sizes from filename
            train_data = data[:train_size]
            train_labels = labels[:train_size]
            test_data = data[train_size:train_size + test_size]
            test_labels = labels[train_size:train_size + test_size]

        if max_samples:
            if len(test_data) > max_samples:
                test_data = test_data[:max_samples]
                test_labels = test_labels[:max_samples]

        return train_data, train_labels, test_data, test_labels
    except Exception as e:
        print(f"Error loading dataset {dataset_path}: {e}")
        return None, None, None, None

def create_sliding_windows(data, window_size, stride=1):
    """
    Create sliding windows from time series data
    Args:
        data: [n_samples, n_features] time series data
        window_size: size of each window
        stride: stride between windows
    Returns:
        windows: [n_windows, window_size, n_features]
        window_labels: [n_windows] - 1 if any point in window is anomaly
    """
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    n_samples, n_features = data.shape
    windows = []
    
    for i in range(0, n_samples - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def evaluate_model(model, sae, test_data, test_labels, window_size=100, device='cpu'):
    """
    Evaluate HTA-AD model on test data using both point-wise and TSB-AD metrics
    Args:
        model: trained HTA-AD model
        sae: trained SAE model
        test_data: test time series data
        test_labels: test labels
        window_size: window size for evaluation
        device: computation device
    Returns:
        results: dictionary with evaluation metrics
    """
    model.eval()
    sae.eval()

    # For point-wise evaluation, we need to get anomaly scores for each point
    # We'll use a sliding window approach but assign scores to center points

    if len(test_data) < window_size:
        print(f"Test data too short ({len(test_data)}) for window size {window_size}")
        return None

    # Pad data to handle edge cases
    pad_size = window_size // 2
    padded_data = np.pad(test_data, ((pad_size, pad_size), (0, 0)), mode='edge')

    point_scores_hta = []
    point_scores_sae = []

    with torch.no_grad():
        for i in range(len(test_data)):
            # Extract window centered at point i
            window = padded_data[i:i + window_size]
            window_tensor = torch.FloatTensor(window).unsqueeze(0).to(device)

            # HTA-AD forward pass
            outputs = model(window_tensor)
            reconstruction = outputs['reconstruction']
            latent_vectors = outputs['latent_vectors']

            # Compute reconstruction error for center point
            center_idx = window_size // 2
            point_error = torch.mean((window_tensor[0, center_idx] - reconstruction[0, center_idx]) ** 2)
            point_scores_hta.append(point_error.cpu().item())

            # SAE forward pass
            z_hat, features = sae(latent_vectors)
            sae_error = torch.mean((latent_vectors[0, center_idx] - z_hat[0, center_idx]) ** 2)
            point_scores_sae.append(sae_error.cpu().item())

    point_scores_hta = np.array(point_scores_hta)
    point_scores_sae = np.array(point_scores_sae)

    # Calculate metrics
    results = {}

    if len(np.unique(test_labels)) > 1:  # Check if we have both normal and anomaly samples
        # Basic metrics
        results['hta_ad_auc'] = roc_auc_score(test_labels, point_scores_hta)
        results['sae_auc'] = roc_auc_score(test_labels, point_scores_sae)
        results['hta_ad_ap'] = average_precision_score(test_labels, point_scores_hta)
        results['sae_ap'] = average_precision_score(test_labels, point_scores_sae)

        # TSB-AD official metrics
        if TSB_AD_AVAILABLE:
            try:
                hta_metrics = get_metrics(point_scores_hta, test_labels, slidingWindow=window_size)
                sae_metrics = get_metrics(point_scores_sae, test_labels, slidingWindow=window_size)

                results['hta_ad_vus_pr'] = hta_metrics.get('VUS-PR', 0)
                results['hta_ad_vus_roc'] = hta_metrics.get('VUS-ROC', 0)
                results['sae_vus_pr'] = sae_metrics.get('VUS-PR', 0)
                results['sae_vus_roc'] = sae_metrics.get('VUS-ROC', 0)

                results['hta_ad_f1'] = hta_metrics.get('Standard-F1', 0)
                results['sae_f1'] = sae_metrics.get('Standard-F1', 0)
            except Exception as e:
                print(f"Warning: TSB-AD metrics calculation failed: {e}")
                results['hta_ad_vus_pr'] = 0
                results['hta_ad_vus_roc'] = 0
                results['sae_vus_pr'] = 0
                results['sae_vus_roc'] = 0
    else:
        results['hta_ad_auc'] = 0.5
        results['sae_auc'] = 0.5
        results['hta_ad_ap'] = np.mean(test_labels)
        results['sae_ap'] = np.mean(test_labels)
        results['hta_ad_vus_pr'] = 0
        results['hta_ad_vus_roc'] = 0
        results['sae_vus_pr'] = 0
        results['sae_vus_roc'] = 0

    results['n_points'] = len(test_labels)
    results['anomaly_ratio'] = np.mean(test_labels)
    results['point_scores_hta'] = point_scores_hta
    results['point_scores_sae'] = point_scores_sae
    results['test_labels'] = test_labels

    return results

def train_models_on_dataset(train_data, input_dim, device='cpu', epochs=50):
    """
    Train HTA-AD and SAE models on training data
    Args:
        train_data: training time series data
        input_dim: input dimension
        device: computation device
        epochs: number of training epochs
    Returns:
        model: trained HTA-AD model
        sae: trained SAE model
    """
    # Create models
    model = HTAADBasic(
        input_dim=input_dim,
        d_model=32,
        seq_len=100
    ).to(device)
    
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128
    ).to(device)
    
    # Create training windows
    train_windows = create_sliding_windows(train_data, 100)
    train_windows = torch.FloatTensor(train_windows).to(device)
    
    if len(train_windows) == 0:
        print("No training windows created - data too short")
        return None, None
    
    print(f"Training on {len(train_windows)} windows...")
    
    # Train HTA-AD
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    model.train()
    
    for epoch in range(epochs):
        total_loss = 0
        batch_size = 32
        
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch)
            losses = model.compute_loss(outputs, batch)
            loss = losses['total']
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 10 == 0:
            avg_loss = total_loss / (len(train_windows) // batch_size + 1)
            print(f"Epoch {epoch + 1}/{epochs}, Loss: {avg_loss:.4f}")
    
    # Collect latent vectors for SAE training
    print("Collecting latent vectors for SAE training...")
    model.eval()
    all_latents = []
    
    with torch.no_grad():
        batch_size = 32
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            outputs = model(batch)
            latents = outputs['latent_vectors']
            all_latents.append(latents)
    
    all_latents = torch.cat(all_latents, dim=0)
    print(f"Collected {all_latents.shape[0]} latent vectors")
    
    # Train SAE
    print("Training SAE...")
    sae_optimizer = torch.optim.Adam(sae.parameters(), lr=0.001)
    sae.train()
    
    for epoch in range(epochs // 2):  # Train SAE for fewer epochs
        total_loss = 0
        batch_size = 64
        
        for i in range(0, len(all_latents), batch_size):
            batch = all_latents[i:i + batch_size]
            
            sae_optimizer.zero_grad()
            losses = sae.compute_loss(batch)
            loss = losses['total']
            
            loss.backward()
            sae_optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 5 == 0:
            avg_loss = total_loss / (len(all_latents) // batch_size + 1)
            print(f"SAE Epoch {epoch + 1}/{epochs // 2}, Loss: {avg_loss:.4f}")
    
    # Identify irrelevant features
    print("Identifying irrelevant features...")
    sae.identify_irrelevant_features(all_latents)
    
    return model, sae

def test_on_tsb_ad_sample():
    """
    Test HTA-AD on a sample of TSB-AD datasets
    """
    print("🚀 Testing HTA-AD Paper Compliant Implementation on TSB-AD")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Test on a few sample datasets
    test_datasets = [
        "TSB-AD/Datasets/TSB-AD-U/001_NAB_id_1_Facility_tr_1007_1st_2014.csv",
        "TSB-AD/Datasets/TSB-AD-U/002_NAB_id_2_WebService_tr_1500_1st_4106.csv",
        "TSB-AD/Datasets/TSB-AD-U/140_MSL_id_1_Sensor_tr_530_1st_630.csv"
    ]
    
    results_summary = []
    
    for dataset_path in test_datasets:
        if not os.path.exists(dataset_path):
            print(f"❌ Dataset not found: {dataset_path}")
            continue
        
        print(f"\n📊 Testing on: {os.path.basename(dataset_path)}")
        print("-" * 50)

        # Load dataset with proper train/test split
        train_data, train_labels, test_data, test_labels = load_tsb_ad_dataset(dataset_path, max_samples=2000)
        if train_data is None:
            continue

        print(f"Train data shape: {train_data.shape}")
        print(f"Test data shape: {test_data.shape}")
        print(f"Train anomaly ratio: {np.mean(train_labels):.3f}")
        print(f"Test anomaly ratio: {np.mean(test_labels):.3f}")

        # Normalize data
        scaler = StandardScaler()
        train_data = scaler.fit_transform(train_data)
        test_data = scaler.transform(test_data)
        
        input_dim = train_data.shape[1] if len(train_data.shape) > 1 else 1
        
        try:
            # Train models
            start_time = time.time()
            model, sae = train_models_on_dataset(train_data, input_dim, device, epochs=30)
            training_time = time.time() - start_time
            
            if model is None or sae is None:
                print("❌ Training failed")
                continue
            
            # Evaluate
            start_time = time.time()
            results = evaluate_model(model, sae, test_data, test_labels, device=device)
            eval_time = time.time() - start_time
            
            if results is None:
                print("❌ Evaluation failed")
                continue
            
            # Print results
            print(f"✅ Results:")
            print(f"   Basic Metrics:")
            print(f"     - HTA-AD AUC: {results['hta_ad_auc']:.4f}")
            print(f"     - HTA-AD AP:  {results['hta_ad_ap']:.4f}")
            print(f"     - SAE AUC:    {results['sae_auc']:.4f}")
            print(f"     - SAE AP:     {results['sae_ap']:.4f}")

            if TSB_AD_AVAILABLE and 'hta_ad_vus_pr' in results:
                print(f"   TSB-AD Metrics:")
                print(f"     - HTA-AD VUS-PR:  {results['hta_ad_vus_pr']:.4f}")
                print(f"     - HTA-AD VUS-ROC: {results['hta_ad_vus_roc']:.4f}")
                print(f"     - SAE VUS-PR:     {results['sae_vus_pr']:.4f}")
                print(f"     - SAE VUS-ROC:    {results['sae_vus_roc']:.4f}")

            print(f"   - Test points: {results['n_points']}")
            print(f"   - Training time: {training_time:.2f}s")
            print(f"   - Eval time:     {eval_time:.2f}s")
            
            # Store results
            result_dict = {
                'dataset': os.path.basename(dataset_path),
                'hta_ad_auc': results['hta_ad_auc'],
                'hta_ad_ap': results['hta_ad_ap'],
                'sae_auc': results['sae_auc'],
                'sae_ap': results['sae_ap'],
                'n_points': results['n_points'],
                'anomaly_ratio': results['anomaly_ratio'],
                'training_time': training_time,
                'eval_time': eval_time
            }

            # Add TSB-AD metrics if available
            if TSB_AD_AVAILABLE and 'hta_ad_vus_pr' in results:
                result_dict.update({
                    'hta_ad_vus_pr': results['hta_ad_vus_pr'],
                    'hta_ad_vus_roc': results['hta_ad_vus_roc'],
                    'sae_vus_pr': results['sae_vus_pr'],
                    'sae_vus_roc': results['sae_vus_roc']
                })

            results_summary.append(result_dict)
            
        except Exception as e:
            print(f"❌ Error processing dataset: {e}")
            continue
    
    # Print summary
    if results_summary:
        print("\n" + "=" * 60)
        print("📈 SUMMARY RESULTS")
        print("=" * 60)
        
        df_results = pd.DataFrame(results_summary)
        print(df_results.to_string(index=False, float_format='%.4f'))
        
        print(f"\n📊 Average Performance:")
        print(f"   Basic Metrics:")
        print(f"     - HTA-AD AUC: {df_results['hta_ad_auc'].mean():.4f} ± {df_results['hta_ad_auc'].std():.4f}")
        print(f"     - HTA-AD AP:  {df_results['hta_ad_ap'].mean():.4f} ± {df_results['hta_ad_ap'].std():.4f}")
        print(f"     - SAE AUC:    {df_results['sae_auc'].mean():.4f} ± {df_results['sae_auc'].std():.4f}")
        print(f"     - SAE AP:     {df_results['sae_ap'].mean():.4f} ± {df_results['sae_ap'].std():.4f}")

        if TSB_AD_AVAILABLE and 'hta_ad_vus_pr' in df_results.columns:
            print(f"   TSB-AD Metrics:")
            print(f"     - HTA-AD VUS-PR:  {df_results['hta_ad_vus_pr'].mean():.4f} ± {df_results['hta_ad_vus_pr'].std():.4f}")
            print(f"     - HTA-AD VUS-ROC: {df_results['hta_ad_vus_roc'].mean():.4f} ± {df_results['hta_ad_vus_roc'].std():.4f}")
            print(f"     - SAE VUS-PR:     {df_results['sae_vus_pr'].mean():.4f} ± {df_results['sae_vus_pr'].std():.4f}")
            print(f"     - SAE VUS-ROC:    {df_results['sae_vus_roc'].mean():.4f} ± {df_results['sae_vus_roc'].std():.4f}")

        print(f"\n⏱️  Average Times:")
        print(f"   - Training: {df_results['training_time'].mean():.2f}s")
        print(f"   - Evaluation: {df_results['eval_time'].mean():.2f}s")
        
        print("\n🎉 Testing completed successfully!")
    else:
        print("\n❌ No datasets were successfully processed.")

if __name__ == "__main__":
    test_on_tsb_ad_sample()
