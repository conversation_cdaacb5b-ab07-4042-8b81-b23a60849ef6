#!/usr/bin/env python3
"""
HTA-AD Main Entry Point
Unified interface for training, evaluation, and inference
"""

import argparse
import os
import sys
import torch
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Add core modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core', 'models'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'core', 'experiments'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'core', 'visualization'))

from hta_ad_integrated import HTAADComplete, HTAADTrainer


def load_data(data_path: str, seq_len: int = 100) -> tuple:
    """Load and preprocess time series data"""
    if data_path.endswith('.csv'):
        data = pd.read_csv(data_path)
        if 'value' in data.columns:
            values = data['value'].values
            labels = data['anomaly'].values if 'anomaly' in data.columns else None
        elif 'anomaly' in data.columns:
            values = data.drop('anomaly', axis=1).values.flatten()
            labels = data['anomaly'].values if 'anomaly' in data.columns else None
        else:
            values = data.iloc[:, 0].values
            labels = data.iloc[:, 1].values if data.shape[1] > 1 else None
    else:
        # Assume numpy array
        data = np.load(data_path)
        if data.ndim == 2:
            values = data[:, 0]
            labels = data[:, 1] if data.shape[1] > 1 else None
        else:
            values = data
            labels = None
    
    # Normalize data
    scaler = StandardScaler()
    values = scaler.fit_transform(values.reshape(-1, 1)).flatten()
    
    # Create sequences
    sequences = []
    sequence_labels = []
    
    for i in range(len(values) - seq_len + 1):
        seq = values[i:i + seq_len]
        sequences.append(seq)
        
        if labels is not None:
            # Use max label in sequence as sequence label
            seq_label = np.max(labels[i:i + seq_len])
            sequence_labels.append(seq_label)
    
    sequences = np.array(sequences).reshape(-1, seq_len, 1)
    
    if labels is not None:
        sequence_labels = np.array(sequence_labels)
        return sequences, sequence_labels, scaler
    else:
        return sequences, None, scaler


def create_data_loaders(sequences, labels=None, batch_size=32, train_ratio=0.7, val_ratio=0.15):
    """Create train/val/test data loaders"""
    n_samples = len(sequences)
    n_train = int(n_samples * train_ratio)
    n_val = int(n_samples * val_ratio)
    
    # Split data
    train_sequences = sequences[:n_train]
    val_sequences = sequences[n_train:n_train + n_val]
    test_sequences = sequences[n_train + n_val:]
    
    if labels is not None:
        train_labels = labels[:n_train]
        val_labels = labels[n_train:n_train + n_val]
        test_labels = labels[n_train + n_val:]
        
        train_dataset = TensorDataset(torch.FloatTensor(train_sequences), torch.FloatTensor(train_labels))
        val_dataset = TensorDataset(torch.FloatTensor(val_sequences), torch.FloatTensor(val_labels))
        test_dataset = TensorDataset(torch.FloatTensor(test_sequences), torch.FloatTensor(test_labels))
    else:
        train_dataset = TensorDataset(torch.FloatTensor(train_sequences))
        val_dataset = TensorDataset(torch.FloatTensor(val_sequences))
        test_dataset = TensorDataset(torch.FloatTensor(test_sequences))
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, val_loader, test_loader


def train_model(args):
    """Train HTA-AD model"""
    print("Loading data...")
    sequences, labels, scaler = load_data(args.data_path, args.seq_len)
    print(f"Loaded {len(sequences)} sequences of length {args.seq_len}")
    
    # Create data loaders
    train_loader, val_loader, test_loader = create_data_loaders(
        sequences, labels, args.batch_size, args.train_ratio, args.val_ratio
    )
    
    # Create model
    print("Creating model...")
    model = HTAADComplete(
        input_dim=1,
        d_model=args.d_model,
        n_heads=args.n_heads,
        n_layers=args.n_layers,
        seq_len=args.seq_len,
        sae_hidden_dim=args.sae_hidden_dim,
        sae_sparsity_weight=args.sae_sparsity_weight,
        enable_sae=args.enable_sae
    )
    
    # Create trainer
    trainer = HTAADTrainer(model, args.device)
    
    # Train model
    print("Training model...")
    history = trainer.train(
        train_loader, val_loader, 
        epochs=args.epochs, 
        lr=args.lr, 
        patience=args.patience
    )
    
    # Evaluate on test set
    print("Evaluating model...")
    test_metrics = trainer.evaluate(test_loader)
    print(f"Test metrics: {test_metrics}")
    
    # Save model
    if args.save_path:
        print(f"Saving model to {args.save_path}")
        model.save_model(args.save_path)
    
    return model, history, test_metrics


def evaluate_model(args):
    """Evaluate trained model"""
    print("Loading model...")
    model = HTAADComplete.load_model(args.model_path, args.device)
    
    print("Loading data...")
    sequences, labels, scaler = load_data(args.data_path, args.seq_len)
    
    # Create test loader
    test_dataset = TensorDataset(torch.FloatTensor(sequences))
    if labels is not None:
        test_dataset = TensorDataset(torch.FloatTensor(sequences), torch.FloatTensor(labels))
    
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)
    
    # Evaluate
    trainer = HTAADTrainer(model, args.device)
    metrics = trainer.evaluate(test_loader)
    
    print(f"Evaluation metrics: {metrics}")
    return metrics


def detect_anomalies(args):
    """Detect anomalies in data"""
    print("Loading model...")
    model = HTAADComplete.load_model(args.model_path, args.device)
    
    print("Loading data...")
    sequences, labels, scaler = load_data(args.data_path, args.seq_len)
    
    # Detect anomalies
    model.eval()
    with torch.no_grad():
        sequences_tensor = torch.FloatTensor(sequences).to(args.device)
        results = model.detect_anomalies(sequences_tensor, threshold=args.threshold)
    
    # Save results
    anomaly_scores = results['anomaly_scores'].cpu().numpy()
    anomaly_predictions = results['anomaly_predictions'].cpu().numpy()
    
    results_df = pd.DataFrame({
        'anomaly_score': anomaly_scores.flatten(),
        'anomaly_prediction': anomaly_predictions.flatten()
    })
    
    if labels is not None:
        results_df['true_label'] = labels
    
    if args.output_path:
        results_df.to_csv(args.output_path, index=False)
        print(f"Results saved to {args.output_path}")
    
    print(f"Detected {np.sum(anomaly_predictions)} anomalies out of {len(anomaly_predictions)} sequences")
    return results_df


def main():
    parser = argparse.ArgumentParser(description='HTA-AD: Hierarchical Temporal Attention for Anomaly Detection')
    
    # Common arguments
    parser.add_argument('--mode', choices=['train', 'evaluate', 'detect'], required=True,
                       help='Mode: train, evaluate, or detect')
    parser.add_argument('--data_path', required=True, help='Path to data file')
    parser.add_argument('--device', default='cuda' if torch.cuda.is_available() else 'cpu',
                       help='Device to use')
    parser.add_argument('--seq_len', type=int, default=100, help='Sequence length')
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size')
    
    # Model arguments
    parser.add_argument('--d_model', type=int, default=32, help='Model dimension')
    parser.add_argument('--n_heads', type=int, default=4, help='Number of attention heads')
    parser.add_argument('--n_layers', type=int, default=2, help='Number of layers')
    parser.add_argument('--enable_sae', action='store_true', help='Enable SAE integration')
    parser.add_argument('--sae_hidden_dim', type=int, default=128, help='SAE hidden dimension')
    parser.add_argument('--sae_sparsity_weight', type=float, default=0.01, help='SAE sparsity weight')
    
    # Training arguments
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-3, help='Learning rate')
    parser.add_argument('--patience', type=int, default=15, help='Early stopping patience')
    parser.add_argument('--train_ratio', type=float, default=0.7, help='Training data ratio')
    parser.add_argument('--val_ratio', type=float, default=0.15, help='Validation data ratio')
    parser.add_argument('--save_path', help='Path to save trained model')
    
    # Evaluation/Detection arguments
    parser.add_argument('--model_path', help='Path to trained model')
    parser.add_argument('--threshold', type=float, default=0.5, help='Anomaly detection threshold')
    parser.add_argument('--output_path', help='Path to save results')
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.mode in ['evaluate', 'detect'] and not args.model_path:
        parser.error(f"--model_path is required for mode '{args.mode}'")
    
    # Execute based on mode
    if args.mode == 'train':
        model, history, metrics = train_model(args)
    elif args.mode == 'evaluate':
        metrics = evaluate_model(args)
    elif args.mode == 'detect':
        results = detect_anomalies(args)
    
    print("Done!")


if __name__ == '__main__':
    main()
