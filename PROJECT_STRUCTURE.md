# HTA-AD 项目文件结构

## 📁 目录结构

```
HTA-AD/
├── 📂 core/                          # 核心模型实现
│   ├── models/
│   │   └── hta_ad_integrated.py      # 集成HTA-AD模型
│   └── sae_integration/
│       └── sparse_autoencoder.py     # SAE集成实现
│
├── 📂 TSB-AD/                        # TSB-AD基准测试框架
│   └── TSB_AD/
│       ├── model_wrapper.py          # 模型包装器（已更新）
│       └── models/
│           ├── HTA_AD.py             # 基础HTA-AD
│           └── HTA_AD_SAE.py         # SAE集成版本
│
├── 📂 benchmarks/                    # 基准测试脚本
│   ├── tsb_ad_vus_pr_benchmark.py   # TSB-AD VUS-PR基准测试 ⭐
│   ├── run_tsb_ad_benchmark.py      # 通用基准测试
│   └── final_paper_compliant_test.py # 论文规范测试
│
├── 📂 experiments/                  # 实验脚本
│   ├── visualize_training_results.py # 训练结果可视化
│   ├── detailed_performance_test.py  # 详细性能测试
│   └── test_anomaly_loss_effect.py  # 异常损失效果测试
│
├── 📂 tests/                        # 测试脚本
│   ├── test_hta_ad_comprehensive.py # 综合测试
│   ├── test_tsb_ad_integration.py   # TSB-AD集成测试
│   ├── test_core_model_fix.py       # 核心模型修复测试
│   └── debug_hta_ad_basic.py        # 调试脚本
│
├── 📂 results/                      # 结果输出
│   ├── data/                        # 数据结果
│   ├── models/                      # 保存的模型
│   └── visualizations/              # 可视化图表
│
├── 📄 main.py                       # 命令行接口
├── 📄 example_usage.py              # 使用示例
├── 📄 paper.tex                     # 论文文档
└── 📄 PROJECT_STRUCTURE.md          # 本文件
```

## 🚀 快速开始

### 1. 运行TSB-AD VUS-PR基准测试（推荐）
```bash
cd benchmarks
python tsb_ad_vus_pr_benchmark.py
```
这将使用论文精确配置和VUS-PR指标进行评估。

### 2. 运行综合测试
```bash
cd tests
python test_hta_ad_comprehensive.py
```

### 3. 使用命令行接口
```bash
# 训练模型
python main.py --mode train --data_path data.csv --enable_sae

# 检测异常
python main.py --mode detect --model_path model.pth --data_path test.csv
```

## 📊 论文相关文件

### 核心实现
- `core/models/hta_ad_integrated.py` - 完整的HTA-AD实现
- `core/sae_integration/sparse_autoencoder.py` - SAE集成模块

### 基准测试
- `benchmarks/tsb_ad_vus_pr_benchmark.py` - **主要基准测试**，使用VUS-PR指标
- `benchmarks/final_paper_compliant_test.py` - 论文规范验证

### TSB-AD集成
- `TSB-AD/TSB_AD/model_wrapper.py` - 已更新的包装器
- `TSB-AD/TSB_AD/models/HTA_AD_SAE.py` - SAE集成实现

## 🎯 论文配置

所有基准测试使用论文精确配置：

```python
paper_config = {
    'window_size': 128,        # 论文表格
    'epochs': 30,              # 论文表格
    'lr': 1e-3,               # 论文表格
    'batch_size': 64,         # 论文表格
    'latent_dim': 32,         # "32-dimensional latent vector"
    'sae_hidden_dim': 128,    # "128 interpretable features"
    'sae_sparsity_weight': 0.001,  # λ = 0.001
    'purification_strength': 0.7    # α = 0.7
}
```

## 📈 评估指标

- **VUS-PR** - 主要指标（论文中使用）
- **AUC** - 辅助指标
- **AP** - 平均精度
- **训练时间** - 效率评估

## 🔧 开发指南

### 添加新实验
1. 在 `experiments/` 目录创建新脚本
2. 使用 `paper_config` 确保一致性
3. 保存结果到 `results/` 目录

### 运行测试
1. 单元测试：`tests/` 目录
2. 集成测试：`test_tsb_ad_integration.py`
3. 性能测试：`benchmarks/` 目录

### 文件命名规范
- `test_*.py` - 测试文件
- `*_benchmark.py` - 基准测试
- `*_experiment.py` - 实验脚本
- `debug_*.py` - 调试工具

## 📝 重要说明

1. **主要基准测试**：使用 `benchmarks/tsb_ad_vus_pr_benchmark.py`
2. **论文数值**：所有结果都使用论文精确配置
3. **VUS-PR指标**：符合TSB-AD基准测试标准
4. **文件整理**：根目录保持整洁，功能文件分类存放

## 🎉 成果总结

- ✅ 严格按照论文规范实现
- ✅ 集成到TSB-AD基准测试框架
- ✅ 使用VUS-PR作为主要评估指标
- ✅ 文件结构清晰，便于维护和扩展
- ✅ 提供完整的测试和实验套件
