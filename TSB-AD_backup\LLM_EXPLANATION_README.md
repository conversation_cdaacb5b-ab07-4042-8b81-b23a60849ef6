# HTA-AD with LLM Explanation Module

这是一个真正集成了大语言模型(LLM)的HTA-AD异常检测解释模块。与传统的模板填充方法不同，本模块使用实际的LLM来生成智能、上下文感知的异常解释。

## 🌟 主要特性

- **真正的LLM集成**: 支持OpenAI GPT、Anthropic Claude、Ollama本地模型等
- **智能异常分析**: LLM基于重构误差、特征统计和领域知识生成解释
- **多提供商支持**: 灵活选择不同的LLM提供商
- **中文解释**: 专门优化的中文异常解释生成
- **可视化支持**: 自动生成异常可视化图表
- **领域知识集成**: 支持注入特定领域的专业知识

## 🚀 快速开始

### 1. 安装依赖

```bash
# 基础依赖
pip install torch numpy pandas matplotlib scikit-learn

# LLM提供商依赖（根据需要选择）
pip install openai          # OpenAI GPT
pip install anthropic       # Anthropic Claude
pip install transformers    # 本地Hugging Face模型
```

### 2. 设置LLM提供商

#### OpenAI GPT
```bash
export OPENAI_API_KEY="your-openai-api-key"
```

#### Anthropic Claude
```bash
export ANTHROPIC_API_KEY="your-anthropic-api-key"
```

#### Ollama (本地)
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull llama2

# 启动服务
ollama serve
```

### 3. 运行演示

```python
from demo_llm_explanation import demo_llm_explanation

# 使用OpenAI GPT
demo_llm_explanation('openai')

# 使用本地模型（无需API密钥）
demo_llm_explanation('local')

# 使用Ollama
demo_llm_explanation('ollama')
```

## 📖 详细使用说明

### 基本用法

```python
from llm_explanation_module import LLMExplanationModule
from llm_config_examples import get_llm_config

# 1. 配置LLM
llm_config = get_llm_config('openai')  # 或 'anthropic', 'ollama', 'local'

# 2. 定义特征名称和领域知识
feature_names = ["温度", "压力", "振动"]
domain_knowledge = {
    "feature_0": {
        "point_anomaly": "温度传感器故障或环境温度突变",
        "contextual_anomaly": "温度控制系统异常",
        # ... 更多领域知识
    }
}

# 3. 初始化解释模块
explanation_module = LLMExplanationModule(
    hta_model=trained_hta_model,
    window_size=64,
    feature_names=feature_names,
    domain_knowledge=domain_knowledge,
    llm_config=llm_config
)

# 4. 训练正常模式
explanation_module.fit(normal_training_windows)

# 5. 解释异常
explanation = explanation_module.explain_anomaly(
    anomaly_window=anomaly_data,
    time_index=anomaly_time_index
)

print(explanation['explanation'])
```

### 高级配置

```python
# 自定义LLM配置
custom_llm_config = {
    'provider': 'openai',
    'model': 'gpt-4',
    'api_key': 'your-api-key',
    'max_tokens': 800,
    'temperature': 0.2
}

# 详细的领域知识
detailed_domain_knowledge = {
    "feature_0": {
        "point_anomaly": "温度传感器故障、环境温度突变或冷却系统异常",
        "contextual_anomaly": "温度控制系统调节异常或环境条件渐变",
        "collective_anomaly": "整体热管理系统故障或外部热源影响",
        "seasonal_anomaly": "季节性温度模式中断或系统校准问题"
    },
    "general": {
        "system_context": "这是一个工业监控系统，监测关键设备的运行状态",
        "critical_thresholds": {
            "temperature": {"min": 15, "max": 35},
            "pressure": {"min": 90, "max": 120},
            "vibration": {"min": 0, "max": 5}
        }
    }
}
```

## 🔧 支持的LLM提供商

### 1. OpenAI GPT
- **模型**: gpt-3.5-turbo, gpt-4, gpt-4-turbo
- **优势**: 高质量输出，稳定的API
- **成本**: 按使用量付费
- **设置**: 需要API密钥

### 2. Anthropic Claude
- **模型**: claude-3-sonnet, claude-3-opus, claude-3-haiku
- **优势**: 长上下文，安全性好
- **成本**: 按使用量付费
- **设置**: 需要API密钥

### 3. Ollama (本地)
- **模型**: llama2, mistral, codellama等
- **优势**: 完全本地运行，无隐私担忧
- **成本**: 免费（需要本地计算资源）
- **设置**: 需要安装Ollama和下载模型

### 4. Hugging Face (本地)
- **模型**: 各种开源模型
- **优势**: 免费，离线运行
- **成本**: 免费（需要本地计算资源）
- **设置**: 自动下载模型

## 📊 输出示例

### LLM生成的异常解释示例

```
基于分析结果，我发现了一个点异常：

**异常概述**
在时间索引 756 处检测到温度传感器的点异常。该异常表现为温度值突然偏离正常范围，重构误差达到 0.045，远超平均误差水平。

**具体表现**
- 异常特征：温度 (°C)
- 异常时间点：窗口内第 23 个时间步
- 实际温度值：28.45°C，而正常范围应在 [18.2°C, 24.8°C]
- 偏差程度：比正常均值高出 32.5%

**可能原因分析**
根据领域知识和数据模式，这种突发性温度异常通常由以下原因引起：
1. 温度传感器故障或校准偏移
2. 局部环境温度突变（如热源靠近）
3. 冷却系统短暂失效

**建议措施**
1. 立即检查温度传感器的物理状态和连接
2. 检查周围是否有新的热源
3. 验证冷却系统的运行状态
4. 考虑重新校准温度传感器
```

## 🎯 核心优势

### 1. 真正的智能解释
- 不是简单的模板填充
- LLM基于上下文生成个性化解释
- 能够理解复杂的异常模式

### 2. 多模态分析
- 结合重构误差分析
- 特征统计信息
- 时间序列模式识别
- 领域知识集成

### 3. 灵活的部署选项
- 云端API（OpenAI, Anthropic）
- 本地部署（Ollama, Hugging Face）
- 自定义API端点

### 4. 中文优化
- 专门针对中文异常解释优化
- 符合中文表达习惯
- 专业术语准确

## 🔍 技术架构

```
异常数据 → 特征提取 → 上下文构建 → LLM推理 → 结构化解释
    ↓           ↓           ↓           ↓           ↓
重构误差    统计分析    领域知识    智能生成    可视化输出
```

## 📝 配置文件

查看 `llm_config_examples.py` 获取详细的配置示例：

```python
from llm_config_examples import get_llm_config, print_setup_instructions

# 查看所有提供商的设置说明
print_setup_instructions()

# 获取特定提供商的配置
config = get_llm_config('openai')
```

## 🚨 注意事项

1. **API成本**: 使用云端LLM会产生API调用费用
2. **隐私考虑**: 敏感数据建议使用本地模型
3. **网络依赖**: 云端API需要稳定的网络连接
4. **计算资源**: 本地模型需要足够的GPU/CPU资源

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个模块！

## 📄 许可证

本项目遵循MIT许可证。
