#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for HTA-AD with SAE Integration
Validates that the integration works correctly for both univariate and multivariate data
"""

import numpy as np
import sys
import os
import torch
from sklearn.metrics import roc_auc_score

# Add TSB-AD path
sys.path.append('TSB-AD')

def create_test_data(n_samples=500, n_features=1, anomaly_ratio=0.1):
    """Create simple test data with known anomalies"""
    np.random.seed(42)
    
    # Generate normal sinusoidal data
    t = np.linspace(0, 10, n_samples)
    data = np.zeros((n_samples, n_features))
    
    for i in range(n_features):
        data[:, i] = np.sin(2 * np.pi * 0.1 * t * (i + 1)) + 0.1 * np.random.randn(n_samples)
    
    # Add anomalies
    labels = np.zeros(n_samples)
    n_anomalies = int(n_samples * anomaly_ratio)
    anomaly_indices = np.random.choice(n_samples, n_anomalies, replace=False)
    
    for idx in anomaly_indices:
        data[idx] += 2.0 * np.random.randn(n_features)  # Spike anomalies
        labels[idx] = 1
    
    return data, labels

def test_basic_functionality():
    """Test basic HTA-AD with SAE functionality"""
    print("🧪 Test 1: Basic Functionality")
    print("-" * 40)
    
    try:
        from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE
        print("✅ Successfully imported HTA_AD_SAE")
    except ImportError as e:
        print(f"❌ Failed to import HTA_AD_SAE: {e}")
        return False
    
    # Create test data
    data, labels = create_test_data(n_samples=300, n_features=1)
    split_idx = int(0.7 * len(data))
    train_data = data[:split_idx]
    test_data = data[split_idx:]
    test_labels = labels[split_idx:]
    
    print(f"   Data shape: {data.shape}")
    print(f"   Train samples: {len(train_data)}")
    print(f"   Test samples: {len(test_data)}")
    print(f"   Anomaly ratio: {np.mean(test_labels):.2%}")
    
    # Configure model
    HP = {
        'window_size': 32,  # Small for quick testing
        'epochs': 5,        # Few epochs for quick testing
        'lr': 1e-3,
        'batch_size': 16,
        'latent_dim': 16,   # Smaller for testing
        'tcn_channels': [16, 16],
        'cnn_channels': 8,
        'downsample_stride': 2,
        'gpu': 0 if torch.cuda.is_available() else -1
    }
    
    sae_config = {
        'hidden_dim': 32,   # Smaller for testing
        'sparsity_weight': 0.01,
        'purification_strength': 0.3
    }
    
    try:
        # Initialize model
        detector = HTA_AD_SAE(HP=HP, normalize=True, sae_config=sae_config)
        print("✅ Successfully initialized HTA_AD_SAE")
        
        # Train model
        detector.fit(train_data)
        print("✅ Successfully trained model")
        
        # Get anomaly scores
        scores = detector.decision_function(test_data)
        print(f"✅ Successfully computed anomaly scores: {scores.shape}")
        
        # Evaluate performance
        if len(np.unique(test_labels)) > 1:  # Check if we have both classes
            auc = roc_auc_score(test_labels, scores)
            print(f"✅ AUC-ROC: {auc:.4f}")
        else:
            print("⚠️ Only one class in test labels, skipping AUC calculation")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during basic functionality test: {e}")
        return False

def test_multivariate_support():
    """Test multivariate data support"""
    print("\n🧪 Test 2: Multivariate Support")
    print("-" * 40)
    
    try:
        from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE
        
        # Create multivariate test data
        data, labels = create_test_data(n_samples=300, n_features=3)
        split_idx = int(0.7 * len(data))
        train_data = data[:split_idx]
        test_data = data[split_idx:]
        test_labels = labels[split_idx:]
        
        print(f"   Multivariate data shape: {data.shape}")
        
        # Configure model for multivariate
        HP = {
            'window_size': 32,
            'epochs': 5,
            'lr': 1e-3,
            'batch_size': 16,
            'latent_dim': 16,
            'tcn_channels': [16, 16],
            'cnn_channels': 8,
            'downsample_stride': 2,
            'gpu': 0 if torch.cuda.is_available() else -1
        }
        
        sae_config = {
            'hidden_dim': 32,
            'sparsity_weight': 0.01,
            'purification_strength': 0.3
        }
        
        # Test multivariate
        detector = HTA_AD_SAE(HP=HP, normalize=True, sae_config=sae_config)
        detector.fit(train_data)
        scores = detector.decision_function(test_data)
        
        print(f"✅ Multivariate test successful: {scores.shape}")
        
        if len(np.unique(test_labels)) > 1:
            auc = roc_auc_score(test_labels, scores)
            print(f"✅ Multivariate AUC-ROC: {auc:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during multivariate test: {e}")
        return False

def test_feature_attribution():
    """Test feature attribution functionality"""
    print("\n🧪 Test 3: Feature Attribution")
    print("-" * 40)
    
    try:
        from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE
        
        # Create test data
        data, labels = create_test_data(n_samples=200, n_features=2)
        split_idx = int(0.7 * len(data))
        train_data = data[:split_idx]
        test_data = data[split_idx:]
        
        # Configure model
        HP = {
            'window_size': 32,
            'epochs': 3,  # Very few epochs for quick test
            'lr': 1e-3,
            'batch_size': 16,
            'latent_dim': 16,
            'tcn_channels': [16, 16],
            'cnn_channels': 8,
            'downsample_stride': 2,
            'gpu': 0 if torch.cuda.is_available() else -1
        }
        
        sae_config = {
            'hidden_dim': 32,
            'sparsity_weight': 0.01,
            'purification_strength': 0.3
        }
        
        # Train model
        detector = HTA_AD_SAE(HP=HP, normalize=True, sae_config=sae_config)
        detector.fit(train_data)
        
        # Test feature attribution
        attribution = detector.get_feature_attribution(test_data[:50], top_k=5)
        
        if attribution is not None:
            print(f"✅ Feature attribution successful")
            print(f"   Feature dimension: {attribution['feature_dim']}")
            print(f"   Number of windows: {len(attribution['top_features'])}")
            
            # Show first window's top features
            if len(attribution['top_features']) > 0:
                first_window = attribution['top_features'][0]
                print(f"   First window top features: {first_window['indices'][:3]}")
                print(f"   First window activations: {first_window['values'][:3]}")
        else:
            print("⚠️ Feature attribution returned None (SAE not available)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during feature attribution test: {e}")
        return False

def test_tsb_ad_integration():
    """Test TSB-AD model wrapper integration"""
    print("\n🧪 Test 4: TSB-AD Integration")
    print("-" * 40)
    
    try:
        from TSB_AD.model_wrapper import run_Semisupervise_AD
        
        # Create test data
        data, labels = create_test_data(n_samples=200, n_features=1)
        split_idx = int(0.7 * len(data))
        train_data = data[:split_idx]
        test_data = data[split_idx:]
        test_labels = labels[split_idx:]
        
        # Test via model wrapper
        scores = run_Semisupervise_AD(
            'HTA_AD_SAE',
            train_data,
            test_data,
            window_size=32,
            epochs=3,
            batch_size=16,
            sae_hidden_dim=32
        )
        
        print(f"✅ TSB-AD integration successful: {scores.shape}")
        
        if len(np.unique(test_labels)) > 1:
            auc = roc_auc_score(test_labels, scores)
            print(f"✅ TSB-AD wrapper AUC-ROC: {auc:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during TSB-AD integration test: {e}")
        return False

def test_pretrained_sae_loading():
    """Test pretrained SAE loading (if available)"""
    print("\n🧪 Test 5: Pretrained SAE Loading")
    print("-" * 40)
    
    pretrained_path = "pretrained_sae.pth"
    
    if not os.path.exists(pretrained_path):
        print(f"⚠️ Pretrained SAE not found at {pretrained_path}")
        print("   Run sae_pretraining_script.py to create pretrained model")
        return True  # Not a failure, just not available
    
    try:
        from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE
        
        # Create test data
        data, _ = create_test_data(n_samples=200, n_features=1)
        split_idx = int(0.7 * len(data))
        train_data = data[:split_idx]
        test_data = data[split_idx:]
        
        # Configure model with pretrained SAE
        HP = {
            'window_size': 32,
            'epochs': 3,
            'lr': 1e-3,
            'batch_size': 16,
            'latent_dim': 32,  # Should match pretrained model
            'tcn_channels': [16, 16],
            'cnn_channels': 8,
            'downsample_stride': 2,
            'gpu': 0 if torch.cuda.is_available() else -1
        }
        
        sae_config = {
            'hidden_dim': 128,  # Should match pretrained model
            'sparsity_weight': 0.01,
            'purification_strength': 0.5
        }
        
        # Test with pretrained SAE
        detector = HTA_AD_SAE(
            HP=HP, 
            normalize=True, 
            sae_config=sae_config,
            pretrained_sae_path=pretrained_path
        )
        
        detector.fit(train_data)
        scores = detector.decision_function(test_data)
        
        print(f"✅ Pretrained SAE loading successful: {scores.shape}")
        return True
        
    except Exception as e:
        print(f"❌ Error during pretrained SAE test: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 HTA-AD with SAE Integration Test Suite")
    print("=" * 60)
    
    # Check PyTorch availability
    print(f"🔧 PyTorch version: {torch.__version__}")
    print(f"🔧 CUDA available: {torch.cuda.is_available()}")
    print()
    
    # Run tests
    tests = [
        test_basic_functionality,
        test_multivariate_support,
        test_feature_attribution,
        test_tsb_ad_integration,
        test_pretrained_sae_loading
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary")
    print("-" * 60)
    
    test_names = [
        "Basic Functionality",
        "Multivariate Support", 
        "Feature Attribution",
        "TSB-AD Integration",
        "Pretrained SAE Loading"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! HTA-AD with SAE integration is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the error messages above.")
    
    print("\n💡 Next steps:")
    print("   1. Run sae_pretraining_script.py to create pretrained SAE")
    print("   2. Try hta_ad_sae_example.py for detailed usage examples")
    print("   3. Use HTA_AD_SAE in your anomaly detection projects!")

if __name__ == "__main__":
    main()
