#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合成数据可视化分析
详细展示当前合成数据的特征，包括训练集测试集划分和数据质量
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import sys
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

def generate_synthetic_data_with_anomaly():
    """生成高度周期性的合成数据，包含明显的异常"""
    np.random.seed(42)  # 确保结果可重现
    
    # 参数设置
    length = 400
    window_size = 100
    
    # 创建高度周期性的基础信号
    t = np.linspace(0, 8*np.pi, length)
    
    # 非常清晰的周期性模式 - 主要正弦波加谐波
    base_signal = (0.6 * np.sin(t) +                    # 主要周期成分
                  0.2 * np.sin(2*t + np.pi/4) +         # 二次谐波
                  0.1 * np.sin(4*t) +                   # 四次谐波
                  0.05 * np.random.normal(0, 0.05, length))  # 极少噪声
    
    # 归一化到[0, 1]范围
    base_signal = (base_signal - base_signal.min()) / (base_signal.max() - base_signal.min())
    
    # 创建标签（初始都是正常）
    labels = np.zeros(length)
    
    # 添加非常明显的尖锐异常，破坏周期性模式
    anomaly_regions = [
        (285, 315),  # 测试窗口中的主要尖锐异常
        (160, 180),  # 额外的上下文异常
    ]
    
    for start, end in anomaly_regions:
        # 创建非常尖锐的尖峰异常
        anomaly_length = end - start
        # 创建尖锐的三角形尖峰
        peak_pos = anomaly_length // 2
        spike_pattern = np.concatenate([
            np.linspace(0, 1, peak_pos),
            np.linspace(1, 0, anomaly_length - peak_pos)
        ])
        # 强异常，明显破坏模式
        base_signal[start:end] += spike_pattern * 1.0  # 非常强的尖峰
        labels[start:end] = 1
    
    # 确保信号保持在合理范围内
    base_signal = np.clip(base_signal, 0, 1.8)
    
    return base_signal.reshape(-1, 1), labels, window_size

def analyze_data_characteristics():
    """分析数据特征并可视化"""
    print("🔍 生成合成数据并分析特征...")
    
    # 生成数据
    data, labels, window_size = generate_synthetic_data_with_anomaly()
    data_flat = data.flatten()
    
    # 数据划分信息
    train_end = 150
    test_start = 285
    test_end = test_start + window_size
    
    train_data = data_flat[:train_end]
    test_data = data_flat[test_start:test_end]
    test_labels = labels[test_start:test_end]
    
    print(f"📊 数据统计信息:")
    print(f"   总长度: {len(data_flat)} 个时间点")
    print(f"   训练集: 0-{train_end-1} ({train_end} 个点)")
    print(f"   测试集: {test_start}-{test_end-1} ({len(test_data)} 个点)")
    print(f"   窗口大小: {window_size}")
    print(f"   异常点数量: {np.sum(labels)} ({np.sum(labels)/len(labels)*100:.1f}%)")
    print(f"   测试集中异常点: {np.sum(test_labels)} ({np.sum(test_labels)/len(test_labels)*100:.1f}%)")
    
    # 创建综合可视化
    fig = plt.figure(figsize=(20, 12))
    
    # 1. 完整数据概览
    ax1 = plt.subplot(2, 3, 1)
    time_full = np.arange(len(data_flat))
    plt.plot(time_full, data_flat, linewidth=2, color='#2E86AB', alpha=0.8)
    
    # 标记训练集和测试集区域
    plt.axvspan(0, train_end-1, alpha=0.2, color='green', label='训练集')
    plt.axvspan(test_start, test_end-1, alpha=0.2, color='blue', label='测试集')
    
    # 标记异常区域
    anomaly_indices = np.where(labels == 1)[0]
    if len(anomaly_indices) > 0:
        for i in range(len(anomaly_indices)):
            if i == 0 or anomaly_indices[i] != anomaly_indices[i-1] + 1:
                start_idx = anomaly_indices[i]
                end_idx = anomaly_indices[i]
                # 找到连续异常的结束点
                while (end_idx + 1 < len(anomaly_indices) and 
                       end_idx + 1 < len(labels) and
                       i + (end_idx - start_idx) + 1 < len(anomaly_indices) and
                       anomaly_indices[i + (end_idx - start_idx) + 1] == end_idx + 1):
                    end_idx += 1
                plt.axvspan(start_idx, end_idx, alpha=0.3, color='red')
    
    plt.title('完整数据概览：训练集 vs 测试集', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 训练集详细视图
    ax2 = plt.subplot(2, 3, 2)
    time_train = np.arange(len(train_data))
    plt.plot(time_train, train_data, linewidth=2, color='green', alpha=0.8)
    plt.title('训练集（仅正常数据）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    train_stats = f'均值: {np.mean(train_data):.3f}\n标准差: {np.std(train_data):.3f}\n最小值: {np.min(train_data):.3f}\n最大值: {np.max(train_data):.3f}'
    plt.text(0.02, 0.98, train_stats, transform=ax2.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8),
            verticalalignment='top')
    
    # 3. 测试集详细视图
    ax3 = plt.subplot(2, 3, 3)
    time_test = np.arange(len(test_data))
    plt.plot(time_test, test_data, linewidth=2, color='blue', alpha=0.8)
    
    # 标记测试集中的异常
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        plt.scatter(time_test[anomaly_mask], test_data[anomaly_mask], 
                   color='red', s=30, alpha=0.7, label='异常点')
    
    plt.title('测试集（包含异常）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    test_stats = f'均值: {np.mean(test_data):.3f}\n标准差: {np.std(test_data):.3f}\n异常点: {np.sum(test_labels)}/{len(test_labels)}'
    plt.text(0.02, 0.98, test_stats, transform=ax3.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8),
            verticalalignment='top')
    
    # 4. 周期性分析
    ax4 = plt.subplot(2, 3, 4)
    # 计算训练集的自相关
    from scipy.signal import correlate
    train_autocorr = correlate(train_data, train_data, mode='full')
    train_autocorr = train_autocorr[train_autocorr.size // 2:]
    train_autocorr = train_autocorr / train_autocorr[0]  # 归一化
    
    lags = np.arange(len(train_autocorr))
    plt.plot(lags[:50], train_autocorr[:50], linewidth=2, color='green')
    plt.title('训练集自相关（周期性检测）', fontsize=14, fontweight='bold')
    plt.xlabel('滞后步数', fontsize=12)
    plt.ylabel('自相关系数', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 找到主要周期
    peaks_idx = []
    for i in range(1, min(40, len(train_autocorr)-1)):
        if train_autocorr[i] > train_autocorr[i-1] and train_autocorr[i] > train_autocorr[i+1] and train_autocorr[i] > 0.3:
            peaks_idx.append(i)
    
    if peaks_idx:
        plt.scatter(peaks_idx, train_autocorr[peaks_idx], color='red', s=50, zorder=5)
        period_info = f'主要周期: {peaks_idx[0] if peaks_idx else "未检测到"}'
        plt.text(0.02, 0.98, period_info, transform=ax4.transAxes, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.8),
                verticalalignment='top')
    
    # 5. 数据分布对比
    ax5 = plt.subplot(2, 3, 5)
    plt.hist(train_data, bins=30, alpha=0.7, color='green', label='训练集', density=True)
    plt.hist(test_data[~anomaly_mask], bins=30, alpha=0.7, color='blue', label='测试集（正常）', density=True)
    if np.any(anomaly_mask):
        plt.hist(test_data[anomaly_mask], bins=15, alpha=0.7, color='red', label='测试集（异常）', density=True)
    
    plt.title('数据分布对比', fontsize=14, fontweight='bold')
    plt.xlabel('信号值', fontsize=12)
    plt.ylabel('密度', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 6. 异常区域放大视图
    ax6 = plt.subplot(2, 3, 6)
    # 选择包含异常的更大区域进行展示
    anomaly_start = 280
    anomaly_end = 320
    extended_data = data_flat[anomaly_start:anomaly_end]
    extended_labels = labels[anomaly_start:anomaly_end]
    extended_time = np.arange(len(extended_data))
    
    plt.plot(extended_time, extended_data, linewidth=2, color='#2E86AB', alpha=0.8)
    
    # 标记异常区域
    anomaly_mask_extended = extended_labels.astype(bool)
    if np.any(anomaly_mask_extended):
        plt.scatter(extended_time[anomaly_mask_extended], extended_data[anomaly_mask_extended], 
                   color='red', s=40, alpha=0.8, label='异常点', zorder=5)
        
        # 添加异常区域背景
        anomaly_regions_extended = []
        start = None
        for i, is_anomaly in enumerate(anomaly_mask_extended):
            if is_anomaly and start is None:
                start = i
            elif not is_anomaly and start is not None:
                anomaly_regions_extended.append((start, i-1))
                start = None
        if start is not None:
            anomaly_regions_extended.append((start, len(anomaly_mask_extended)-1))
        
        for start, end in anomaly_regions_extended:
            plt.axvspan(start, end, alpha=0.2, color='red')
    
    plt.title('异常区域放大视图', fontsize=14, fontweight='bold')
    plt.xlabel('时间步（相对位置）', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/synthetic_data_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 数据质量评估
    print(f"\n📈 数据质量评估:")
    print(f"   训练集周期性强度: {np.max(train_autocorr[1:20]):.3f}")
    print(f"   训练集变异系数: {np.std(train_data)/np.mean(train_data):.3f}")
    print(f"   异常强度（相对于正常数据）: {np.max(test_data[anomaly_mask])/np.mean(train_data):.2f}×" if np.any(anomaly_mask) else "   无异常数据")
    print(f"   信噪比估计: {np.var(train_data)/np.var(np.diff(train_data)):.2f}")

if __name__ == "__main__":
    print("=" * 80)
    print("🔍 合成数据可视化分析")
    print("=" * 80)
    
    analyze_data_characteristics()
    
    print("\n" + "=" * 80)
    print("✅ 数据分析完成！图像已保存为: experiments/synthetic_data_analysis.png")
    print("=" * 80) 
# -*- coding: utf-8 -*-
"""
合成数据可视化分析
详细展示当前合成数据的特征，包括训练集测试集划分和数据质量
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import sys
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

def generate_synthetic_data_with_anomaly():
    """生成高度周期性的合成数据，包含明显的异常"""
    np.random.seed(42)  # 确保结果可重现
    
    # 参数设置
    length = 400
    window_size = 100
    
    # 创建高度周期性的基础信号
    t = np.linspace(0, 8*np.pi, length)
    
    # 非常清晰的周期性模式 - 主要正弦波加谐波
    base_signal = (0.6 * np.sin(t) +                    # 主要周期成分
                  0.2 * np.sin(2*t + np.pi/4) +         # 二次谐波
                  0.1 * np.sin(4*t) +                   # 四次谐波
                  0.05 * np.random.normal(0, 0.05, length))  # 极少噪声
    
    # 归一化到[0, 1]范围
    base_signal = (base_signal - base_signal.min()) / (base_signal.max() - base_signal.min())
    
    # 创建标签（初始都是正常）
    labels = np.zeros(length)
    
    # 添加非常明显的尖锐异常，破坏周期性模式
    anomaly_regions = [
        (285, 315),  # 测试窗口中的主要尖锐异常
        (160, 180),  # 额外的上下文异常
    ]
    
    for start, end in anomaly_regions:
        # 创建非常尖锐的尖峰异常
        anomaly_length = end - start
        # 创建尖锐的三角形尖峰
        peak_pos = anomaly_length // 2
        spike_pattern = np.concatenate([
            np.linspace(0, 1, peak_pos),
            np.linspace(1, 0, anomaly_length - peak_pos)
        ])
        # 强异常，明显破坏模式
        base_signal[start:end] += spike_pattern * 1.0  # 非常强的尖峰
        labels[start:end] = 1
    
    # 确保信号保持在合理范围内
    base_signal = np.clip(base_signal, 0, 1.8)
    
    return base_signal.reshape(-1, 1), labels, window_size

def analyze_data_characteristics():
    """分析数据特征并可视化"""
    print("🔍 生成合成数据并分析特征...")
    
    # 生成数据
    data, labels, window_size = generate_synthetic_data_with_anomaly()
    data_flat = data.flatten()
    
    # 数据划分信息
    train_end = 150
    test_start = 285
    test_end = test_start + window_size
    
    train_data = data_flat[:train_end]
    test_data = data_flat[test_start:test_end]
    test_labels = labels[test_start:test_end]
    
    print(f"📊 数据统计信息:")
    print(f"   总长度: {len(data_flat)} 个时间点")
    print(f"   训练集: 0-{train_end-1} ({train_end} 个点)")
    print(f"   测试集: {test_start}-{test_end-1} ({len(test_data)} 个点)")
    print(f"   窗口大小: {window_size}")
    print(f"   异常点数量: {np.sum(labels)} ({np.sum(labels)/len(labels)*100:.1f}%)")
    print(f"   测试集中异常点: {np.sum(test_labels)} ({np.sum(test_labels)/len(test_labels)*100:.1f}%)")
    
    # 创建综合可视化
    fig = plt.figure(figsize=(20, 12))
    
    # 1. 完整数据概览
    ax1 = plt.subplot(2, 3, 1)
    time_full = np.arange(len(data_flat))
    plt.plot(time_full, data_flat, linewidth=2, color='#2E86AB', alpha=0.8)
    
    # 标记训练集和测试集区域
    plt.axvspan(0, train_end-1, alpha=0.2, color='green', label='训练集')
    plt.axvspan(test_start, test_end-1, alpha=0.2, color='blue', label='测试集')
    
    # 标记异常区域
    anomaly_indices = np.where(labels == 1)[0]
    if len(anomaly_indices) > 0:
        for i in range(len(anomaly_indices)):
            if i == 0 or anomaly_indices[i] != anomaly_indices[i-1] + 1:
                start_idx = anomaly_indices[i]
                end_idx = anomaly_indices[i]
                # 找到连续异常的结束点
                while (end_idx + 1 < len(anomaly_indices) and 
                       end_idx + 1 < len(labels) and
                       i + (end_idx - start_idx) + 1 < len(anomaly_indices) and
                       anomaly_indices[i + (end_idx - start_idx) + 1] == end_idx + 1):
                    end_idx += 1
                plt.axvspan(start_idx, end_idx, alpha=0.3, color='red')
    
    plt.title('完整数据概览：训练集 vs 测试集', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 训练集详细视图
    ax2 = plt.subplot(2, 3, 2)
    time_train = np.arange(len(train_data))
    plt.plot(time_train, train_data, linewidth=2, color='green', alpha=0.8)
    plt.title('训练集（仅正常数据）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    train_stats = f'均值: {np.mean(train_data):.3f}\n标准差: {np.std(train_data):.3f}\n最小值: {np.min(train_data):.3f}\n最大值: {np.max(train_data):.3f}'
    plt.text(0.02, 0.98, train_stats, transform=ax2.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8),
            verticalalignment='top')
    
    # 3. 测试集详细视图
    ax3 = plt.subplot(2, 3, 3)
    time_test = np.arange(len(test_data))
    plt.plot(time_test, test_data, linewidth=2, color='blue', alpha=0.8)
    
    # 标记测试集中的异常
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        plt.scatter(time_test[anomaly_mask], test_data[anomaly_mask], 
                   color='red', s=30, alpha=0.7, label='异常点')
    
    plt.title('测试集（包含异常）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    test_stats = f'均值: {np.mean(test_data):.3f}\n标准差: {np.std(test_data):.3f}\n异常点: {np.sum(test_labels)}/{len(test_labels)}'
    plt.text(0.02, 0.98, test_stats, transform=ax3.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8),
            verticalalignment='top')
    
    # 4. 周期性分析
    ax4 = plt.subplot(2, 3, 4)
    # 计算训练集的自相关
    from scipy.signal import correlate
    train_autocorr = correlate(train_data, train_data, mode='full')
    train_autocorr = train_autocorr[train_autocorr.size // 2:]
    train_autocorr = train_autocorr / train_autocorr[0]  # 归一化
    
    lags = np.arange(len(train_autocorr))
    plt.plot(lags[:50], train_autocorr[:50], linewidth=2, color='green')
    plt.title('训练集自相关（周期性检测）', fontsize=14, fontweight='bold')
    plt.xlabel('滞后步数', fontsize=12)
    plt.ylabel('自相关系数', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 找到主要周期
    peaks_idx = []
    for i in range(1, min(40, len(train_autocorr)-1)):
        if train_autocorr[i] > train_autocorr[i-1] and train_autocorr[i] > train_autocorr[i+1] and train_autocorr[i] > 0.3:
            peaks_idx.append(i)
    
    if peaks_idx:
        plt.scatter(peaks_idx, train_autocorr[peaks_idx], color='red', s=50, zorder=5)
        period_info = f'主要周期: {peaks_idx[0] if peaks_idx else "未检测到"}'
        plt.text(0.02, 0.98, period_info, transform=ax4.transAxes, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.8),
                verticalalignment='top')
    
    # 5. 数据分布对比
    ax5 = plt.subplot(2, 3, 5)
    plt.hist(train_data, bins=30, alpha=0.7, color='green', label='训练集', density=True)
    plt.hist(test_data[~anomaly_mask], bins=30, alpha=0.7, color='blue', label='测试集（正常）', density=True)
    if np.any(anomaly_mask):
        plt.hist(test_data[anomaly_mask], bins=15, alpha=0.7, color='red', label='测试集（异常）', density=True)
    
    plt.title('数据分布对比', fontsize=14, fontweight='bold')
    plt.xlabel('信号值', fontsize=12)
    plt.ylabel('密度', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 6. 异常区域放大视图
    ax6 = plt.subplot(2, 3, 6)
    # 选择包含异常的更大区域进行展示
    anomaly_start = 280
    anomaly_end = 320
    extended_data = data_flat[anomaly_start:anomaly_end]
    extended_labels = labels[anomaly_start:anomaly_end]
    extended_time = np.arange(len(extended_data))
    
    plt.plot(extended_time, extended_data, linewidth=2, color='#2E86AB', alpha=0.8)
    
    # 标记异常区域
    anomaly_mask_extended = extended_labels.astype(bool)
    if np.any(anomaly_mask_extended):
        plt.scatter(extended_time[anomaly_mask_extended], extended_data[anomaly_mask_extended], 
                   color='red', s=40, alpha=0.8, label='异常点', zorder=5)
        
        # 添加异常区域背景
        anomaly_regions_extended = []
        start = None
        for i, is_anomaly in enumerate(anomaly_mask_extended):
            if is_anomaly and start is None:
                start = i
            elif not is_anomaly and start is not None:
                anomaly_regions_extended.append((start, i-1))
                start = None
        if start is not None:
            anomaly_regions_extended.append((start, len(anomaly_mask_extended)-1))
        
        for start, end in anomaly_regions_extended:
            plt.axvspan(start, end, alpha=0.2, color='red')
    
    plt.title('异常区域放大视图', fontsize=14, fontweight='bold')
    plt.xlabel('时间步（相对位置）', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/synthetic_data_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 数据质量评估
    print(f"\n📈 数据质量评估:")
    print(f"   训练集周期性强度: {np.max(train_autocorr[1:20]):.3f}")
    print(f"   训练集变异系数: {np.std(train_data)/np.mean(train_data):.3f}")
    print(f"   异常强度（相对于正常数据）: {np.max(test_data[anomaly_mask])/np.mean(train_data):.2f}×" if np.any(anomaly_mask) else "   无异常数据")
    print(f"   信噪比估计: {np.var(train_data)/np.var(np.diff(train_data)):.2f}")

if __name__ == "__main__":
    print("=" * 80)
    print("🔍 合成数据可视化分析")
    print("=" * 80)
    
    analyze_data_characteristics()
    
    print("\n" + "=" * 80)
    print("✅ 数据分析完成！图像已保存为: experiments/synthetic_data_analysis.png")
    print("=" * 80) 