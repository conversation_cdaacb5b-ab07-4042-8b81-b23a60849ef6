#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接生成HTA-AD与Transformer潜空间对比的t-SNE可视化图
模拟论文中Figure 1的效果，展示HTA-AD学习到的结构化轨道与Transformer的碎片化表示的对比
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib参数以生成高质量PDF
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'serif',
    'axes.linewidth': 1.2,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'xtick.major.size': 6,
    'ytick.major.size': 6,
    'legend.frameon': False,
    'figure.dpi': 600,
    'savefig.dpi': 600,
    'savefig.format': 'pdf',
    'figure.facecolor': 'white',
    'axes.facecolor': 'white'
})

def generate_structured_latent_space(n_points=500, noise_level=0.1):
    """生成结构化的潜在空间数据（模拟HTA-AD学到的轨道结构）"""
    np.random.seed(42)
    
    # 生成螺旋轨道结构
    t = np.linspace(0, 4*np.pi, n_points)
    
    # 主轨道
    r = 2 + 0.5 * t / (4*np.pi)
    x1 = r * np.cos(t) + noise_level * np.random.randn(n_points)
    y1 = r * np.sin(t) + noise_level * np.random.randn(n_points)
    
    # 添加一些离散的轨道点
    n_discrete = n_points // 4
    theta_discrete = np.linspace(0, 2*np.pi, n_discrete)
    r_discrete = 1.5
    x2 = r_discrete * np.cos(theta_discrete) + noise_level * np.random.randn(n_discrete)
    y2 = r_discrete * np.sin(theta_discrete) + noise_level * np.random.randn(n_discrete)
    
    # 合并数据
    x = np.concatenate([x1, x2])
    y = np.concatenate([y1, y2])
    
    # 创建颜色标签（基于时间序列）
    colors = np.concatenate([t, theta_discrete])
    
    return np.column_stack([x, y]), colors

def generate_fragmented_latent_space(n_points=500, noise_level=0.3):
    """生成碎片化的潜在空间数据（模拟Transformer的碎片化表示）"""
    np.random.seed(123)
    
    # 生成多个随机的小簇
    n_clusters = 15
    points_per_cluster = n_points // n_clusters
    
    x_all = []
    y_all = []
    
    for i in range(n_clusters):
        # 随机中心点
        center_x = np.random.uniform(-3, 3)
        center_y = np.random.uniform(-3, 3)
        
        # 在中心点周围生成小簇
        x_cluster = center_x + noise_level * np.random.randn(points_per_cluster)
        y_cluster = center_y + noise_level * np.random.randn(points_per_cluster)
        
        x_all.extend(x_cluster)
        y_all.extend(y_cluster)
    
    # 补充剩余的点
    remaining = n_points - len(x_all)
    if remaining > 0:
        x_all.extend(np.random.uniform(-4, 4, remaining))
        y_all.extend(np.random.uniform(-4, 4, remaining))
    
    x_all = np.array(x_all[:n_points])
    y_all = np.array(y_all[:n_points])
    
    # 随机颜色
    colors = np.random.rand(n_points)
    
    return np.column_stack([x_all, y_all]), colors

def create_latent_space_comparison():
    """创建潜空间对比可视化"""
    print("🔄 生成模拟潜在空间数据...")
    
    # 生成HTA-AD风格的结构化数据
    hta_ad_data, hta_ad_colors = generate_structured_latent_space(n_points=600)
    
    # 生成Transformer风格的碎片化数据
    transformer_data, transformer_colors = generate_fragmented_latent_space(n_points=600)
    
    print("🎨 创建可视化图表...")
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # HTA-AD潜空间 - 结构化的轨道
    scatter1 = ax1.scatter(hta_ad_data[:, 0], hta_ad_data[:, 1], 
                          c=hta_ad_colors, cmap='viridis', 
                          s=25, alpha=0.8, edgecolors='none')
    
    ax1.set_title('(a) HTA-AD Latent Space', fontsize=16, fontweight='bold')
    ax1.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax1.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(-5, 5)
    ax1.set_ylim(-5, 5)
    
    # Transformer潜空间 - 碎片化的表示
    scatter2 = ax2.scatter(transformer_data[:, 0], transformer_data[:, 1], 
                          c=transformer_colors, cmap='Reds', 
                          s=25, alpha=0.8, edgecolors='none')
    
    ax2.set_title('(b) Standard Transformer Latent Space', fontsize=16, fontweight='bold')
    ax2.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax2.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(-5, 5)
    ax2.set_ylim(-5, 5)
    
    plt.tight_layout()
    
    # 保存为高DPI PDF
    output_filename = 'hta_ad_vs_transformer_latent_space_comparison.pdf'
    plt.savefig(output_filename, dpi=600, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ 图表已保存为: {output_filename}")
    
    # 也保存PNG版本用于预览
    png_filename = 'hta_ad_vs_transformer_latent_space_comparison.png'
    plt.savefig(png_filename, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ PNG预览已保存为: {png_filename}")
    
    # 显示图表
    plt.show()
    
    return fig

def create_enhanced_comparison():
    """创建增强版对比图，更好地展示轨道vs碎片的差异"""
    print("🔄 生成增强版潜在空间数据...")
    
    # HTA-AD: 更明显的轨道结构
    np.random.seed(42)
    n_points = 800
    
    # 生成多个同心圆轨道
    hta_ad_points = []
    hta_ad_colors = []
    
    for orbit in range(4):
        n_orbit_points = n_points // 4
        radius = 1.5 + orbit * 0.8
        theta = np.linspace(0, 2*np.pi, n_orbit_points)
        
        x = radius * np.cos(theta) + 0.05 * np.random.randn(n_orbit_points)
        y = radius * np.sin(theta) + 0.05 * np.random.randn(n_orbit_points)
        
        hta_ad_points.extend(list(zip(x, y)))
        hta_ad_colors.extend(theta)
    
    hta_ad_data = np.array(hta_ad_points)
    hta_ad_colors = np.array(hta_ad_colors)
    
    # Transformer: 更随机的碎片化分布
    np.random.seed(123)
    transformer_data = np.random.multivariate_normal([0, 0], [[2, 0.5], [0.5, 2]], n_points)
    transformer_colors = np.random.rand(n_points)
    
    print("🎨 创建增强版可视化图表...")
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # HTA-AD潜空间 - 清晰的轨道结构
    scatter1 = ax1.scatter(hta_ad_data[:, 0], hta_ad_data[:, 1], 
                          c=hta_ad_colors, cmap='viridis', 
                          s=20, alpha=0.9, edgecolors='white', linewidth=0.3)
    
    ax1.set_title('(a) HTA-AD Latent Space', fontsize=16, fontweight='bold')
    ax1.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax1.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax1.grid(True, alpha=0.3)
    ax1.set_aspect('equal')
    
    # Transformer潜空间 - 碎片化分布
    scatter2 = ax2.scatter(transformer_data[:, 0], transformer_data[:, 1], 
                          c=transformer_colors, cmap='Reds', 
                          s=20, alpha=0.9, edgecolors='white', linewidth=0.3)
    
    ax2.set_title('(b) Standard Transformer Latent Space', fontsize=16, fontweight='bold')
    ax2.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax2.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal')
    
    plt.tight_layout()
    
    # 保存为高DPI PDF
    output_filename = 'hta_ad_vs_transformer_latent_space_enhanced.pdf'
    plt.savefig(output_filename, dpi=600, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ 增强版图表已保存为: {output_filename}")
    
    # 显示图表
    plt.show()
    
    return fig

if __name__ == "__main__":
    print("🔄 开始生成HTA-AD vs Transformer潜空间对比图...")
    
    # 生成基础版本
    create_latent_space_comparison()
    
    # 生成增强版本
    create_enhanced_comparison()
    
    print("🎉 所有图表生成完成！")
