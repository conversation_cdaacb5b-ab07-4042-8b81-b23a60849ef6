#!/usr/bin/env python3
"""
运行TSB-AD基准测试，包含HTA-AD和HTA-AD-SAE
"""

import numpy as np
import pandas as pd
import sys
import os
import time
import warnings
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

def load_sample_dataset():
    """加载示例数据集"""
    print("📊 生成示例数据集...")
    
    np.random.seed(42)
    n_samples = 1000
    
    # 生成正常数据 (多个周期的正弦波)
    t = np.linspace(0, 8*np.pi, n_samples)
    data = np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.randn(n_samples)
    
    # 添加异常
    anomaly_indices = []
    
    # 添加点异常
    for _ in range(20):
        idx = np.random.randint(100, n_samples-100)
        data[idx] += np.random.normal(3, 0.5)
        anomaly_indices.append(idx)
    
    # 添加集体异常
    for _ in range(5):
        start_idx = np.random.randint(100, n_samples-150)
        end_idx = start_idx + np.random.randint(20, 50)
        data[start_idx:end_idx] += np.random.normal(1.5, 0.3, end_idx-start_idx)
        anomaly_indices.extend(range(start_idx, end_idx))
    
    # 创建标签
    labels = np.zeros(n_samples)
    labels[anomaly_indices] = 1
    
    print(f"  - 数据点数: {n_samples}")
    print(f"  - 异常点数: {np.sum(labels)}")
    print(f"  - 异常比例: {np.sum(labels)/n_samples:.2%}")
    
    return data, labels


def run_benchmark_comparison():
    """运行基准测试对比"""
    print("🚀 运行TSB-AD基准测试对比")
    print("=" * 60)
    
    # 加载数据
    data, labels = load_sample_dataset()
    
    # 分割数据
    split_point = int(len(data) * 0.7)
    train_data = data[:split_point]
    test_data = data[split_point:]
    test_labels = labels[split_point:]
    
    print(f"\n📋 数据分割:")
    print(f"  - 训练数据: {len(train_data)} 点")
    print(f"  - 测试数据: {len(test_data)} 点")
    print(f"  - 测试异常: {np.sum(test_labels)} 点")
    
    # 测试模型列表
    models_to_test = [
        {
            'name': 'HTA-AD',
            'function': 'run_HTA_AD',
            'params': {
                'window_size': 64,
                'epochs': 10,
                'lr': 1e-3,
                'batch_size': 32,
                'latent_dim': 16
            }
        },
        {
            'name': 'HTA-AD-SAE',
            'function': 'run_HTA_AD_SAE',
            'params': {
                'window_size': 64,
                'epochs': 10,
                'lr': 1e-3,
                'batch_size': 32,
                'latent_dim': 16,
                'sae_hidden_dim': 64,
                'sae_sparsity_weight': 0.01
            }
        }
    ]
    
    results = []
    
    for model_config in models_to_test:
        print(f"\n🧪 测试 {model_config['name']}")
        print("-" * 40)
        
        try:
            # 导入函数
            from TSB_AD.model_wrapper import run_Semisupervise_AD
            
            # 记录开始时间
            start_time = time.time()
            
            # 运行模型
            scores = run_Semisupervise_AD(
                model_config['function'].replace('run_', ''),
                train_data, test_data,
                **model_config['params']
            )
            
            # 记录结束时间
            end_time = time.time()
            training_time = end_time - start_time
            
            # 计算评估指标
            metrics = calculate_metrics(test_labels, scores)
            metrics['training_time'] = training_time
            metrics['model'] = model_config['name']
            
            results.append(metrics)
            
            print(f"  ✅ {model_config['name']} 完成")
            print(f"  📊 AUC: {metrics['auc']:.4f}")
            print(f"  📊 F1: {metrics['f1']:.4f}")
            print(f"  ⏱️ 训练时间: {training_time:.2f}s")
            
        except Exception as e:
            print(f"  ❌ {model_config['name']} 失败: {e}")
            results.append({
                'model': model_config['name'],
                'auc': 0.0,
                'f1': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'training_time': 0.0,
                'error': str(e)
            })
    
    return results


def calculate_metrics(y_true, y_scores):
    """计算评估指标"""
    from sklearn.metrics import roc_auc_score, precision_recall_curve, f1_score
    
    # 计算AUC
    try:
        auc = roc_auc_score(y_true, y_scores)
    except:
        auc = 0.5
    
    # 找到最佳阈值
    precision, recall, thresholds = precision_recall_curve(y_true, y_scores)
    f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
    best_threshold_idx = np.argmax(f1_scores)
    best_threshold = thresholds[best_threshold_idx] if best_threshold_idx < len(thresholds) else np.median(y_scores)
    
    # 计算二分类指标
    y_pred = (y_scores >= best_threshold).astype(int)
    
    from sklearn.metrics import precision_score, recall_score
    precision_val = precision_score(y_true, y_pred, zero_division=0)
    recall_val = recall_score(y_true, y_pred, zero_division=0)
    f1_val = f1_score(y_true, y_pred, zero_division=0)
    
    return {
        'auc': auc,
        'precision': precision_val,
        'recall': recall_val,
        'f1': f1_val,
        'best_threshold': best_threshold
    }


def save_results(results):
    """保存结果到CSV文件"""
    df = pd.DataFrame(results)
    
    # 确保目录存在
    os.makedirs('results/data', exist_ok=True)
    
    # 保存结果
    output_path = 'results/data/tsb_ad_benchmark_results.csv'
    df.to_csv(output_path, index=False)
    
    print(f"\n💾 结果已保存到: {output_path}")
    
    return df


def print_summary(results_df):
    """打印结果总结"""
    print("\n" + "=" * 60)
    print("🎯 基准测试结果总结")
    print("=" * 60)
    
    print(f"{'模型':<15} {'AUC':<8} {'F1':<8} {'精确率':<8} {'召回率':<8} {'时间(s)':<10}")
    print("-" * 60)
    
    for _, row in results_df.iterrows():
        if 'error' not in row or pd.isna(row.get('error')):
            print(f"{row['model']:<15} {row['auc']:<8.4f} {row['f1']:<8.4f} "
                  f"{row['precision']:<8.4f} {row['recall']:<8.4f} {row['training_time']:<10.2f}")
        else:
            print(f"{row['model']:<15} {'ERROR':<8} {'ERROR':<8} {'ERROR':<8} {'ERROR':<8} {'ERROR':<10}")
    
    # 找到最佳模型
    valid_results = results_df[~results_df['auc'].isna() & (results_df['auc'] > 0)]
    if not valid_results.empty:
        best_model = valid_results.loc[valid_results['auc'].idxmax()]
        print(f"\n🏆 最佳模型: {best_model['model']} (AUC: {best_model['auc']:.4f})")


def main():
    """主函数"""
    print("🚀 HTA-AD TSB-AD 基准测试")
    print("=" * 60)
    
    try:
        # 运行基准测试
        results = run_benchmark_comparison()
        
        # 保存结果
        results_df = save_results(results)
        
        # 打印总结
        print_summary(results_df)
        
        print("\n✅ 基准测试完成！")
        
    except Exception as e:
        print(f"\n❌ 基准测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
