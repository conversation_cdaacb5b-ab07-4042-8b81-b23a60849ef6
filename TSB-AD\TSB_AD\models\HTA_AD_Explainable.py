# -*- coding: utf-8 -*-
# HTA-AD with LLM Explainer: Explainable Hourglass Temporal Autoencoder for Anomaly Detection
# 集成大语言模型的可解释异常检测模型

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
import warnings
from torch.nn.utils import weight_norm
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import json
import openai
import os

warnings.filterwarnings('ignore')

# 导入基础HTA-AD组件
from .HTA_AD import HTA_Model, HTA_AD

@dataclass
class ExplainableAnomalyResult:
    """可解释异常检测结果"""
    anomaly_scores: np.ndarray
    explanations: List[Dict]
    feature_importance: Dict
    temporal_analysis: Dict
    confidence_scores: np.ndarray
    repair_suggestions: List[str]

class LLMExplainerModule:
    """LLM解释器模块"""
    
    def __init__(self, api_key: Optional[str] = None, model_name: str = "gpt-3.5-turbo"):
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.model_name = model_name
        self.client = None
        
        if self.api_key:
            try:
                openai.api_key = self.api_key
                self.client = openai
            except Exception as e:
                print(f"Warning: OpenAI client initialization failed: {e}")
                print("Will use mock explanations instead.")
        
        self.prompt_templates = self._initialize_prompts()
        self.anomaly_patterns = self._initialize_patterns()
    
    def _initialize_prompts(self) -> Dict[str, str]:
        """初始化提示词模板"""
        return {
            'anomaly_explanation': """
你是一个时序异常检测专家。基于以下技术分析，请生成易懂的异常解释：

数据特征分析：
{feature_analysis}

异常检测结果：
- 异常分数: {anomaly_score:.4f}
- 异常类型: {anomaly_type}
- 严重程度: {severity}
- 影响范围: {affected_range}

请提供：
1. 用通俗语言描述发现的异常（2-3句话）
2. 分析可能的根本原因（考虑技术和业务因素）
3. 提供3-5个具体的修复建议
4. 给出预防措施建议

请以JSON格式回答：
{
    "summary": "异常简要描述",
    "detailed_description": "详细异常描述",
    "root_causes": ["原因1", "原因2", "原因3"],
    "repair_actions": ["修复建议1", "修复建议2", "修复建议3"],
    "prevention_measures": ["预防措施1", "预防措施2"],
    "confidence": 0.85,
    "urgency": "high/medium/low"
}
""",
            
            'feature_importance': """
基于时序异常检测模型的内部表示，解释哪些特征对异常检测最关键：

模型分析：
{model_analysis}

请解释：
1. 最重要的特征及其贡献
2. 这些特征如何帮助识别异常
3. 模型决策的可信度
"""
        }
    
    def _initialize_patterns(self) -> Dict:
        """初始化异常模式库"""
        return {
            'spike': {
                'name': '尖峰异常',
                'description': '数值突然大幅偏离正常范围',
                'typical_causes': ['传感器故障', '外部干扰', '瞬时过载'],
                'repair_strategies': ['检查传感器', '排查干扰源', '验证系统负载']
            },
            'drift': {
                'name': '漂移异常',
                'description': '数值呈现持续性偏离趋势',
                'typical_causes': ['设备老化', '环境变化', '校准偏差'],
                'repair_strategies': ['重新校准', '检查环境', '更新参数']
            },
            'level_shift': {
                'name': '水平偏移',
                'description': '数值发生阶跃性变化',
                'typical_causes': ['配置变更', '设备更换', '模式改变'],
                'repair_strategies': ['检查配置', '验证设备', '确认变更']
            },
            'oscillation': {
                'name': '振荡异常',
                'description': '出现异常的周期性波动',
                'typical_causes': ['控制系统不稳定', '反馈回路问题', '共振现象'],
                'repair_strategies': ['调整控制参数', '检查反馈系统', '分析频率响应']
            },
            'noise': {
                'name': '噪声异常',
                'description': '随机波动明显增加',
                'typical_causes': ['信号干扰', '设备故障', '环境噪声'],
                'repair_strategies': ['屏蔽干扰', '检修设备', '改善环境']
            }
        }
    
    def generate_explanation(self, anomaly_data: Dict) -> Dict:
        """生成异常解释"""
        try:
            if self.client:
                return self._call_llm_api(anomaly_data)
            else:
                return self._generate_rule_based_explanation(anomaly_data)
        except Exception as e:
            print(f"LLM explanation failed: {e}, falling back to rule-based explanation")
            return self._generate_rule_based_explanation(anomaly_data)
    
    def _call_llm_api(self, anomaly_data: Dict) -> Dict:
        """调用LLM API生成解释"""
        prompt = self.prompt_templates['anomaly_explanation'].format(
            feature_analysis=json.dumps(anomaly_data.get('features', {}), indent=2),
            anomaly_score=anomaly_data.get('score', 0.0),
            anomaly_type=anomaly_data.get('type', 'unknown'),
            severity=anomaly_data.get('severity', 'unknown'),
            affected_range=anomaly_data.get('range', 'unknown')
        )
        
        response = self.client.ChatCompletion.create(
            model=self.model_name,
            messages=[
                {"role": "system", "content": "你是一个专业的时序数据异常检测专家。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=1000
        )
        
        content = response.choices[0].message.content
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            # 如果JSON解析失败，返回文本内容
            return {"summary": content, "confidence": 0.7}
    
    def _generate_rule_based_explanation(self, anomaly_data: Dict) -> Dict:
        """基于规则的解释生成（备用方案）"""
        anomaly_type = anomaly_data.get('type', 'unknown')
        severity = anomaly_data.get('severity', 'medium')
        score = anomaly_data.get('score', 0.0)
        
        if anomaly_type in self.anomaly_patterns:
            pattern = self.anomaly_patterns[anomaly_type]
            
            return {
                "summary": f"检测到{severity}级别的{pattern['name']}",
                "detailed_description": f"{pattern['description']}，异常分数为{score:.4f}",
                "root_causes": pattern['typical_causes'],
                "repair_actions": pattern['repair_strategies'],
                "prevention_measures": [
                    "建立实时监控机制",
                    "定期维护和校准设备",
                    "建立异常响应流程"
                ],
                "confidence": min(0.9, score * 2),
                "urgency": "high" if score > 0.8 else "medium" if score > 0.5 else "low"
            }
        else:
            return {
                "summary": f"检测到{severity}级别的异常模式",
                "detailed_description": f"发现未知类型的异常，异常分数为{score:.4f}",
                "root_causes": ["需要进一步分析"],
                "repair_actions": ["详细检查数据源", "分析历史模式", "咨询领域专家"],
                "prevention_measures": ["加强数据质量监控"],
                "confidence": 0.6,
                "urgency": "medium"
            }

class FeatureAnalyzer:
    """特征分析器"""
    
    def __init__(self):
        self.feature_extractors = {
            'statistical': self._extract_statistical_features,
            'frequency': self._extract_frequency_features,
            'temporal': self._extract_temporal_features,
            'morphological': self._extract_morphological_features
        }
    
    def analyze_features(self, data: np.ndarray, latent_repr: np.ndarray = None) -> Dict:
        """综合特征分析"""
        features = {}
        
        for feature_type, extractor in self.feature_extractors.items():
            try:
                features[feature_type] = extractor(data)
            except Exception as e:
                print(f"Feature extraction failed for {feature_type}: {e}")
                features[feature_type] = {}
        
        if latent_repr is not None:
            features['latent'] = self._analyze_latent_representation(latent_repr)
        
        return features
    
    def _extract_statistical_features(self, data: np.ndarray) -> Dict:
        """提取统计特征"""
        return {
            'mean': float(np.mean(data)),
            'std': float(np.std(data)),
            'skewness': float(self._calculate_skewness(data)),
            'kurtosis': float(self._calculate_kurtosis(data)),
            'range': float(np.max(data) - np.min(data)),
            'variance': float(np.var(data))
        }
    
    def _extract_frequency_features(self, data: np.ndarray) -> Dict:
        """提取频域特征"""
        fft = np.fft.fft(data.flatten())
        freqs = np.fft.fftfreq(len(data.flatten()))
        
        power_spectrum = np.abs(fft) ** 2
        dominant_freq_idx = np.argmax(power_spectrum[1:len(power_spectrum)//2]) + 1
        
        return {
            'dominant_frequency': float(freqs[dominant_freq_idx]),
            'spectral_energy': float(np.sum(power_spectrum)),
            'spectral_centroid': float(self._calculate_spectral_centroid(fft, freqs)),
            'spectral_rolloff': float(self._calculate_spectral_rolloff(power_spectrum, freqs))
        }
    
    def _extract_temporal_features(self, data: np.ndarray) -> Dict:
        """提取时序特征"""
        diff1 = np.diff(data.flatten())
        diff2 = np.diff(diff1)
        
        return {
            'trend': float(np.polyfit(range(len(data.flatten())), data.flatten(), 1)[0]),
            'volatility': float(np.std(diff1)),
            'acceleration': float(np.std(diff2)),
            'turning_points': int(len(self._find_turning_points(data.flatten()))),
            'autocorrelation': float(self._calculate_autocorrelation(data.flatten()))
        }
    
    def _extract_morphological_features(self, data: np.ndarray) -> Dict:
        """提取形态特征"""
        data_flat = data.flatten()
        peaks = self._find_peaks(data_flat)
        valleys = self._find_valleys(data_flat)
        
        return {
            'num_peaks': len(peaks),
            'num_valleys': len(valleys),
            'peak_prominence': float(np.mean([data_flat[p] for p in peaks]) if peaks else 0),
            'valley_depth': float(np.mean([data_flat[v] for v in valleys]) if valleys else 0),
            'smoothness': float(self._calculate_smoothness(data_flat))
        }
    
    def _analyze_latent_representation(self, latent_repr: np.ndarray) -> Dict:
        """分析潜在表示"""
        return {
            'latent_mean': float(np.mean(latent_repr)),
            'latent_std': float(np.std(latent_repr)),
            'latent_energy': float(np.sum(latent_repr ** 2)),
            'latent_sparsity': float(np.sum(np.abs(latent_repr) < 0.1) / len(latent_repr))
        }
    
    # 辅助方法
    def _calculate_skewness(self, x):
        mean = np.mean(x)
        std = np.std(x)
        return np.mean(((x - mean) / std) ** 3) if std > 0 else 0
    
    def _calculate_kurtosis(self, x):
        mean = np.mean(x)
        std = np.std(x)
        return np.mean(((x - mean) / std) ** 4) - 3 if std > 0 else 0
    
    def _calculate_spectral_centroid(self, fft, freqs):
        magnitude = np.abs(fft)
        return np.sum(freqs * magnitude) / np.sum(magnitude) if np.sum(magnitude) > 0 else 0
    
    def _calculate_spectral_rolloff(self, power_spectrum, freqs, threshold=0.85):
        cumsum = np.cumsum(power_spectrum)
        total_energy = cumsum[-1]
        rolloff_idx = np.where(cumsum >= threshold * total_energy)[0]
        return freqs[rolloff_idx[0]] if len(rolloff_idx) > 0 else 0
    
    def _find_turning_points(self, x):
        turning_points = []
        for i in range(1, len(x) - 1):
            if (x[i] > x[i-1] and x[i] > x[i+1]) or (x[i] < x[i-1] and x[i] < x[i+1]):
                turning_points.append(i)
        return turning_points
    
    def _calculate_autocorrelation(self, x, lag=1):
        if len(x) <= lag:
            return 0
        return np.corrcoef(x[:-lag], x[lag:])[0, 1] if not np.isnan(np.corrcoef(x[:-lag], x[lag:])[0, 1]) else 0
    
    def _find_peaks(self, x, threshold=None):
        if threshold is None:
            threshold = np.mean(x)
        peaks = []
        for i in range(1, len(x) - 1):
            if x[i] > x[i-1] and x[i] > x[i+1] and x[i] > threshold:
                peaks.append(i)
        return peaks
    
    def _find_valleys(self, x, threshold=None):
        if threshold is None:
            threshold = np.mean(x)
        valleys = []
        for i in range(1, len(x) - 1):
            if x[i] < x[i-1] and x[i] < x[i+1] and x[i] < threshold:
                valleys.append(i)
        return valleys
    
    def _calculate_smoothness(self, x):
        if len(x) < 3:
            return 1.0
        second_diff = np.diff(x, 2)
        return 1.0 / (1.0 + np.mean(np.abs(second_diff)))

class AnomalyPatternClassifier:
    """异常模式分类器"""
    
    def __init__(self):
        self.pattern_rules = {
            'spike': lambda features: (
                features.get('temporal', {}).get('volatility', 0) > 0.5 and
                features.get('morphological', {}).get('num_peaks', 0) > 0 and
                features.get('statistical', {}).get('kurtosis', 0) > 2
            ),
            'drift': lambda features: (
                abs(features.get('temporal', {}).get('trend', 0)) > 0.1 and
                features.get('temporal', {}).get('volatility', 0) < 0.3
            ),
            'level_shift': lambda features: (
                features.get('temporal', {}).get('turning_points', 0) < 3 and
                features.get('statistical', {}).get('range', 0) > 1.0
            ),
            'oscillation': lambda features: (
                features.get('frequency', {}).get('dominant_frequency', 0) > 0.1 and
                features.get('temporal', {}).get('autocorrelation', 0) > 0.5
            ),
            'noise': lambda features: (
                features.get('temporal', {}).get('volatility', 0) > 0.8 and
                features.get('statistical', {}).get('std', 0) > 0.5
            )
        }
    
    def classify_anomaly_pattern(self, features: Dict) -> Tuple[str, float]:
        """分类异常模式"""
        pattern_scores = {}
        
        for pattern_name, rule_func in self.pattern_rules.items():
            try:
                if rule_func(features):
                    pattern_scores[pattern_name] = self._calculate_pattern_confidence(pattern_name, features)
                else:
                    pattern_scores[pattern_name] = 0.0
            except Exception as e:
                print(f"Pattern classification failed for {pattern_name}: {e}")
                pattern_scores[pattern_name] = 0.0
        
        if not pattern_scores or max(pattern_scores.values()) == 0:
            return 'unknown', 0.5
        
        best_pattern = max(pattern_scores, key=pattern_scores.get)
        confidence = pattern_scores[best_pattern]
        
        return best_pattern, confidence
    
    def _calculate_pattern_confidence(self, pattern_name: str, features: Dict) -> float:
        """计算模式置信度"""
        # 基于特征强度计算置信度
        base_confidence = 0.7
        
        if pattern_name == 'spike':
            volatility = features.get('temporal', {}).get('volatility', 0)
            kurtosis = features.get('statistical', {}).get('kurtosis', 0)
            return min(0.95, base_confidence + 0.1 * volatility + 0.05 * kurtosis)
        
        elif pattern_name == 'drift':
            trend = abs(features.get('temporal', {}).get('trend', 0))
            return min(0.95, base_confidence + 0.2 * trend)
        
        elif pattern_name == 'oscillation':
            autocorr = features.get('temporal', {}).get('autocorrelation', 0)
            return min(0.95, base_confidence + 0.2 * autocorr)
        
        return base_confidence

class HTA_AD_Explainable(HTA_AD):
    """可解释的HTA-AD模型"""
    
    def __init__(self, HP, normalize=True, enable_llm=True, openai_api_key=None):
        super().__init__(HP, normalize)
        
        self.enable_llm = enable_llm
        self.feature_analyzer = FeatureAnalyzer()
        self.pattern_classifier = AnomalyPatternClassifier()
        
        if enable_llm:
            self.llm_explainer = LLMExplainerModule(api_key=openai_api_key)
        else:
            self.llm_explainer = None
        
        print(f"🔍 Explainable HTA-AD initialized (LLM: {'enabled' if enable_llm else 'disabled'})")
    
    def explain_anomalies(self, X, threshold_percentile=95) -> ExplainableAnomalyResult:
        """生成可解释的异常检测结果"""
        
        # 1. 基础异常检测
        anomaly_scores = self.decision_function(X)
        
        # 2. 获取模型内部表示
        latent_representations = self.get_latent_representations(X)
        reconstructions, reconstruction_errors = self.get_reconstruction_details(X)
        
        # 3. 识别异常区域
        threshold = np.percentile(anomaly_scores, threshold_percentile)
        anomaly_mask = anomaly_scores > threshold
        anomaly_indices = np.where(anomaly_mask)[0]
        
        # 4. 生成解释
        explanations = []
        confidence_scores = np.zeros_like(anomaly_scores)
        
        if len(anomaly_indices) > 0:
            # 分析异常区域
            anomaly_regions = self._identify_anomaly_regions(anomaly_mask)
            
            for region in anomaly_regions:
                start, end = region['start'], region['end']
                region_data = X[start:end+1]
                region_scores = anomaly_scores[start:end+1]
                
                # 特征分析
                features = self.feature_analyzer.analyze_features(
                    region_data, 
                    latent_representations[start:end+1] if len(latent_representations) > 0 else None
                )
                
                # 模式分类
                pattern_type, pattern_confidence = self.pattern_classifier.classify_anomaly_pattern(features)
                
                # 严重程度评估
                severity = self._assess_severity(region_scores)
                
                # 生成解释
                anomaly_data = {
                    'features': features,
                    'score': float(np.max(region_scores)),
                    'type': pattern_type,
                    'severity': severity,
                    'range': f"{start}-{end}",
                    'duration': end - start + 1
                }
                
                if self.llm_explainer:
                    explanation = self.llm_explainer.generate_explanation(anomaly_data)
                else:
                    explanation = self._generate_basic_explanation(anomaly_data)
                
                explanation['region'] = region
                explanation['pattern_confidence'] = pattern_confidence
                explanations.append(explanation)
                
                # 设置置信度分数
                confidence_scores[start:end+1] = pattern_confidence
        
        # 5. 特征重要性分析
        feature_importance = self._analyze_feature_importance(X, anomaly_scores)
        
        # 6. 时序分析
        temporal_analysis = self._analyze_temporal_patterns(X, anomaly_scores, anomaly_mask)
        
        # 7. 修复建议汇总
        repair_suggestions = self._generate_repair_suggestions(explanations)
        
        return ExplainableAnomalyResult(
            anomaly_scores=anomaly_scores,
            explanations=explanations,
            feature_importance=feature_importance,
            temporal_analysis=temporal_analysis,
            confidence_scores=confidence_scores,
            repair_suggestions=repair_suggestions
        )
    
    def _identify_anomaly_regions(self, anomaly_mask: np.ndarray) -> List[Dict]:
        """识别连续的异常区域"""
        regions = []
        in_anomaly = False
        start_idx = 0
        
        for i, is_anomaly in enumerate(anomaly_mask):
            if is_anomaly and not in_anomaly:
                start_idx = i
                in_anomaly = True
            elif not is_anomaly and in_anomaly:
                regions.append({
                    'start': start_idx,
                    'end': i - 1,
                    'duration': i - start_idx
                })
                in_anomaly = False
        
        if in_anomaly:
            regions.append({
                'start': start_idx,
                'end': len(anomaly_mask) - 1,
                'duration': len(anomaly_mask) - start_idx
            })
        
        return regions
    
    def _assess_severity(self, scores: np.ndarray) -> str:
        """评估异常严重程度"""
        max_score = np.max(scores)
        mean_score = np.mean(scores)
        
        if max_score > 0.9:
            return 'critical'
        elif max_score > 0.7:
            return 'high'
        elif max_score > 0.5:
            return 'medium'
        else:
            return 'low'
    
    def _generate_basic_explanation(self, anomaly_data: Dict) -> Dict:
        """生成基础解释（无LLM时使用）"""
        pattern_type = anomaly_data['type']
        severity = anomaly_data['severity']
        score = anomaly_data['score']
        
        return {
            'summary': f"检测到{severity}级别的{pattern_type}异常",
            'detailed_description': f"异常分数: {score:.4f}，持续时间: {anomaly_data['duration']}个时间点",
            'confidence': 0.7,
            'urgency': severity
        }
    
    def _analyze_feature_importance(self, X: np.ndarray, scores: np.ndarray) -> Dict:
        """分析特征重要性"""
        # 简化的特征重要性分析
        features = self.feature_analyzer.analyze_features(X)
        
        importance = {}
        for feature_type, feature_values in features.items():
            if isinstance(feature_values, dict):
                for feature_name, value in feature_values.items():
                    # 基于异常分数的相关性计算重要性
                    correlation = abs(np.corrcoef([value] * len(scores), scores)[0, 1])
                    importance[f"{feature_type}_{feature_name}"] = correlation if not np.isnan(correlation) else 0
        
        # 排序并返回前10个最重要的特征
        sorted_importance = sorted(importance.items(), key=lambda x: x[1], reverse=True)
        return dict(sorted_importance[:10])
    
    def _analyze_temporal_patterns(self, X: np.ndarray, scores: np.ndarray, anomaly_mask: np.ndarray) -> Dict:
        """分析时序模式"""
        return {
            'anomaly_ratio': float(np.sum(anomaly_mask) / len(anomaly_mask)),
            'max_anomaly_score': float(np.max(scores)),
            'mean_anomaly_score': float(np.mean(scores[anomaly_mask])) if np.any(anomaly_mask) else 0.0,
            'anomaly_distribution': 'clustered' if self._is_clustered(anomaly_mask) else 'scattered',
            'temporal_trend': 'increasing' if self._has_increasing_trend(scores) else 'stable'
        }
    
    def _generate_repair_suggestions(self, explanations: List[Dict]) -> List[str]:
        """生成修复建议汇总"""
        all_suggestions = []
        for explanation in explanations:
            if 'repair_actions' in explanation:
                all_suggestions.extend(explanation['repair_actions'])
        
        # 去重并返回
        unique_suggestions = list(set(all_suggestions))
        return unique_suggestions[:5]  # 返回前5个建议
    
    def _is_clustered(self, anomaly_mask: np.ndarray) -> bool:
        """判断异常是否聚集"""
        if not np.any(anomaly_mask):
            return False
        
        anomaly_indices = np.where(anomaly_mask)[0]
        if len(anomaly_indices) < 2:
            return True
        
        distances = np.diff(anomaly_indices)
        avg_distance = np.mean(distances)
        return avg_distance < len(anomaly_mask) * 0.1
    
    def _has_increasing_trend(self, scores: np.ndarray) -> bool:
        """判断异常分数是否有增长趋势"""
        if len(scores) < 3:
            return False
        
        trend = np.polyfit(range(len(scores)), scores, 1)[0]
        return trend > 0.01
