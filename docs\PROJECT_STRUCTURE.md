# HTA-AD Project Structure

## 📁 **Organized Directory Structure**

```
HTA-AD/
├── 📂 core/                          # 🎯 Core Implementation
│   ├── models/                       # Model implementations
│   │   ├── hta_ad_correct.py        # ✅ Main HTA-AD + SAE implementation
│   │   └── __init__.py
│   ├── training/                     # Training modules
│   │   ├── sae_pretrainer.py        # ✅ SAE pre-training system
│   │   └── __init__.py
│   ├── visualization/                # Visualization tools
│   ├── sae_integration/             # SAE integration utilities
│   └── experiments/                 # Core experiment utilities
│
├── 📂 scripts/                       # 🚀 Executable Scripts
│   ├── experiments/                  # Experiment scripts
│   │   ├── run_random_dataset_experiment.py  # ✅ Random dataset testing
│   │   ├── test_hta_ad_with_sae_pretraining.py  # ✅ SAE pre-training demo
│   │   ├── main.py                  # Main entry point
│   │   ├── example_usage.py         # Usage examples
│   │   ├── detailed_performance_test.py
│   │   └── visualize_training_results.py
│   ├── benchmarks/                   # Benchmark scripts
│   │   ├── run_tsb_ad_benchmark.py  # TSB-AD benchmark runner
│   │   ├── final_paper_compliant_test.py
│   │   └── tsb_ad_vus_pr_benchmark.py
│   ├── debug/                        # Debug utilities
│   │   ├── debug_hta_ad_latents.py  # Latent vector debugging
│   │   └── debug_sae_issue.py       # SAE debugging
│   └── tests/                        # Test scripts
│       ├── test_hta_ad_correct_comprehensive.py  # ✅ Main test
│       ├── test_fixed_hta_ad.py     # ✅ Fixed model test
│       └── [other test files...]
│
├── 📂 results/                       # 🎯 Experiment Results
│   ├── models/                       # Saved models
│   │   └── pretrained_sae.pt        # ✅ Pre-trained SAE
│   ├── figures/                      # Generated figures
│   ├── data/                         # Processed data
│   ├── random_dataset_experiment_results.csv  # ✅ Latest results
│   └── visualizations/               # Result visualizations
│
├── 📂 interpretability_results/      # 🔍 Interpretability Analysis
│   ├── sae_interpretability_experiment.py  # ✅ Interpretability code
│   ├── feature_dictionary.png       # Feature visualization
│   └── interpretability_case_study.png
│
├── 📂 figures/                       # 📊 Publication Figures
│   ├── interpretability_core.pdf    # Core interpretability diagram
│   ├── feature_dictionary.pdf       # Feature dictionary visualization
│   ├── purification_mechanism.pdf   # Purification process
│   ├── anomaly_attribution.pdf      # Anomaly attribution
│   └── combined_tsb_ad_results.png  # Benchmark results
│
├── 📂 docs/                          # 📚 Documentation
│   ├── reports/                      # Project reports
│   │   ├── PROJECT_ORGANIZATION.md  # Organization summary
│   │   ├── TEST_REPORT.md           # Testing results
│   │   └── TSB_AD_INTEGRATION_REPORT.md
│   └── papers/                       # Research papers
│
├── 📂 configs/                       # ⚙️ Configuration Files
│   └── [configuration files]
│
├── 📂 archive/                       # 🗄️ Archived Files
│   ├── backup_files/                # Backup files
│   ├── old_tests/                   # Old test scripts
│   └── extra/                       # Extra utilities
│
├── 📂 TSB-AD/                        # 📊 TSB-AD Benchmark
│   ├── Datasets/                     # Benchmark datasets
│   └── TSB_AD/                      # Benchmark code
│
└── 📂 TSB-AD_backup/                 # 🔄 TSB-AD Backup
    └── [backup of original TSB-AD]
```

## 🎯 **Key Components**

### **✅ Working Components**
1. **`core/models/hta_ad_correct.py`** - Main HTA-AD + SAE implementation
2. **`core/training/sae_pretrainer.py`** - SAE pre-training system
3. **`scripts/experiments/run_random_dataset_experiment.py`** - Comprehensive testing
4. **`interpretability_results/sae_interpretability_experiment.py`** - Interpretability analysis

### **🚀 Quick Start Scripts**
- **`scripts/experiments/test_hta_ad_with_sae_pretraining.py`** - Demo with SAE pre-training
- **`scripts/experiments/main.py`** - Main entry point
- **`scripts/benchmarks/run_tsb_ad_benchmark.py`** - TSB-AD benchmark

### **📊 Latest Results**
- **`results/random_dataset_experiment_results.csv`** - 17 datasets tested
- **`results/models/pretrained_sae.pt`** - Pre-trained SAE model
- **`figures/combined_tsb_ad_results.png`** - Benchmark visualization

## 🔧 **Usage Examples**

### **Run SAE Pre-training Demo**
```bash
cd scripts/experiments
python test_hta_ad_with_sae_pretraining.py
```

### **Run Random Dataset Experiment**
```bash
cd scripts/experiments  
python run_random_dataset_experiment.py
```

### **Run Interpretability Analysis**
```bash
cd interpretability_results
python sae_interpretability_experiment.py
```

### **Run TSB-AD Benchmark**
```bash
cd scripts/benchmarks
python run_tsb_ad_benchmark.py
```

## 📈 **Recent Achievements**

1. **✅ SAE Pre-training System** - Successfully implemented and tested
2. **✅ Random Dataset Testing** - 17 datasets from TSB-AD-U/M tested
3. **✅ Project Organization** - Clean, organized structure
4. **✅ Interpretability Framework** - Feature analysis and visualization
5. **✅ Comprehensive Documentation** - Complete project documentation

## 🎯 **Next Steps**

1. **Configuration Management** - Add YAML config files
2. **Enhanced Interpretability** - More detailed feature analysis
3. **Performance Optimization** - Model efficiency improvements
4. **Extended Benchmarking** - More comprehensive evaluation
