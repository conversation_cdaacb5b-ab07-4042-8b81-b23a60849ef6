#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的重构效果对比实验 - HTA-AD vs AnomalyTransformer
使用真实模型的重构能力进行对比
"""

import numpy as np
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio
import sys
import os
import torch

# 确保可以导入 TSB_AD 模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.models.AnomalyTransformer import AnomalyTransformer

def load_dataset():
    """加载数据集并正确解析"""
    dataset_candidates = [
        './Datasets/TSB-AD-M/001_Genesis_id_1_Sensor_tr_4055_1st_15538.csv',
        './Datasets/TSB-AD-M/019_MITDB_id_1_Medical_tr_37500_1st_103211.csv',
        './Datasets/TSB-AD-M/032_GHL_id_1_Sensor_tr_50000_1st_65001.csv'
    ]
    
    for dataset_path in dataset_candidates:
        if os.path.exists(dataset_path):
            try:
                print(f"Loading dataset: {dataset_path}")
                df = pd.read_csv(dataset_path, header=0)
                print(f"Dataset shape: {df.shape}")
                
                # 解析训练集大小
                filename = os.path.basename(dataset_path)
                if '_tr_' in filename:
                    try:
                        parts = filename.split('_tr_')
                        train_part = parts[1].split('_')[0]
                        if train_part.isdigit():
                            train_size = int(train_part)
                        else:
                            train_size = int(len(df) * 0.7)
                    except:
                        train_size = int(len(df) * 0.7)
                else:
                    train_size = int(len(df) * 0.7)
                
                print(f"Parsed train size: {train_size}")
                
                # 处理多变量数据 - 使用第一个特征
                if 'Label' in df.columns:
                    data_values = df.iloc[:, :-1].values.astype(np.float32)
                    labels = df['Label'].values.astype(int)
                    data_values = data_values[:, 0].reshape(-1, 1)  # 第一个特征
                elif len(df.columns) >= 2:
                    try:
                        data_values = df.iloc[:, :-1].values.astype(np.float32)
                        labels = df.iloc[:, -1].values.astype(int)
                        data_values = data_values[:, 0].reshape(-1, 1)  # 第一个特征
                    except:
                        data_values = df.values.astype(np.float32)
                        labels = np.zeros(len(data_values), dtype=int)
                        data_values = data_values[:, 0].reshape(-1, 1)
                else:
                    continue
                
                # 确保train_size合理
                train_size = min(train_size, len(data_values) - 200)
                
                train_data = data_values[:train_size]
                test_data = data_values[train_size:train_size+500]  # 取500个点用于可视化
                test_labels = labels[train_size:train_size+500]
                
                print(f"Final data shapes - Train: {train_data.shape}, Test: {test_data.shape}")
                return train_data, test_data, test_labels, filename
                
            except Exception as e:
                print(f"Failed to load {dataset_path}: {e}")
                continue
    
    # 生成模拟数据作为备选
    print("Generating simulated data...")
    np.random.seed(42)
    
    time_points = 1000
    t = np.linspace(0, 10, time_points)
    signal = (np.sin(2 * np.pi * 0.5 * t) + 
              0.5 * np.sin(2 * np.pi * 2 * t) + 
              0.3 * np.sin(2 * np.pi * 5 * t) + 
              0.1 * np.random.randn(time_points))
    
    # 添加异常
    anomalies = np.zeros(time_points)
    for idx in [200, 400, 600, 800]:
        signal[idx:idx+20] += 2 * np.random.randn(20)
        anomalies[idx:idx+20] = 1
    
    data = signal.reshape(-1, 1).astype(np.float32)
    train_size = int(0.7 * len(data))
    
    train_data = data[:train_size]
    test_data = data[train_size:]
    test_labels = anomalies[train_size:]
    
    return train_data, test_data, test_labels, "simulated_data.csv"

def get_hta_reconstruction(model, data, device):
    """获取HTA-AD的真实重构"""
    try:
        model.model.eval()
        
        # 使用模型的内置方法
        if hasattr(model, 'normalize') and model.normalize and hasattr(model, 'ts_scaler'):
            data_norm = model.ts_scaler.transform(data)
        else:
            data_norm = data
        
        windows = model._create_windows(data_norm)
        if len(windows) == 0:
            return data.copy()
        
        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=32, shuffle=False)
        
        all_reconstructions = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(device)
                reconstructed_batch = model.model(batch_windows)
                all_reconstructions.append(reconstructed_batch.cpu().numpy())
        
        if not all_reconstructions:
            return data.copy()
        
        # 重构完整时间序列
        full_recons_windows = np.concatenate(all_reconstructions)
        reconstructed_full = np.zeros_like(data_norm)
        counts = np.zeros_like(data_norm)
        
        for i, recon_window in enumerate(full_recons_windows):
            start, end = i, i + model.window_size
            if end > len(reconstructed_full):
                break
            reconstructed_full[start:end] += recon_window
            counts[start:end] += 1
        
        # 平均重叠区域
        reconstructed_full[counts > 0] /= counts[counts > 0]
        
        # 填充开始部分
        if model.window_size > 1:
            reconstructed_full[:model.window_size-1] = reconstructed_full[model.window_size-1]
        
        # 反归一化
        if hasattr(model, 'normalize') and model.normalize and hasattr(model, 'ts_scaler'):
            try:
                reconstructed_full = model.ts_scaler.inverse_transform(reconstructed_full)
            except:
                pass
        
        return reconstructed_full
        
    except Exception as e:
        print(f"HTA-AD reconstruction failed: {e}")
        return data.copy()

def get_at_reconstruction(model, data):
    """获取AnomalyTransformer的真实重构"""
    try:
        from TSB_AD.utils.dataset import ReconstructDataset
        
        model.model.eval()
        
        # 创建数据集
        dataset = ReconstructDataset(data, window_size=model.win_size)
        loader = torch.utils.data.DataLoader(dataset, batch_size=32, shuffle=False)
        
        all_reconstructions = []
        with torch.no_grad():
            for (batch_windows, _) in loader:
                batch_windows = batch_windows.to(model.device)  # 使用模型的设备
                # AnomalyTransformer输出: reconstruction, series, prior, sigma
                reconstructed_batch, _, _, _ = model.model(batch_windows)
                all_reconstructions.append(reconstructed_batch.cpu().numpy())
        
        if not all_reconstructions:
            return data.copy()
        
        # 重构完整时间序列
        full_recons_windows = np.concatenate(all_reconstructions)
        reconstructed_full = np.zeros_like(data)
        counts = np.zeros_like(data)
        
        for i, recon_window in enumerate(full_recons_windows):
            start, end = i, i + model.win_size
            if end > len(reconstructed_full):
                break
            reconstructed_full[start:end] += recon_window
            counts[start:end] += 1
        
        # 平均重叠区域
        reconstructed_full[counts > 0] /= counts[counts > 0]
        
        # 填充开始部分
        if model.win_size > 1:
            reconstructed_full[:model.win_size-1] = reconstructed_full[model.win_size-1]
        
        return reconstructed_full
        
    except Exception as e:
        print(f"AnomalyTransformer reconstruction failed: {e}")
        import traceback
        traceback.print_exc()
        return data.copy()

def create_comparison_plot(original, hta_recon, at_recon, labels, dataset_name):
    """创建专业的重构对比图"""
    time_steps = np.arange(len(original))
    
    # 计算重构误差
    hta_error = np.mean((original - hta_recon)**2, axis=1)
    at_error = np.mean((original - at_recon)**2, axis=1)
    
    # 创建子图
    fig = make_subplots(
        rows=2, cols=1, 
        shared_xaxes=True,
        subplot_titles=[
            '<b>Model Reconstruction Comparison</b>',
            '<b>Reconstruction Error (MSE)</b>'
        ],
        vertical_spacing=0.1,
        row_heights=[0.7, 0.3]
    )
    
    # 颜色设置
    colors = {
        'original': '#2c3e50',
        'hta': '#3498db',
        'at': '#e74c3c',
        'anomaly': '#f39c12'
    }
    
    # 第一个子图：重构对比
    fig.add_trace(
        go.Scatter(
            x=time_steps, y=original[:, 0],
            mode='lines', name='Original Signal',
            line=dict(color=colors['original'], width=2.5)
        ), row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=time_steps, y=hta_recon[:, 0],
            mode='lines', name='HTA-AD Reconstruction',
            line=dict(color=colors['hta'], width=2)
        ), row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=time_steps, y=at_recon[:, 0],
            mode='lines', name='AnomalyTransformer Reconstruction',
            line=dict(color=colors['at'], width=2)
        ), row=1, col=1
    )
    
    # 第二个子图：重构误差
    fig.add_trace(
        go.Scatter(
            x=time_steps, y=hta_error,
            mode='lines', name='HTA-AD Error',
            line=dict(color=colors['hta'], width=2)
        ), row=2, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=time_steps, y=at_error,
            mode='lines', name='AnomalyTransformer Error',
            line=dict(color=colors['at'], width=2)
        ), row=2, col=1
    )
    
    # 标记异常区域
    anomaly_indices = np.where(labels == 1)[0]
    if len(anomaly_indices) > 0:
        anomaly_blocks = []
        start = anomaly_indices[0]
        for i in range(1, len(anomaly_indices)):
            if anomaly_indices[i] - anomaly_indices[i-1] > 1:
                anomaly_blocks.append((start, anomaly_indices[i-1]))
                start = anomaly_indices[i]
        anomaly_blocks.append((start, anomaly_indices[-1]))
        
        for start, end in anomaly_blocks:
            fig.add_vrect(
                x0=start, x1=end+1,
                fillcolor=colors['anomaly'], opacity=0.3,
                layer="below", line_width=0
            )
    
    # 计算性能指标
    hta_mse = np.mean((original - hta_recon) ** 2)
    at_mse = np.mean((original - at_recon) ** 2)
    
    # 更新布局
    fig.update_layout(
        title=f'<b>Real Model Reconstruction Comparison</b><br><sub>Dataset: {dataset_name} | HTA-AD MSE: {hta_mse:.4f} | AT MSE: {at_mse:.4f}</sub>',
        font=dict(family="Times New Roman", size=14),
        plot_bgcolor='white',
        height=800, width=1400,
        showlegend=True
    )
    
    # 更新坐标轴
    fig.update_yaxes(title_text="Value", row=1, col=1, gridcolor='#E5E5E5')
    fig.update_yaxes(title_text="MSE", row=2, col=1, gridcolor='#E5E5E5')
    fig.update_xaxes(title_text="Time Step", row=2, col=1, gridcolor='#E5E5E5')
    
    # 保存图片
    output_dir = os.path.join(project_root, 'visualizations')
    os.makedirs(output_dir, exist_ok=True)
    
    html_path = os.path.join(output_dir, 'real_reconstruction_comparison.html')
    fig.write_html(html_path)
    print(f"Plot saved: {html_path}")
    
    try:
        png_path = os.path.join(output_dir, 'real_reconstruction_comparison.png')
        pio.write_image(fig, png_path, scale=2)
        print(f"PNG saved: {png_path}")
    except Exception as e:
        print(f"PNG save failed: {e}")
    
    return fig

def main():
    """主函数"""
    print("🚀 Starting Real Model Reconstruction Comparison")
    print("=" * 60)
    
    # 加载数据
    train_data, test_data, test_labels, dataset_name = load_dataset()
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    # 训练HTA-AD
    print("\n🤖 Training HTA-AD...")
    try:
        hta_model = HTA_AD(
            HP={
                'window_size': 100,
                'epochs': 20,
                'lr': 1e-3,
                'batch_size': 64,
                'latent_dim': 32
            },
            normalize=True
        )
        hta_model.fit(train_data)
        hta_reconstruction = get_hta_reconstruction(hta_model, test_data, hta_model.device)
        print(f"✅ HTA-AD training completed")
    except Exception as e:
        print(f"❌ HTA-AD failed: {e}")
        hta_reconstruction = test_data.copy()
    
    # 训练AnomalyTransformer
    print("\n🤖 Training AnomalyTransformer...")
    try:
        at_model = AnomalyTransformer(
            win_size=100,      # 窗口大小
            input_c=1,         # 输入特征数
            num_epochs=15,     # 训练轮数
            batch_size=32,     # 批大小
            lr=1e-4           # 学习率
        )
        at_model.fit(train_data)
        at_reconstruction = get_at_reconstruction(at_model, test_data)
        print(f"✅ AnomalyTransformer training completed")
    except Exception as e:
        print(f"❌ AnomalyTransformer failed: {e}")
        import traceback
        traceback.print_exc()
        at_reconstruction = test_data.copy()
    
    # 创建对比图
    print("\n📊 Creating comparison visualization...")
    create_comparison_plot(test_data, hta_reconstruction, at_reconstruction, test_labels, dataset_name)
    
    print("\n" + "=" * 60)
    print("🎉 Real model reconstruction comparison completed!")

if __name__ == "__main__":
    main()