#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的合成数据生成器
生成更现实的时间序列数据，适合异常检测任务
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.signal import correlate
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

def generate_realistic_time_series():
    """生成更现实的时间序列数据"""
    np.random.seed(42)
    
    # 参数设置
    total_length = 500
    window_size = 100
    
    # 创建时间轴
    t = np.linspace(0, 10*np.pi, total_length)
    
    # 1. 基础趋势组件（缓慢变化的趋势）
    trend = 0.3 * np.sin(0.1 * t) + 0.1 * t / total_length
    
    # 2. 主要周期性组件（不完全规律）
    # 主周期 + 一些随机相位和幅度变化
    main_period = np.sin(t) * (0.8 + 0.2 * np.sin(0.05 * t))  # 幅度调制
    secondary_period = 0.3 * np.sin(2.3 * t + 0.5 * np.sin(0.03 * t))  # 频率调制
    
    # 3. 季节性组件
    seasonal = 0.2 * np.sin(0.5 * t) + 0.1 * np.sin(0.8 * t)
    
    # 4. 随机噪声（更现实的噪声水平）
    noise = 0.15 * np.random.normal(0, 1, total_length)
    
    # 5. 偶发的小扰动（模拟真实环境中的小异常）
    small_disturbances = np.zeros(total_length)
    for _ in range(5):  # 添加5个小扰动
        pos = np.random.randint(50, total_length - 50)
        duration = np.random.randint(3, 8)
        intensity = np.random.uniform(0.1, 0.3)
        small_disturbances[pos:pos+duration] += intensity * np.random.randn(duration)
    
    # 组合所有组件
    base_signal = trend + main_period + secondary_period + seasonal + noise + small_disturbances
    
    # 归一化到合理范围
    base_signal = (base_signal - np.mean(base_signal)) / np.std(base_signal)
    base_signal = 0.5 + 0.4 * base_signal  # 缩放到[0.1, 0.9]左右
    
    # 创建标签
    labels = np.zeros(total_length)
    
    # 添加更现实的异常
    anomaly_types = [
        # 类型1: 渐变异常（系统性偏移）
        {
            'start': 350,
            'end': 380,
            'type': 'gradual_shift',
            'intensity': 0.6
        },
        # 类型2: 尖峰异常（传感器故障）
        {
            'start': 420,
            'end': 435,
            'type': 'spike',
            'intensity': 0.8
        },
        # 类型3: 模式破坏（周期性中断）
        {
            'start': 180,
            'end': 200,
            'type': 'pattern_break',
            'intensity': 0.4
        }
    ]
    
    for anomaly in anomaly_types:
        start, end = anomaly['start'], anomaly['end']
        anom_type = anomaly['type']
        intensity = anomaly['intensity']
        
        if anom_type == 'gradual_shift':
            # 渐变偏移
            shift_pattern = np.linspace(0, intensity, end - start)
            base_signal[start:end] += shift_pattern
            
        elif anom_type == 'spike':
            # 尖峰异常
            spike_length = end - start
            peak_pos = spike_length // 2
            spike_pattern = np.concatenate([
                np.linspace(0, intensity, peak_pos),
                np.linspace(intensity, 0, spike_length - peak_pos)
            ])
            base_signal[start:end] += spike_pattern
            
        elif anom_type == 'pattern_break':
            # 模式破坏 - 用随机噪声替换正常模式
            base_signal[start:end] = np.mean(base_signal) + intensity * np.random.randn(end - start)
        
        labels[start:end] = 1
    
    # 确保信号在合理范围内
    base_signal = np.clip(base_signal, 0, 1.5)
    
    return base_signal.reshape(-1, 1), labels, window_size

def analyze_improved_data():
    """分析改进后的数据"""
    print("🔍 生成改进的现实时间序列数据...")
    
    # 生成数据
    data, labels, window_size = generate_realistic_time_series()
    data_flat = data.flatten()
    
    # 数据划分（更现实的划分）
    train_end = 300  # 增加训练数据
    test_start = 350  # 连续的测试区间
    test_end = test_start + window_size
    
    train_data = data_flat[:train_end]
    test_data = data_flat[test_start:test_end]
    test_labels = labels[test_start:test_end]
    
    print(f"📊 改进数据统计信息:")
    print(f"   总长度: {len(data_flat)} 个时间点")
    print(f"   训练集: 0-{train_end-1} ({train_end} 个点)")
    print(f"   测试集: {test_start}-{test_end-1} ({len(test_data)} 个点)")
    print(f"   窗口大小: {window_size}")
    print(f"   总异常点数量: {np.sum(labels)} ({np.sum(labels)/len(labels)*100:.1f}%)")
    print(f"   测试集中异常点: {np.sum(test_labels)} ({np.sum(test_labels)/len(test_labels)*100:.1f}%)")
    
    # 创建对比可视化
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 完整数据概览
    ax1 = plt.subplot(3, 3, 1)
    time_full = np.arange(len(data_flat))
    plt.plot(time_full, data_flat, linewidth=1.5, color='#2E86AB', alpha=0.8)
    
    # 标记训练集和测试集
    plt.axvspan(0, train_end-1, alpha=0.2, color='green', label='训练集')
    plt.axvspan(test_start, test_end-1, alpha=0.2, color='blue', label='测试集')
    
    # 标记所有异常区域
    anomaly_indices = np.where(labels == 1)[0]
    if len(anomaly_indices) > 0:
        # 找到连续的异常区域
        anomaly_regions = []
        start = anomaly_indices[0]
        for i in range(1, len(anomaly_indices)):
            if anomaly_indices[i] != anomaly_indices[i-1] + 1:
                anomaly_regions.append((start, anomaly_indices[i-1]))
                start = anomaly_indices[i]
        anomaly_regions.append((start, anomaly_indices[-1]))
        
        for start_idx, end_idx in anomaly_regions:
            plt.axvspan(start_idx, end_idx, alpha=0.3, color='red')
    
    plt.title('改进数据完整概览', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 训练集分析
    ax2 = plt.subplot(3, 3, 2)
    time_train = np.arange(len(train_data))
    plt.plot(time_train, train_data, linewidth=2, color='green', alpha=0.8)
    plt.title('训练集（包含轻微异常）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 训练集统计
    train_stats = (f'均值: {np.mean(train_data):.3f}\n'
                  f'标准差: {np.std(train_data):.3f}\n'
                  f'最小值: {np.min(train_data):.3f}\n'
                  f'最大值: {np.max(train_data):.3f}')
    plt.text(0.02, 0.98, train_stats, transform=ax2.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8),
            verticalalignment='top')
    
    # 3. 测试集分析
    ax3 = plt.subplot(3, 3, 3)
    time_test = np.arange(len(test_data))
    plt.plot(time_test, test_data, linewidth=2, color='blue', alpha=0.8)
    
    # 标记测试集异常
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        plt.scatter(time_test[anomaly_mask], test_data[anomaly_mask], 
                   color='red', s=25, alpha=0.8, label='异常点')
    
    plt.title('测试集（包含异常）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 测试集统计
    test_stats = (f'均值: {np.mean(test_data):.3f}\n'
                 f'标准差: {np.std(test_data):.3f}\n'
                 f'异常点: {np.sum(test_labels)}/{len(test_labels)}')
    plt.text(0.02, 0.98, test_stats, transform=ax3.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8),
            verticalalignment='top')
    
    # 4. 周期性分析
    ax4 = plt.subplot(3, 3, 4)
    train_autocorr = correlate(train_data, train_data, mode='full')
    train_autocorr = train_autocorr[train_autocorr.size // 2:]
    train_autocorr = train_autocorr / train_autocorr[0]
    
    lags = np.arange(min(60, len(train_autocorr)))
    plt.plot(lags, train_autocorr[:len(lags)], linewidth=2, color='green')
    plt.title('训练集自相关（周期性）', fontsize=14, fontweight='bold')
    plt.xlabel('滞后步数', fontsize=12)
    plt.ylabel('自相关系数', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 找主要周期
    peaks_idx = []
    for i in range(1, min(50, len(train_autocorr)-1)):
        if (train_autocorr[i] > train_autocorr[i-1] and 
            train_autocorr[i] > train_autocorr[i+1] and 
            train_autocorr[i] > 0.2):
            peaks_idx.append(i)
    
    if peaks_idx:
        plt.scatter(peaks_idx, train_autocorr[peaks_idx], color='red', s=50, zorder=5)
    
    # 5. 噪声分析
    ax5 = plt.subplot(3, 3, 5)
    # 计算一阶差分来估计噪声
    train_diff = np.diff(train_data)
    plt.hist(train_diff, bins=40, alpha=0.7, color='orange', density=True)
    plt.title('训练集噪声分布（一阶差分）', fontsize=14, fontweight='bold')
    plt.xlabel('差分值', fontsize=12)
    plt.ylabel('密度', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 噪声统计
    noise_stats = (f'差分均值: {np.mean(train_diff):.4f}\n'
                  f'差分标准差: {np.std(train_diff):.4f}\n'
                  f'估计SNR: {np.var(train_data)/np.var(train_diff):.2f}')
    plt.text(0.02, 0.98, noise_stats, transform=ax5.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
            verticalalignment='top')
    
    # 6. 异常类型分析
    ax6 = plt.subplot(3, 3, 6)
    # 展示不同类型的异常
    anomaly_regions = [
        (350, 380, '渐变偏移'),
        (420, 435, '尖峰异常'),
        (180, 200, '模式破坏')
    ]
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    for i, (start, end, anom_type) in enumerate(anomaly_regions):
        if end <= len(data_flat):
            region_data = data_flat[start:end]
            region_time = np.arange(len(region_data))
            plt.plot(region_time, region_data, linewidth=2, color=colors[i], 
                    label=anom_type, alpha=0.8)
    
    plt.title('异常类型对比', fontsize=14, fontweight='bold')
    plt.xlabel('时间步（相对）', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 7. 频谱分析
    ax7 = plt.subplot(3, 3, 7)
    from scipy.fft import fft, fftfreq
    
    # 对训练数据进行FFT
    train_fft = fft(train_data)
    train_freqs = fftfreq(len(train_data))
    
    # 只显示正频率部分
    pos_mask = train_freqs > 0
    plt.loglog(train_freqs[pos_mask], np.abs(train_fft[pos_mask]), 
              linewidth=2, color='green', alpha=0.8)
    plt.title('训练集频谱分析', fontsize=14, fontweight='bold')
    plt.xlabel('频率', fontsize=12)
    plt.ylabel('幅度', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 8. 数据分布对比
    ax8 = plt.subplot(3, 3, 8)
    plt.hist(train_data, bins=30, alpha=0.7, color='green', label='训练集', density=True)
    plt.hist(test_data[~anomaly_mask], bins=20, alpha=0.7, color='blue', label='测试集（正常）', density=True)
    if np.any(anomaly_mask):
        plt.hist(test_data[anomaly_mask], bins=15, alpha=0.7, color='red', label='测试集（异常）', density=True)
    
    plt.title('数据分布对比', fontsize=14, fontweight='bold')
    plt.xlabel('信号值', fontsize=12)
    plt.ylabel('密度', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 9. 滑动窗口统计
    ax9 = plt.subplot(3, 3, 9)
    window_means = []
    window_stds = []
    window_size_stat = 20
    
    for i in range(0, len(data_flat) - window_size_stat, 10):
        window_data = data_flat[i:i+window_size_stat]
        window_means.append(np.mean(window_data))
        window_stds.append(np.std(window_data))
    
    window_centers = np.arange(window_size_stat//2, len(data_flat) - window_size_stat//2, 10)
    plt.plot(window_centers, window_means, linewidth=2, color='blue', label='滑动均值', alpha=0.8)
    plt.plot(window_centers, window_stds, linewidth=2, color='red', label='滑动标准差', alpha=0.8)
    
    plt.title('滑动窗口统计', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('统计值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/improved_synthetic_data_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 数据质量评估
    print(f"\n📈 改进数据质量评估:")
    print(f"   训练集周期性强度: {np.max(train_autocorr[1:30]):.3f}")
    print(f"   训练集变异系数: {np.std(train_data)/np.mean(train_data):.3f}")
    print(f"   估计信噪比: {np.var(train_data)/np.var(np.diff(train_data)):.2f}")
    print(f"   异常强度范围: {np.min(test_data[anomaly_mask]):.3f} - {np.max(test_data[anomaly_mask]):.3f}" if np.any(anomaly_mask) else "   无异常数据")
    print(f"   数据复杂度（熵估计）: {-np.sum(np.histogram(train_data, bins=20)[0]/len(train_data) * np.log(np.histogram(train_data, bins=20)[0]/len(train_data) + 1e-10)):.3f}")

if __name__ == "__main__":
    print("=" * 80)
    print("🔧 改进的合成数据生成与分析")
    print("=" * 80)
    
    analyze_improved_data()
    
    print("\n" + "=" * 80)
    print("✅ 改进数据分析完成！图像已保存为: experiments/improved_synthetic_data_analysis.png")
    print("=" * 80) 
# -*- coding: utf-8 -*-
"""
改进的合成数据生成器
生成更现实的时间序列数据，适合异常检测任务
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.signal import correlate
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

def generate_realistic_time_series():
    """生成更现实的时间序列数据"""
    np.random.seed(42)
    
    # 参数设置
    total_length = 500
    window_size = 100
    
    # 创建时间轴
    t = np.linspace(0, 10*np.pi, total_length)
    
    # 1. 基础趋势组件（缓慢变化的趋势）
    trend = 0.3 * np.sin(0.1 * t) + 0.1 * t / total_length
    
    # 2. 主要周期性组件（不完全规律）
    # 主周期 + 一些随机相位和幅度变化
    main_period = np.sin(t) * (0.8 + 0.2 * np.sin(0.05 * t))  # 幅度调制
    secondary_period = 0.3 * np.sin(2.3 * t + 0.5 * np.sin(0.03 * t))  # 频率调制
    
    # 3. 季节性组件
    seasonal = 0.2 * np.sin(0.5 * t) + 0.1 * np.sin(0.8 * t)
    
    # 4. 随机噪声（更现实的噪声水平）
    noise = 0.15 * np.random.normal(0, 1, total_length)
    
    # 5. 偶发的小扰动（模拟真实环境中的小异常）
    small_disturbances = np.zeros(total_length)
    for _ in range(5):  # 添加5个小扰动
        pos = np.random.randint(50, total_length - 50)
        duration = np.random.randint(3, 8)
        intensity = np.random.uniform(0.1, 0.3)
        small_disturbances[pos:pos+duration] += intensity * np.random.randn(duration)
    
    # 组合所有组件
    base_signal = trend + main_period + secondary_period + seasonal + noise + small_disturbances
    
    # 归一化到合理范围
    base_signal = (base_signal - np.mean(base_signal)) / np.std(base_signal)
    base_signal = 0.5 + 0.4 * base_signal  # 缩放到[0.1, 0.9]左右
    
    # 创建标签
    labels = np.zeros(total_length)
    
    # 添加更现实的异常
    anomaly_types = [
        # 类型1: 渐变异常（系统性偏移）
        {
            'start': 350,
            'end': 380,
            'type': 'gradual_shift',
            'intensity': 0.6
        },
        # 类型2: 尖峰异常（传感器故障）
        {
            'start': 420,
            'end': 435,
            'type': 'spike',
            'intensity': 0.8
        },
        # 类型3: 模式破坏（周期性中断）
        {
            'start': 180,
            'end': 200,
            'type': 'pattern_break',
            'intensity': 0.4
        }
    ]
    
    for anomaly in anomaly_types:
        start, end = anomaly['start'], anomaly['end']
        anom_type = anomaly['type']
        intensity = anomaly['intensity']
        
        if anom_type == 'gradual_shift':
            # 渐变偏移
            shift_pattern = np.linspace(0, intensity, end - start)
            base_signal[start:end] += shift_pattern
            
        elif anom_type == 'spike':
            # 尖峰异常
            spike_length = end - start
            peak_pos = spike_length // 2
            spike_pattern = np.concatenate([
                np.linspace(0, intensity, peak_pos),
                np.linspace(intensity, 0, spike_length - peak_pos)
            ])
            base_signal[start:end] += spike_pattern
            
        elif anom_type == 'pattern_break':
            # 模式破坏 - 用随机噪声替换正常模式
            base_signal[start:end] = np.mean(base_signal) + intensity * np.random.randn(end - start)
        
        labels[start:end] = 1
    
    # 确保信号在合理范围内
    base_signal = np.clip(base_signal, 0, 1.5)
    
    return base_signal.reshape(-1, 1), labels, window_size

def analyze_improved_data():
    """分析改进后的数据"""
    print("🔍 生成改进的现实时间序列数据...")
    
    # 生成数据
    data, labels, window_size = generate_realistic_time_series()
    data_flat = data.flatten()
    
    # 数据划分（更现实的划分）
    train_end = 300  # 增加训练数据
    test_start = 350  # 连续的测试区间
    test_end = test_start + window_size
    
    train_data = data_flat[:train_end]
    test_data = data_flat[test_start:test_end]
    test_labels = labels[test_start:test_end]
    
    print(f"📊 改进数据统计信息:")
    print(f"   总长度: {len(data_flat)} 个时间点")
    print(f"   训练集: 0-{train_end-1} ({train_end} 个点)")
    print(f"   测试集: {test_start}-{test_end-1} ({len(test_data)} 个点)")
    print(f"   窗口大小: {window_size}")
    print(f"   总异常点数量: {np.sum(labels)} ({np.sum(labels)/len(labels)*100:.1f}%)")
    print(f"   测试集中异常点: {np.sum(test_labels)} ({np.sum(test_labels)/len(test_labels)*100:.1f}%)")
    
    # 创建对比可视化
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 完整数据概览
    ax1 = plt.subplot(3, 3, 1)
    time_full = np.arange(len(data_flat))
    plt.plot(time_full, data_flat, linewidth=1.5, color='#2E86AB', alpha=0.8)
    
    # 标记训练集和测试集
    plt.axvspan(0, train_end-1, alpha=0.2, color='green', label='训练集')
    plt.axvspan(test_start, test_end-1, alpha=0.2, color='blue', label='测试集')
    
    # 标记所有异常区域
    anomaly_indices = np.where(labels == 1)[0]
    if len(anomaly_indices) > 0:
        # 找到连续的异常区域
        anomaly_regions = []
        start = anomaly_indices[0]
        for i in range(1, len(anomaly_indices)):
            if anomaly_indices[i] != anomaly_indices[i-1] + 1:
                anomaly_regions.append((start, anomaly_indices[i-1]))
                start = anomaly_indices[i]
        anomaly_regions.append((start, anomaly_indices[-1]))
        
        for start_idx, end_idx in anomaly_regions:
            plt.axvspan(start_idx, end_idx, alpha=0.3, color='red')
    
    plt.title('改进数据完整概览', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 训练集分析
    ax2 = plt.subplot(3, 3, 2)
    time_train = np.arange(len(train_data))
    plt.plot(time_train, train_data, linewidth=2, color='green', alpha=0.8)
    plt.title('训练集（包含轻微异常）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 训练集统计
    train_stats = (f'均值: {np.mean(train_data):.3f}\n'
                  f'标准差: {np.std(train_data):.3f}\n'
                  f'最小值: {np.min(train_data):.3f}\n'
                  f'最大值: {np.max(train_data):.3f}')
    plt.text(0.02, 0.98, train_stats, transform=ax2.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8),
            verticalalignment='top')
    
    # 3. 测试集分析
    ax3 = plt.subplot(3, 3, 3)
    time_test = np.arange(len(test_data))
    plt.plot(time_test, test_data, linewidth=2, color='blue', alpha=0.8)
    
    # 标记测试集异常
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        plt.scatter(time_test[anomaly_mask], test_data[anomaly_mask], 
                   color='red', s=25, alpha=0.8, label='异常点')
    
    plt.title('测试集（包含异常）', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 测试集统计
    test_stats = (f'均值: {np.mean(test_data):.3f}\n'
                 f'标准差: {np.std(test_data):.3f}\n'
                 f'异常点: {np.sum(test_labels)}/{len(test_labels)}')
    plt.text(0.02, 0.98, test_stats, transform=ax3.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8),
            verticalalignment='top')
    
    # 4. 周期性分析
    ax4 = plt.subplot(3, 3, 4)
    train_autocorr = correlate(train_data, train_data, mode='full')
    train_autocorr = train_autocorr[train_autocorr.size // 2:]
    train_autocorr = train_autocorr / train_autocorr[0]
    
    lags = np.arange(min(60, len(train_autocorr)))
    plt.plot(lags, train_autocorr[:len(lags)], linewidth=2, color='green')
    plt.title('训练集自相关（周期性）', fontsize=14, fontweight='bold')
    plt.xlabel('滞后步数', fontsize=12)
    plt.ylabel('自相关系数', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 找主要周期
    peaks_idx = []
    for i in range(1, min(50, len(train_autocorr)-1)):
        if (train_autocorr[i] > train_autocorr[i-1] and 
            train_autocorr[i] > train_autocorr[i+1] and 
            train_autocorr[i] > 0.2):
            peaks_idx.append(i)
    
    if peaks_idx:
        plt.scatter(peaks_idx, train_autocorr[peaks_idx], color='red', s=50, zorder=5)
    
    # 5. 噪声分析
    ax5 = plt.subplot(3, 3, 5)
    # 计算一阶差分来估计噪声
    train_diff = np.diff(train_data)
    plt.hist(train_diff, bins=40, alpha=0.7, color='orange', density=True)
    plt.title('训练集噪声分布（一阶差分）', fontsize=14, fontweight='bold')
    plt.xlabel('差分值', fontsize=12)
    plt.ylabel('密度', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 噪声统计
    noise_stats = (f'差分均值: {np.mean(train_diff):.4f}\n'
                  f'差分标准差: {np.std(train_diff):.4f}\n'
                  f'估计SNR: {np.var(train_data)/np.var(train_diff):.2f}')
    plt.text(0.02, 0.98, noise_stats, transform=ax5.transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
            verticalalignment='top')
    
    # 6. 异常类型分析
    ax6 = plt.subplot(3, 3, 6)
    # 展示不同类型的异常
    anomaly_regions = [
        (350, 380, '渐变偏移'),
        (420, 435, '尖峰异常'),
        (180, 200, '模式破坏')
    ]
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    for i, (start, end, anom_type) in enumerate(anomaly_regions):
        if end <= len(data_flat):
            region_data = data_flat[start:end]
            region_time = np.arange(len(region_data))
            plt.plot(region_time, region_data, linewidth=2, color=colors[i], 
                    label=anom_type, alpha=0.8)
    
    plt.title('异常类型对比', fontsize=14, fontweight='bold')
    plt.xlabel('时间步（相对）', fontsize=12)
    plt.ylabel('信号值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 7. 频谱分析
    ax7 = plt.subplot(3, 3, 7)
    from scipy.fft import fft, fftfreq
    
    # 对训练数据进行FFT
    train_fft = fft(train_data)
    train_freqs = fftfreq(len(train_data))
    
    # 只显示正频率部分
    pos_mask = train_freqs > 0
    plt.loglog(train_freqs[pos_mask], np.abs(train_fft[pos_mask]), 
              linewidth=2, color='green', alpha=0.8)
    plt.title('训练集频谱分析', fontsize=14, fontweight='bold')
    plt.xlabel('频率', fontsize=12)
    plt.ylabel('幅度', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 8. 数据分布对比
    ax8 = plt.subplot(3, 3, 8)
    plt.hist(train_data, bins=30, alpha=0.7, color='green', label='训练集', density=True)
    plt.hist(test_data[~anomaly_mask], bins=20, alpha=0.7, color='blue', label='测试集（正常）', density=True)
    if np.any(anomaly_mask):
        plt.hist(test_data[anomaly_mask], bins=15, alpha=0.7, color='red', label='测试集（异常）', density=True)
    
    plt.title('数据分布对比', fontsize=14, fontweight='bold')
    plt.xlabel('信号值', fontsize=12)
    plt.ylabel('密度', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 9. 滑动窗口统计
    ax9 = plt.subplot(3, 3, 9)
    window_means = []
    window_stds = []
    window_size_stat = 20
    
    for i in range(0, len(data_flat) - window_size_stat, 10):
        window_data = data_flat[i:i+window_size_stat]
        window_means.append(np.mean(window_data))
        window_stds.append(np.std(window_data))
    
    window_centers = np.arange(window_size_stat//2, len(data_flat) - window_size_stat//2, 10)
    plt.plot(window_centers, window_means, linewidth=2, color='blue', label='滑动均值', alpha=0.8)
    plt.plot(window_centers, window_stds, linewidth=2, color='red', label='滑动标准差', alpha=0.8)
    
    plt.title('滑动窗口统计', fontsize=14, fontweight='bold')
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('统计值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/improved_synthetic_data_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 数据质量评估
    print(f"\n📈 改进数据质量评估:")
    print(f"   训练集周期性强度: {np.max(train_autocorr[1:30]):.3f}")
    print(f"   训练集变异系数: {np.std(train_data)/np.mean(train_data):.3f}")
    print(f"   估计信噪比: {np.var(train_data)/np.var(np.diff(train_data)):.2f}")
    print(f"   异常强度范围: {np.min(test_data[anomaly_mask]):.3f} - {np.max(test_data[anomaly_mask]):.3f}" if np.any(anomaly_mask) else "   无异常数据")
    print(f"   数据复杂度（熵估计）: {-np.sum(np.histogram(train_data, bins=20)[0]/len(train_data) * np.log(np.histogram(train_data, bins=20)[0]/len(train_data) + 1e-10)):.3f}")

if __name__ == "__main__":
    print("=" * 80)
    print("🔧 改进的合成数据生成与分析")
    print("=" * 80)
    
    analyze_improved_data()
    
    print("\n" + "=" * 80)
    print("✅ 改进数据分析完成！图像已保存为: experiments/improved_synthetic_data_analysis.png")
    print("=" * 80) 