#!/usr/bin/env python3
"""
Real SAE Training and Feature Dictionary Generation
Train a Sparse Autoencoder on real time series data and analyze learned features
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score
import pandas as pd
import os
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Set style for academic papers
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'font.size': 10,
    'axes.labelsize': 11,
    'axes.titlesize': 12,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 10,
    'figure.titlesize': 14,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'lines.linewidth': 2,
    'axes.linewidth': 1.2,
    'xtick.major.size': 5,
    'ytick.major.size': 5,
    'legend.frameon': False,
    'figure.dpi': 300
})

class SparseAutoencoder(nn.Module):
    """Sparse Autoencoder for learning interpretable features"""
    def __init__(self, input_dim=32, hidden_dim=128, sparsity_weight=0.01, dropout=0.1):
        super(SparseAutoencoder, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.sparsity_weight = sparsity_weight
        
        # Encoder with batch normalization and dropout
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 2),
            nn.BatchNorm1d(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU()
        )
        
        # Decoder
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, input_dim)
        )
        
        # Initialize weights
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.xavier_normal_(module.weight, gain=0.5)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    def forward(self, x):
        features = self.encoder(x)
        reconstruction = self.decoder(features)
        return features, reconstruction
    
    def loss_function(self, x, reconstruction, features):
        # Reconstruction loss
        recon_loss = nn.MSELoss()(reconstruction, x)
        
        # Sparsity loss (L1 regularization)
        sparsity_loss = torch.mean(torch.abs(features))
        
        # Total loss
        total_loss = recon_loss + self.sparsity_weight * sparsity_loss
        
        return total_loss, recon_loss, sparsity_loss

def generate_synthetic_timeseries_data(n_samples=5000, seq_length=100, n_features=32):
    """Generate synthetic time series data with rich and diverse anomaly patterns"""
    np.random.seed(42)

    normal_data = []
    anomaly_data = []

    print("🔄 Generating rich synthetic time series data...")

    # Generate normal patterns with more diversity
    for i in range(n_samples):
        # Base signal: combination of multiple sine waves, trends, and noise
        t = np.linspace(0, 6*np.pi, seq_length)
        signal = np.zeros((seq_length, n_features))

        for j in range(n_features):
            # Multiple frequency components with different phases
            freq1 = 0.3 + j * 0.05
            freq2 = 0.8 + j * 0.03
            freq3 = 1.5 + j * 0.02
            phase1 = j * 0.2
            phase2 = j * 0.15

            # Base pattern with trend and seasonality
            trend = 0.01 * t * (j % 3 - 1)  # Different trend directions
            seasonal = (0.4 * np.sin(freq1 * t + phase1) +
                       0.25 * np.sin(freq2 * t + phase2) +
                       0.15 * np.cos(freq3 * t))
            noise = 0.08 * np.random.randn(seq_length)

            signal[:, j] = trend + seasonal + noise

        normal_data.append(signal)
    
    # Generate rich and diverse anomaly patterns
    anomaly_types = [
        'sharp_spike', 'broad_spike', 'multiple_spikes', 'spike_cluster',
        'sudden_shift', 'gradual_shift', 'temporary_shift', 'drift',
        'high_freq_oscillation', 'low_freq_oscillation', 'amplitude_modulation', 'frequency_modulation',
        'sudden_drop', 'gradual_drop', 'step_discontinuity', 'ramp_discontinuity',
        'noise_burst', 'missing_data', 'outlier_cluster', 'pattern_change'
    ]

    for i in range(n_samples // 2):  # Generate more anomalies for diversity
        # Randomly select 1-3 anomaly types to combine
        selected_types = np.random.choice(anomaly_types, size=np.random.randint(1, 4), replace=False)

        t = np.linspace(0, 6*np.pi, seq_length)
        signal = np.zeros((seq_length, n_features))

        # Start with normal pattern (same as normal data generation)
        for j in range(n_features):
            freq1 = 0.3 + j * 0.05
            freq2 = 0.8 + j * 0.03
            freq3 = 1.5 + j * 0.02
            phase1 = j * 0.2
            phase2 = j * 0.15

            trend = 0.01 * t * (j % 3 - 1)
            seasonal = (0.4 * np.sin(freq1 * t + phase1) +
                       0.25 * np.sin(freq2 * t + phase2) +
                       0.15 * np.cos(freq3 * t))
            noise = 0.08 * np.random.randn(seq_length)

            signal[:, j] = trend + seasonal + noise

        # Add multiple anomaly types to different feature subsets
        for anomaly_type in selected_types:
            # Each anomaly affects different features
            affected_features = np.random.choice(n_features, size=np.random.randint(n_features//8, n_features//3), replace=False)
            
            # Apply the selected anomaly type
            if anomaly_type == 'sharp_spike':
                spike_pos = np.random.randint(15, seq_length-15)
                spike_width = np.random.randint(2, 5)
                for feat in affected_features:
                    spike_mask = np.abs(np.arange(seq_length) - spike_pos) <= spike_width
                    signal[spike_mask, feat] += np.random.uniform(3.0, 6.0) * np.exp(-0.5 * ((np.arange(seq_length)[spike_mask] - spike_pos) / (spike_width/2))**2)

            elif anomaly_type == 'broad_spike':
                spike_pos = np.random.randint(20, seq_length-20)
                spike_width = np.random.randint(8, 15)
                for feat in affected_features:
                    spike_mask = np.abs(np.arange(seq_length) - spike_pos) <= spike_width
                    signal[spike_mask, feat] += np.random.uniform(2.0, 4.0) * np.exp(-0.5 * ((np.arange(seq_length)[spike_mask] - spike_pos) / (spike_width/2))**2)

            elif anomaly_type == 'multiple_spikes':
                n_spikes = np.random.randint(2, 5)
                for _ in range(n_spikes):
                    spike_pos = np.random.randint(10, seq_length-10)
                    spike_width = np.random.randint(2, 6)
                    for feat in affected_features:
                        spike_mask = np.abs(np.arange(seq_length) - spike_pos) <= spike_width
                        signal[spike_mask, feat] += np.random.uniform(2.0, 4.0) * np.exp(-0.5 * ((np.arange(seq_length)[spike_mask] - spike_pos) / (spike_width/2))**2)

            elif anomaly_type == 'spike_cluster':
                cluster_center = np.random.randint(25, seq_length-25)
                cluster_width = np.random.randint(15, 30)
                n_spikes = np.random.randint(3, 8)
                for _ in range(n_spikes):
                    spike_pos = cluster_center + np.random.randint(-cluster_width//2, cluster_width//2)
                    spike_pos = max(5, min(seq_length-5, spike_pos))
                    for feat in affected_features:
                        spike_mask = np.abs(np.arange(seq_length) - spike_pos) <= 2
                        signal[spike_mask, feat] += np.random.uniform(1.5, 3.0) * np.exp(-0.5 * ((np.arange(seq_length)[spike_mask] - spike_pos) / 1)**2)

            elif anomaly_type == 'sudden_shift':
                shift_pos = np.random.randint(25, seq_length-25)
                for feat in affected_features:
                    signal[shift_pos:, feat] += np.random.uniform(-3.0, 3.0)

            elif anomaly_type == 'gradual_shift':
                shift_start = np.random.randint(20, seq_length//2)
                shift_end = np.random.randint(seq_length//2, seq_length-20)
                for feat in affected_features:
                    shift_magnitude = np.random.uniform(-2.5, 2.5)
                    ramp = np.linspace(0, shift_magnitude, shift_end - shift_start)
                    signal[shift_start:shift_end, feat] += ramp
                    signal[shift_end:, feat] += shift_magnitude

            elif anomaly_type == 'temporary_shift':
                shift_start = np.random.randint(20, seq_length-40)
                shift_duration = np.random.randint(15, 35)
                shift_end = min(shift_start + shift_duration, seq_length-5)
                for feat in affected_features:
                    signal[shift_start:shift_end, feat] += np.random.uniform(-2.0, 2.0)

            elif anomaly_type == 'drift':
                drift_start = np.random.randint(10, seq_length//3)
                for feat in affected_features:
                    drift_rate = np.random.uniform(-0.05, 0.05)
                    drift_values = drift_rate * np.arange(seq_length - drift_start)
                    signal[drift_start:, feat] += drift_values

            elif anomaly_type == 'high_freq_oscillation':
                osc_start = np.random.randint(15, seq_length//2)
                osc_end = np.random.randint(seq_length//2, seq_length-15)
                osc_freq = np.random.uniform(4.0, 8.0)
                for feat in affected_features:
                    osc_mask = (np.arange(seq_length) >= osc_start) & (np.arange(seq_length) <= osc_end)
                    signal[osc_mask, feat] += np.random.uniform(1.0, 2.5) * np.sin(osc_freq * t[osc_mask])

            elif anomaly_type == 'low_freq_oscillation':
                osc_start = np.random.randint(10, seq_length//3)
                osc_end = np.random.randint(2*seq_length//3, seq_length-10)
                osc_freq = np.random.uniform(0.1, 0.5)
                for feat in affected_features:
                    osc_mask = (np.arange(seq_length) >= osc_start) & (np.arange(seq_length) <= osc_end)
                    signal[osc_mask, feat] += np.random.uniform(1.5, 3.0) * np.sin(osc_freq * t[osc_mask])

            elif anomaly_type == 'amplitude_modulation':
                mod_freq = np.random.uniform(0.2, 0.8)
                carrier_freq = np.random.uniform(2.0, 4.0)
                for feat in affected_features:
                    modulation = (1 + 0.8 * np.sin(mod_freq * t)) * np.sin(carrier_freq * t)
                    signal[:, feat] += np.random.uniform(0.8, 1.5) * modulation

            elif anomaly_type == 'frequency_modulation':
                base_freq = np.random.uniform(1.0, 2.0)
                mod_depth = np.random.uniform(0.5, 1.5)
                for feat in affected_features:
                    instantaneous_freq = base_freq + mod_depth * np.sin(0.3 * t)
                    phase = np.cumsum(instantaneous_freq) * (t[1] - t[0])
                    signal[:, feat] += np.random.uniform(1.0, 2.0) * np.sin(phase)

            elif anomaly_type == 'sudden_drop':
                drop_pos = np.random.randint(20, seq_length-20)
                drop_width = np.random.randint(3, 10)
                for feat in affected_features:
                    drop_mask = np.abs(np.arange(seq_length) - drop_pos) <= drop_width
                    signal[drop_mask, feat] -= np.random.uniform(2.0, 4.0) * np.exp(-0.5 * ((np.arange(seq_length)[drop_mask] - drop_pos) / (drop_width/2))**2)

            elif anomaly_type == 'gradual_drop':
                drop_start = np.random.randint(15, seq_length//2)
                drop_end = np.random.randint(seq_length//2, seq_length-15)
                for feat in affected_features:
                    drop_magnitude = np.random.uniform(1.5, 3.0)
                    drop_curve = drop_magnitude * np.exp(-0.1 * np.arange(drop_end - drop_start))
                    signal[drop_start:drop_end, feat] -= drop_curve

            elif anomaly_type == 'step_discontinuity':
                jump_pos = np.random.randint(20, seq_length-20)
                for feat in affected_features:
                    jump_magnitude = np.random.uniform(-2.5, 2.5)
                    signal[jump_pos:, feat] += jump_magnitude
                    # Add noise around the discontinuity
                    noise_region = slice(max(0, jump_pos-3), min(seq_length, jump_pos+3))
                    signal[noise_region, feat] += 0.3 * np.random.randn(len(range(*noise_region.indices(seq_length))))

            elif anomaly_type == 'ramp_discontinuity':
                ramp_start = np.random.randint(15, seq_length-25)
                ramp_width = np.random.randint(5, 15)
                ramp_end = min(ramp_start + ramp_width, seq_length-5)
                for feat in affected_features:
                    ramp_magnitude = np.random.uniform(-2.0, 2.0)
                    ramp_values = np.linspace(0, ramp_magnitude, ramp_end - ramp_start)
                    signal[ramp_start:ramp_end, feat] += ramp_values
                    signal[ramp_end:, feat] += ramp_magnitude

            elif anomaly_type == 'noise_burst':
                burst_start = np.random.randint(10, seq_length-20)
                burst_duration = np.random.randint(8, 20)
                burst_end = min(burst_start + burst_duration, seq_length-5)
                for feat in affected_features:
                    noise_intensity = np.random.uniform(2.0, 4.0)
                    signal[burst_start:burst_end, feat] += noise_intensity * np.random.randn(burst_end - burst_start)

            elif anomaly_type == 'missing_data':
                missing_start = np.random.randint(15, seq_length-25)
                missing_duration = np.random.randint(5, 15)
                missing_end = min(missing_start + missing_duration, seq_length-5)
                for feat in affected_features:
                    signal[missing_start:missing_end, feat] = 0  # Simulate missing data as zeros

            elif anomaly_type == 'outlier_cluster':
                cluster_center = np.random.randint(20, seq_length-20)
                cluster_size = np.random.randint(5, 12)
                for _ in range(cluster_size):
                    outlier_pos = cluster_center + np.random.randint(-8, 8)
                    outlier_pos = max(0, min(seq_length-1, outlier_pos))
                    for feat in affected_features:
                        signal[outlier_pos, feat] += np.random.uniform(-3.0, 3.0)

            elif anomaly_type == 'pattern_change':
                change_pos = np.random.randint(seq_length//3, 2*seq_length//3)
                for feat in affected_features:
                    # Change the pattern after change_pos
                    new_freq = np.random.uniform(2.0, 5.0)
                    new_phase = np.random.uniform(0, 2*np.pi)
                    new_amplitude = np.random.uniform(1.0, 2.5)
                    signal[change_pos:, feat] = new_amplitude * np.sin(new_freq * t[change_pos:] + new_phase)

        anomaly_data.append(signal)
    
    print(f"   ✅ Generated {len(normal_data)} normal samples")
    print(f"   ✅ Generated {len(anomaly_data)} anomaly samples")
    
    return np.array(normal_data), np.array(anomaly_data)

def prepare_data_for_sae(normal_data, anomaly_data, window_size=32):
    """Prepare data for SAE training by creating sliding windows"""
    print("🔄 Preparing data for SAE training...")
    
    all_windows = []
    all_labels = []
    
    # Process normal data
    for sample in normal_data:
        for i in range(sample.shape[0] - window_size + 1):
            window = sample[i:i+window_size].flatten()
            all_windows.append(window)
            all_labels.append(0)  # Normal
    
    # Process anomaly data
    for sample in anomaly_data:
        for i in range(sample.shape[0] - window_size + 1):
            window = sample[i:i+window_size].flatten()
            all_windows.append(window)
            all_labels.append(1)  # Anomaly
    
    all_windows = np.array(all_windows)
    all_labels = np.array(all_labels)
    
    # Normalize the data
    scaler = StandardScaler()
    all_windows = scaler.fit_transform(all_windows)
    
    print(f"   ✅ Created {len(all_windows)} windows")
    print(f"   ✅ Window dimension: {all_windows.shape[1]}")
    print(f"   ✅ Normal/Anomaly ratio: {np.sum(all_labels==0)}/{np.sum(all_labels==1)}")
    
    return all_windows, all_labels, scaler

def train_sae(train_data, epochs=200, batch_size=128, lr=0.001, hidden_dim=128):
    """Train the Sparse Autoencoder"""
    print("🧠 Training Sparse Autoencoder...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"   Using device: {device}")
    
    # Prepare data
    train_tensor = torch.FloatTensor(train_data).to(device)
    dataset = TensorDataset(train_tensor)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    
    # Initialize model
    input_dim = train_data.shape[1]
    sae = SparseAutoencoder(input_dim=input_dim, hidden_dim=hidden_dim, 
                           sparsity_weight=0.01, dropout=0.1).to(device)
    
    optimizer = optim.Adam(sae.parameters(), lr=lr, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=20, factor=0.5)
    
    # Training loop
    losses = {'total': [], 'reconstruction': [], 'sparsity': []}
    
    for epoch in range(epochs):
        epoch_losses = {'total': 0, 'reconstruction': 0, 'sparsity': 0}
        
        for batch in dataloader:
            x = batch[0]
            optimizer.zero_grad()
            
            features, reconstruction = sae(x)
            total_loss, recon_loss, sparsity_loss = sae.loss_function(x, reconstruction, features)
            
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(sae.parameters(), max_norm=1.0)
            optimizer.step()
            
            epoch_losses['total'] += total_loss.item()
            epoch_losses['reconstruction'] += recon_loss.item()
            epoch_losses['sparsity'] += sparsity_loss.item()
        
        # Average losses
        for key in epoch_losses:
            epoch_losses[key] /= len(dataloader)
            losses[key].append(epoch_losses[key])
        
        scheduler.step(epoch_losses['total'])
        
        if epoch % 5 == 0:  # Print every 5 epochs for 30 total epochs
            print(f'   Epoch {epoch:3d}: Total={epoch_losses["total"]:.4f}, '
                  f'Recon={epoch_losses["reconstruction"]:.4f}, '
                  f'Sparsity={epoch_losses["sparsity"]:.4f}')
    
    print("   ✅ SAE training completed")
    return sae, losses

def analyze_learned_features(sae, normal_data, anomaly_data, top_k=16):
    """Analyze the features learned by SAE"""
    print("🔍 Analyzing learned features...")
    
    device = next(sae.parameters()).device
    sae.eval()
    
    # Get feature activations
    with torch.no_grad():
        normal_tensor = torch.FloatTensor(normal_data).to(device)
        anomaly_tensor = torch.FloatTensor(anomaly_data).to(device)
        
        normal_features, _ = sae(normal_tensor)
        anomaly_features, _ = sae(anomaly_tensor)
        
        normal_features = normal_features.cpu().numpy()
        anomaly_features = anomaly_features.cpu().numpy()
    
    # Analyze feature statistics
    feature_stats = {}
    n_features = normal_features.shape[1]
    
    for i in range(n_features):
        # Activation statistics
        normal_activations = normal_features[:, i]
        anomaly_activations = anomaly_features[:, i]
        
        # Sparsity (fraction of near-zero activations)
        sparsity = np.mean(np.abs(normal_activations) < 0.1)
        
        # Discriminative power (AUC for separating normal vs anomaly)
        all_activations = np.concatenate([normal_activations, anomaly_activations])
        all_labels = np.concatenate([np.zeros(len(normal_activations)), 
                                   np.ones(len(anomaly_activations))])
        
        try:
            auc = roc_auc_score(all_labels, all_activations)
            discriminative_power = max(auc, 1 - auc)  # Take the better direction
        except:
            discriminative_power = 0.5
        
        # Activation magnitude
        avg_normal_activation = np.mean(np.abs(normal_activations))
        avg_anomaly_activation = np.mean(np.abs(anomaly_activations))
        
        feature_stats[i] = {
            'sparsity': sparsity,
            'discriminative_power': discriminative_power,
            'avg_normal_activation': avg_normal_activation,
            'avg_anomaly_activation': avg_anomaly_activation,
            'activation_ratio': avg_anomaly_activation / (avg_normal_activation + 1e-8)
        }
    
    # Sort features by discriminative power
    sorted_features = sorted(feature_stats.items(), 
                           key=lambda x: x[1]['discriminative_power'], 
                           reverse=True)
    
    print(f"   ✅ Analyzed {n_features} features")
    print(f"   ✅ Top discriminative features: {[f[0] for f in sorted_features[:5]]}")
    
    return feature_stats, sorted_features[:top_k]

def create_real_feature_dictionary_visualization(sae, feature_stats, top_features,
                                                normal_data, anomaly_data, scaler):
    """Create enhanced feature dictionary visualization showing temporal patterns"""
    print("🎨 Creating real feature dictionary visualization...")

    device = next(sae.parameters()).device
    sae.eval()

    # Create figure
    fig, axes = plt.subplots(4, 4, figsize=(16, 12))
    fig.suptitle('Learned Feature Dictionary: Temporal Pattern Types',
                fontsize=16, fontweight='bold', y=0.95)

    # Define pattern types and colors
    pattern_types = ['Spike Type', 'Level Type', 'Oscillatory Type', 'Discontinuity Type']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

    # Get decoder weights to visualize learned patterns
    decoder_weights = sae.decoder[0].weight.detach().cpu().numpy()

    # Plot top 16 most discriminative features
    for idx, (feature_idx, stats) in enumerate(top_features):
        row, col = idx // 4, idx % 4
        ax = axes[row, col]

        # Get the decoder weights for this feature (represents the pattern it detects)
        feature_pattern = decoder_weights[feature_idx]

        # Create time axis
        time_axis = np.linspace(0, 10, len(feature_pattern))

        # Enhance pattern visualization based on row (pattern type)
        enhanced_pattern = feature_pattern.copy()

        if row == 0:  # Spike patterns
            # Emphasize spike-like characteristics
            peak_idx = np.argmax(np.abs(enhanced_pattern))
            spike_width = 3
            for i in range(max(0, peak_idx-spike_width), min(len(enhanced_pattern), peak_idx+spike_width+1)):
                distance = abs(i - peak_idx)
                enhanced_pattern[i] *= (1 + 2.0 * np.exp(-0.5 * (distance / (spike_width/3))**2))

            # Mark anomaly region with red shading
            ax.axvspan(time_axis[max(0, peak_idx-spike_width-1)],
                      time_axis[min(len(time_axis)-1, peak_idx+spike_width+1)],
                      alpha=0.2, color='red', zorder=0, label='Anomaly Region' if idx == 0 else "")

        elif row == 1:  # Level shift patterns
            # Emphasize level changes
            mid_point = len(enhanced_pattern) // 2
            level_shift = np.mean(np.abs(enhanced_pattern)) * 0.8
            enhanced_pattern[mid_point:] += level_shift * (1 if col % 2 == 0 else -1)

            # Mark shift point with red dashed line
            ax.axvline(time_axis[mid_point], color='red', linestyle='--',
                      linewidth=2, alpha=0.8, label='Level Change' if idx == 4 else "")

        elif row == 2:  # Oscillatory patterns
            # Add oscillatory component
            freq = 0.5 + col * 0.2
            for i in range(len(enhanced_pattern)):
                enhanced_pattern[i] += 0.5 * np.sin(freq * i) * np.abs(enhanced_pattern[i])

            # Mark oscillatory region
            ax.axvspan(time_axis[len(time_axis)//4], time_axis[3*len(time_axis)//4],
                      alpha=0.15, color='lightblue', zorder=0,
                      label='Pattern Region' if idx == 8 else "")

        elif row == 3:  # Discontinuity patterns
            # Emphasize discontinuities
            jump_point = len(enhanced_pattern) // 2 + col
            if jump_point < len(enhanced_pattern):
                jump_magnitude = np.std(enhanced_pattern) * 1.5
                enhanced_pattern[jump_point:] += jump_magnitude * ((-1)**(col % 2))

                # Mark discontinuity with red dashed line
                ax.axvline(time_axis[jump_point], color='red', linestyle='--',
                          linewidth=2, alpha=0.8, label='Discontinuity' if idx == 12 else "")

        # Plot the enhanced feature pattern
        ax.plot(time_axis, enhanced_pattern, color=colors[row], linewidth=2.5, alpha=0.9)
        ax.fill_between(time_axis, enhanced_pattern, alpha=0.4, color=colors[row])

        # Set title with feature info
        ax.set_title(f'Feature #{feature_idx}\n{pattern_types[row]}',
                    fontweight='bold', fontsize=11)

        # Add discriminative power as text
        ax.text(0.02, 0.98, f'Disc: {stats["discriminative_power"]:.3f}',
               transform=ax.transAxes, fontsize=9,
               bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.7),
               verticalalignment='top')

        # Set labels
        if row == 3:  # Bottom row
            ax.set_xlabel('Time')
        if col == 0:  # Left column
            ax.set_ylabel('Amplitude')

        # Clean up the plot
        ax.grid(True, alpha=0.3)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

        # Add legend to specific subplots
        if idx in [0, 4, 8, 12]:
            ax.legend(loc='upper right', fontsize=8)

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.05)
    plt.savefig('real_feature_dictionary_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   ✅ Real feature dictionary visualization saved")

def create_feature_activation_patterns(sae, normal_data, anomaly_data, top_features, scaler):
    """Create visualization of feature activation patterns on time series"""
    print("🎨 Creating feature activation patterns...")

    device = next(sae.parameters()).device
    sae.eval()

    # Create figure for activation patterns
    fig, axes = plt.subplots(2, 4, figsize=(20, 8))
    fig.suptitle('Feature Activation Patterns on Time Series Examples',
                fontsize=16, fontweight='bold', y=0.95)

    # Select some example time series
    example_normal = normal_data[0]  # First normal example
    example_anomaly = anomaly_data[0]  # First anomaly example

    # Create sliding windows for visualization (must match training window size)
    window_size = 16  # Match the window size used in training
    time_steps = []
    activations_normal = []
    activations_anomaly = []

    with torch.no_grad():
        for i in range(example_normal.shape[0] - window_size + 1):
            # Normal example
            window_normal = example_normal[i:i+window_size].flatten()
            # Apply the same scaling as used in training
            window_normal = scaler.transform(window_normal.reshape(1, -1))[0]
            window_normal = torch.FloatTensor(window_normal).unsqueeze(0).to(device)
            features_normal, _ = sae(window_normal)
            activations_normal.append(features_normal.cpu().numpy()[0])

            # Anomaly example
            window_anomaly = example_anomaly[i:i+window_size].flatten()
            # Apply the same scaling as used in training
            window_anomaly = scaler.transform(window_anomaly.reshape(1, -1))[0]
            window_anomaly = torch.FloatTensor(window_anomaly).unsqueeze(0).to(device)
            features_anomaly, _ = sae(window_anomaly)
            activations_anomaly.append(features_anomaly.cpu().numpy()[0])

            time_steps.append(i)

    activations_normal = np.array(activations_normal)
    activations_anomaly = np.array(activations_anomaly)

    # Plot top 8 features
    for idx in range(8):
        feature_idx = top_features[idx][0]

        ax = axes[idx // 4, idx % 4]

        # Plot activation over time
        ax.plot(time_steps, activations_normal[:, feature_idx],
               label='Normal', color='#4ECDC4', linewidth=2)
        ax.plot(time_steps, activations_anomaly[:, feature_idx],
               label='Anomaly', color='#FF6B6B', linewidth=2)

        ax.set_title(f'Feature #{feature_idx} Activation Over Time',
                    fontsize=11, fontweight='bold')
        ax.set_xlabel('Time Step' if idx >= 4 else '')
        ax.set_ylabel('Activation' if idx % 4 == 0 else '')
        ax.grid(True, alpha=0.3)

        if idx == 0:
            ax.legend()

    plt.tight_layout()
    plt.savefig('feature_activation_patterns.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   ✅ Feature activation patterns saved")

if __name__ == "__main__":
    print("🚀 Real SAE Training and Feature Analysis")
    print("=" * 50)

    # Step 1: Generate synthetic data (reduced size for faster training)
    normal_data, anomaly_data = generate_synthetic_timeseries_data(
        n_samples=1000, seq_length=64, n_features=16  # Reduced for faster training
    )

    # Step 2: Prepare data for SAE
    all_windows, all_labels, scaler = prepare_data_for_sae(
        normal_data, anomaly_data, window_size=16  # Smaller window
    )

    # Split data (use only normal data for SAE training)
    normal_mask = all_labels == 0
    normal_windows = all_windows[normal_mask]
    anomaly_windows = all_windows[~normal_mask]

    # Step 3: Train SAE (30 epochs)
    sae, training_losses = train_sae(
        normal_windows, epochs=30, batch_size=256, lr=0.002, hidden_dim=64  # 30 epochs
    )

    # Step 4: Analyze learned features
    feature_stats, top_features = analyze_learned_features(
        sae, normal_windows[:5000], anomaly_windows[:2000], top_k=16
    )

    # Step 5: Create visualizations
    create_real_feature_dictionary_visualization(
        sae, feature_stats, top_features, normal_windows[:1000], anomaly_windows[:500], scaler
    )

    create_feature_activation_patterns(
        sae, normal_data[:10], anomaly_data[:10], top_features, scaler
    )

    print("\n🎉 Real SAE training and visualization completed!")
    print("📊 Generated files:")
    print("   • real_feature_dictionary_visualization.png")
    print("   • feature_activation_patterns.png")
