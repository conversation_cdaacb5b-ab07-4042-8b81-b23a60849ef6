#!/usr/bin/env python3
"""
HTA-AD Integrated Model with Sparse Autoencoder
Complete implementation combining hierarchical temporal attention with interpretable features
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, precision_recall_fscore_support
import pandas as pd
import os
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# Import SAE components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'sae_integration'))
from sparse_autoencoder import SparseAutoencoder, HTAADWithSAE, SAETrainer


class HTAADComplete(nn.Module):
    """Complete HTA-AD model with all components integrated"""
    
    def __init__(self, 
                 input_dim: int = 1,
                 d_model: int = 32,
                 n_heads: int = 4,
                 n_layers: int = 2,
                 seq_len: int = 100,
                 sae_hidden_dim: int = 128,
                 sae_sparsity_weight: float = 0.01,
                 dropout: float = 0.1,
                 enable_sae: bool = True):
        super(HTAADComplete, self).__init__()
        
        self.input_dim = input_dim
        self.d_model = d_model
        self.seq_len = seq_len
        self.enable_sae = enable_sae
        
        # Core HTA-AD with SAE integration
        if enable_sae:
            self.model = HTAADWithSAE(
                input_dim=input_dim,
                d_model=d_model,
                n_heads=n_heads,
                n_layers=n_layers,
                seq_len=seq_len,
                sae_hidden_dim=sae_hidden_dim,
                sae_sparsity_weight=sae_sparsity_weight
            )
        else:
            # Fallback to basic HTA-AD without SAE
            self.model = self._create_basic_hta_ad(
                input_dim, d_model, n_heads, n_layers, seq_len, dropout
            )
        
        # Additional components for enhanced functionality
        self.anomaly_threshold = 0.5
        self.feature_importance_cache = {}
    
    def _create_basic_hta_ad(self, input_dim, d_model, n_heads, n_layers, seq_len, dropout):
        """Create basic HTA-AD without SAE for fallback"""
        class BasicHTAAD(nn.Module):
            def __init__(self):
                super(BasicHTAAD, self).__init__()
                self.input_projection = nn.Linear(input_dim, d_model)
                self.pos_encoding = self._create_positional_encoding(seq_len, d_model)
                
                self.attention_layers = nn.ModuleList([
                    nn.MultiheadAttention(d_model, n_heads, batch_first=True)
                    for _ in range(n_layers)
                ])
                
                self.layer_norms = nn.ModuleList([
                    nn.LayerNorm(d_model) for _ in range(n_layers)
                ])
                
                self.anomaly_head = nn.Sequential(
                    nn.Linear(d_model, d_model // 2),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(d_model // 2, 1),
                    nn.Sigmoid()
                )
                
                self.reconstruction_head = nn.Linear(d_model, input_dim)
            
            def _create_positional_encoding(self, seq_len, d_model):
                pe = torch.zeros(seq_len, d_model)
                position = torch.arange(0, seq_len, dtype=torch.float).unsqueeze(1)
                div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                                   (-np.log(10000.0) / d_model))
                pe[:, 0::2] = torch.sin(position * div_term)
                pe[:, 1::2] = torch.cos(position * div_term)
                return pe.unsqueeze(0)
            
            def forward(self, x, return_interpretability=False):
                batch_size, seq_len, _ = x.shape
                
                x = self.input_projection(x)
                x = x + self.pos_encoding[:, :seq_len, :].to(x.device)
                
                attention_weights = []
                for attn, norm in zip(self.attention_layers, self.layer_norms):
                    residual = x
                    attn_output, attn_weight = attn(x, x, x)
                    x = norm(attn_output + residual)
                    attention_weights.append(attn_weight)
                
                anomaly_scores = self.anomaly_head(x).squeeze(-1)
                reconstruction = self.reconstruction_head(x)
                
                results = {
                    'anomaly_scores': anomaly_scores,
                    'reconstruction': reconstruction
                }
                
                if return_interpretability:
                    results.update({
                        'attention_weights': attention_weights,
                        'hidden_states': x
                    })
                
                return results
            
            def compute_loss(self, outputs, targets, anomaly_labels=None):
                losses = {}
                
                recon_loss = nn.MSELoss()(outputs['reconstruction'], targets)
                losses['reconstruction'] = recon_loss
                
                if anomaly_labels is not None:
                    anomaly_loss = nn.BCELoss()(outputs['anomaly_scores'], anomaly_labels.float())
                    losses['anomaly'] = anomaly_loss
                
                total_loss = sum(losses.values())
                losses['total'] = total_loss
                
                return losses
        
        return BasicHTAAD()
    
    def forward(self, x: torch.Tensor, return_interpretability: bool = False) -> Dict[str, torch.Tensor]:
        """Forward pass through the complete model"""
        return self.model(x, return_interpretability=return_interpretability)
    
    def compute_loss(self, outputs: Dict[str, torch.Tensor], targets: torch.Tensor,
                    anomaly_labels: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Compute loss using the underlying model"""
        return self.model.compute_loss(outputs, targets, anomaly_labels)
    
    def detect_anomalies(self, x: torch.Tensor, threshold: Optional[float] = None) -> Dict[str, torch.Tensor]:
        """Detect anomalies with optional threshold"""
        if threshold is None:
            threshold = self.anomaly_threshold
        
        self.eval()
        with torch.no_grad():
            outputs = self.forward(x, return_interpretability=True)
            
            anomaly_scores = outputs['anomaly_scores']
            anomaly_predictions = (anomaly_scores > threshold).float()
            
            # Compute reconstruction error
            recon_error = torch.mean((outputs['reconstruction'] - x) ** 2, dim=-1)
            
            results = {
                'anomaly_scores': anomaly_scores,
                'anomaly_predictions': anomaly_predictions,
                'reconstruction_error': recon_error,
                'reconstruction': outputs['reconstruction']
            }
            
            # Add SAE-specific results if available
            if self.enable_sae and 'sae_features' in outputs:
                results.update({
                    'sae_features': outputs['sae_features'],
                    'feature_importance': outputs.get('feature_importance'),
                    'interpretable_features': self._extract_interpretable_features(outputs['sae_features'])
                })
            
            return results
    
    def _extract_interpretable_features(self, sae_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Extract interpretable feature patterns"""
        # Compute feature activation statistics
        feature_means = torch.mean(sae_features, dim=(0, 1))
        feature_stds = torch.std(sae_features, dim=(0, 1))
        feature_max = torch.max(sae_features.view(-1, sae_features.size(-1)), dim=0)[0]
        
        # Identify most active features
        top_features = torch.topk(feature_means, k=min(10, len(feature_means)))[1]
        
        return {
            'feature_means': feature_means,
            'feature_stds': feature_stds,
            'feature_max': feature_max,
            'top_active_features': top_features,
            'sparsity_ratio': (sae_features > 0.1).float().mean()
        }
    
    def explain_anomaly(self, x: torch.Tensor, anomaly_idx: int) -> Dict[str, Any]:
        """Provide explanation for a specific anomaly"""
        if not self.enable_sae:
            return {"error": "SAE not enabled for interpretability"}
        
        self.eval()
        with torch.no_grad():
            outputs = self.forward(x, return_interpretability=True)
            
            # Extract features for the anomalous sample
            anomaly_features = outputs['sae_features'][anomaly_idx]
            
            # Find most contributing features
            feature_contributions = torch.abs(anomaly_features)
            top_contributing_features = torch.topk(feature_contributions.mean(dim=0), k=5)[1]
            
            # Compute attention patterns
            attention_weights = outputs.get('attention_weights', [])
            if attention_weights:
                # Average attention across heads and layers
                avg_attention = torch.stack(attention_weights).mean(dim=(0, 1))
                anomaly_attention = avg_attention[anomaly_idx]
            else:
                anomaly_attention = None
            
            explanation = {
                'anomaly_score': outputs['anomaly_scores'][anomaly_idx].item(),
                'top_contributing_features': top_contributing_features.tolist(),
                'feature_contributions': feature_contributions.mean(dim=0)[top_contributing_features].tolist(),
                'attention_pattern': anomaly_attention.tolist() if anomaly_attention is not None else None,
                'reconstruction_error': torch.mean((outputs['reconstruction'][anomaly_idx] - x[anomaly_idx]) ** 2).item()
            }
            
            return explanation
    
    def get_feature_dictionary(self) -> Dict[str, Any]:
        """Get interpretable feature dictionary from SAE"""
        if not self.enable_sae:
            return {"error": "SAE not enabled"}
        
        # Extract SAE decoder weights as feature dictionary
        sae_decoder = self.model.sae.decoder
        feature_dict = {}
        
        for i, layer in enumerate(sae_decoder):
            if isinstance(layer, nn.Linear):
                weights = layer.weight.detach().cpu().numpy()
                feature_dict[f'layer_{i}'] = {
                    'weights': weights,
                    'shape': weights.shape,
                    'feature_patterns': self._analyze_feature_patterns(weights)
                }
        
        return feature_dict
    
    def _analyze_feature_patterns(self, weights: np.ndarray) -> Dict[str, Any]:
        """Analyze patterns in feature weights"""
        # Compute basic statistics
        patterns = {
            'mean_activation': np.mean(np.abs(weights), axis=1),
            'max_activation': np.max(np.abs(weights), axis=1),
            'sparsity': np.mean(np.abs(weights) < 0.01, axis=1),
            'dominant_features': np.argmax(np.abs(weights), axis=1)
        }
        
        return patterns
    
    def save_model(self, path: str):
        """Save the complete model"""
        torch.save({
            'model_state_dict': self.state_dict(),
            'config': {
                'input_dim': self.input_dim,
                'd_model': self.d_model,
                'seq_len': self.seq_len,
                'enable_sae': self.enable_sae
            }
        }, path)
    
    @classmethod
    def load_model(cls, path: str, device: str = 'cpu'):
        """Load the complete model"""
        checkpoint = torch.load(path, map_location=device)
        config = checkpoint['config']
        
        model = cls(**config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(device)
        
        return model


class HTAADTrainer:
    """Complete trainer for HTA-AD with SAE integration"""
    
    def __init__(self, model: HTAADComplete, device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.training_history = []
    
    def train(self, train_loader: DataLoader, val_loader: Optional[DataLoader] = None,
              epochs: int = 100, lr: float = 1e-3, patience: int = 15) -> Dict[str, List[float]]:
        """Train the complete model"""
        if self.model.enable_sae:
            # Use SAE trainer
            sae_trainer = SAETrainer(self.model.model, self.device)
            return sae_trainer.fit(train_loader, val_loader, epochs, lr)
        else:
            # Use basic trainer
            return self._train_basic(train_loader, val_loader, epochs, lr, patience)
    
    def _train_basic(self, train_loader, val_loader, epochs, lr, patience):
        """Basic training without SAE"""
        optimizer = optim.Adam(self.model.parameters(), lr=lr)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=patience//2, factor=0.5)
        
        history = {'train_loss': [], 'val_loss': []}
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            # Training
            self.model.train()
            train_loss = 0
            for batch_data in train_loader:
                if len(batch_data) == 2:
                    x, labels = batch_data
                    x, labels = x.to(self.device), labels.to(self.device)
                else:
                    x = batch_data[0].to(self.device)
                    labels = None
                
                optimizer.zero_grad()
                outputs = self.model(x)
                losses = self.model.compute_loss(outputs, x, labels)
                losses['total'].backward()
                optimizer.step()
                
                train_loss += losses['total'].item()
            
            train_loss /= len(train_loader)
            history['train_loss'].append(train_loss)
            
            # Validation
            if val_loader is not None:
                self.model.eval()
                val_loss = 0
                with torch.no_grad():
                    for batch_data in val_loader:
                        if len(batch_data) == 2:
                            x, labels = batch_data
                            x, labels = x.to(self.device), labels.to(self.device)
                        else:
                            x = batch_data[0].to(self.device)
                            labels = None
                        
                        outputs = self.model(x)
                        losses = self.model.compute_loss(outputs, x, labels)
                        val_loss += losses['total'].item()
                
                val_loss /= len(val_loader)
                history['val_loss'].append(val_loss)
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        print(f"Early stopping at epoch {epoch}")
                        break
            
            if epoch % 10 == 0:
                print(f"Epoch {epoch}: Train Loss = {train_loss:.4f}")
                if val_loader is not None:
                    print(f"           Val Loss = {val_loss:.4f}")
        
        return history
    
    def evaluate(self, test_loader: DataLoader) -> Dict[str, float]:
        """Evaluate the model"""
        self.model.eval()
        all_scores = []
        all_labels = []
        total_loss = 0
        
        with torch.no_grad():
            for batch_data in test_loader:
                if len(batch_data) == 2:
                    x, labels = batch_data
                    x, labels = x.to(self.device), labels.to(self.device)
                    all_labels.extend(labels.cpu().numpy())
                else:
                    x = batch_data[0].to(self.device)
                    labels = None
                
                outputs = self.model(x)
                losses = self.model.compute_loss(outputs, x, labels)
                total_loss += losses['total'].item()
                
                # Collect anomaly scores
                anomaly_scores = outputs['anomaly_scores'].cpu().numpy()
                all_scores.extend(anomaly_scores.flatten())
        
        metrics = {'test_loss': total_loss / len(test_loader)}
        
        # Compute AUC if labels available
        if all_labels:
            auc = roc_auc_score(all_labels, all_scores)
            metrics['auc'] = auc
        
        return metrics
