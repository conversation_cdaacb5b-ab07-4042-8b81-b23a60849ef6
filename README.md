# HTA-AD: Hierarchical Temporal Attention for Anomaly Detection

A comprehensive implementation of Hierarchical Temporal Attention with Sparse Autoencoder integration for interpretable time series anomaly detection.

## 🏗️ Project Structure

```
HTA-AD/
├── main.py                     # Main entry point
├── requirements.txt            # Dependencies
├── README.md                   # This file
│
├── core/                       # Core implementation
│   ├── models/                 # Model implementations
│   │   ├── hta_ad.py          # Original HTA-AD model
│   │   └── hta_ad_integrated.py # Complete integrated model
│   │
│   ├── sae_integration/        # Sparse Autoencoder components
│   │   ├── sparse_autoencoder.py # SAE implementation
│   │   └── real_sae_training.py  # SAE training utilities
│   │
│   ├── experiments/            # Experiment scripts
│   │   ├── comprehensive_10_dataset_benchmark.py
│   │   ├── full_tsb_ad_benchmark.py
│   │   └── run_benchmark_with_pretrained_sae.py
│   │
│   └── visualization/          # Visualization tools
│       ├── generate_paper_figures.py
│       └── create_exact_latent_space_comparison.py
│
├── results/                    # Results and outputs
│   ├── figures/               # Generated figures
│   ├── data/                  # Result data files
│   └── models/                # Trained models
│
├── archive/                   # Archived files
│   ├── old_experiments/       # Old experiment scripts
│   ├── old_visualizations/    # Old visualization files
│   ├── temp_files/           # Temporary files
│   └── test_files/           # Test scripts
│
├── docs/                      # Documentation
├── figures/                   # Paper figures
├── interpretability_results/  # Interpretability analysis
└── TSB-AD/                   # TSB-AD benchmark dataset
```

## 🚀 Quick Start

### Installation

```bash
pip install -r requirements.txt
```

### Basic Usage

#### 1. Training a Model

```bash
# Train HTA-AD with SAE integration
python main.py --mode train \
    --data_path data/your_dataset.csv \
    --enable_sae \
    --epochs 100 \
    --save_path results/models/hta_ad_model.pth

# Train basic HTA-AD without SAE
python main.py --mode train \
    --data_path data/your_dataset.csv \
    --epochs 100 \
    --save_path results/models/basic_hta_ad.pth
```

#### 2. Evaluating a Model

```bash
python main.py --mode evaluate \
    --model_path results/models/hta_ad_model.pth \
    --data_path data/test_dataset.csv
```

#### 3. Detecting Anomalies

```bash
python main.py --mode detect \
    --model_path results/models/hta_ad_model.pth \
    --data_path data/new_data.csv \
    --threshold 0.5 \
    --output_path results/data/anomaly_results.csv
```

## 🧠 Model Architecture

### HTA-AD Core Components

1. **Hierarchical Temporal Attention**: Multi-scale attention mechanism for capturing temporal dependencies
2. **Sparse Autoencoder Integration**: Interpretable feature learning for anomaly attribution
3. **Anomaly Detection Head**: Binary classification for anomaly detection
4. **Reconstruction Head**: Time series reconstruction for unsupervised learning

### Key Features

- **Interpretability**: SAE provides interpretable features and anomaly attribution
- **Scalability**: Efficient attention mechanism for long sequences
- **Flexibility**: Can work with or without labeled data
- **Robustness**: Handles multivariate and univariate time series

## 📊 Experiments

### Available Benchmarks

1. **TSB-AD Benchmark**: Comprehensive evaluation on 10 datasets
2. **Large-scale SAE Pretraining**: Scalable feature learning
3. **Interpretability Analysis**: Feature attribution and visualization

### Running Experiments

```bash
# Run comprehensive benchmark
python core/experiments/comprehensive_10_dataset_benchmark.py

# Run TSB-AD benchmark
python core/experiments/full_tsb_ad_benchmark.py

# Run with pretrained SAE
python core/experiments/run_benchmark_with_pretrained_sae.py
```

## 🎨 Visualization

### Generate Paper Figures

```bash
# Generate latent space comparison
python core/visualization/create_exact_latent_space_comparison.py

# Generate all paper figures
python core/visualization/generate_paper_figures.py
```

### Available Visualizations

- Latent space comparisons (HTA-AD vs Transformer)
- Feature dictionary visualization
- Anomaly attribution plots
- Purification mechanism analysis

## 🔧 Configuration

### Model Parameters

```python
model = HTAADComplete(
    input_dim=1,           # Input dimension
    d_model=32,            # Model dimension
    n_heads=4,             # Attention heads
    n_layers=2,            # Number of layers
    seq_len=100,           # Sequence length
    sae_hidden_dim=128,    # SAE hidden dimension
    sae_sparsity_weight=0.01,  # Sparsity regularization
    enable_sae=True        # Enable SAE integration
)
```

### Training Parameters

- **Learning Rate**: 1e-3 (default)
- **Batch Size**: 32 (default)
- **Epochs**: 100 (default)
- **Early Stopping**: 15 epochs patience

## 📈 Results

### Performance Metrics

- **AUC-ROC**: Area under ROC curve
- **Precision/Recall**: Classification metrics
- **Reconstruction Error**: MSE for reconstruction
- **Feature Sparsity**: SAE feature activation ratio

### Interpretability Features

- **Feature Attribution**: Which features contribute to anomalies
- **Attention Patterns**: Temporal attention visualization
- **Feature Dictionary**: Learned interpretable patterns

## 🔬 Research Applications

### Academic Use

This implementation supports research in:
- Time series anomaly detection
- Interpretable machine learning
- Attention mechanisms
- Sparse representation learning

### Industrial Applications

- Network intrusion detection
- Equipment failure prediction
- Financial fraud detection
- IoT sensor monitoring

## 📚 Citation

If you use this code in your research, please cite:

```bibtex
@article{hta_ad_2024,
    title={HTA-AD: Hierarchical Temporal Attention for Anomaly Detection with Sparse Autoencoder Integration},
    author={Your Name},
    journal={Conference/Journal},
    year={2024}
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions and support:
- Open an issue on GitHub
- Check the documentation in `docs/`
- Review example scripts in `core/experiments/`

## 🔄 Version History

- **v1.0.0**: Initial release with basic HTA-AD
- **v2.0.0**: SAE integration and interpretability features
- **v2.1.0**: Comprehensive benchmarking and visualization tools

---

**Note**: This project is actively maintained. Check the `archive/` folder for older experimental code that may be removed in future versions.
