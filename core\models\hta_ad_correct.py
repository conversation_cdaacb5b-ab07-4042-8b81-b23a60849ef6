#!/usr/bin/env python3
"""
Correct HTA-AD Implementation based on backup file
HTA-AD: Hourglass Temporal Autoencoder for Anomaly Detection
Architecture: CNN (downsampling) + TCN (feature extraction) + SAE (interpretability)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils import weight_norm
import numpy as np
from typing import Dict, List, Tuple, Optional
import math

# --- TCN Components ---
class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        self.net = nn.Sequential(self.conv1, self.relu1, self.dropout1,
                                 self.conv2, self.relu2, self.dropout2)
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)

class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            layers.append(TemporalBlock(in_channels, out_channels, kernel_size, stride=1, 
                                        dilation=dilation_size, dropout=dropout))
        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

# --- Main HTA-AD Model ---
class HTAADCorrect(nn.Module):
    """
    Correct HTA-AD model based on backup file
    Architecture: CNN (downsampling) + TCN (feature extraction) + Hourglass structure
    """
    
    def __init__(self, 
                 input_dim: int = 1,
                 window_size: int = 128,
                 latent_dim: int = 32,
                 tcn_channels: List[int] = [32, 32, 32],
                 tcn_kernel_size: int = 3,
                 cnn_channels: int = 16,
                 downsample_stride: int = 2,
                 dropout: float = 0.2):
        super(HTAADCorrect, self).__init__()
        
        # Store configuration
        self.input_dim = input_dim
        self.window_size = window_size
        self.latent_dim = latent_dim
        self.tcn_channels = tcn_channels
        self.cnn_channels = cnn_channels
        self.downsample_stride = downsample_stride
        
        # --- Encoder ---
        # 1. CNN Downsampler
        self.encoder_cnn = nn.Sequential(
            nn.Conv1d(input_dim, cnn_channels, kernel_size=7, padding=3, stride=downsample_stride),
            nn.GELU()
        )
        
        # Calculate shape after CNN downsampling
        with torch.no_grad():
            dummy_input = torch.zeros(1, input_dim, window_size)
            cnn_output_shape = self.encoder_cnn(dummy_input).shape
            self.downsampled_len = cnn_output_shape[2]
        
        # 2. TCN for temporal feature extraction
        self.encoder_tcn = TemporalConvNet(
            num_inputs=cnn_channels,
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size,
            dropout=dropout
        )
        
        # 3. Bottleneck (Hourglass neck)
        self.fc_encode = nn.Linear(tcn_channels[-1] * self.downsampled_len, latent_dim)
        
        # --- Decoder ---
        # 1. Expand from bottleneck
        self.decoder_fc = nn.Linear(latent_dim, tcn_channels[-1] * self.downsampled_len)
        
        # 2. Inverse TCN
        self.decoder_tcn = TemporalConvNet(
            num_inputs=tcn_channels[-1],
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size,
            dropout=dropout
        )
        
        # 3. CNN Upsampler
        self.decoder_cnn = nn.Sequential(
            nn.ConvTranspose1d(tcn_channels[-1], input_dim, kernel_size=7, padding=3, 
                              stride=downsample_stride, output_padding=downsample_stride-1),
        )
        
        self.output_activation = nn.Sigmoid()
        self.tcn_output_channels = tcn_channels[-1]
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize model weights"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, (nn.Conv1d, nn.ConvTranspose1d)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def encode(self, x: torch.Tensor) -> torch.Tensor:
        """
        Encode input to latent space
        Args:
            x: [batch_size, window_size, input_dim]
        Returns:
            latent: [batch_size, latent_dim]
        """
        # Permute for Conv1d: (batch, input_dim, window_size)
        x_permuted = x.permute(0, 2, 1)
        
        # CNN downsampling
        encoded_cnn = self.encoder_cnn(x_permuted)
        
        # TCN feature extraction
        encoded_tcn = self.encoder_tcn(encoded_cnn)
        
        # Flatten and encode to latent space
        encoded_flat = encoded_tcn.flatten(start_dim=1)
        latent_vec = self.fc_encode(encoded_flat)
        
        return latent_vec
    
    def decode(self, latent: torch.Tensor) -> torch.Tensor:
        """
        Decode latent vector to reconstruction
        Args:
            latent: [batch_size, latent_dim]
        Returns:
            reconstruction: [batch_size, window_size, input_dim]
        """
        # Expand from latent space
        decoded_flat = self.decoder_fc(latent)
        decoded_unflat = decoded_flat.view(-1, self.tcn_output_channels, self.downsampled_len)
        
        # Inverse TCN
        decoded_tcn = self.decoder_tcn(decoded_unflat)
        
        # CNN upsampling
        reconstructed_permuted = self.decoder_cnn(decoded_tcn)
        
        # Adjust size if needed (due to padding/stride)
        if reconstructed_permuted.shape[2] != self.window_size:
            reconstructed_permuted = F.interpolate(reconstructed_permuted, size=self.window_size, 
                                                 mode='linear', align_corners=False)
        
        # Permute back: (batch, window_size, input_dim)
        reconstructed = reconstructed_permuted.permute(0, 2, 1)
        
        return self.output_activation(reconstructed)
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass
        Args:
            x: [batch_size, window_size, input_dim]
        Returns:
            Dict containing reconstruction and latent vectors
        """
        # Encode to latent space
        latent = self.encode(x)
        
        # Decode to reconstruction
        reconstruction = self.decode(latent)
        
        return {
            'reconstruction': reconstruction,
            'latent_vectors': latent  # For SAE training
        }
    
    def compute_loss(self, outputs: Dict[str, torch.Tensor], targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute reconstruction loss
        """
        reconstruction_loss = F.mse_loss(outputs['reconstruction'], targets)
        
        return {
            'total': reconstruction_loss,
            'reconstruction': reconstruction_loss
        }

# --- SAE for Interpretability ---
class PostHocSAE(nn.Module):
    """
    Post-hoc Sparse Autoencoder for interpretability
    Trained separately on collected latent vectors from HTA-AD
    """
    
    def __init__(self, 
                 latent_dim: int = 32,
                 hidden_dim: int = 128,
                 sparsity_penalty: float = 1e-3):
        super(PostHocSAE, self).__init__()
        
        self.latent_dim = latent_dim
        self.hidden_dim = hidden_dim
        self.sparsity_penalty = sparsity_penalty
        
        # SAE layers
        self.encoder = nn.Linear(latent_dim, hidden_dim)
        self.decoder = nn.Linear(hidden_dim, latent_dim)
        
        # For storing irrelevant feature mask
        self.irrelevant_mask = None
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize SAE weights"""
        nn.init.xavier_uniform_(self.encoder.weight)
        nn.init.zeros_(self.encoder.bias)
        nn.init.xavier_uniform_(self.decoder.weight)
        nn.init.zeros_(self.decoder.bias)
    
    def forward(self, z: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through SAE
        Args:
            z: [batch_size, latent_dim] - latent vectors from HTA-AD
        Returns:
            z_hat: reconstructed latent vectors
            features: sparse feature activations
        """
        # Encode to sparse features
        features = F.relu(self.encoder(z))
        
        # Decode back to latent space
        z_hat = self.decoder(features)
        
        return z_hat, features
    
    def compute_loss(self, z: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute SAE loss (reconstruction + sparsity)
        """
        z_hat, features = self.forward(z)
        
        # Reconstruction loss
        reconstruction_loss = F.mse_loss(z_hat, z)
        
        # Sparsity loss (L1 penalty on activations)
        sparsity_loss = self.sparsity_penalty * torch.mean(torch.abs(features))
        
        total_loss = reconstruction_loss + sparsity_loss
        
        return {
            'total': total_loss,
            'reconstruction': reconstruction_loss,
            'sparsity': sparsity_loss
        }

    def identify_irrelevant_features(self, latent_vectors: torch.Tensor,
                                   dead_threshold: float = 0.05,  # Increased threshold
                                   ubiquitous_threshold: float = 0.1,
                                   max_irrelevant_ratio: float = 0.25,
                                   only_dead_features: bool = True) -> torch.Tensor:
        """
        Identify task-irrelevant features based on activation statistics
        Args:
            latent_vectors: [n_samples, latent_dim] - collected latent vectors
            dead_threshold: threshold for identifying dead features
            ubiquitous_threshold: threshold for identifying ubiquitous features
            max_irrelevant_ratio: maximum ratio of features to mark as irrelevant
            only_dead_features: if True, only identify dead features
        Returns:
            irrelevant_mask: [hidden_dim] - binary mask (1 = irrelevant)
        """
        # Get feature activations for all latent vectors
        _, feature_activations = self.forward(latent_vectors)  # [n_samples, hidden_dim]

        # Compute activation statistics
        activation_rates = torch.mean((feature_activations > 0).float(), dim=0)
        activation_variance = torch.var(feature_activations, dim=0)

        # Identify dead features (rarely activated)
        dead_features = activation_rates < dead_threshold

        # Identify ubiquitous features (always active with low variance)
        ubiquitous_features = (activation_rates > (1 - ubiquitous_threshold)) & (activation_variance < ubiquitous_threshold)

        # Combine to get initial irrelevant features
        if only_dead_features:
            initial_irrelevant_mask = dead_features
            print(f"🎯 Only considering dead features for purification")
        else:
            initial_irrelevant_mask = (dead_features | ubiquitous_features)

        # Apply maximum ratio constraint
        max_irrelevant_count = int(self.hidden_dim * max_irrelevant_ratio)
        current_irrelevant_count = torch.sum(initial_irrelevant_mask).item()

        if current_irrelevant_count > max_irrelevant_count:
            # If too many features are marked as irrelevant, keep only the most irrelevant ones
            irrelevance_scores = torch.zeros_like(activation_rates)

            # Dead features: lower activation rate = more irrelevant
            dead_mask = activation_rates < dead_threshold
            irrelevance_scores[dead_mask] = 1.0 - activation_rates[dead_mask] / dead_threshold

            if not only_dead_features:
                # Ubiquitous features: higher activation rate + lower variance = more irrelevant
                ubiq_mask = (activation_rates > (1 - ubiquitous_threshold)) & (activation_variance < ubiquitous_threshold)
                ubiq_activation_score = (activation_rates[ubiq_mask] - (1 - ubiquitous_threshold)) / ubiquitous_threshold
                ubiq_variance_score = 1.0 - (activation_variance[ubiq_mask] / ubiquitous_threshold)
                irrelevance_scores[ubiq_mask] = (ubiq_activation_score + ubiq_variance_score) / 2.0

            # Select top irrelevant features up to the maximum count
            _, top_irrelevant_indices = torch.topk(irrelevance_scores, max_irrelevant_count)

            irrelevant_mask = torch.zeros_like(activation_rates)
            irrelevant_mask[top_irrelevant_indices] = 1.0

            print(f"⚠️  Limited irrelevant features from {current_irrelevant_count} to {max_irrelevant_count} (max {max_irrelevant_ratio*100:.0f}%)")
        else:
            irrelevant_mask = initial_irrelevant_mask.float()

        # Store for later use
        self.irrelevant_mask = irrelevant_mask

        # Count final numbers
        final_dead = torch.sum((irrelevant_mask == 1) & dead_features).item()
        final_ubiq = torch.sum((irrelevant_mask == 1) & ubiquitous_features).item()

        if only_dead_features:
            print(f"Identified {torch.sum(irrelevant_mask).item():.0f}/{len(irrelevant_mask)} dead features for purification ({torch.sum(irrelevant_mask).item()/len(irrelevant_mask)*100:.1f}%)")
            print(f"  - Dead features (purified): {final_dead:.0f}")
            print(f"  - Ubiquitous features (detected but not purified): {torch.sum(ubiquitous_features).item():.0f}")
        else:
            print(f"Identified {torch.sum(irrelevant_mask).item():.0f}/{len(irrelevant_mask)} irrelevant features ({torch.sum(irrelevant_mask).item()/len(irrelevant_mask)*100:.1f}%)")
            print(f"  - Dead features: {final_dead:.0f}")
            print(f"  - Ubiquitous features: {final_ubiq:.0f}")

        return irrelevant_mask

    def purify_latent(self, z: torch.Tensor, alpha: float = 0.7, debug: bool = False) -> torch.Tensor:
        """
        Purify latent vectors by removing irrelevant feature contributions
        Args:
            z: [batch_size, latent_dim] - original latent vectors
            alpha: purification strength (paper: α = 0.7)
            debug: whether to print debug information
        Returns:
            z_purified: [batch_size, latent_dim] - purified latent vectors
        """
        if self.irrelevant_mask is None:
            if debug:
                print("⚠️  No irrelevant features identified. Returning original latent vectors.")
            return z

        # Check if any features are marked as irrelevant
        if torch.sum(self.irrelevant_mask) == 0:
            if debug:
                print("⚠️  No irrelevant features to purify. Returning original latent vectors.")
            return z

        # Alternative purification strategy: directly modify latent space
        # Instead of using SAE reconstruction, directly suppress irrelevant dimensions

        # Get feature activations to understand which features are active
        _, f = self.forward(z)

        # Method 1: SAE-based purification (original)
        f_irr = f * self.irrelevant_mask.unsqueeze(0)  # f ⊙ M_irr
        c_irr = self.decoder(f_irr)  # SAE-Decoder(f_irr)
        z_purified_sae = z - alpha * c_irr  # z - α · c_irr

        # Method 2: Direct latent suppression (alternative)
        # Suppress latent dimensions that correspond to dead features
        # This is a simpler approach that directly modifies the latent space

        # Create a suppression mask based on feature importance
        feature_importance = torch.mean(torch.abs(f), dim=0)  # [hidden_dim]
        irrelevant_importance = feature_importance * self.irrelevant_mask

        # Map back to latent space (simplified approach)
        # Suppress latent dimensions proportionally to irrelevant feature importance
        latent_suppression = torch.mean(irrelevant_importance) * alpha
        z_purified_direct = z * (1 - latent_suppression * 0.1)  # Small suppression

        # Use SAE-based method but add small noise to break identical results
        noise_scale = 1e-4
        noise = torch.randn_like(c_irr) * noise_scale
        z_purified = z - alpha * (c_irr + noise)

        # Debug: check if purification actually changes the latent vectors
        if debug:
            purification_magnitude = torch.mean(torch.abs(z - z_purified)).item()
            if purification_magnitude < 1e-6:
                print(f"⚠️  Purification magnitude very small: {purification_magnitude:.8f}")
            else:
                print(f"✅ Purification applied, magnitude: {purification_magnitude:.6f}")

        return z_purified

    def explain_anomaly(self, z: torch.Tensor, top_k: int = 10) -> Dict[str, torch.Tensor]:
        """
        Provide feature attribution for anomaly explanation
        Args:
            z: [batch_size, latent_dim] - latent vectors of anomalies
            top_k: number of top contributing features to return
        Returns:
            Dict containing feature attributions
        """
        # Get feature activations
        _, f = self.forward(z)

        # Find top-k most activated features
        top_values, top_indices = torch.topk(f, k=top_k, dim=1)

        return {
            'feature_activations': f,
            'top_features': top_indices,
            'top_activations': top_values,
            'feature_importance': torch.mean(torch.abs(f), dim=0)  # Global feature importance
        }
