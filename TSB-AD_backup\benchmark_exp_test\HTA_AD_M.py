# -*- coding: utf-8 -*-
# HTA-AD-M: Hourglass Temporal Autoencoder for Multivariate Anomaly Detection
# A model that uses Conv2d to capture inter-channel correlations.

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import os, sys
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import warnings
from torch.nn.utils import weight_norm

warnings.filterwarnings('ignore')

# --- TSB-AD Imports ---
try:
    from TSB_AD.models.base import BaseDetector
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from TSB_AD.models.base import BaseDetector

# --- TCN Components (from gru_tcn_ae.py) ---
class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size,
                                           stride=stride, padding='same', dilation=dilation))
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        self.net = nn.Sequential(self.conv1, self.relu1, self.dropout1,
                                 self.conv2, self.relu2, self.dropout2)
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)

class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            layers.append(TemporalBlock(in_channels, out_channels, kernel_size, stride=1, dilation=dilation_size,
                                        padding=0, dropout=dropout))
        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)

# ----------------------------------------------------
# 1. HTA_M_Model Definition (Multivariate)
# ----------------------------------------------------
class HTA_M_Model(nn.Module):
    def __init__(self, input_dim, window_size, latent_dim, tcn_channels, tcn_kernel_size=3, cnn_channels=16, downsample_stride=2):
        super(HTA_M_Model, self).__init__()
        
        self.input_dim = input_dim
        self.window_size = window_size
        
        # --- Encoder ---
        # 1. Conv2D Downsampler to capture inter-variable relationships
        self.encoder_cnn = nn.Sequential(
            nn.Conv2d(1, cnn_channels, kernel_size=(3, 7), padding=(1, 3), stride=(1, downsample_stride)),
            nn.GELU()
        )
        
        # Calculate shape after Conv2D
        with torch.no_grad():
            dummy_input = torch.zeros(1, 1, input_dim, window_size)
            cnn_output_shape = self.encoder_cnn(dummy_input).shape
            self.cnn_out_channels = cnn_output_shape[1]
            self.cnn_out_dim = cnn_output_shape[2]
            self.downsampled_len = cnn_output_shape[3]

        tcn_input_size = self.cnn_out_channels * self.cnn_out_dim

        # 2. Lightweight TCN
        self.encoder_tcn = TemporalConvNet(
            num_inputs=tcn_input_size,
            num_channels=tcn_channels,
            kernel_size=tcn_kernel_size
        )

        # 3. Bottleneck
        self.fc_encode = nn.Linear(tcn_channels[-1] * self.downsampled_len, latent_dim)

        # --- Decoder ---
        # 1. Expand from Bottleneck
        self.decoder_fc = nn.Linear(latent_dim, tcn_channels[-1] * self.downsampled_len)
        
        # 2. Lightweight Inverse TCN
        self.decoder_tcn = TemporalConvNet(
            num_inputs=tcn_channels[-1],
            num_channels=[tcn_input_size] * len(tcn_channels), # Ensure output matches TCN input
            kernel_size=tcn_kernel_size
        )
        
        # 3. Conv2D Upsampler
        self.decoder_cnn = nn.Sequential(
            nn.ConvTranspose2d(self.cnn_out_channels, 1, kernel_size=(3, 7), padding=(1, 3), stride=(1, downsample_stride), output_padding=(0, downsample_stride-1)),
        )
        self.output_activation = nn.Sigmoid()
        self.tcn_output_channels = tcn_channels[-1]

    def forward(self, x):
        # x shape: (batch_size, window_size, input_dim)
        x_reshaped = x.permute(0, 2, 1).unsqueeze(1) # -> (batch, 1, input_dim, window_size)

        # === Encoder ===
        encoded_cnn = self.encoder_cnn(x_reshaped) # -> (batch, cnn_channels, cnn_out_dim, downsampled_len)
        
        # Flatten feature and channel dimensions for TCN
        encoded_cnn_flat = encoded_cnn.permute(0, 2, 1, 3).flatten(start_dim=1, end_dim=2) # -> (batch, cnn_out_dim * cnn_channels, downsampled_len)
        
        encoded_tcn = self.encoder_tcn(encoded_cnn_flat)
        encoded_flat = encoded_tcn.flatten(start_dim=1)
        latent_vec = self.fc_encode(encoded_flat)
        
        # === Decoder ===
        decoded_flat = self.decoder_fc(latent_vec)
        decoded_unflat_tcn = decoded_flat.view(-1, self.tcn_output_channels, self.downsampled_len)
        
        # Pass through decoder TCN
        decoded_tcn = self.decoder_tcn(decoded_unflat_tcn) # -> (batch, tcn_input_size, downsampled_len)
        
        # Reshape for ConvTranspose2d
        decoded_unflat_cnn = decoded_tcn.view(-1, self.cnn_out_dim, self.cnn_out_channels, self.downsampled_len).permute(0, 2, 1, 3) # -> (batch, cnn_channels, cnn_out_dim, downsampled_len)
        
        reconstructed_reshaped = self.decoder_cnn(decoded_unflat_cnn) # -> (batch, 1, input_dim, window_size)
        
        # Adjust size if needed
        if reconstructed_reshaped.shape[3] != self.window_size:
             reconstructed_reshaped = F.interpolate(reconstructed_reshaped, size=(self.input_dim, self.window_size), mode='bilinear', align_corners=False)

        reconstructed = reconstructed_reshaped.squeeze(1).permute(0, 2, 1)
        
        return self.output_activation(reconstructed)


# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class HTA_AD_M(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        # Hyperparameters
        self.window_size = HP.get('window_size', 128)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 32)
        self.patience = HP.get('patience', 5)
        
        self.model = None
        self.ts_scaler = MinMaxScaler()
        self.criterion = nn.MSELoss()

    def fit(self, X_train, X_test=None):
        if self.normalize:
            X_train = self.ts_scaler.fit_transform(X_train)
            if X_test is not None:
                X_test = self.ts_scaler.transform(X_test)

        train_windows = self._create_windows(X_train)
        train_dataset = TensorDataset(torch.from_numpy(train_windows).float())
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)

        input_dim = X_train.shape[1]
        
        self.model = HTA_M_Model(
                input_dim=input_dim,
                window_size=self.window_size,
                latent_dim=self.latent_dim,
            tcn_channels=self.HP.get('tcn_channels', [32, 32]),
            tcn_kernel_size=self.HP.get('tcn_kernel_size', 3),
                cnn_channels=self.HP.get('cnn_channels', 16),
                downsample_stride=self.HP.get('downsample_stride', 2)
            ).to(self.device)
        
        optimizer = optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        
        best_loss = float('inf')
        patience_counter = 0
        best_model_state = None

        for epoch in range(self.epochs):
            self.model.train()
            train_losses = []
            for (batch,) in train_loader:
                batch = batch.to(self.device)
                optimizer.zero_grad()
                reconstructed = self.model(batch)
                loss = self.criterion(reconstructed, batch)
                loss.backward()
                optimizer.step()
                train_losses.append(loss.item())
            
            epoch_train_loss = np.mean(train_losses)

            if X_test is not None:
                self.model.eval()
                val_losses = []
                test_windows = self._create_windows(X_test)
                if test_windows.shape[0] == 0:
                    print("Validation set too small to create windows. Skipping validation.")
                    best_model_state = self.model.state_dict()
                    continue

                test_dataset = TensorDataset(torch.from_numpy(test_windows).float())
                test_loader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False)

                with torch.no_grad():
                    for (batch,) in test_loader:
                        batch = batch.to(self.device)
                        reconstructed = self.model(batch)
                        loss = self.criterion(reconstructed, batch)
                        val_losses.append(loss.item())
                
                epoch_val_loss = np.mean(val_losses)

                if epoch_val_loss < best_loss:
                    best_loss = epoch_val_loss
                    patience_counter = 0
                    best_model_state = self.model.state_dict()
                else:
                    patience_counter += 1
                
                if patience_counter >= self.patience:
                    print(f"Epoch {epoch+1}/{self.epochs}: Early stopping triggered.")
                    break
            else:
                best_model_state = self.model.state_dict()

        if best_model_state:
            self.model.load_state_dict(best_model_state)
            
        return self

    def decision_function(self, X):
        if self.normalize:
            X = self.ts_scaler.transform(X)

        test_windows = self._create_windows(X)
        if test_windows.shape[0] == 0:
            return np.zeros(len(X))
            
        test_dataset = TensorDataset(torch.from_numpy(test_windows).float())
        test_loader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        all_scores = []
        with torch.no_grad():
            for (batch,) in test_loader:
                batch = batch.to(self.device)
                reconstructed = self.model(batch)
                error = torch.mean((reconstructed - batch)**2, dim=list(range(1, batch.dim())))
                all_scores.append(error.cpu().numpy())
        
        scores = np.concatenate(all_scores)
        
        point_scores = np.zeros(len(X))
        indices = np.arange(self.window_size - 1, len(X))
        if len(scores) == len(indices):
            point_scores[indices] = scores
            if scores.size > 0:
                point_scores[:self.window_size - 1] = scores[0]
        else:
            # This case should ideally not be hit with the checks above, but as a fallback:
            if len(point_scores) > 0:
                point_scores[:] = 0
        
        return point_scores

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides) 