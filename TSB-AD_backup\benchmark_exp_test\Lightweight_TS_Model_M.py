# -*- coding: utf-8 -*-
# Lightweight Time Series Anomaly Detector - Multivariate (LTS_AD_M)
# A simple, effective, and lightweight model for time series anomaly detection.
# This is a MULTIVARIATE version adapted from the original Lightweight_TS_Model.

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import os, sys
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import warnings
import math # Add math import

warnings.filterwarnings('ignore')

# --- TSB-AD Imports ---
try:
    from TSB_AD.models.base import BaseDetector
except ImportError:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from TSB_AD.models.base import BaseDetector

# --- Association Discrepancy Loss ---
def get_prior_association(window_size, sigma_scale=1.0):
    """Generates a prior association matrix based on temporal distance."""
    prior = torch.zeros(window_size, window_size)
    for i in range(window_size):
        for j in range(window_size):
            prior[i, j] = abs(i - j)
    
    # Gaussian-like prior
    sigma = window_size * sigma_scale
    prior = 1.0 / (math.sqrt(2 * math.pi) * sigma) * torch.exp(-prior**2 / (2 * sigma**2))
    return prior

def association_discrepancy_loss(attention_weights, prior_association, device):
    """Calculates the KL divergence between series-derived and prior associations."""
    # attention_weights shape: (batch_size, num_heads, seq_len, seq_len)
    # We can average over the heads or take the first one. Let's average.
    series_association = torch.mean(attention_weights, dim=1) # Avg over heads
    
    # Ensure prior is on the same device and broadcastable
    prior_association = prior_association.to(device).unsqueeze(0) # Add batch dim

    # Use log_softmax and kl_div for numerical stability
    log_series = F.log_softmax(series_association, dim=-1)
    log_prior = F.log_softmax(prior_association, dim=-1)
    
    # Calculate KL divergence
    kl_div = F.kl_div(log_series, log_prior, reduction='batchmean', log_target=True)
    return kl_div

# ----------------------------------------------------
# 1. Lightweight Model Definition (Multivariate)
# ----------------------------------------------------
class LTS_Model_M(nn.Module):
    """
    Lightweight Convolutional-Recurrent Autoencoder for MULTIVARIATE Time Series.
    Now with a self-attention mechanism and designed for association discrepancy loss.
    """
    def __init__(self, input_dim, window_size=100, latent_dim=16, rnn_layers=1, n_heads=4):
        super(LTS_Model_M, self).__init__()
        self.input_dim = input_dim
        
        # --- Encoder ---
        self.encoder_cnn = nn.Sequential(
            nn.Conv1d(in_channels=input_dim, out_channels=16, kernel_size=7, padding=3, stride=2),
            nn.GELU(),
            nn.Conv1d(in_channels=16, out_channels=32, kernel_size=5, padding=2, stride=2),
            nn.GELU(),
            nn.Conv1d(in_channels=32, out_channels=64, kernel_size=3, padding=1, stride=2),
            nn.GELU(),
        )
        
        self.encoder_output_size = self._get_conv_output_size(window_size)

        # --- Attention Layer ---
        self.attention = nn.MultiheadAttention(embed_dim=64, num_heads=n_heads, batch_first=True)
        
        self.encoder_gru = nn.GRU(
            input_size=64, 
            hidden_size=latent_dim, 
            num_layers=rnn_layers, 
            batch_first=True
        )

        # --- Decoder ---
        self.decoder_gru = nn.GRU(
            input_size=latent_dim, 
            hidden_size=latent_dim, 
            num_layers=rnn_layers, 
            batch_first=True
        )
        
        self.decoder_proj = nn.Linear(latent_dim, self.encoder_output_size * 64)

        self.decoder_cnn = nn.Sequential(
            nn.ConvTranspose1d(in_channels=64, out_channels=32, kernel_size=3, stride=2, padding=1, output_padding=1),
            nn.GELU(),
            nn.ConvTranspose1d(in_channels=32, out_channels=16, kernel_size=5, stride=2, padding=2, output_padding=1),
            nn.GELU(),
            nn.ConvTranspose1d(in_channels=16, out_channels=input_dim, kernel_size=7, stride=2, padding=3, output_padding=1),
        )
        
        self.window_size = window_size

    def _get_conv_output_size(self, window_size):
        with torch.no_grad():
            x = torch.zeros(1, self.input_dim, window_size)
            x = self.encoder_cnn(x)
            return x.shape[2]

    def forward(self, x):
        # x shape: (batch_size, window_size, input_dim)
        x = x.permute(0, 2, 1) # -> (batch_size, input_dim, window_size)
        
        encoded_conv = self.encoder_cnn(x)
        encoded_conv_flat = encoded_conv.permute(0, 2, 1) # -> (batch, seq_len, features=64)
        
        # Apply self-attention
        # Query, Key, Value are all the same for self-attention
        attn_output, attn_weights = self.attention(encoded_conv_flat, encoded_conv_flat, encoded_conv_flat)
        
        _, latent_vector = self.encoder_gru(attn_output) # Use attention output
        latent_vector = latent_vector.permute(1, 0, 2)
        
        # Decoder
        projected_decoder = self.decoder_proj(latent_vector.squeeze(0))
        projected_decoder = projected_decoder.view(-1, 64, self.encoder_output_size)
        
        reconstructed = self.decoder_cnn(projected_decoder)
        
        # Final permute to match input shape
        reconstructed = reconstructed.permute(0, 2, 1) # -> (batch_size, window_size, input_dim)

        if reconstructed.shape[1] != self.window_size:
            # Resizing needs to be done carefully for multivariate data
            # F.interpolate works on the last dimension
            reconstructed_permuted = reconstructed.permute(0, 2, 1) # to (B, C, L)
            resized = F.interpolate(reconstructed_permuted, size=self.window_size, mode='linear', align_corners=False)
            reconstructed = resized.permute(0, 2, 1) # back to (B, L, C)

        return reconstructed, attn_weights # Return attention weights as well

# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class LTS_AD_M(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        self.window_size = HP.get('window_size', 100)
        self.epochs = HP.get('epochs', 20)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 32)
        self.lambda_ = HP.get('lambda_', 0.5) # Weight for discrepancy loss
        
        print(f"🔄 初始化LTS_AD_M (多变量, 注意力增强) 检测器... (设备: {self.device})")
        
        self.model = None # Initialize lazily in fit()
        self.ts_scaler = StandardScaler()
        self.score_scaler = MinMaxScaler()
        self.criterion_recon = nn.MSELoss() # Renamed for clarity
        
        # Pre-calculate prior association
        self.prior_association = get_prior_association(self.window_size)

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
            
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def fit(self, X, y=None):
        input_dim = X.shape[1]
        
        # Initialize model now that we know the input_dim
        if self.model is None:
            self.model = LTS_Model_M(
                input_dim=input_dim,
                window_size=self.window_size,
                latent_dim=self.latent_dim,
                n_heads=self.HP.get('n_heads', 4) # Add n_heads HP
            ).to(self.device)

        X_original_for_scoring = X
        if self.normalize:
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        if len(windows) == 0:
            self.decision_scores_ = np.zeros(X_original_for_scoring.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        optimizer = optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', factor=0.5, patience=5, verbose=False)

        self.model.train()
        for epoch in range(self.epochs):
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                
                reconstructed, attn_weights = self.model(batch_windows)
                
                recon_loss = self.criterion_recon(reconstructed, batch_windows)
                assoc_loss = association_discrepancy_loss(attn_weights, self.prior_association, self.device)
                
                loss = (1 - self.lambda_) * recon_loss + self.lambda_ * assoc_loss

                if not (torch.isnan(loss) or torch.isinf(loss)):
                    loss.backward()
                    optimizer.step()
            
            scheduler.step(loss)
        
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self

    def decision_function(self, X):
        return self._compute_scores(X, fit_scaler=False)
    
    def _compute_scores(self, X, fit_scaler=False):
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                reconstructed, attn_weights = self.model(batch_windows)
                
                recon_error = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                assoc_error = association_discrepancy_loss(attn_weights, self.prior_association, self.device)
                
                # Combine scores
                combined_score = (1 - self.lambda_) * recon_error + self.lambda_ * assoc_error.expand_as(recon_error)
                
                window_scores.extend(combined_score.cpu().numpy())
        
        window_scores = np.array(window_scores)
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(window_scores):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        first_score_idx = self.window_size - 1
        if n_samples > first_score_idx:
             scores_mapped[:first_score_idx] = scores_mapped[first_score_idx]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

# ----------------------------------------------------
# 3. Visualization Function (for the first feature)
# ----------------------------------------------------
def create_visualizations_m(filename, data, label, output, train_size, save_dir, model_name="LTS_AD_M"):
    """
    Creates a visualization for the first feature of multivariate data.
    """
    os.makedirs(save_dir, exist_ok=True)
    try:
        data_1d = data[:, 0] # Visualize the first feature
        df = pd.DataFrame({
            'value': data_1d.flatten(),
            'score': output,
            'label': label
        })
        
        precision, recall, thresholds = precision_recall_curve(df['label'], df['score'])
        f1_scores = np.divide(2 * recall * precision, recall + precision, out=np.zeros_like(recall), where=(recall + precision) != 0)
        best_f1_idx = np.argmax(f1_scores)
        smart_threshold = thresholds[best_f1_idx]
        df['pred'] = (df['score'] >= smart_threshold).astype(int)

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 8), sharex=True)
        
        ax1.plot(df.index, df['value'], color='cornflowerblue', label='Feature 1 Value')
        true_anomalies = df[df['label'] == 1]
        if not true_anomalies.empty:
            ax1.scatter(true_anomalies.index, true_anomalies['value'], color='red', marker='o', s=50, label='True Anomalies')
        ax1.axvline(x=train_size, color='seagreen', linestyle='--', label='Train/Test Split')
        ax1.set_title(f'{model_name} Detection Results on First Feature: {os.path.basename(filename)}', fontsize=16)
        ax1.set_ylabel('Feature 1 Value')
        ax1.legend()

        ax2.plot(df.index, df['score'], color='darkviolet', label='Anomaly Score')
        ax2.axhline(y=smart_threshold, color='darkorange', linestyle='--', label=f'Threshold={smart_threshold:.4f}')
        ax2.set_ylabel('Anomaly Score')
        ax2.set_xlabel('Time Step')
        ax2.legend()
        
        plt.tight_layout()
        save_path = os.path.join(save_dir, f"{model_name}_{os.path.basename(filename).replace('.csv', '')}_detection_m.png")
        plt.savefig(save_path, dpi=300)
        plt.close(fig)
        print(f"   -> 多变量可视化结果已保存到: {save_path}")

    except Exception as e:
        print(f"❌ 创建多变量可视化失败: {e}")
        import traceback
        traceback.print_exc()

# ----------------------------------------------------
# 4. Main Execution Block for Standalone Test
# ----------------------------------------------------
if __name__ == '__main__':
    print("--- Running Standalone Test for LTS_AD_M with Interleaved SE-Attention ---")
    
    # --- Synthetic Data Generation ---
    n_features = 3
    train_data = np.random.randn(2000, n_features)
    test_data = np.random.randn(1000, n_features)
    test_data[200:300, 1] += 5  # Inject anomaly into the second feature
    
    # Combine data and create labels
    full_data = np.vstack([train_data, test_data])
    labels = np.zeros(len(full_data))
    labels[2000 + 200 : 2000 + 300] = 1
    
    LTS_AD_M_HP = {
        'window_size': 128,
        'epochs': 10, # Fewer epochs for a quick test
        'lr': 1e-3,
        'batch_size': 128,
        'latent_dim': 16,
        'gpu': 0
    }
    
    try:
        train_size = len(train_data)
        
        start_time = time.time()
        clf = LTS_AD_M(HP=LTS_AD_M_HP)
        clf.fit(train_data)
        scores = clf.decision_function(full_data)
        runtime = time.time() - start_time
        print(f"⏱️ 模型运行时间: {runtime:.2f}s")
        
        min_len = min(len(scores), len(labels))
        scores, labels = scores[:min_len], labels[:min_len]
        
        # Check if scores are higher in the anomaly region
        normal_max = np.max(np.concatenate([scores[:2200], scores[2300:]]))
        anomaly_max = np.max(scores[2200:2300])
        print(f"Max score in normal region: {normal_max:.4f}")
        print(f"Max score in anomaly region: {anomaly_max:.4f}")
        
        if anomaly_max > normal_max:
            print("✅ Test PASSED: Anomaly scores are higher than normal scores.")
        else:
            print("❌ Test FAILED: Anomaly scores are NOT distinctly higher.")
            
        create_visualizations_m(
            filename="synthetic_multivariate_test_attn.csv",
            data=full_data,
            label=labels,
            output=scores,
            train_size=train_size,
            save_dir='./lts_ad_results/',
            model_name="LTS_AD_M"
        )
        print("\n🎉 LTS_AD_M 独立测试完成!")

    except Exception as e:
        print(f"\n❌ 独立测试失败")
        import traceback
        traceback.print_exc() 