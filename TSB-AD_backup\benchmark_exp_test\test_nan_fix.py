#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NaN修复验证脚本
"""

import sys
import os
import pandas as pd
import numpy as np
import torch
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 导入我们的检测器
from Run_LERN_Detector import LERN_AD

def test_nan_fix():
    """测试NaN修复是否有效"""
    print("🔧 测试NaN修复效果")
    print("=" * 50)
    
    # 加载一个小数据集进行测试
    file_path = "../Datasets/TSB-AD-U/003_NAB_id_3_WebService_tr_1362_1st_1462.csv"
    
    try:
        df = pd.read_csv(file_path)
        data = df['Data'].values.reshape(-1, 1)
        print(f"📂 数据加载成功: {data.shape}")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False
    
    # 使用小规模数据进行快速测试
    train_size = 500
    test_data = data[:train_size]
    
    print(f"📊 测试数据: {test_data.shape}")
    print(f"   数据范围: [{np.min(test_data):.3f}, {np.max(test_data):.3f}]")
    
    # 创建检测器
    HP = {
        'window_size': 30,  # 使用较小的窗口
        'epochs': 3,        # 只训练3个epoch
        'lr': 1e-3,
        'batch_size': 16    # 使用较小的批大小
    }
    
    try:
        print("\n🚀 创建LERN检测器...")
        detector = LERN_AD(HP=HP, normalize=True)
        
        print("🏋️ 开始训练...")
        detector.fit(test_data)
        print("✅ 训练完成")
        
        print("🔍 开始预测...")
        scores = detector.decision_function(test_data)
        print("✅ 预测完成")
        
        # 检查结果
        has_nan = np.isnan(scores).any()
        has_inf = np.isinf(scores).any()
        
        print(f"\n📊 结果检查:")
        print(f"   分数数组长度: {len(scores)}")
        print(f"   包含NaN: {has_nan}")
        print(f"   包含Inf: {has_inf}")
        
        if not has_nan and not has_inf:
            print(f"   分数范围: [{np.min(scores):.6f}, {np.max(scores):.6f}]")
            print(f"   分数均值: {np.mean(scores):.6f}")
            print(f"   分数标准差: {np.std(scores):.6f}")
            print("\n✅ NaN修复成功！")
            return True
        else:
            print(f"\n❌ 仍然存在NaN/Inf问题")
            if has_nan:
                nan_count = np.sum(np.isnan(scores))
                print(f"   NaN数量: {nan_count}/{len(scores)}")
            return False
            
    except Exception as e:
        print(f"\n💥 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 LERN NaN修复验证")
    print("=" * 60)
    
    success = test_nan_fix()
    
    if success:
        print("\n🎉 修复验证成功！可以开始正式测试了。")
        print("💡 建议使用以下命令运行小规模测试:")
        print("   ./start_gpu1_background.sh small")
    else:
        print("\n⚠️ 修复验证失败，需要进一步调试。")

if __name__ == "__main__":
    main() 