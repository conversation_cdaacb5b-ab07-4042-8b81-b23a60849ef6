#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的TSB-AD基准测试
在所有TSB-AD数据集上运行HTA-AD和HTA-AD+SAE，收集所有指标
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import sys
import glob
from sklearn.preprocessing import MinMaxScaler
import time
import json
from tqdm import tqdm

# 添加TSB-AD路径
sys.path.append('TSB-AD')
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict

# 导入SAE模型
from run_benchmark_with_pretrained_sae import LargeScaleSAE

class MultivariateSAE(nn.Module):
    """多变量SAE模型"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU()
        )
        
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 3, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, input_dim)
        )
        
    def forward(self, x):
        activations = self.encoder(x)
        reconstructed = self.decoder(activations)
        return reconstructed, activations

class HTA_AD_SAE_Enhanced(HTA_AD):
    """增强的HTA-AD + SAE"""
    def __init__(self, HP, pretrained_sae=None, sae_scaler=None, irrelevant_indices=None, normalize=True):
        super().__init__(HP, normalize)
        
        self.pretrained_sae = pretrained_sae
        self.sae_scaler = sae_scaler
        self.irrelevant_indices = irrelevant_indices or []
        self.purification_strength = HP.get('purification_strength', 0.7)
        
    def _ensure_sae_device(self):
        """确保SAE在正确的设备上"""
        if self.pretrained_sae is not None:
            self.pretrained_sae = self.pretrained_sae.to(self.device)
            for param in self.pretrained_sae.parameters():
                param.requires_grad = False
            self.pretrained_sae.eval()
    
    def _purify_latent_vector(self, latent_vec):
        """使用预训练SAE净化潜在向量"""
        if self.pretrained_sae is None or len(self.irrelevant_indices) == 0:
            return latent_vec
        
        with torch.no_grad():
            # 如果有归一化器，先归一化
            if self.sae_scaler is not None:
                latent_vec_cpu = latent_vec.cpu().numpy()
                latent_vec_normalized = self.sae_scaler.transform(latent_vec_cpu)
                latent_vec_input = torch.FloatTensor(latent_vec_normalized).to(latent_vec.device)
            else:
                latent_vec_input = latent_vec
            
            z_recon, activations = self.pretrained_sae(latent_vec_input)
            
            # 如果有归一化器，反归一化重构结果
            if self.sae_scaler is not None:
                z_recon_cpu = z_recon.cpu().numpy()
                z_recon_denormalized = self.sae_scaler.inverse_transform(z_recon_cpu)
                z_recon = torch.FloatTensor(z_recon_denormalized).to(latent_vec.device)
            
            # 计算无关特征的贡献
            irrelevant_mask = torch.zeros_like(activations)
            irrelevant_mask[:, self.irrelevant_indices] = 1.0
            
            irrelevant_activations = activations * irrelevant_mask
            
            # 重构无关特征贡献
            if self.sae_scaler is not None:
                irrelevant_recon = self.pretrained_sae.decoder(irrelevant_activations)
                irrelevant_recon_cpu = irrelevant_recon.cpu().numpy()
                irrelevant_contribution = torch.FloatTensor(
                    self.sae_scaler.inverse_transform(irrelevant_recon_cpu)
                ).to(latent_vec.device)
            else:
                irrelevant_contribution = self.pretrained_sae.decoder(irrelevant_activations)
            
            # 净化
            purified_latent = latent_vec - self.purification_strength * irrelevant_contribution
            
        return purified_latent
    
    def _compute_scores(self, X, fit_scaler=False):
        """重写评分计算，加入SAE净化"""
        # 确保SAE在正确的设备上
        self._ensure_sae_device()
        
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: 
            return np.zeros(n_samples)

        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                # 获取潜在向量
                x_permuted = batch_windows.permute(0, 2, 1)
                encoded_cnn = self.model.encoder_cnn(x_permuted)
                encoded_tcn = self.model.encoder_tcn(encoded_cnn)
                encoded_flat = encoded_tcn.flatten(start_dim=1)
                latent_vec = self.model.fc_encode(encoded_flat)
                
                # SAE净化
                purified_latent = self._purify_latent_vector(latent_vec)
                
                # 解码
                decoded_flat = self.model.decoder_fc(purified_latent)
                decoded_unflat = decoded_flat.view(-1, self.model.tcn_output_channels, self.model.downsampled_len)
                decoded_tcn = self.model.decoder_tcn(decoded_unflat)
                reconstructed_permuted = self.model.decoder_cnn(decoded_tcn)
                
                # 调整尺寸
                if reconstructed_permuted.shape[2] != self.model.window_size:
                    reconstructed_permuted = F.interpolate(
                        reconstructed_permuted, size=self.model.window_size, 
                        mode='linear', align_corners=False
                    )
                
                reconstructed = self.model.output_activation(reconstructed_permuted.permute(0, 2, 1))
                
                # 计算重构误差
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        # 映射到原始序列
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(np.array(window_scores)):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        if n_samples > self.window_size - 1:
            scores_mapped[:self.window_size-1] = scores_mapped[self.window_size-1]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

def get_all_tsb_ad_datasets():
    """获取所有TSB-AD数据集"""
    datasets = []
    
    # 单变量数据集
    univariate_path = 'TSB-AD/Datasets/TSB-AD-U'
    if os.path.exists(univariate_path):
        for file_path in glob.glob(os.path.join(univariate_path, '*.csv')):
            datasets.append({
                'name': os.path.basename(file_path).replace('.csv', ''),
                'path': file_path,
                'type': 'univariate'
            })
    
    # 多变量数据集
    multivariate_path = 'TSB-AD/Datasets/TSB-AD-M'
    if os.path.exists(multivariate_path):
        for file_path in glob.glob(os.path.join(multivariate_path, '*.csv')):
            datasets.append({
                'name': os.path.basename(file_path).replace('.csv', ''),
                'path': file_path,
                'type': 'multivariate'
            })
    
    print(f"📊 发现 {len(datasets)} 个数据集")
    print(f"   单变量: {len([d for d in datasets if d['type'] == 'univariate'])}")
    print(f"   多变量: {len([d for d in datasets if d['type'] == 'multivariate'])}")
    
    return datasets

def load_dataset_safe(dataset_info):
    """安全加载数据集"""
    try:
        df = pd.read_csv(dataset_info['path'])
        
        # 处理不同的标签列名
        if 'label' in df.columns:
            labels = df['label'].values
            data = df.drop('label', axis=1).values
        elif 'Label' in df.columns:
            labels = df['Label'].values
            data = df.drop('Label', axis=1).values
        elif 'anomaly' in df.columns:
            labels = df['anomaly'].values
            data = df.drop('anomaly', axis=1).values
        else:
            # 假设最后一列是标签
            labels = df.iloc[:, -1].values
            data = df.iloc[:, :-1].values
        
        # 对于单变量数据集，只取第一列
        if dataset_info['type'] == 'univariate' and data.shape[1] > 1:
            data = data[:, 0:1]
        
        return data, labels, None
        
    except Exception as e:
        return None, None, str(e)

def load_pretrained_models():
    """加载预训练模型"""
    models = {}
    
    # 单变量SAE
    if os.path.exists('large_scale_pretrained_sae.pth'):
        univariate_sae = LargeScaleSAE(input_dim=32, hidden_dim=128)
        state_dict = torch.load('large_scale_pretrained_sae.pth', map_location='cpu')
        univariate_sae.load_state_dict(state_dict)
        models['univariate_sae'] = univariate_sae
        models['univariate_scaler'] = None
        print("✅ 加载单变量预训练SAE")
    
    # 多变量SAE
    if os.path.exists('multivariate_pretrained_sae.pth'):
        multivariate_sae = MultivariateSAE(input_dim=32, hidden_dim=128)
        state_dict = torch.load('multivariate_pretrained_sae.pth', map_location='cpu')
        multivariate_sae.load_state_dict(state_dict)
        models['multivariate_sae'] = multivariate_sae
        
        # 加载归一化器
        if os.path.exists('multivariate_pretrained_sae_scaler.pkl'):
            import pickle
            with open('multivariate_pretrained_sae_scaler.pkl', 'rb') as f:
                models['multivariate_scaler'] = pickle.load(f)
        else:
            models['multivariate_scaler'] = None
        print("✅ 加载多变量预训练SAE")
    
    return models

def identify_irrelevant_features(sae_model, sample_size=5000):
    """识别无关特征"""
    # 强制在CPU上进行分析，避免设备冲突
    original_device = next(sae_model.parameters()).device
    sae_model = sae_model.cpu()  # 临时移到CPU
    sae_model.eval()
    
    with torch.no_grad():
        # 创建随机输入样本，在CPU上
        random_inputs = torch.randn(sample_size, sae_model.input_dim)
        _, activations = sae_model(random_inputs)
        activations = activations.numpy()
    
    irrelevant_indices = []
    
    for i in range(activations.shape[1]):
        feature_acts = activations[:, i]
        
        activation_rate = np.mean(feature_acts > 0.01)
        activation_std = np.std(feature_acts)
        activation_mean = np.mean(feature_acts)
        
        if (activation_rate < 0.02 or  
            (activation_rate > 0.98 and activation_std < 0.001) or  
            activation_mean < 0.0001):
            irrelevant_indices.append(i)
    
    # 限制无关特征比例
    max_irrelevant = int(activations.shape[1] * 0.3)
    if len(irrelevant_indices) > max_irrelevant:
        feature_scores = []
        for idx in irrelevant_indices:
            score = np.mean(activations[:, idx]) + np.std(activations[:, idx])
            feature_scores.append((idx, score))
        
        feature_scores.sort(key=lambda x: x[1])
        irrelevant_indices = [idx for idx, _ in feature_scores[:max_irrelevant]]
    
    # 恢复模型到原始设备
    sae_model = sae_model.to(original_device)
    
    return irrelevant_indices

def safe_get_metrics(scores, labels, slidingWindow=100):
    """安全的评估函数，返回6个核心指标"""
    try:
        unique_labels = np.unique(labels)
        if len(unique_labels) < 2:
            return {
                'VUS-PR': 0.0,
                'VUS-ROC': 0.0,
                'AUC-PR': 0.0,
                'AUC-ROC': 0.0,
                'Standard-F1': 0.0,
                'R-based-F1': 0.0
            }

        metrics = get_metrics(
            score=scores,
            labels=labels,
            slidingWindow=slidingWindow,
            version='opt'
        )

        # 确保返回6个核心指标
        result = {
            'VUS-PR': metrics.get('VUS-PR', 0.0),
            'VUS-ROC': metrics.get('VUS-ROC', 0.0),
            'AUC-PR': metrics.get('AUC-PR', 0.0),
            'AUC-ROC': metrics.get('AUC-ROC', 0.0),
            'Standard-F1': metrics.get('Standard-F1', 0.0),
            'R-based-F1': metrics.get('R-based-F1', 0.0)
        }

        return result

    except Exception as e:
        print(f"⚠️  评估出错: {e}")
        return {
            'VUS-PR': 0.0,
            'VUS-ROC': 0.0,
            'AUC-PR': 0.0,
            'AUC-ROC': 0.0,
            'Standard-F1': 0.0,
            'R-based-F1': 0.0
        }

def run_full_benchmark():
    """运行完整基准测试"""
    print("🚀 完整TSB-AD基准测试")
    print("=" * 120)
    
    # 获取所有数据集
    datasets = get_all_tsb_ad_datasets()
    
    # 加载预训练模型
    pretrained_models = load_pretrained_models()
    
    # 获取HTA-AD配置
    optimal_hp = Optimal_Uni_algo_HP_dict.get('HTA_AD', {
        'window_size': 128,
        'epochs': 30,
        'lr': 1e-3,
        'batch_size': 64,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2
    })
    
    results = []
    successful_tests = 0
    total_datasets = len(datasets)
    
    # 创建进度条
    pbar = tqdm(datasets, desc="📊 基准测试进度", unit="数据集")
    
    for i, dataset_info in enumerate(pbar, 1):
        # 更新进度条描述
        pbar.set_description(f"📊 测试 [{i}/{total_datasets}] {dataset_info['name'][:30]}")
        
        print(f"\n🔬 [{i}/{total_datasets}] {dataset_info['name']}")
        print(f"📁 类型: {dataset_info['type']}")
        print("-" * 100)
        
        start_time = time.time()
        
        try:
            # 加载数据
            data, labels, error = load_dataset_safe(dataset_info)
            
            if data is None:
                print(f"❌ 数据加载失败: {error}")
                continue
            
            print(f"📊 数据形状: {data.shape}")
            print(f"📊 异常样本: {np.sum(labels)} / {len(labels)} ({np.mean(labels):.1%})")
            
            # 数据质量检查
            if len(data) < 500:
                print(f"⚠️  数据太少，跳过")
                continue
            
            # 分割数据
            split_point = int(len(data) * 0.7)
            train_data = data[:split_point]
            test_data = data[split_point:]
            test_labels = labels[split_point:]
            
            # 检查测试集标签分布
            unique_labels = np.unique(test_labels)
            if len(unique_labels) < 2:
                print(f"⚠️  测试集只有一种标签，跳过")
                continue
            
            # 测试原始HTA-AD
            print("1️⃣ 原始HTA-AD")
            original_model = HTA_AD(optimal_hp)
            original_model.fit(train_data)
            original_scores = original_model.decision_function(test_data)
            
            original_metrics = safe_get_metrics(original_scores, test_labels)

            print(f"   VUS-PR: {original_metrics.get('VUS-PR', 0):.4f}")
            print(f"   VUS-ROC: {original_metrics.get('VUS-ROC', 0):.4f}")
            print(f"   AUC-PR: {original_metrics.get('AUC-PR', 0):.4f}")
            print(f"   AUC-ROC: {original_metrics.get('AUC-ROC', 0):.4f}")
            print(f"   Standard-F1: {original_metrics.get('Standard-F1', 0):.4f}")
            print(f"   R-based-F1: {original_metrics.get('R-based-F1', 0):.4f}")
            
            # 测试HTA-AD + SAE
            print("2️⃣ HTA-AD + SAE")
            
            # 选择对应的SAE
            if dataset_info['type'] == 'univariate' and 'univariate_sae' in pretrained_models:
                sae_model = pretrained_models['univariate_sae']
                sae_scaler = pretrained_models['univariate_scaler']
            elif dataset_info['type'] == 'multivariate' and 'multivariate_sae' in pretrained_models:
                sae_model = pretrained_models['multivariate_sae']
                sae_scaler = pretrained_models['multivariate_scaler']
            else:
                print(f"   ❌ 没有对应的预训练SAE")
                sae_model = None
                sae_scaler = None
            
            if sae_model is not None:
                irrelevant_indices = identify_irrelevant_features(sae_model)
                print(f"   无关特征: {len(irrelevant_indices)}/{sae_model.hidden_dim}")
                
                # 测试不同净化强度
                best_metrics = original_metrics
                best_strength = 0.0
                
                for strength in [0.3, 0.5, 0.7, 1.0]:
                    enhanced_hp = optimal_hp.copy()
                    enhanced_hp['purification_strength'] = strength
                    
                    enhanced_model = HTA_AD_SAE_Enhanced(
                        enhanced_hp, 
                        pretrained_sae=sae_model,
                        sae_scaler=sae_scaler,
                        irrelevant_indices=irrelevant_indices
                    )
                    
                    enhanced_model.fit(train_data)
                    enhanced_scores = enhanced_model.decision_function(test_data)
                    
                    enhanced_metrics = safe_get_metrics(enhanced_scores, test_labels)
                    
                    vus_pr = enhanced_metrics.get('VUS-PR', 0)
                    
                    if vus_pr > best_metrics.get('VUS-PR', 0):
                        best_metrics = enhanced_metrics
                        best_strength = strength
                
                print(f"   最佳强度: {best_strength}")
                print(f"   最佳VUS-PR: {best_metrics.get('VUS-PR', 0):.4f}")
                print(f"   最佳VUS-ROC: {best_metrics.get('VUS-ROC', 0):.4f}")
                print(f"   最佳AUC-PR: {best_metrics.get('AUC-PR', 0):.4f}")
                print(f"   最佳AUC-ROC: {best_metrics.get('AUC-ROC', 0):.4f}")
                print(f"   最佳Standard-F1: {best_metrics.get('Standard-F1', 0):.4f}")
                print(f"   最佳R-based-F1: {best_metrics.get('R-based-F1', 0):.4f}")
            else:
                best_metrics = original_metrics
                best_strength = 0.0
            
            # 计算改进
            original_vus_pr = original_metrics.get('VUS-PR', 0)
            best_vus_pr = best_metrics.get('VUS-PR', 0)
            
            if original_vus_pr > 0:
                improvement = (best_vus_pr - original_vus_pr) / original_vus_pr * 100
            else:
                improvement = 0.0
            
            elapsed_time = time.time() - start_time
            
            # 记录结果
            result = {
                'dataset': dataset_info['name'],
                'type': dataset_info['type'],
                'data_shape': f"{data.shape[0]}x{data.shape[1]}",
                'anomaly_ratio': f"{np.mean(labels):.1%}",
                'test_samples': len(test_data),
                'test_anomalies': np.sum(test_labels),
                
                # 原始HTA-AD指标 (6个核心指标)
                'original_vus_pr': original_metrics.get('VUS-PR', 0),
                'original_vus_roc': original_metrics.get('VUS-ROC', 0),
                'original_auc_pr': original_metrics.get('AUC-PR', 0),
                'original_auc_roc': original_metrics.get('AUC-ROC', 0),
                'original_standard_f1': original_metrics.get('Standard-F1', 0),
                'original_r_based_f1': original_metrics.get('R-based-F1', 0),

                # HTA-AD + SAE指标 (6个核心指标)
                'best_vus_pr': best_metrics.get('VUS-PR', 0),
                'best_vus_roc': best_metrics.get('VUS-ROC', 0),
                'best_auc_pr': best_metrics.get('AUC-PR', 0),
                'best_auc_roc': best_metrics.get('AUC-ROC', 0),
                'best_standard_f1': best_metrics.get('Standard-F1', 0),
                'best_r_based_f1': best_metrics.get('R-based-F1', 0),
                
                # 改进和配置
                'improvement': improvement,
                'best_strength': best_strength,
                'irrelevant_features': len(irrelevant_indices) if sae_model else 0,
                'processing_time': elapsed_time,
                'has_sae': sae_model is not None
            }
            
            results.append(result)
            successful_tests += 1
            
            # 更新进度条后缀信息
            pbar.set_postfix({
                '成功': f"{successful_tests}/{i}",
                '改进': f"{improvement:+.1f}%",
                '耗时': f"{elapsed_time:.1f}s"
            })
            
            print(f"🏆 改进: {improvement:+.1f}% (耗时: {elapsed_time:.1f}s)")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            # 更新进度条显示失败信息
            pbar.set_postfix({
                '成功': f"{successful_tests}/{i}",
                '状态': '失败',
                '错误': str(e)[:20]
            })
            import traceback
            traceback.print_exc()
            continue
    
    # 关闭进度条
    pbar.close()
    
    return results, successful_tests, total_datasets

def save_results(results, successful_tests, total_datasets):
    """保存结果"""
    if not results:
        print("❌ 没有结果可保存")
        return
    
    # 保存详细结果
    df_results = pd.DataFrame(results)
    df_results.to_csv('full_tsb_ad_benchmark_results.csv', index=False)
    
    # 保存JSON格式
    with open('full_tsb_ad_benchmark_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📊 基准测试完成！")
    print("=" * 120)
    print(f"✅ 成功测试: {successful_tests}/{total_datasets} 个数据集")
    print(f"💾 结果已保存:")
    print(f"   CSV: full_tsb_ad_benchmark_results.csv")
    print(f"   JSON: full_tsb_ad_benchmark_results.json")
    
    # 简要统计
    if len(results) > 0:
        avg_improvement = np.mean([r['improvement'] for r in results])
        positive_improvements = [r for r in results if r['improvement'] > 0]
        
        print(f"\n📈 统计摘要:")
        print(f"   平均改进: {avg_improvement:+.2f}%")
        print(f"   有效改进: {len(positive_improvements)}/{len(results)} ({len(positive_improvements)/len(results)*100:.1f}%)")
        
        if positive_improvements:
            avg_positive = np.mean([r['improvement'] for r in positive_improvements])
            max_improvement = max([r['improvement'] for r in results])
            print(f"   有效改进平均: {avg_positive:+.2f}%")
            print(f"   最大改进: {max_improvement:+.2f}%")

def main():
    """主函数"""
    print("🎯 完整TSB-AD基准测试")
    print("=" * 120)
    print("📊 将在所有TSB-AD数据集上测试HTA-AD和HTA-AD+SAE")
    print("📋 收集所有TSB-AD官方指标")
    print("=" * 120)
    
    results, successful_tests, total_datasets = run_full_benchmark()
    save_results(results, successful_tests, total_datasets)

if __name__ == "__main__":
    main()
