#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成HTA-AD与Transformer潜空间对比的t-SNE可视化图
重现论文中Figure 1的效果，展示HTA-AD学习到的结构化轨道与Transformer的碎片化表示的对比
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import warnings
import sys
import os
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.models.AnomalyTransformer import AnomalyTransformer

# 设置matplotlib参数以生成高质量PDF
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'serif',
    'axes.linewidth': 1.2,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'xtick.major.size': 6,
    'ytick.major.size': 6,
    'legend.frameon': False,
    'figure.dpi': 600,
    'savefig.dpi': 600,
    'savefig.format': 'pdf',
    'figure.facecolor': 'white',
    'axes.facecolor': 'white'
})

def generate_periodic_dataset(n_samples=1000, sequence_length=64, noise_level=0.1):
    """生成周期性数据集用于演示"""
    np.random.seed(42)
    torch.manual_seed(42)
    
    data = []
    for i in range(n_samples):
        # 生成基础周期信号
        t = np.linspace(0, 4*np.pi, sequence_length)
        
        # 多种周期模式
        if i % 4 == 0:
            # 正弦波
            signal = np.sin(t + np.random.uniform(0, 2*np.pi))
        elif i % 4 == 1:
            # 余弦波
            signal = np.cos(t + np.random.uniform(0, 2*np.pi))
        elif i % 4 == 2:
            # 方波
            signal = np.sign(np.sin(t + np.random.uniform(0, 2*np.pi)))
        else:
            # 三角波
            signal = 2 * np.arcsin(np.sin(t + np.random.uniform(0, 2*np.pi))) / np.pi
        
        # 添加噪声
        signal += noise_level * np.random.randn(sequence_length)
        data.append(signal)
    
    return np.array(data)

class SimpleHTA_AD_Encoder(nn.Module):
    """简化的HTA-AD编码器用于演示"""
    def __init__(self, input_dim=64, latent_dim=32):
        super().__init__()
        
        # CNN编码器
        self.encoder_cnn = nn.Sequential(
            nn.Conv1d(1, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv1d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(16)
        )
        
        # TCN编码器（简化版）
        self.encoder_tcn = nn.Sequential(
            nn.Conv1d(32, 64, kernel_size=3, padding=1, dilation=1),
            nn.ReLU(),
            nn.Conv1d(64, 64, kernel_size=3, padding=1, dilation=2),
            nn.ReLU(),
        )
        
        # 全连接层
        self.fc_encode = nn.Linear(64 * 16, latent_dim)
        
    def forward(self, x):
        # x: (batch_size, sequence_length, 1)
        x = x.permute(0, 2, 1)  # (batch_size, 1, sequence_length)
        
        # CNN编码
        encoded_cnn = self.encoder_cnn(x)
        
        # TCN编码
        encoded_tcn = self.encoder_tcn(encoded_cnn)
        
        # 展平并全连接
        encoded_flat = encoded_tcn.flatten(start_dim=1)
        latent_vec = self.fc_encode(encoded_flat)
        
        return latent_vec

class SimpleTransformerEncoder(nn.Module):
    """简化的Transformer编码器用于演示"""
    def __init__(self, input_dim=64, d_model=128, nhead=8, num_layers=3, latent_dim=32):
        super().__init__()
        
        self.input_projection = nn.Linear(1, d_model)
        self.pos_encoding = nn.Parameter(torch.randn(input_dim, d_model))
        
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, 
            nhead=nhead, 
            dim_feedforward=256,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 全局平均池化 + 全连接
        self.fc_encode = nn.Linear(d_model, latent_dim)
        
    def forward(self, x):
        # x: (batch_size, sequence_length, 1)
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)  # (batch_size, seq_len, d_model)
        
        # 位置编码
        x = x + self.pos_encoding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # Transformer编码
        encoded = self.transformer(x)  # (batch_size, seq_len, d_model)
        
        # 全局平均池化
        pooled = encoded.mean(dim=1)  # (batch_size, d_model)
        
        # 全连接到潜在空间
        latent_vec = self.fc_encode(pooled)
        
        return latent_vec

def train_model(model, data, epochs=50, lr=1e-3, batch_size=64):
    """训练模型（自监督重构任务）"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 创建数据加载器
    dataset = TensorDataset(torch.FloatTensor(data).unsqueeze(-1))
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=lr)
    criterion = nn.MSELoss()
    
    # 添加解码器用于重构任务
    if isinstance(model, SimpleHTA_AD_Encoder):
        decoder = nn.Sequential(
            nn.Linear(32, 64 * 16),
            nn.ReLU(),
            nn.Linear(64 * 16, data.shape[1])
        ).to(device)
    else:  # Transformer
        decoder = nn.Sequential(
            nn.Linear(32, 128),
            nn.ReLU(),
            nn.Linear(128, data.shape[1])
        ).to(device)
    
    model.train()
    decoder.train()
    
    for epoch in range(epochs):
        total_loss = 0
        for batch_data, in dataloader:
            batch_data = batch_data.to(device)
            
            optimizer.zero_grad()
            
            # 编码
            latent = model(batch_data)
            
            # 解码重构
            reconstructed = decoder(latent)
            
            # 重构损失
            loss = criterion(reconstructed, batch_data.squeeze(-1))
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{epochs}], Loss: {total_loss/len(dataloader):.4f}')
    
    return model, decoder

def extract_latent_representations(model, data, batch_size=64):
    """提取潜在表示"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()
    
    dataset = TensorDataset(torch.FloatTensor(data).unsqueeze(-1))
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
    
    latent_representations = []
    
    with torch.no_grad():
        for batch_data, in dataloader:
            batch_data = batch_data.to(device)
            latent = model(batch_data)
            latent_representations.append(latent.cpu().numpy())
    
    return np.concatenate(latent_representations, axis=0)

def create_latent_space_visualization_with_real_models():
    """使用真实的HTA-AD和AnomalyTransformer模型创建潜空间可视化对比图"""
    print("🔄 生成周期性数据集...")
    data = generate_periodic_dataset(n_samples=1000, sequence_length=100)

    # 分割训练和测试数据
    train_data = data[:800]
    test_data = data[800:]

    print("🏗️ 初始化真实模型...")
    # 使用您的真实HTA-AD模型
    hta_ad_model = HTA_AD(
        window_size=100,
        input_dim=1,
        normalize=True,
        epochs=20,
        batch_size=32
    )

    # 使用AnomalyTransformer模型
    transformer_model = AnomalyTransformer(
        win_size=100,
        input_c=1,
        num_epochs=20,
        batch_size=32
    )

    print("🚀 训练HTA-AD模型...")
    hta_ad_model.fit(train_data.reshape(-1, 1))

    print("🚀 训练AnomalyTransformer模型...")
    transformer_model.fit(train_data.reshape(-1, 1))

    print("🔧 提取潜在表示...")
    # 使用HTA-AD的get_latent_representations方法
    hta_ad_latents = hta_ad_model.get_latent_representations(test_data.reshape(-1, 1))

    # 对于Transformer，我们需要手动提取潜在表示
    transformer_latents = extract_transformer_latents(transformer_model, test_data)

    print("📊 执行t-SNE降维...")
    # 对HTA-AD潜在表示进行t-SNE
    tsne_hta_ad = TSNE(n_components=2, random_state=42, perplexity=30, n_iter=1000)
    hta_ad_2d = tsne_hta_ad.fit_transform(hta_ad_latents)

    # 对Transformer潜在表示进行t-SNE
    tsne_transformer = TSNE(n_components=2, random_state=42, perplexity=30, n_iter=1000)
    transformer_2d = tsne_transformer.fit_transform(transformer_latents)

    print("🎨 生成可视化图表...")
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

    # HTA-AD潜空间 - 结构化的轨道
    # 使用连续的颜色映射来展示轨道结构
    colors_hta_ad = np.arange(len(hta_ad_2d))
    scatter1 = ax1.scatter(hta_ad_2d[:, 0], hta_ad_2d[:, 1],
                          c=colors_hta_ad, cmap='viridis',
                          s=30, alpha=0.8, edgecolors='white', linewidth=0.5)

    ax1.set_title('(a) HTA-AD Latent Space', fontsize=16, fontweight='bold')
    ax1.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax1.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax1.grid(True, alpha=0.3)

    # Transformer潜空间 - 碎片化的表示
    colors_transformer = np.random.RandomState(42).rand(len(transformer_2d))
    scatter2 = ax2.scatter(transformer_2d[:, 0], transformer_2d[:, 1],
                          c=colors_transformer, cmap='Reds',
                          s=30, alpha=0.8, edgecolors='white', linewidth=0.5)

    ax2.set_title('(b) Standard Transformer Latent Space', fontsize=16, fontweight='bold')
    ax2.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax2.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存为高DPI PDF
    output_filename = 'hta_ad_vs_transformer_latent_space_comparison.pdf'
    plt.savefig(output_filename, dpi=600, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print(f"✅ 图表已保存为: {output_filename}")

    # 显示图表
    plt.show()

    return fig

def extract_transformer_latents(transformer_model, data):
    """从AnomalyTransformer模型中提取潜在表示"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    transformer_model.model.eval()

    # 创建窗口数据
    windows = []
    window_size = transformer_model.win_size

    for i in range(len(data) - window_size + 1):
        windows.append(data[i:i + window_size])

    windows = np.array(windows)
    dataset = TensorDataset(torch.FloatTensor(windows).unsqueeze(-1))
    dataloader = DataLoader(dataset, batch_size=32, shuffle=False)

    latent_representations = []

    with torch.no_grad():
        for batch_data, in dataloader:
            batch_data = batch_data.to(device)

            # 通过Transformer编码器获取潜在表示
            enc_out = transformer_model.model.embedding(batch_data)
            enc_out, _, _, _ = transformer_model.model.encoder(enc_out)

            # 使用平均池化获取固定大小的表示
            pooled = enc_out.mean(dim=1)  # (batch_size, d_model)

            latent_representations.append(pooled.cpu().numpy())

    return np.concatenate(latent_representations, axis=0)

def create_enhanced_visualization():
    """创建增强版可视化，更好地展示结构化vs碎片化的对比"""
    print("🔄 生成增强版周期性数据集...")

    # 生成更有结构的周期性数据
    np.random.seed(42)
    torch.manual_seed(42)

    n_samples = 1000
    sequence_length = 64
    data = []
    phase_labels = []

    for i in range(n_samples):
        t = np.linspace(0, 4*np.pi, sequence_length)
        phase = (i / n_samples) * 2 * np.pi  # 连续的相位变化

        # 生成具有连续相位变化的正弦波，形成轨道结构
        signal = np.sin(t + phase) + 0.3 * np.sin(2*t + phase) + 0.1 * np.random.randn(sequence_length)

        data.append(signal)
        phase_labels.append(phase)

    data = np.array(data)
    phase_labels = np.array(phase_labels)

    print("🏗️ 初始化增强模型...")
    # 使用更复杂的模型来展示差异
    hta_ad_model = SimpleHTA_AD_Encoder(input_dim=64, latent_dim=16)  # 更小的潜在空间
    transformer_model = SimpleTransformerEncoder(input_dim=64, latent_dim=16)

    print("🚀 训练增强HTA-AD模型...")
    hta_ad_model, _ = train_model(hta_ad_model, data, epochs=50, lr=1e-3)

    print("🚀 训练增强Transformer模型...")
    transformer_model, _ = train_model(transformer_model, data, epochs=50, lr=1e-3)

    print("🔧 提取增强潜在表示...")
    hta_ad_latents = extract_latent_representations(hta_ad_model, data)
    transformer_latents = extract_latent_representations(transformer_model, data)

    print("📊 执行增强t-SNE降维...")
    # 使用相同的t-SNE参数确保公平比较
    tsne_params = {'n_components': 2, 'random_state': 42, 'perplexity': 50, 'n_iter': 2000}

    tsne_hta_ad = TSNE(**tsne_params)
    hta_ad_2d = tsne_hta_ad.fit_transform(hta_ad_latents)

    tsne_transformer = TSNE(**tsne_params)
    transformer_2d = tsne_transformer.fit_transform(transformer_latents)

    print("🎨 生成增强可视化图表...")
    # 创建更精美的图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

    # HTA-AD潜空间 - 使用相位作为颜色，展示结构化轨道
    scatter1 = ax1.scatter(hta_ad_2d[:, 0], hta_ad_2d[:, 1],
                          c=phase_labels, cmap='viridis',
                          s=25, alpha=0.8, edgecolors='white', linewidth=0.5)

    ax1.set_title('(a) HTA-AD Latent Space', fontsize=16, fontweight='bold', pad=20)
    ax1.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax1.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax1.grid(True, alpha=0.3, linestyle='--')
    ax1.set_aspect('equal')

    # 添加颜色条
    cbar1 = plt.colorbar(scatter1, ax=ax1, shrink=0.8)
    cbar1.set_label('Phase', fontsize=12)

    # Transformer潜空间 - 使用随机颜色展示碎片化
    colors_transformer = np.random.RandomState(42).rand(len(transformer_2d))
    scatter2 = ax2.scatter(transformer_2d[:, 0], transformer_2d[:, 1],
                          c=colors_transformer, cmap='Reds',
                          s=25, alpha=0.8, edgecolors='white', linewidth=0.5)

    ax2.set_title('(b) Standard Transformer Latent Space', fontsize=16, fontweight='bold', pad=20)
    ax2.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax2.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax2.grid(True, alpha=0.3, linestyle='--')
    ax2.set_aspect('equal')

    plt.tight_layout()

    # 保存为高DPI PDF
    output_filename = 'hta_ad_vs_transformer_latent_space_enhanced.pdf'
    plt.savefig(output_filename, dpi=600, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print(f"✅ 增强图表已保存为: {output_filename}")

    # 显示图表
    plt.show()

    return fig

if __name__ == "__main__":
    print("🔄 开始生成HTA-AD vs Transformer潜空间对比图...")

    try:
        # 使用真实模型生成对比图
        create_latent_space_visualization_with_real_models()
        print("🎉 图表生成完成！")
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")
        print("🔄 尝试使用简化版本...")

        # 如果真实模型出错，使用简化版本
        try:
            create_enhanced_visualization()
            print("🎉 简化版图表生成完成！")
        except Exception as e2:
            print(f"❌ 简化版本也出现错误: {e2}")
            print("请检查依赖项和数据路径")
