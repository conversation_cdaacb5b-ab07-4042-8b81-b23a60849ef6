#!/usr/bin/env python3
"""
Sparse Autoencoder Integration for HTA-AD
Provides interpretable feature learning and anomaly attribution
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score
import pandas as pd
import os
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')


class SparseAutoencoder(nn.Module):
    """Sparse Autoencoder for learning interpretable features"""
    
    def __init__(self, input_dim: int = 32, hidden_dim: int = 128, 
                 sparsity_weight: float = 0.01, dropout: float = 0.1):
        super(SparseAutoencoder, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.sparsity_weight = sparsity_weight
        
        # Encoder
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )
        
        # Decoder
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, input_dim)
        )
        
        # Feature activation tracking
        self.feature_activations = []
        self.register_forward_hook(self._activation_hook)
    
    def _activation_hook(self, module, input, output):
        """Hook to capture feature activations"""
        if hasattr(self, '_capture_activations') and self._capture_activations:
            self.feature_activations.append(output.detach().cpu())
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass returning reconstruction and encoded features"""
        encoded = self.encoder(x)
        decoded = self.decoder(encoded)
        return decoded, encoded
    
    def encode(self, x: torch.Tensor) -> torch.Tensor:
        """Encode input to feature space"""
        return self.encoder(x)
    
    def decode(self, z: torch.Tensor) -> torch.Tensor:
        """Decode features to input space"""
        return self.decoder(z)
    
    def sparsity_loss(self, encoded: torch.Tensor, target_sparsity: float = 0.05) -> torch.Tensor:
        """Compute sparsity regularization loss"""
        # KL divergence sparsity loss
        rho_hat = torch.mean(encoded, dim=0)
        rho = target_sparsity
        kl_div = rho * torch.log(rho / (rho_hat + 1e-8)) + \
                 (1 - rho) * torch.log((1 - rho) / (1 - rho_hat + 1e-8))
        return torch.sum(kl_div)


class HTAADWithSAE(nn.Module):
    """HTA-AD model integrated with Sparse Autoencoder"""
    
    def __init__(self, input_dim: int = 1, d_model: int = 32, n_heads: int = 4,
                 n_layers: int = 2, seq_len: int = 100, sae_hidden_dim: int = 128,
                 sae_sparsity_weight: float = 0.01):
        super(HTAADWithSAE, self).__init__()
        
        self.input_dim = input_dim
        self.d_model = d_model
        self.seq_len = seq_len
        
        # Input embedding
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # Positional encoding
        self.pos_encoding = self._create_positional_encoding(seq_len, d_model)
        
        # Hierarchical Temporal Attention layers
        self.attention_layers = nn.ModuleList([
            nn.MultiheadAttention(d_model, n_heads, batch_first=True)
            for _ in range(n_layers)
        ])
        
        # Layer normalization
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(d_model) for _ in range(n_layers)
        ])
        
        # Sparse Autoencoder for interpretability
        self.sae = SparseAutoencoder(
            input_dim=d_model,
            hidden_dim=sae_hidden_dim,
            sparsity_weight=sae_sparsity_weight
        )
        
        # Anomaly detection head
        self.anomaly_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, 1),
            nn.Sigmoid()
        )
        
        # Reconstruction head
        self.reconstruction_head = nn.Linear(d_model, input_dim)
    
    def _create_positional_encoding(self, seq_len: int, d_model: int) -> torch.Tensor:
        """Create positional encoding"""
        pe = torch.zeros(seq_len, d_model)
        position = torch.arange(0, seq_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        return pe.unsqueeze(0)
    
    def forward(self, x: torch.Tensor, return_interpretability: bool = False) -> Dict[str, torch.Tensor]:
        """Forward pass with optional interpretability features"""
        batch_size, seq_len, _ = x.shape

        # Input projection and positional encoding
        x = self.input_projection(x)

        # Handle variable sequence lengths
        if seq_len <= self.pos_encoding.size(1):
            pos_enc = self.pos_encoding[:, :seq_len, :].to(x.device)
        else:
            # Create longer positional encoding if needed
            pos_enc = self._create_positional_encoding(seq_len, self.d_model).to(x.device)

        x = x + pos_enc

        # Hierarchical attention layers
        attention_weights = []
        for i, (attn, norm) in enumerate(zip(self.attention_layers, self.layer_norms)):
            residual = x
            attn_output, attn_weight = attn(x, x, x)
            x = norm(attn_output + residual)
            attention_weights.append(attn_weight)

        # SAE processing for interpretability
        x_flat = x.view(-1, self.d_model)
        sae_reconstruction, sae_features = self.sae(x_flat)
        sae_features = sae_features.view(batch_size, seq_len, -1)

        # Anomaly detection
        anomaly_scores = self.anomaly_head(x).squeeze(-1)

        # Reconstruction
        reconstruction = self.reconstruction_head(x)

        results = {
            'anomaly_scores': anomaly_scores,
            'anomaly_scores_timestep': anomaly_scores,  # Keep timestep-level scores for interpretability
            'reconstruction': reconstruction,
            'sae_features': sae_features,
            'sae_reconstruction': sae_reconstruction.view(batch_size, seq_len, self.d_model),
            'hidden_states': x  # Always include hidden_states for loss computation
        }

        if return_interpretability:
            results.update({
                'attention_weights': attention_weights,
                'feature_importance': self._compute_feature_importance(sae_features)
            })

        return results
    
    def _compute_feature_importance(self, sae_features: torch.Tensor) -> torch.Tensor:
        """Compute feature importance scores"""
        # Use L1 norm as importance measure
        importance = torch.norm(sae_features, p=1, dim=-1)
        return importance / (torch.max(importance, dim=-1, keepdim=True)[0] + 1e-8)
    
    def compute_loss(self, outputs: Dict[str, torch.Tensor], targets: torch.Tensor,
                    anomaly_labels: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Compute comprehensive loss including SAE regularization"""
        losses = {}
        
        # Reconstruction loss
        recon_loss = nn.MSELoss()(outputs['reconstruction'], targets)
        losses['reconstruction'] = recon_loss
        
        # SAE reconstruction loss
        sae_recon_loss = nn.MSELoss()(outputs['sae_reconstruction'], outputs['hidden_states'])
        losses['sae_reconstruction'] = sae_recon_loss
        
        # SAE sparsity loss
        sae_sparsity = self.sae.sparsity_loss(outputs['sae_features'].view(-1, outputs['sae_features'].size(-1)))
        losses['sae_sparsity'] = sae_sparsity * self.sae.sparsity_weight
        
        # Anomaly detection loss (if labels provided)
        if anomaly_labels is not None:
            # Use sequence-level anomaly scores (mean over time)
            seq_anomaly_scores = torch.mean(outputs['anomaly_scores'], dim=1)
            anomaly_loss = nn.BCELoss()(seq_anomaly_scores, anomaly_labels.float())
            losses['anomaly'] = anomaly_loss
        
        # Total loss
        total_loss = sum(losses.values())
        losses['total'] = total_loss
        
        return losses


class SAETrainer:
    """Trainer for SAE-integrated HTA-AD model"""
    
    def __init__(self, model: HTAADWithSAE, device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.training_history = []
    
    def train_epoch(self, dataloader: DataLoader, optimizer: torch.optim.Optimizer) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        epoch_losses = {'total': 0, 'reconstruction': 0, 'sae_reconstruction': 0, 'sae_sparsity': 0, 'anomaly': 0}
        num_batches = 0
        
        for batch_data in dataloader:
            if len(batch_data) == 2:
                x, labels = batch_data
                x, labels = x.to(self.device), labels.to(self.device)
            else:
                x = batch_data[0].to(self.device)
                labels = None
            
            optimizer.zero_grad()
            
            # Forward pass
            outputs = self.model(x)
            
            # Compute losses
            losses = self.model.compute_loss(outputs, x, labels)
            
            # Backward pass
            losses['total'].backward()
            optimizer.step()
            
            # Accumulate losses
            for key, value in losses.items():
                epoch_losses[key] += value.item()
            num_batches += 1
        
        # Average losses
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        return epoch_losses
    
    def evaluate(self, dataloader: DataLoader) -> Dict[str, float]:
        """Evaluate model"""
        self.model.eval()
        eval_losses = {'total': 0, 'reconstruction': 0, 'sae_reconstruction': 0, 'sae_sparsity': 0, 'anomaly': 0}
        num_batches = 0
        
        with torch.no_grad():
            for batch_data in dataloader:
                if len(batch_data) == 2:
                    x, labels = batch_data
                    x, labels = x.to(self.device), labels.to(self.device)
                else:
                    x = batch_data[0].to(self.device)
                    labels = None
                
                outputs = self.model(x)
                losses = self.model.compute_loss(outputs, x, labels)
                
                for key, value in losses.items():
                    eval_losses[key] += value.item()
                num_batches += 1
        
        for key in eval_losses:
            eval_losses[key] /= num_batches
        
        return eval_losses
    
    def fit(self, train_loader: DataLoader, val_loader: Optional[DataLoader] = None,
            epochs: int = 100, lr: float = 1e-3) -> Dict[str, List[float]]:
        """Train the model"""
        optimizer = optim.Adam(self.model.parameters(), lr=lr)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        history = {'train_loss': [], 'val_loss': []}
        
        for epoch in range(epochs):
            # Training
            train_losses = self.train_epoch(train_loader, optimizer)
            history['train_loss'].append(train_losses['total'])
            
            # Validation
            if val_loader is not None:
                val_losses = self.evaluate(val_loader)
                history['val_loss'].append(val_losses['total'])
                scheduler.step(val_losses['total'])
            
            if epoch % 10 == 0:
                print(f"Epoch {epoch}: Train Loss = {train_losses['total']:.4f}")
                if val_loader is not None:
                    print(f"           Val Loss = {val_losses['total']:.4f}")
        
        return history
