# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在本代码库中工作时提供指导。

## 项目概述

TSB-AD 是一个综合性的时间序列异常检测基准测试平台，提供：
- 来自40个不同数据集的1070个高质量时间序列的精选数据集
- 实现了40+种异常检测算法（统计方法、神经网络和基础模型）
- 标准化评估指标，包括VUS-PR（精确率-召回率曲面下体积）
- 单变量(TSB-AD-U)和多变量(TSB-AD-M)时间序列数据集

## 开发命令

### 安装和设置
```bash
# 安装依赖
pip install -r requirements.txt

# 以开发模式安装包
pip install -e .

# 如果pip安装PyTorch失败，使用替代方案
conda install pytorch==2.3.0 torchvision==0.18.0 torchaudio==2.3.0 pytorch-cuda=12.1 -c pytorch -c nvidia
```

### 运行异常检测
```bash
# 使用默认参数的基本用法
python -m TSB_AD.main --AD_Name IForest

# 自定义数据集和算法
python -m TSB_AD.main --AD_Name OCSVM --filename 002_MSL_id_1_Sensor_tr_500_1st_900.csv --data_direc Datasets/TSB-AD-M/

# 运行基准测试实验
python benchmark_exp/Run_Detector_U.py  # 单变量数据集
python benchmark_exp/Run_Detector_M.py  # 多变量数据集

# 超参数调优
python benchmark_exp/HP_Tuning_U.py
python benchmark_exp/HP_Tuning_M.py
```

## 架构概述

### 核心组件

1. **模型包装器系统** (`TSB_AD/model_wrapper.py`)
   - 两个主要池：`Unsupervise_AD_Pool` 和 `Semisupervise_AD_Pool`
   - 动态函数执行：`run_Unsupervise_AD()` 和 `run_Semisupervise_AD()`
   - 每个算法都有对应的 `run_[算法名]()` 函数

2. **算法分类** (`TSB_AD/models/`)
   - **统计方法**：IForest, LOF, OCSVM, PCA, HBOS, KNN 等
   - **神经网络**：AutoEncoder, LSTM, CNN, TranAD, AnomalyTransformer, TimesNet 等
   - **基础模型**：OFA, Chronos, TimesFM, MOMENT, Lag-Llama

3. **基础类** (`TSB_AD/models/base.py`)
   - `BaseDetector`：所有异常检测器的抽象基类
   - 实现标准方法：`fit()`, `decision_function()`, `predict()`
   - 处理污染阈值计算和评分标准化

4. **评估系统** (`TSB_AD/evaluation/`)
   - `metrics.py`：主要评估接口，包含 `get_metrics()` 函数
   - `basic_metrics.py`：核心指标实现
   - 支持阈值无关（AUC-ROC, AUC-PR, VUS）和阈值相关指标（F1变体）

5. **实用工具** (`TSB_AD/utils/`)
   - `dataset.py`：数据集加载和预处理
   - `slidingWindows.py`：基于窗口的特征提取，包含 `find_length_rank()`
   - `torch_utility.py`：神经模型的PyTorch专用工具

### 数据流架构

1. **输入处理**：时间序列数据 → 滑动窗口转换 → 特征提取
2. **模型执行**：算法特定处理 → 异常分数
3. **后处理**：分数标准化（MinMaxScaler）→ 阈值应用
4. **评估**：使用分数和二进制预测进行多指标计算

### 关键设计模式

- **工厂模式**：模型包装器函数动态调用算法实现
- **模板方法**：`BaseDetector` 为所有算法提供标准化接口
- **策略模式**：不同算法实现相同接口但采用不同策略

## 数据集结构

- `Datasets/TSB-AD-U/`：单变量时间序列（357个数据集）
- `Datasets/TSB-AD-M/`：多变量时间序列（713个数据集）
- `Datasets/File_List/`：训练/测试划分和评估列表
- 文件名格式：`{ID}_{来源}_{领域}_tr_{训练长度}_1st_{总长度}.csv`

## 自定义算法开发

添加新异常检测算法的步骤：

1. **创建模型类** 在 `TSB_AD/models/[你的算法].py`
   - 继承自 `BaseDetector` 或实现类似接口
   - 实现 `fit()` 和 `decision_function()` 方法

2. **添加包装器函数** 在 `TSB_AD/model_wrapper.py`
   - 创建 `run_你的算法()` 函数
   - 将算法名称添加到适当的池中（`Unsupervise_AD_Pool` 或 `Semisupervise_AD_Pool`）

3. **配置超参数** 在 `TSB_AD/HP_list.py`
   - 将最优超参数添加到 `Optimal_Uni_algo_HP_dict`

4. **测试实现** 使用 `benchmark_exp/Run_Custom_Detector.py` 作为模板

## 重要说明

- 默认情况下所有时间序列都进行z-score标准化
- 滑动窗口大小由 `find_length_rank()` 基于周期性确定
- 神经网络模型需要GPU支持（CUDA）以获得最佳性能
- 基础模型可能需要额外设置（参见 `TSB_AD/models/README.md`）
- VUS-PR是推荐的时间序列异常检测评估指标
- 框架支持点级和段级异常检测