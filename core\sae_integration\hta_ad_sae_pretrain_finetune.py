#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTA-AD + SAE 预训练+微调方案
1. 在大规模时序数据上预训练SAE
2. 在特定任务上微调
3. 正确使用TSB-AD评估函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
import os
import sys
import glob
from sklearn.preprocessing import MinMaxScaler

# 添加TSB-AD路径
sys.path.append('TSB-AD')
try:
    from TSB_AD.evaluation.metrics import get_metrics
    TSB_AVAILABLE = True
except ImportError:
    print("⚠️  TSB-AD不可用，使用备用评估")
    TSB_AVAILABLE = False
    from sklearn.metrics import roc_auc_score, average_precision_score

from hta_ad_sae_integration import HTA_AD_Original, create_sliding_windows

class PretrainableSAE(nn.Module):
    """可预训练的SAE"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU()
        )
        
        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, input_dim)
        )
        
        # 初始化
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.xavier_normal_(module.weight, gain=0.5)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    def forward(self, x):
        activations = self.encoder(x)
        reconstructed = self.decoder(activations)
        return reconstructed, activations

class HTA_AD_SAE_Pretrained(nn.Module):
    """使用预训练SAE的HTA-AD"""
    def __init__(self, hta_ad_model, pretrained_sae, irrelevant_indices=None):
        super().__init__()
        
        self.hta_ad = hta_ad_model
        self.sae = pretrained_sae
        self.irrelevant_indices = irrelevant_indices or []
        
        # 冻结SAE参数（可选）
        # for param in self.sae.parameters():
        #     param.requires_grad = False
        
    def purify_latent(self, z, strength=1.0):
        """使用预训练SAE净化潜在向量"""
        if len(self.irrelevant_indices) == 0:
            return z
        
        z_recon, activations = self.sae(z)
        
        # 计算无关特征的贡献
        irrelevant_mask = torch.zeros_like(activations)
        irrelevant_mask[:, self.irrelevant_indices] = 1.0
        
        irrelevant_activations = activations * irrelevant_mask
        irrelevant_contribution = self.sae.decoder(irrelevant_activations)
        
        # 净化
        z_purified = z - strength * irrelevant_contribution
        return z_purified
    
    def forward(self, x, use_purification=False, purification_strength=1.0):
        z = self.hta_ad.encode(x)
        
        if use_purification:
            z_purified = self.purify_latent(z, purification_strength)
        else:
            z_purified = z
        
        x_recon = self.hta_ad.decode(z_purified)
        return x_recon, z, z_purified

def load_multiple_datasets_for_pretraining(data_dir, max_datasets=5):
    """加载多个数据集用于SAE预训练"""
    print("📊 加载多个数据集用于SAE预训练...")

    # 查找特定的数据集文件
    target_files = [
        "TSB-AD/Datasets/TSB-AD-U/001_NAB_id_1_Facility_tr_1007_1st_2014.csv",
        "TSB-AD/Datasets/TSB-AD-U/169_Daphnet_id_1_HumanActivity_tr_9693_1st_20732.csv",
        "TSB-AD/Datasets/TSB-AD-U/531_SMAP_id_1_Sensor_tr_1811_1st_4510.csv",
        "TSB-AD/Datasets/TSB-AD-M/002_MSL_id_1_Sensor_tr_500_1st_900.csv",
        "TSB-AD/Datasets/TSB-AD-U/100_UCR_id_1_Anomaly_tr_1000_1st_1500.csv"
    ]

    all_data = []
    total_samples = 0

    for i, file_path in enumerate(target_files[:max_datasets]):
        if not os.path.exists(file_path):
            print(f"    ⚠️  文件不存在: {file_path}")
            continue

        try:
            print(f"  加载 {i+1}/{min(len(target_files), max_datasets)}: {os.path.basename(file_path)}")

            df = pd.read_csv(file_path)

            # 处理标签列
            if 'label' in df.columns:
                data = df.drop('label', axis=1).values
            elif 'anomaly' in df.columns:
                data = df.drop('anomaly', axis=1).values
            else:
                data = df.iloc[:, :-1].values

            # 处理多变量数据
            if data.shape[1] > 1:
                print(f"    多变量数据，使用第一个特征 (维度: {data.shape[1]})")
                data = data[:, 0]
            else:
                data = data.flatten()

            # 归一化
            if len(data) > 500:  # 只处理足够长的序列
                scaler = MinMaxScaler()
                data = scaler.fit_transform(data.reshape(-1, 1)).flatten()
                all_data.append(data)
                total_samples += len(data)
                print(f"    成功加载 {len(data)} 个样本")
            else:
                print(f"    序列太短，跳过 ({len(data)} 样本)")

        except Exception as e:
            print(f"    ⚠️  跳过文件 {file_path}: {e}")
            continue

    if len(all_data) == 0:
        print("❌ 没有成功加载任何数据集")
        return None

    print(f"✅ 成功加载 {len(all_data)} 个数据集，总计 {total_samples:,} 个样本")
    return all_data

def pretrain_sae(sae_model, datasets, window_size=128, epochs=50, batch_size=64):
    """在多个数据集上预训练SAE"""
    print("🚀 开始SAE预训练...")
    
    # 从所有数据集创建训练样本
    all_windows = []
    
    for i, data in enumerate(datasets):
        print(f"  处理数据集 {i+1}/{len(datasets)}: {len(data)} 样本")
        
        if len(data) >= window_size:
            windows = create_sliding_windows(data, window_size, stride=window_size//4)
            all_windows.append(windows)
            print(f"    创建 {len(windows)} 个窗口")
    
    if len(all_windows) == 0:
        print("❌ 没有足够的数据进行预训练")
        return
    
    # 合并所有窗口
    all_windows = np.concatenate(all_windows, axis=0)
    print(f"📊 预训练数据: {len(all_windows)} 个窗口")
    
    # 创建HTA-AD编码器来提取特征
    temp_hta_ad = HTA_AD_Original(input_dim=1, window_size=window_size)
    
    # 提取所有窗口的潜在向量
    print("🔧 提取潜在向量...")
    temp_hta_ad.eval()
    
    latent_vectors = []
    with torch.no_grad():
        for i in range(0, len(all_windows), batch_size):
            batch = torch.FloatTensor(all_windows[i:i+batch_size]).unsqueeze(1)
            z = temp_hta_ad.encode(batch)
            latent_vectors.append(z)
    
    latent_vectors = torch.cat(latent_vectors, dim=0)
    print(f"📊 提取到 {len(latent_vectors)} 个潜在向量，维度: {latent_vectors.shape[1]}")
    
    # 训练SAE
    optimizer = torch.optim.Adam(sae_model.parameters(), lr=0.001, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.7)
    
    dataset = torch.utils.data.TensorDataset(latent_vectors)
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    sae_model.train()
    
    for epoch in range(epochs):
        total_loss = 0
        valid_batches = 0
        
        for batch in dataloader:
            z_batch = batch[0]
            
            optimizer.zero_grad()
            z_recon, activations = sae_model(z_batch)
            
            # 重构损失
            recon_loss = F.mse_loss(z_recon, z_batch)
            
            # 渐进式稀疏性损失
            sparsity_weight = 0.001 * (1 + epoch / epochs)
            sparsity_loss = sparsity_weight * torch.mean(torch.abs(activations))
            
            loss = recon_loss + sparsity_loss
            
            if torch.isfinite(loss):
                loss.backward()
                torch.nn.utils.clip_grad_norm_(sae_model.parameters(), max_norm=1.0)
                optimizer.step()
                total_loss += loss.item()
                valid_batches += 1
        
        if valid_batches > 0:
            avg_loss = total_loss / valid_batches
            scheduler.step(avg_loss)
            
            if (epoch + 1) % 10 == 0:
                print(f"  Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
    
    print("✅ SAE预训练完成")
    
    # 保存预训练模型
    torch.save(sae_model.state_dict(), 'pretrained_sae.pth')
    print("💾 预训练模型已保存为 'pretrained_sae.pth'")

def identify_irrelevant_features_advanced(sae_model, latent_vectors):
    """高级无关特征识别"""
    print("🔍 识别无关特征...")
    
    sae_model.eval()
    with torch.no_grad():
        if isinstance(latent_vectors, np.ndarray):
            latent_vectors = torch.FloatTensor(latent_vectors)
        
        _, activations = sae_model(latent_vectors)
        activations = activations.numpy()
    
    irrelevant_indices = []
    
    for i in range(activations.shape[1]):
        feature_acts = activations[:, i]
        
        # 多重判断标准
        activation_rate = np.mean(feature_acts > 0.01)
        activation_std = np.std(feature_acts)
        activation_mean = np.mean(feature_acts)
        
        # 更严格的标准
        if (activation_rate < 0.02 or  # 极少激活
            (activation_rate > 0.98 and activation_std < 0.001) or  # 总是激活且无变化
            activation_mean < 0.001):  # 平均激活极低
            irrelevant_indices.append(i)
    
    # 限制无关特征比例
    max_irrelevant = int(activations.shape[1] * 0.3)  # 最多30%
    if len(irrelevant_indices) > max_irrelevant:
        # 按激活率排序选择最无关的
        feature_scores = []
        for idx in irrelevant_indices:
            score = np.mean(activations[:, idx]) + np.std(activations[:, idx])
            feature_scores.append((idx, score))
        
        feature_scores.sort(key=lambda x: x[1])
        irrelevant_indices = [idx for idx, _ in feature_scores[:max_irrelevant]]
    
    print(f"📊 识别出 {len(irrelevant_indices)}/{activations.shape[1]} 个无关特征")
    return irrelevant_indices

def evaluate_with_correct_tsb_metrics(model, test_data, test_labels, use_purification=False, strength=1.0):
    """正确使用TSB-AD评估函数"""
    model.eval()
    
    # 计算异常分数
    scores = []
    test_tensor = torch.FloatTensor(test_data).unsqueeze(1)
    
    with torch.no_grad():
        for i in range(len(test_tensor)):
            x = test_tensor[i:i+1]
            
            if hasattr(model, 'forward') and 'use_purification' in model.forward.__code__.co_varnames:
                x_recon, _, _ = model(x, use_purification=use_purification, 
                                    purification_strength=strength)
            else:
                x_recon, _ = model(x)
            
            score = F.mse_loss(x_recon, x, reduction='mean').item()
            scores.append(score)
    
    scores = np.array(scores)
    labels = np.array(test_labels)
    
    # 检查数据有效性
    if len(np.unique(labels)) <= 1:
        print("⚠️  测试集只有一种标签，无法计算指标")
        return {'VUS-PR': 0.0, 'AUC-ROC': 0.0, 'AUC-PR': 0.0}
    
    # 使用TSB-AD标准评估
    if TSB_AVAILABLE:
        try:
            # 正确调用get_metrics函数
            metrics = get_metrics(
                score=scores,
                labels=labels,
                slidingWindow=100,  # 滑动窗口大小
                pred=None,  # 不提供预测标签，使用oracle threshold
                version='opt',  # 使用优化版本
                thre=250  # 阈值数量
            )
            
            return {
                'VUS-PR': metrics.get('VUS-PR', 0.0),
                'VUS-ROC': metrics.get('VUS-ROC', 0.0),
                'AUC-PR': metrics.get('AUC-PR', 0.0),
                'AUC-ROC': metrics.get('AUC-ROC', 0.0)
            }
            
        except Exception as e:
            print(f"⚠️  TSB-AD评估出错: {e}")
    
    # 备用评估
    try:
        auc_roc = roc_auc_score(labels, scores)
        auc_pr = average_precision_score(labels, scores)
    except:
        auc_roc = auc_pr = 0.0
    
    return {
        'VUS-PR': 0.0,  # 无法计算
        'VUS-ROC': 0.0,
        'AUC-PR': auc_pr,
        'AUC-ROC': auc_roc
    }

def run_pretrain_finetune_experiment():
    """运行预训练+微调实验"""
    print("🧪 HTA-AD + SAE 预训练+微调实验")
    print("=" * 80)
    
    # 1. 预训练阶段
    print("\n🔧 阶段1: SAE预训练")
    print("-" * 50)
    
    # 加载多个数据集
    datasets = load_multiple_datasets_for_pretraining("TSB-AD/Datasets", max_datasets=5)
    
    if datasets is None:
        print("❌ 预训练数据加载失败")
        return
    
    # 创建SAE并预训练
    sae_model = PretrainableSAE(input_dim=64, hidden_dim=128)
    pretrain_sae(sae_model, datasets, epochs=30)
    
    # 2. 微调阶段
    print("\n🎯 阶段2: 特定任务微调")
    print("-" * 50)
    
    # 选择目标数据集
    target_dataset = "TSB-AD/Datasets/TSB-AD-U/001_NAB_id_1_Facility_tr_1007_1st_2014.csv"
    
    # 加载目标数据集
    try:
        df = pd.read_csv(target_dataset)
        labels = df['label'].values if 'label' in df.columns else df.iloc[:, -1].values
        data = df.drop('label', axis=1).values if 'label' in df.columns else df.iloc[:, :-1].values
        
        if data.shape[1] > 1:
            data = data[:, 0]
        else:
            data = data.flatten()
        
        scaler = MinMaxScaler()
        data = scaler.fit_transform(data.reshape(-1, 1)).flatten()
        
        print(f"📊 目标数据集: {len(data)}样本, {np.sum(labels)}异常 ({np.mean(labels):.1%})")
        
    except Exception as e:
        print(f"❌ 目标数据集加载失败: {e}")
        return
    
    # 数据分割
    split_point = int(len(data) * 0.7)
    train_data = data[:split_point]
    test_data = data[split_point:]
    test_labels = labels[split_point:]
    
    # 创建窗口
    window_size = 128
    train_windows = create_sliding_windows(train_data, window_size, stride=64)
    test_windows = create_sliding_windows(test_data, window_size, stride=64)
    test_window_labels = create_sliding_windows(test_labels, window_size, stride=64)
    test_window_labels = (np.sum(test_window_labels, axis=1) > 0).astype(int)
    
    print(f"📊 窗口数据: 训练{len(train_windows)}, 测试{len(test_windows)} (异常{np.mean(test_window_labels):.1%})")
    
    # 训练HTA-AD
    hta_ad_model = HTA_AD_Original(input_dim=1, window_size=window_size)
    
    print("🚀 训练HTA-AD...")
    train_tensor = torch.FloatTensor(train_windows).unsqueeze(1)
    optimizer = torch.optim.Adam(hta_ad_model.parameters(), lr=0.001)
    
    hta_ad_model.train()
    for epoch in range(25):
        total_loss = 0
        for i in range(0, len(train_tensor), 32):
            batch = train_tensor[i:i+32]
            optimizer.zero_grad()
            x_recon, _ = hta_ad_model(batch)
            loss = F.mse_loss(x_recon, batch)
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        
        if (epoch + 1) % 10 == 0:
            print(f"  Epoch {epoch+1}, Loss: {total_loss/(len(train_tensor)//32 + 1):.6f}")
    
    # 微调SAE
    print("🔧 微调预训练SAE...")
    
    # 提取目标数据的潜在向量
    hta_ad_model.eval()
    with torch.no_grad():
        latent_vectors = []
        for i in range(0, len(train_tensor), 32):
            batch = train_tensor[i:i+32]
            z = hta_ad_model.encode(batch)
            latent_vectors.append(z)
        latent_vectors = torch.cat(latent_vectors, dim=0)
    
    # 微调SAE（少量epoch）
    optimizer_sae = torch.optim.Adam(sae_model.parameters(), lr=0.0001)  # 更小的学习率
    
    sae_model.train()
    for epoch in range(10):  # 少量epoch
        optimizer_sae.zero_grad()
        z_recon, activations = sae_model(latent_vectors)
        
        recon_loss = F.mse_loss(z_recon, latent_vectors)
        sparsity_loss = 0.001 * torch.mean(torch.abs(activations))
        
        loss = recon_loss + sparsity_loss
        loss.backward()
        optimizer_sae.step()
        
        if (epoch + 1) % 5 == 0:
            print(f"  微调 Epoch {epoch+1}, Loss: {loss.item():.6f}")
    
    # 识别无关特征
    irrelevant_indices = identify_irrelevant_features_advanced(sae_model, latent_vectors)
    
    # 3. 评估阶段
    print("\n📊 阶段3: 性能评估")
    print("-" * 50)
    
    # 创建集成模型
    integrated_model = HTA_AD_SAE_Pretrained(hta_ad_model, sae_model, irrelevant_indices)
    
    # 评估原始模型
    original_metrics = evaluate_with_correct_tsb_metrics(hta_ad_model, test_windows, test_window_labels)
    print(f"原始HTA-AD: VUS-PR={original_metrics['VUS-PR']:.4f}, AUC-ROC={original_metrics['AUC-ROC']:.4f}")
    
    # 评估不同净化强度
    best_metrics = original_metrics
    best_strength = 0.0
    
    for strength in [0.3, 0.5, 0.7, 1.0]:
        metrics = evaluate_with_correct_tsb_metrics(
            integrated_model, test_windows, test_window_labels,
            use_purification=True, strength=strength
        )
        
        print(f"净化强度{strength}: VUS-PR={metrics['VUS-PR']:.4f}, AUC-ROC={metrics['AUC-ROC']:.4f}")
        
        if metrics['VUS-PR'] > best_metrics['VUS-PR']:
            best_metrics = metrics
            best_strength = strength
    
    # 计算改进
    if original_metrics['VUS-PR'] > 0:
        vus_improvement = (best_metrics['VUS-PR'] - original_metrics['VUS-PR']) / original_metrics['VUS-PR'] * 100
    else:
        vus_improvement = 0.0
    
    print(f"\n🏆 最佳结果:")
    print(f"   净化强度: {best_strength}")
    print(f"   VUS-PR: {best_metrics['VUS-PR']:.4f} ({vus_improvement:+.1f}%)")
    print(f"   AUC-ROC: {best_metrics['AUC-ROC']:.4f}")
    
    return {
        'original': original_metrics,
        'best': best_metrics,
        'improvement': vus_improvement,
        'irrelevant_count': len(irrelevant_indices)
    }

if __name__ == "__main__":
    print("🚀 HTA-AD + SAE 预训练+微调实验")
    print("=" * 90)
    
    torch.manual_seed(42)
    np.random.seed(42)
    
    if not os.path.exists("TSB-AD/Datasets"):
        print("❌ TSB-AD数据集路径不存在")
        exit(1)
    
    results = run_pretrain_finetune_experiment()
    
    if results:
        print("\n🎉 预训练+微调实验完成！")
        print(f"💡 VUS-PR改进: {results['improvement']:+.1f}%")
        print("🔬 预训练策略为SAE提供了更好的初始化")
