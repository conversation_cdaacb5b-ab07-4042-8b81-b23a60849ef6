import pandas as pd
import plotly.graph_objects as go
import plotly.io as pio
import os

# --- Data Preparation ---

# HTA-AD performance data
hta_ad_u_data = {'Method': 'HTA_AD', 'AUC-PR': 0.4129, 'AUC-ROC': 0.8298, 'VUS-PR': 0.4407, 'VUS-ROC': 0.8453, 'Standard-F1': 0.4405, 'PA-F1': 0.5133, 'Event-based-F1': 0.4724, 'R-based-F1': 0.4617, 'Affiliation-F1': 0.8713}
hta_ad_m_data = {'Method': 'HTA_AD', 'AUC-PR': 0.4351, 'AUC-ROC': 0.7719, 'VUS-PR': 0.3871, 'VUS-ROC': 0.7381, 'Standard-F1': 0.4765, 'PA-F1': 0.5530, 'Event-based-F1': 0.4971, 'R-based-F1': 0.5049, 'Affiliation-F1': 0.8402}

# Baseline data provided by the user
tsb_ad_u_dicts = [
    {'Method': 'Sub-PCA', 'AUC-PR': 0.37, 'AUC-ROC': 0.71, 'VUS-PR': 0.42, 'VUS-ROC': 0.76, 'Standard-F1': 0.42, 'PA-F1': 0.56, 'Event-based-F1': 0.49, 'R-based-F1': 0.41, 'Affiliation-F1': 0.85},
    {'Method': 'KShapeAD', 'AUC-PR': 0.35, 'AUC-ROC': 0.74, 'VUS-PR': 0.4, 'VUS-ROC': 0.76, 'Standard-F1': 0.39, 'PA-F1': 0.58, 'Event-based-F1': 0.46, 'R-based-F1': 0.4, 'Affiliation-F1': 0.83},
    {'Method': 'POLY', 'AUC-PR': 0.31, 'AUC-ROC': 0.73, 'VUS-PR': 0.39, 'VUS-ROC': 0.76, 'Standard-F1': 0.37, 'PA-F1': 0.53, 'Event-based-F1': 0.45, 'R-based-F1': 0.35, 'Affiliation-F1': 0.85},
    {'Method': 'Series2Graph', 'AUC-PR': 0.33, 'AUC-ROC': 0.76, 'VUS-PR': 0.39, 'VUS-ROC': 0.8, 'Standard-F1': 0.38, 'PA-F1': 0.65, 'Event-based-F1': 0.5, 'R-based-F1': 0.35, 'Affiliation-F1': 0.85},
    {'Method': 'MOMENT(FT)', 'AUC-PR': 0.3, 'AUC-ROC': 0.69, 'VUS-PR': 0.39, 'VUS-ROC': 0.76, 'Standard-F1': 0.35, 'PA-F1': 0.65, 'Event-based-F1': 0.49, 'R-based-F1': 0.35, 'Affiliation-F1': 0.86},
    {'Method': 'MOMENT(ZS)', 'AUC-PR': 0.3, 'AUC-ROC': 0.68, 'VUS-PR': 0.38, 'VUS-ROC': 0.75, 'Standard-F1': 0.35, 'PA-F1': 0.61, 'Event-based-F1': 0.49, 'R-based-F1': 0.36, 'Affiliation-F1': 0.86},
    {'Method': 'KMeansAD', 'AUC-PR': 0.32, 'AUC-ROC': 0.74, 'VUS-PR': 0.37, 'VUS-ROC': 0.76, 'Standard-F1': 0.37, 'PA-F1': 0.56, 'Event-based-F1': 0.44, 'R-based-F1': 0.38, 'Affiliation-F1': 0.82},
    {'Method': 'USAD', 'AUC-PR': 0.32, 'AUC-ROC': 0.66, 'VUS-PR': 0.36, 'VUS-ROC': 0.71, 'Standard-F1': 0.37, 'PA-F1': 0.5, 'Event-based-F1': 0.43, 'R-based-F1': 0.4, 'Affiliation-F1': 0.84},
    {'Method': 'Sub-KNN', 'AUC-PR': 0.27, 'AUC-ROC': 0.76, 'VUS-PR': 0.35, 'VUS-ROC': 0.79, 'Standard-F1': 0.34, 'PA-F1': 0.61, 'Event-based-F1': 0.43, 'R-based-F1': 0.32, 'Affiliation-F1': 0.84},
    {'Method': 'MatrixProfile', 'AUC-PR': 0.26, 'AUC-ROC': 0.73, 'VUS-PR': 0.35, 'VUS-ROC': 0.76, 'Standard-F1': 0.33, 'PA-F1': 0.63, 'Event-based-F1': 0.44, 'R-based-F1': 0.32, 'Affiliation-F1': 0.84},
    {'Method': 'SAND', 'AUC-PR': 0.29, 'AUC-ROC': 0.73, 'VUS-PR': 0.34, 'VUS-ROC': 0.76, 'Standard-F1': 0.44, 'PA-F1': 0.56, 'Event-based-F1': 0.52, 'R-based-F1': 0.46, 'Affiliation-F1': 0.87},
    {'Method': 'CNN', 'AUC-PR': 0.33, 'AUC-ROC': 0.71, 'VUS-PR': 0.34, 'VUS-ROC': 0.79, 'Standard-F1': 0.38, 'PA-F1': 0.78, 'Event-based-F1': 0.66, 'R-based-F1': 0.35, 'Affiliation-F1': 0.88},
    {'Method': 'LSTMED', 'AUC-PR': 0.31, 'AUC-ROC': 0.68, 'VUS-PR': 0.33, 'VUS-ROC': 0.76, 'Standard-F1': 0.37, 'PA-F1': 0.71, 'Event-based-F1': 0.59, 'R-based-F1': 0.34, 'Affiliation-F1': 0.86},
    {'Method': 'SR', 'AUC-PR': 0.32, 'AUC-ROC': 0.74, 'VUS-PR': 0.32, 'VUS-ROC': 0.81, 'Standard-F1': 0.38, 'PA-F1': 0.87, 'Event-based-F1': 0.67, 'R-based-F1': 0.35, 'Affiliation-F1': 0.89},
    {'Method': 'TimesFM', 'AUC-PR': 0.28, 'AUC-ROC': 0.67, 'VUS-PR': 0.3, 'VUS-ROC': 0.74, 'Standard-F1': 0.34, 'PA-F1': 0.84, 'Event-based-F1': 0.63, 'R-based-F1': 0.34, 'Affiliation-F1': 0.89},
    {'Method': 'IForest', 'AUC-PR': 0.29, 'AUC-ROC': 0.71, 'VUS-PR': 0.3, 'VUS-ROC': 0.78, 'Standard-F1': 0.35, 'PA-F1': 0.73, 'Event-based-F1': 0.56, 'R-based-F1': 0.3, 'Affiliation-F1': 0.84},
    {'Method': 'OmniAnomaly', 'AUC-PR': 0.27, 'AUC-ROC': 0.65, 'VUS-PR': 0.29, 'VUS-ROC': 0.72, 'Standard-F1': 0.31, 'PA-F1': 0.59, 'Event-based-F1': 0.46, 'R-based-F1': 0.29, 'Affiliation-F1': 0.83},
    {'Method': 'Lag-Llama', 'AUC-PR': 0.25, 'AUC-ROC': 0.65, 'VUS-PR': 0.27, 'VUS-ROC': 0.72, 'Standard-F1': 0.3, 'PA-F1': 0.77, 'Event-based-F1': 0.59, 'R-based-F1': 0.31, 'Affiliation-F1': 0.88},
    {'Method': 'Chronos', 'AUC-PR': 0.26, 'AUC-ROC': 0.66, 'VUS-PR': 0.27, 'VUS-ROC': 0.73, 'Standard-F1': 0.32, 'PA-F1': 0.83, 'Event-based-F1': 0.61, 'R-based-F1': 0.33, 'Affiliation-F1': 0.88},
    {'Method': 'TimesNet', 'AUC-PR': 0.18, 'AUC-ROC': 0.61, 'VUS-PR': 0.26, 'VUS-ROC': 0.72, 'Standard-F1': 0.24, 'PA-F1': 0.67, 'Event-based-F1': 0.47, 'R-based-F1': 0.21, 'Affiliation-F1': 0.86},
    {'Method': 'AutoEncoder', 'AUC-PR': 0.19, 'AUC-ROC': 0.63, 'VUS-PR': 0.26, 'VUS-ROC': 0.69, 'Standard-F1': 0.25, 'PA-F1': 0.54, 'Event-based-F1': 0.36, 'R-based-F1': 0.28, 'Affiliation-F1': 0.82},
    {'Method': 'TranAD', 'AUC-PR': 0.2, 'AUC-ROC': 0.57, 'VUS-PR': 0.26, 'VUS-ROC': 0.68, 'Standard-F1': 0.25, 'PA-F1': 0.58, 'Event-based-F1': 0.43, 'R-based-F1': 0.25, 'Affiliation-F1': 0.83},
    {'Method': 'FITS', 'AUC-PR': 0.17, 'AUC-ROC': 0.61, 'VUS-PR': 0.26, 'VUS-ROC': 0.73, 'Standard-F1': 0.23, 'PA-F1': 0.65, 'Event-based-F1': 0.42, 'R-based-F1': 0.2, 'Affiliation-F1': 0.86},
    {'Method': 'Sub-LOF', 'AUC-PR': 0.16, 'AUC-ROC': 0.68, 'VUS-PR': 0.25, 'VUS-ROC': 0.73, 'Standard-F1': 0.24, 'PA-F1': 0.57, 'Event-based-F1': 0.35, 'R-based-F1': 0.25, 'Affiliation-F1': 0.82},
    {'Method': 'EFA', 'AUC-PR': 0.16, 'AUC-ROC': 0.59, 'VUS-PR': 0.24, 'VUS-ROC': 0.71, 'Standard-F1': 0.22, 'PA-F1': 0.67, 'Event-based-F1': 0.45, 'R-based-F1': 0.2, 'Affiliation-F1': 0.86},
    {'Method': 'Sub-MCD', 'AUC-PR': 0.15, 'AUC-ROC': 0.67, 'VUS-PR': 0.24, 'VUS-ROC': 0.72, 'Standard-F1': 0.23, 'PA-F1': 0.54, 'Event-based-F1': 0.32, 'R-based-F1': 0.24, 'Affiliation-F1': 0.81},
    {'Method': 'Sub-HBOS', 'AUC-PR': 0.18, 'AUC-ROC': 0.61, 'VUS-PR': 0.23, 'VUS-ROC': 0.67, 'Standard-F1': 0.23, 'PA-F1': 0.6, 'Event-based-F1': 0.35, 'R-based-F1': 0.27, 'Affiliation-F1': 0.79},
    {'Method': 'Sub-OCSVM', 'AUC-PR': 0.16, 'AUC-ROC': 0.65, 'VUS-PR': 0.23, 'VUS-ROC': 0.73, 'Standard-F1': 0.22, 'PA-F1': 0.55, 'Event-based-F1': 0.32, 'R-based-F1': 0.23, 'Affiliation-F1': 0.79},
    {'Method': 'Sub-IForest', 'AUC-PR': 0.16, 'AUC-ROC': 0.63, 'VUS-PR': 0.22, 'VUS-ROC': 0.72, 'Standard-F1': 0.22, 'PA-F1': 0.63, 'Event-based-F1': 0.34, 'R-based-F1': 0.23, 'Affiliation-F1': 0.8},
    {'Method': 'Donut', 'AUC-PR': 0.14, 'AUC-ROC': 0.56, 'VUS-PR': 0.2, 'VUS-ROC': 0.68, 'Standard-F1': 0.2, 'PA-F1': 0.57, 'Event-based-F1': 0.38, 'R-based-F1': 0.2, 'Affiliation-F1': 0.82},
    {'Method': 'LOF', 'AUC-PR': 0.14, 'AUC-ROC': 0.58, 'VUS-PR': 0.17, 'VUS-ROC': 0.68, 'Standard-F1': 0.21, 'PA-F1': 0.62, 'Event-based-F1': 0.41, 'R-based-F1': 0.22, 'Affiliation-F1': 0.79},
    {'Method': 'AnomalyTransformer', 'AUC-PR': 0.08, 'AUC-ROC': 0.5, 'VUS-PR': 0.12, 'VUS-ROC': 0.56, 'Standard-F1': 0.12, 'PA-F1': 0.53, 'Event-based-F1': 0.34, 'R-based-F1': 0.14, 'Affiliation-F1': 0.77}
]
tsb_ad_m_dicts = [
    {'Method': 'CNN', 'AUC-PR': 0.32, 'AUC-ROC': 0.73, 'VUS-PR': 0.31, 'VUS-ROC': 0.76, 'Standard-F1': 0.37, 'PA-F1': 0.78, 'Event-based-F1': 0.65, 'R-based-F1': 0.37, 'Affiliation-F1': 0.87},
    {'Method': 'OmniAnomaly', 'AUC-PR': 0.27, 'AUC-ROC': 0.65, 'VUS-PR': 0.31, 'VUS-ROC': 0.69, 'Standard-F1': 0.32, 'PA-F1': 0.55, 'Event-based-F1': 0.41, 'R-based-F1': 0.37, 'Affiliation-F1': 0.81},
    {'Method': 'PCA', 'AUC-PR': 0.31, 'AUC-ROC': 0.7, 'VUS-PR': 0.31, 'VUS-ROC': 0.74, 'Standard-F1': 0.37, 'PA-F1': 0.79, 'Event-based-F1': 0.59, 'R-based-F1': 0.29, 'Affiliation-F1': 0.85},
    {'Method': 'LSTMED', 'AUC-PR': 0.31, 'AUC-ROC': 0.7, 'VUS-PR': 0.31, 'VUS-ROC': 0.74, 'Standard-F1': 0.36, 'PA-F1': 0.79, 'Event-based-F1': 0.64, 'R-based-F1': 0.38, 'Affiliation-F1': 0.87},
    {'Method': 'USAD', 'AUC-PR': 0.26, 'AUC-ROC': 0.64, 'VUS-PR': 0.3, 'VUS-ROC': 0.68, 'Standard-F1': 0.31, 'PA-F1': 0.53, 'Event-based-F1': 0.4, 'R-based-F1': 0.37, 'Affiliation-F1': 0.8},
    {'Method': 'AutoEncoder', 'AUC-PR': 0.3, 'AUC-ROC': 0.67, 'VUS-PR': 0.3, 'VUS-ROC': 0.69, 'Standard-F1': 0.34, 'PA-F1': 0.6, 'Event-based-F1': 0.44, 'R-based-F1': 0.28, 'Affiliation-F1': 0.8},
    {'Method': 'KMeansAD', 'AUC-PR': 0.25, 'AUC-ROC': 0.69, 'VUS-PR': 0.29, 'VUS-ROC': 0.73, 'Standard-F1': 0.31, 'PA-F1': 0.68, 'Event-based-F1': 0.49, 'R-based-F1': 0.33, 'Affiliation-F1': 0.82},
    {'Method': 'CBLOF', 'AUC-PR': 0.28, 'AUC-ROC': 0.67, 'VUS-PR': 0.27, 'VUS-ROC': 0.7, 'Standard-F1': 0.32, 'PA-F1': 0.65, 'Event-based-F1': 0.45, 'R-based-F1': 0.31, 'Affiliation-F1': 0.81},
    {'Method': 'MCD', 'AUC-PR': 0.27, 'AUC-ROC': 0.65, 'VUS-PR': 0.27, 'VUS-ROC': 0.69, 'Standard-F1': 0.33, 'PA-F1': 0.46, 'Event-based-F1': 0.33, 'R-based-F1': 0.2, 'Affiliation-F1': 0.76},
    {'Method': 'OCSVM', 'AUC-PR': 0.23, 'AUC-ROC': 0.61, 'VUS-PR': 0.26, 'VUS-ROC': 0.67, 'Standard-F1': 0.28, 'PA-F1': 0.48, 'Event-based-F1': 0.41, 'R-based-F1': 0.3, 'Affiliation-F1': 0.8},
    {'Method': 'Donut', 'AUC-PR': 0.2, 'AUC-ROC': 0.64, 'VUS-PR': 0.26, 'VUS-ROC': 0.71, 'Standard-F1': 0.28, 'PA-F1': 0.52, 'Event-based-F1': 0.36, 'R-based-F1': 0.21, 'Affiliation-F1': 0.81},
    {'Method': 'RobustPCA', 'AUC-PR': 0.24, 'AUC-ROC': 0.58, 'VUS-PR': 0.24, 'VUS-ROC': 0.61, 'Standard-F1': 0.29, 'PA-F1': 0.6, 'Event-based-F1': 0.42, 'R-based-F1': 0.33, 'Affiliation-F1': 0.81},
    {'Method': 'FITS', 'AUC-PR': 0.15, 'AUC-ROC': 0.58, 'VUS-PR': 0.21, 'VUS-ROC': 0.66, 'Standard-F1': 0.22, 'PA-F1': 0.72, 'Event-based-F1': 0.32, 'R-based-F1': 0.16, 'Affiliation-F1': 0.81},
    {'Method': 'EFA', 'AUC-PR': 0.15, 'AUC-ROC': 0.55, 'VUS-PR': 0.21, 'VUS-ROC': 0.63, 'Standard-F1': 0.21, 'PA-F1': 0.72, 'Event-based-F1': 0.41, 'R-based-F1': 0.17, 'Affiliation-F1': 0.83},
    {'Method': 'OIF', 'AUC-PR': 0.19, 'AUC-ROC': 0.67, 'VUS-PR': 0.21, 'VUS-ROC': 0.71, 'Standard-F1': 0.26, 'PA-F1': 0.74, 'Event-based-F1': 0.44, 'R-based-F1': 0.26, 'Affiliation-F1': 0.81},
    {'Method': 'COPOD', 'AUC-PR': 0.2, 'AUC-ROC': 0.65, 'VUS-PR': 0.2, 'VUS-ROC': 0.69, 'Standard-F1': 0.27, 'PA-F1': 0.72, 'Event-based-F1': 0.41, 'R-based-F1': 0.24, 'Affiliation-F1': 0.8},
    {'Method': 'IForest', 'AUC-PR': 0.19, 'AUC-ROC': 0.66, 'VUS-PR': 0.2, 'VUS-ROC': 0.69, 'Standard-F1': 0.26, 'PA-F1': 0.68, 'Event-based-F1': 0.41, 'R-based-F1': 0.24, 'Affiliation-F1': 0.8},
    {'Method': 'HBOS', 'AUC-PR': 0.16, 'AUC-ROC': 0.63, 'VUS-PR': 0.19, 'VUS-ROC': 0.67, 'Standard-F1': 0.24, 'PA-F1': 0.67, 'Event-based-F1': 0.4, 'R-based-F1': 0.24, 'Affiliation-F1': 0.8},
    {'Method': 'TimesNet', 'AUC-PR': 0.13, 'AUC-ROC': 0.56, 'VUS-PR': 0.19, 'VUS-ROC': 0.64, 'Standard-F1': 0.2, 'PA-F1': 0.68, 'Event-based-F1': 0.32, 'R-based-F1': 0.17, 'Affiliation-F1': 0.82},
    {'Method': 'KNN', 'AUC-PR': 0.14, 'AUC-ROC': 0.51, 'VUS-PR': 0.18, 'VUS-ROC': 0.59, 'Standard-F1': 0.19, 'PA-F1': 0.69, 'Event-based-F1': 0.45, 'R-based-F1': 0.21, 'Affiliation-F1': 0.79},
    {'Method': 'TranAD', 'AUC-PR': 0.14, 'AUC-ROC': 0.59, 'VUS-PR': 0.18, 'VUS-ROC': 0.65, 'Standard-F1': 0.21, 'PA-F1': 0.68, 'Event-based-F1': 0.4, 'R-based-F1': 0.21, 'Affiliation-F1': 0.79},
    {'Method': 'LOF', 'AUC-PR': 0.1, 'AUC-ROC': 0.53, 'VUS-PR': 0.14, 'VUS-ROC': 0.6, 'Standard-F1': 0.15, 'PA-F1': 0.57, 'Event-based-F1': 0.32, 'R-based-F1': 0.14, 'Affiliation-F1': 0.76},
    {'Method': 'AnomalyTransformer', 'AUC-PR': 0.07, 'AUC-ROC': 0.52, 'VUS-PR': 0.12, 'VUS-ROC': 0.57, 'Standard-F1': 0.12, 'PA-F1': 0.53, 'Event-based-F1': 0.33, 'R-based-F1': 0.14, 'Affiliation-F1': 0.74}
]

# Create DataFrames
df_u = pd.DataFrame(tsb_ad_u_dicts + [hta_ad_u_data])
df_m = pd.DataFrame(tsb_ad_m_dicts + [hta_ad_m_data])

# --- Function to create visualizations ---

def create_plots(df, dataset_name, output_dir):
    """Creates and saves a grouped bar chart for performance comparison and a bar chart for VUS-PR ranking."""

    # --- 1. Comparative Performance Analysis (Grouped Bar Chart) ---
    
    if dataset_name == 'TSB-AD-U':
        comparison_models = ['HTA_AD', 'Sub-PCA', 'KShapeAD', 'POLY', 'AnomalyTransformer']
        colors_map = {
            'HTA_AD': '#d62728',
            'Sub-PCA': '#1f77b4',
            'KShapeAD': '#aec7e8',
            'POLY': '#7f7f7f',
            'AnomalyTransformer': '#c7c7c7'
        }
        # From image, the colors are more like:
        colors_map = {
            'HTA_AD': 'red',
            'Sub-PCA': 'cornflowerblue',
            'KShapeAD': 'lightskyblue',
            'POLY': 'lightsteelblue',
            'AnomalyTransformer': 'lightgrey'
        }
    else: # TSB-AD-M
        comparison_models = ['HTA_AD', 'LSTMED', 'OmniAnomaly', 'CNN', 'AnomalyTransformer']
        colors_map = {
            'HTA_AD': 'red',
            'LSTMED': 'cornflowerblue',
            'OmniAnomaly': 'lightskyblue',
            'CNN': 'lightsteelblue',
            'AnomalyTransformer': 'lightgrey'
        }

    metrics = ['VUS-PR', 'AUC-PR', 'AUC-ROC', 'Standard-F1', 'R-based-F1']
    
    df_comparison = df[df['Method'].isin(comparison_models)].copy()
    df_comparison['Method'] = pd.Categorical(df_comparison['Method'], categories=comparison_models, ordered=True)
    df_comparison = df_comparison.sort_values('Method')
    
    fig_comp = go.Figure()

    for model_name in comparison_models:
        model_data = df_comparison[df_comparison['Method'] == model_name].iloc[0]
        fig_comp.add_trace(go.Bar(
            x=metrics,
            y=model_data[metrics].values,
            name=model_name,
            marker_color=colors_map.get(model_name),
            text=[f'{v:.3f}' for v in model_data[metrics].values],
            textposition='outside'
        ))

    y_max_comp = df_comparison[metrics].max().max()

    fig_comp.update_layout(
        title=f'<b>Comparative Performance Analysis on {dataset_name}</b>',
        xaxis_title='Performance Metric',
        yaxis_title='Score',
        barmode='group',
        font=dict(size=14, family="Times New Roman"),
        legend_title_text='Model',
        legend=dict(font=dict(size=16)),
        plot_bgcolor='white',
        yaxis_gridcolor='#E5E5E5',
        xaxis=dict(showline=True, linecolor='black', linewidth=1),
        yaxis=dict(range=[0, y_max_comp * 1.25], showline=True, linecolor='black', linewidth=1)
    )

    # --- 2. Bar Chart (VUS-PR Ranking) ---
    
    df_sorted = df.sort_values('VUS-PR', ascending=False)
    
    colors = ['red' if method == 'HTA_AD' else 'slategray' for method in df_sorted['Method']]
    
    fig_bar = go.Figure(go.Bar(
        x=df_sorted['Method'],
        y=df_sorted['VUS-PR'],
        marker_color=colors,
        text=df_sorted['VUS-PR'].round(3),
        textposition='outside'
    ))
    
    max_y = df_sorted['VUS-PR'].max()

    fig_bar.update_layout(
        title=f'<b>Model Performance Ranking on {dataset_name} (VUS-PR)</b>',
        xaxis_title='Method',
        yaxis_title='VUS-PR Score',
        font=dict(size=14, family="Times New Roman"),
        plot_bgcolor='white',
        yaxis_gridcolor='#E5E5E5',
        xaxis_tickangle=-60,
        xaxis=dict(showline=True, linecolor='black', linewidth=1, categoryorder='array', categoryarray=df_sorted['Method']),
        yaxis=dict(showline=True, linecolor='black', linewidth=1, range=[0, max_y * 1.15])
    )

    # --- Save plots ---
    os.makedirs(output_dir, exist_ok=True)
    pio.write_image(fig_comp, file=os.path.join(output_dir, f'comparative_performance_{dataset_name.lower()}.png'), width=1400, height=700, scale=2)
    pio.write_image(fig_bar, file=os.path.join(output_dir, f'vus_pr_ranking_{dataset_name.lower()}.png'), width=1800, height=800, scale=2)

    print(f"PNG images for {dataset_name} saved to {output_dir}")


# --- Main Execution ---

if __name__ == '__main__':
    output_directory = './visualizations/summary_plots/'
    create_plots(df_u, 'TSB-AD-U', output_directory)
    create_plots(df_m, 'TSB-AD-M', output_directory)

