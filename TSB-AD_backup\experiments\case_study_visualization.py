#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTA-AD Model Real Data Case Study - 保持简化版风格
使用真实单变量数据，但保持与simplified_model_visualization.py相同的视觉风格
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
from scipy.ndimage import gaussian_filter1d
import warnings
import pandas as pd

# Ensure TSB_AD module can be imported
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_AD

warnings.filterwarnings('ignore')

# Set plotting parameters - 与simplified版本保持一致
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 13
plt.rcParams['figure.dpi'] = 300

def load_real_univariate_data():
    """Load real univariate data - using Stock financial data"""
    
    # 使用Stock金融数据 - 有明显价格波动和异常模式
    stock_data_path = "../Datasets/TSB-AD-U/TSB-AD-U/151_Stock_id_3_Finance_tr_500_1st_62.csv"
    
    print("🔍 加载Stock金融数据...")
    
    try:
        print(f"\n加载: 151_Stock_id_3_Finance_tr_500_1st_62.csv")
        df = pd.read_csv(stock_data_path)
        data = df['Data'].values.astype(np.float32)
        labels = df['Label'].values.astype(int)
        
        print(f"  总数据点: {len(data)}")
        print(f"  异常数: {labels.sum()} ({labels.sum()/len(labels):.1%})")
        print(f"  数据范围: {data.min():.2f} - {data.max():.2f}")
        print(f"  标准差: {np.std(data):.2f}")
        
        # 找到异常点
        anomaly_indices = np.where(labels == 1)[0]
        
        # 选择包含多个异常点的有趣区间 (1000个点)
        # 从异常开始前200个点开始，包含前面的正常模式
        first_anomaly = anomaly_indices[0]  # 位置62
        start_idx = max(0, first_anomaly - 200)  # 从位置-138开始（如果存在的话），实际是0
        end_idx = min(len(data), start_idx + 1000)  # 取1000个点
        
        if end_idx - start_idx < 1000:
            start_idx = max(0, end_idx - 1000)
        
        data = data[start_idx:end_idx]
        labels = labels[start_idx:end_idx]
        
        final_anomalies = labels.sum()
        final_ratio = final_anomalies / len(labels)
        
        print(f"选择区域: {start_idx}-{end_idx}")
        print(f"最终异常数: {final_anomalies} ({final_ratio:.1%})")
        print(f"✅ Stock数据加载成功！")
        
    except Exception as e:
        print(f"⚠️ 加载Stock数据失败: {e}")
        print("使用默认医疗数据...")
        # 备选方案
        fallback_path = "../Datasets/TSB-AD-U/TSB-AD-U/501_SVDB_id_1_Medical_tr_50000_1st_107354.csv"
        df = pd.read_csv(fallback_path)
        data = df['Data'].values.astype(np.float32)[:1000]
        labels = df['Label'].values.astype(int)[:1000]
    
    print(f"\n📊 最终数据统计:")
    print(f"  长度: {len(data)}")
    print(f"  异常点: {labels.sum()} ({labels.sum()/len(labels):.1%})")
    print(f"  数值范围: [{data.min():.3f}, {data.max():.3f}]")
    print(f"  标准差: {np.std(data):.3f}")
    
    # Normalize data
    scaler = StandardScaler()
    data_normalized = scaler.fit_transform(data.reshape(-1, 1)).flatten()
    
    # Scale to similar range as simplified version (0.3 to 1.1)
    data_scaled = 0.7 + 0.2 * data_normalized
    data_scaled = np.clip(data_scaled, 0.3, 1.1)
    
    return data_scaled.reshape(-1, 1), labels, 100

def experiment_1_cnn_real():
    """实验一：CNN特征提取 - 真实数据版本，保持简化版风格"""
    print("🔍 Experiment 1: CNN Feature Extraction Analysis")
    
    data, labels, window_size = load_real_univariate_data()
    
    model_params = {
        'window_size': window_size,
        'latent_dim': 16,
        'lr': 0.001,
        'epochs': 50,
        'batch_size': 32
    }
    
    hta_model = HTA_AD(HP=model_params)
    
    # Training - 使用前600个点训练
    normal_data = data[:600]
    hta_model.fit(normal_data)
    
    # 选择一个有趣的测试窗口（包含一些模式变化）
    # 在异常区域附近选择窗口
    test_start = 100  # 选择包含异常前的正常模式的区域
    test_window = data[test_start:test_start+window_size]
    
    print("🎨 Analyzing real facility monitoring data patterns...")
    
    # Extract CNN features
    hta_model.model.eval()
    with torch.no_grad():
        input_tensor = torch.FloatTensor(test_window.reshape(1, window_size, 1)).to(hta_model.device)
        x_permuted = input_tensor.permute(0, 2, 1)
        cnn_features = hta_model.model.encoder_cnn(x_permuted)
        cnn_features = cnn_features.permute(0, 2, 1).cpu().numpy()[0]
    
    # 选择最具代表性的通道
    best_channel = np.argmax(np.var(cnn_features, axis=0))
    compressed_signal = cnn_features[:, best_channel].copy()
    
    print(f"🔍 CNN Features Debug:")
    print(f"   - Shape: {cnn_features.shape}")
    print(f"   - Selected channel {best_channel} (highest variance)")
    
    # 时间对齐 - 与simplified版本相同的处理方式
    stride = 2
    compressed_length = len(compressed_signal)
    time_compressed = np.linspace(0, len(test_window)-1, compressed_length)
    
    # 边界修复 - 与simplified版本相同
    if len(compressed_signal) >= 3:
        median_val = np.median(compressed_signal)
        std_val = np.std(compressed_signal)
        
        for i in [0, -1]:
            if abs(compressed_signal[i] - median_val) > 3 * std_val:
                if i == 0:
                    compressed_signal[i] = compressed_signal[1]
                else:
                    compressed_signal[i] = compressed_signal[-2]
    
    # 缩放处理 - 与simplified版本相同
    signal_min = np.min(compressed_signal)
    signal_max = np.max(compressed_signal)
    if signal_max > signal_min:
        compressed_normalized = (compressed_signal - signal_min) / (signal_max - signal_min)
    else:
        compressed_normalized = np.ones_like(compressed_signal) * 0.5
    
    original_min = np.min(test_window)
    original_max = np.max(test_window)
    original_range = original_max - original_min
    target_range = original_range * 0.8
    target_center = (original_min + original_max) / 2
    compressed_scaled = compressed_normalized * target_range + (target_center - target_range/2)
    
    # 创建可视化 - 与simplified版本完全相同的风格
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))
    
    # 使用完全相同的颜色方案
    original_color = '#2E86AB'
    compressed_color = '#E76F51'
    channel_colors = ['#E76F51', '#A23B72', '#F18F01', '#264653']
        
    # 左图：原始vs压缩信号
    time_original = np.arange(len(test_window))
    ax1.plot(time_original, test_window.flatten(), linewidth=2.5, color=original_color, 
             label='Real Facility Signal', alpha=0.9)
    
    ax1.plot(time_compressed, compressed_scaled, linewidth=2.5, color=compressed_color, 
             label='CNN Compressed', marker='o', markersize=3, alpha=0.9)
    
    ax1.set_xlabel('Time Steps')
    ax1.set_ylabel('Signal Value')
    ax1.legend(framealpha=0.95, edgecolor='gray', fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_title('CNN Processing Real Facility Data: 100→50 points', fontsize=12)
    
    # 右图：多通道特征多样性
    channel_variances = np.var(cnn_features, axis=0)
    top_channels = np.argsort(channel_variances)[-4:]
    
    for i, ch_idx in enumerate(top_channels):
        channel_signal = cnn_features[:, ch_idx].copy()
        
        # 边界修复
        if len(channel_signal) >= 3:
            median_val = np.median(channel_signal)
            std_val = np.std(channel_signal)
            
            for j in [0, -1]:
                if abs(channel_signal[j] - median_val) > 3 * std_val:
                    if j == 0:
                        channel_signal[j] = channel_signal[1]
                    else:
                        channel_signal[j] = channel_signal[-2]
        
        normalized_features = (channel_signal - np.mean(channel_signal)) / (np.std(channel_signal) + 1e-8)
        ax2.plot(time_compressed, normalized_features, linewidth=2.5, color=channel_colors[i], 
                label=f'Ch {ch_idx} (σ²={channel_variances[ch_idx]:.3f})', alpha=0.8)
    
    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Normalized Features')
    ax2.legend(framealpha=0.95, edgecolor='gray', fontsize=10)
    ax2.grid(True, alpha=0.3)
    ax2.set_title('Multi-Channel Feature Diversity', fontsize=12)
    
    plt.tight_layout()
    plt.savefig('experiments/cnn_real.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Real facility data compression: 100→50 (2:1)")
    print(f"✅ CNN successfully extracted diverse features from real patterns")

def experiment_2_tcn_real():
    """实验二：TCN感受野分析 - 与simplified版本完全相同"""
    print("📡 Experiment 2: TCN Receptive Field Analysis")
    
    # 与simplified版本完全相同的配置
    tcn_channels = [32, 32, 32, 32, 32]
    kernel_size = 7
    dilations = [1, 2, 4, 8, 16]
    
    layers = np.arange(1, len(tcn_channels) + 1)
    tcn_rfs = []
    std_rfs = []
    
    for i, dilation in enumerate(dilations):
        if i == 0:
            tcn_rf = kernel_size
            std_rf = kernel_size
        else:
            tcn_rf = tcn_rfs[-1] + (kernel_size - 1) * dilation
            std_rf = std_rfs[-1] + (kernel_size - 1)
        
        tcn_rfs.append(tcn_rf)
        std_rfs.append(std_rf)
    
    # 与simplified版本完全相同的可视化
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    tcn_color = '#E76F51'
    std_color = '#264653'
    fill_color = '#F4A261'
    
    ax.plot(layers, tcn_rfs, 'o-', linewidth=3, color=tcn_color, markersize=8, label='TCN (Dilated Conv)')
    ax.plot(layers, std_rfs, 's--', linewidth=2, color=std_color, markersize=6, label='Standard Conv')
    
    # 相同的标注方式
    for i, (layer, tcn_rf, std_rf) in enumerate(zip(layers, tcn_rfs, std_rfs)):
        if i < 3:
            ax.annotate(f'{tcn_rf}', (layer, tcn_rf), textcoords="offset points", 
                       xytext=(0, 12), ha='center', fontweight='bold', fontsize=11, 
                       color=tcn_color)
        else:
            ax.annotate(f'{tcn_rf}', (layer, tcn_rf), textcoords="offset points", 
                       xytext=(-8, 8), ha='center', fontweight='bold', fontsize=11, 
                       color=tcn_color)
        
        ax.annotate(f'{std_rf}', (layer, std_rf), textcoords="offset points", 
                   xytext=(0, -18), ha='center', fontweight='bold', fontsize=10, 
                   color=std_color)
    
    ax.fill_between(layers, std_rfs, tcn_rfs, alpha=0.3, color=fill_color)
    
    ax.set_xlabel('Network Layer')
    ax.set_ylabel('Receptive Field Size')
    ax.set_yscale('log')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/tcn_real.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Final Receptive Field: TCN={tcn_rfs[-1]}, Standard={std_rfs[-1]}")

def experiment_3_reconstruction_real():
    """实验三：重构误差分析 - 真实数据版本，保持简化版风格"""
    print("🎯 Experiment 3: Reconstruction Error Analysis")
    
    data, labels, window_size = load_real_univariate_data()
    
    # 与simplified版本相同的模型参数
    model_params = {
        'window_size': window_size,
        'latent_dim': 16,
        'lr': 0.0005,
        'epochs': 100,
        'batch_size': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16
    }
    
    hta_model = HTA_AD(HP=model_params)
    
    # 训练设置
    normal_data = data[:800]
    hta_model.fit(normal_data)
    
    # 选择包含异常的测试窗口
    # 找到异常点位置
    anomaly_indices = np.where(labels == 1)[0]
    if len(anomaly_indices) > 0:
        # 选择第一个异常点前50个时间步开始的窗口，这样包含异常
        anomaly_start = anomaly_indices[0]
        test_start = max(0, anomaly_start - 20)  # 异常前20个点开始
        if test_start + window_size > len(data):
            test_start = len(data) - window_size
    else:
        test_start = 750  # 如果没有异常，使用默认位置
    
    test_window = data[test_start:test_start+window_size]
    test_labels = labels[test_start:test_start+window_size]
    
    print(f"测试窗口位置: {test_start}-{test_start+window_size}")
    print(f"窗口内异常点数量: {test_labels.sum()}")
    
    # 获取重构结果
    hta_model.model.eval()
    with torch.no_grad():
        input_tensor = torch.FloatTensor(test_window.reshape(1, window_size, 1)).to(hta_model.device)
        reconstructed = hta_model.model(input_tensor).cpu().numpy()[0].flatten()
    
    # 计算重构误差
    errors = np.abs(test_window.flatten() - reconstructed)
    
    # 与simplified版本完全相同的可视化风格
    fig, ax = plt.subplots(1, 1, figsize=(14, 6))
    
    time_steps = np.arange(len(test_window))
    
    # 相同的颜色方案
    original_color = '#2E86AB'
    reconstructed_color = '#A23B72'
    error_color = '#F18F01'
    anomaly_color = '#C73E1D'
    
    # 绘制信号
    ax.plot(time_steps, test_window.flatten(), linewidth=3, color=original_color, 
            label='Original Signal', alpha=0.9)
    ax.plot(time_steps, reconstructed, linewidth=2.5, color=reconstructed_color, 
            label='Reconstructed Signal', alpha=0.8, linestyle='--')
    
    # 标记异常区域
    anomaly_mask = test_labels.astype(bool)
    if np.any(anomaly_mask):
        anomaly_indices = np.where(anomaly_mask)[0]
        if len(anomaly_indices) > 0:
            ax.axvspan(anomaly_indices[0], anomaly_indices[-1], 
                      alpha=0.15, color=anomaly_color, label='Anomaly Region')
    
    # 重构误差（右y轴）
    ax2 = ax.twinx()
    ax2.plot(time_steps, errors, linewidth=2, color=error_color, 
             label='Reconstruction Error', alpha=0.8)
    ax2.fill_between(time_steps, errors, alpha=0.2, color=error_color)
    
    # 相同的样式设置
    ax.set_xlabel('Time Steps', fontsize=13)
    ax.set_ylabel('Signal Value', fontsize=13, color=original_color)
    ax2.set_ylabel('Reconstruction Error', fontsize=13, color=error_color)
    
    ax.tick_params(axis='y', labelcolor=original_color)
    ax2.tick_params(axis='y', labelcolor=error_color)
    
    # 统一图例
    lines1, labels1 = ax.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left', 
              framealpha=0.95, edgecolor='gray', fontsize=11)
    
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiments/reconstruction_real.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 计算统计数据
    normal_error = np.mean(errors[~anomaly_mask]) if np.any(~anomaly_mask) else 0
    anomaly_error = np.mean(errors[anomaly_mask]) if np.any(anomaly_mask) else 0
    
    print(f"✅ Normal Error: {normal_error:.4f}")
    print(f"✅ Anomaly Error: {anomaly_error:.4f}")
    if normal_error > 0:
        amplification = anomaly_error/normal_error
        print(f"✅ Error Amplification: {amplification:.1f}x")

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 HTA-AD Model Real Data Case Study - Simplified Style")
    print("=" * 60)
    
    experiment_1_cnn_real()
    print()
    experiment_2_tcn_real()
    print()
    experiment_3_reconstruction_real()
    
    print("\n" + "=" * 60)
    print("✅ All real data visualizations completed!")
    print("📁 Generated files:")
    print("   📊 cnn_real.png")
    print("   📊 tcn_real.png") 
    print("   📊 reconstruction_real.png")
    print("=" * 60) 