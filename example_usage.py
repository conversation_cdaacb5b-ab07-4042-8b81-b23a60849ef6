#!/usr/bin/env python3
"""
HTA-AD Usage Example
Demonstrates basic usage of the integrated HTA-AD model
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Import HTA-AD components
from core.models.hta_ad_integrated import HTAADComplete, HTAADTrainer


def generate_synthetic_data(n_samples=1000, seq_len=100, anomaly_ratio=0.1):
    """Generate synthetic time series data with anomalies"""
    np.random.seed(42)
    
    # Generate normal data (sine wave with noise)
    t = np.linspace(0, 4*np.pi, seq_len)
    normal_data = []
    anomaly_labels = []
    
    for i in range(n_samples):
        # Base sine wave
        base_signal = np.sin(t + np.random.normal(0, 0.1)) + 0.1 * np.random.randn(seq_len)
        
        # Add anomalies
        if np.random.random() < anomaly_ratio:
            # Inject anomaly (spike or shift)
            anomaly_start = np.random.randint(20, seq_len-20)
            anomaly_end = anomaly_start + np.random.randint(5, 15)
            
            if np.random.random() < 0.5:
                # Spike anomaly
                base_signal[anomaly_start:anomaly_end] += np.random.normal(2, 0.5, anomaly_end-anomaly_start)
            else:
                # Shift anomaly
                base_signal[anomaly_start:anomaly_end] += np.random.normal(1.5, 0.3)
            
            anomaly_labels.append(1)
        else:
            anomaly_labels.append(0)
        
        normal_data.append(base_signal)
    
    return np.array(normal_data), np.array(anomaly_labels)


def main():
    print("🚀 HTA-AD Usage Example")
    print("=" * 50)
    
    # 1. Generate synthetic data
    print("📊 Generating synthetic time series data...")
    sequences, labels = generate_synthetic_data(n_samples=500, seq_len=100, anomaly_ratio=0.15)
    print(f"Generated {len(sequences)} sequences, {np.sum(labels)} anomalies")
    
    # Normalize data
    scaler = StandardScaler()
    sequences_normalized = scaler.fit_transform(sequences.reshape(-1, 1)).reshape(sequences.shape)
    sequences_normalized = sequences_normalized.reshape(-1, 100, 1)  # Add feature dimension
    
    # 2. Split data
    train_size = int(0.7 * len(sequences))
    val_size = int(0.15 * len(sequences))
    
    train_sequences = sequences_normalized[:train_size]
    train_labels = labels[:train_size]
    
    val_sequences = sequences_normalized[train_size:train_size+val_size]
    val_labels = labels[train_size:train_size+val_size]
    
    test_sequences = sequences_normalized[train_size+val_size:]
    test_labels = labels[train_size+val_size:]
    
    # Create data loaders
    train_dataset = TensorDataset(torch.FloatTensor(train_sequences), torch.FloatTensor(train_labels))
    val_dataset = TensorDataset(torch.FloatTensor(val_sequences), torch.FloatTensor(val_labels))
    test_dataset = TensorDataset(torch.FloatTensor(test_sequences), torch.FloatTensor(test_labels))
    
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # 3. Create model
    print("\n🧠 Creating HTA-AD model with SAE integration...")
    model = HTAADComplete(
        input_dim=1,
        d_model=32,
        n_heads=4,
        n_layers=2,
        seq_len=100,
        sae_hidden_dim=64,
        sae_sparsity_weight=0.01,
        enable_sae=True  # Enable SAE for interpretability
    )
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 4. Train model
    print("\n🏋️ Training model...")
    trainer = HTAADTrainer(model, device='cpu')  # Use CPU for demo
    
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=20,  # Reduced for demo
        lr=1e-3,
        patience=10
    )
    
    print(f"Training completed. Final train loss: {history['train_loss'][-1]:.4f}")
    if 'val_loss' in history:
        print(f"Final validation loss: {history['val_loss'][-1]:.4f}")
    
    # 5. Evaluate model
    print("\n📈 Evaluating model...")
    test_metrics = trainer.evaluate(test_loader)
    print(f"Test metrics: {test_metrics}")
    
    # 6. Detect anomalies
    print("\n🔍 Detecting anomalies...")
    model.eval()
    with torch.no_grad():
        test_sequences_tensor = torch.FloatTensor(test_sequences)
        results = model.detect_anomalies(test_sequences_tensor, threshold=0.5)
    
    # Calculate accuracy
    predictions = results['anomaly_predictions'].numpy().astype(int)
    accuracy = np.mean(predictions == test_labels)
    precision = np.sum((predictions == 1) & (test_labels == 1)) / max(np.sum(predictions == 1), 1)
    recall = np.sum((predictions == 1) & (test_labels == 1)) / max(np.sum(test_labels == 1), 1)
    
    print(f"Anomaly detection accuracy: {accuracy:.3f}")
    print(f"Precision: {precision:.3f}")
    print(f"Recall: {recall:.3f}")
    print(f"Detected {np.sum(predictions)} anomalies out of {len(predictions)} sequences")
    print(f"True anomalies: {np.sum(test_labels)}")
    
    # 7. Interpretability analysis (if SAE enabled)
    if model.enable_sae:
        print("\n🔬 Interpretability analysis...")
        
        # Find an anomalous sample for explanation
        anomaly_indices = np.where(test_labels == 1)[0]
        if len(anomaly_indices) > 0:
            anomaly_idx = anomaly_indices[0]
            explanation = model.explain_anomaly(test_sequences_tensor, anomaly_idx)
            
            print(f"Explanation for anomaly at index {anomaly_idx}:")
            print(f"  - Anomaly score: {explanation['anomaly_score']:.3f}")
            print(f"  - Top contributing features: {explanation['top_contributing_features']}")
            print(f"  - Reconstruction error: {explanation['reconstruction_error']:.4f}")
        
        # Get feature dictionary
        feature_dict = model.get_feature_dictionary()
        if 'error' not in feature_dict:
            print(f"Feature dictionary contains {len(feature_dict)} layers")
    
    # 8. Visualization
    print("\n📊 Creating visualizations...")
    
    # Plot training history
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='Train Loss')
    if 'val_loss' in history:
        plt.plot(history['val_loss'], label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training History')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot anomaly scores
    plt.subplot(1, 2, 2)
    anomaly_scores = results['anomaly_scores'].numpy()
    normal_scores = anomaly_scores[test_labels == 0]
    anomaly_scores_true = anomaly_scores[test_labels == 1]
    
    plt.hist(normal_scores, bins=20, alpha=0.7, label='Normal', color='blue')
    plt.hist(anomaly_scores_true, bins=20, alpha=0.7, label='Anomaly', color='red')
    plt.axvline(x=0.5, color='black', linestyle='--', label='Threshold')
    plt.xlabel('Anomaly Score')
    plt.ylabel('Frequency')
    plt.title('Anomaly Score Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/figures/example_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 9. Save model
    print("\n💾 Saving model...")
    model.save_model('results/models/example_hta_ad_model.pth')
    print("Model saved to 'results/models/example_hta_ad_model.pth'")
    
    print("\n✅ Example completed successfully!")
    print("\nNext steps:")
    print("1. Try with your own data using main.py")
    print("2. Experiment with different model parameters")
    print("3. Explore interpretability features")
    print("4. Run comprehensive benchmarks in core/experiments/")


if __name__ == '__main__':
    main()
