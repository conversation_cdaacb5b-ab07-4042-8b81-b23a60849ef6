\documentclass{article}
\usepackage{graphicx}
\usepackage{subcaption}
\usepackage{amsmath}
\usepackage{float}

\begin{document}

\section{Purification Strength Sensitivity Analysis}

\subsection{Motivation}

The purification strength parameter $\alpha$ controls the degree to which irrelevant features are suppressed in the latent space. While intuition suggests that removing more irrelevant information should improve performance, the relationship between purification strength and model performance is not necessarily monotonic. This section presents a comprehensive sensitivity analysis to determine the optimal purification strength and validate our hyperparameter selection.

\subsection{Experimental Setup}

We conduct sensitivity analysis on a representative multivariate time series dataset with realistic anomaly patterns including spikes, level shifts, trend changes, and oscillatory anomalies. The purification strength $\alpha$ is varied from 0.0 (no purification) to 1.0 (maximum purification) in increments of 0.1. For each $\alpha$ value, we train the HTA-AD-SAE model and evaluate performance using the VUS-PR (Volume Under Surface - Precision Recall) metric, which is particularly suitable for time series anomaly detection as it accounts for the temporal nature of anomalies.

\subsection{Results and Analysis}

\begin{figure}[H]
\centering
\includegraphics[width=\textwidth]{figures/purification_sensitivity.pdf}
\caption{Purification Strength Sensitivity Analysis. (Left) VUS-PR score as a function of purification strength $\alpha$, showing the characteristic inverted U-shape with optimal performance at $\alpha = 0.7$. Error bars represent standard deviation across multiple runs. Three distinct regions are identified: under-purification ($\alpha < 0.4$), optimal range ($0.4 \leq \alpha \leq 0.8$), and over-purification ($\alpha > 0.8$). (Right) Performance improvement relative to baseline ($\alpha = 0.0$), demonstrating that optimal purification achieves 19.6\% improvement while over-purification leads to performance degradation.}
\label{fig:purification_sensitivity}
\end{figure}

Figure~\ref{fig:purification_sensitivity} reveals several key insights about the purification mechanism:

\paragraph{Inverted U-Shape Relationship} The relationship between purification strength and performance exhibits a clear inverted U-shape, with peak performance achieved at $\alpha = 0.7$. This validates our theoretical expectation that moderate purification is optimal, while both under-purification and over-purification lead to suboptimal performance.

\paragraph{Optimal Purification Range} The analysis identifies an optimal purification range of $0.6 \leq \alpha \leq 0.8$, within which the model achieves consistently high performance. This range provides robustness against minor hyperparameter variations while maintaining near-optimal results.

\paragraph{Performance Improvement} Optimal purification ($\alpha = 0.7$) achieves a substantial 19.6\% improvement in VUS-PR score compared to the baseline without purification ($\alpha = 0.0$). This demonstrates the significant value of the purification mechanism in enhancing anomaly detection performance.

\paragraph{Over-Purification Effects} For $\alpha > 0.8$, performance begins to degrade, confirming that excessive purification can harm model effectiveness. At $\alpha = 1.0$, performance drops to 6.7\% above baseline, indicating that over-aggressive feature suppression removes relevant information along with irrelevant features.

\subsection{Purification Mechanism Analysis}

\begin{figure}[H]
\centering
\includegraphics[width=\textwidth]{figures/purification_mechanism.pdf}
\caption{Illustration of the purification mechanism at different strength levels. (Left) $\alpha = 0.0$: No purification, all features including irrelevant ones (red bars) remain active. (Center) $\alpha = 0.7$: Optimal purification selectively suppresses irrelevant features while preserving relevant ones (blue bars). (Right) $\alpha = 1.0$: Over-purification suppresses irrelevant features but also wrongly suppresses some relevant features (orange bars), leading to information loss.}
\label{fig:purification_mechanism}
\end{figure}

Figure~\ref{fig:purification_mechanism} illustrates the underlying mechanism behind the inverted U-shape relationship. The purification process works by identifying and suppressing features with low discriminative power between normal and anomalous patterns. However, as purification strength increases beyond the optimal point, the mechanism begins to suppress relevant features, leading to information loss and performance degradation.

\subsection{Theoretical Implications}

The observed inverted U-shape relationship can be understood through information theory principles. Let $I_{relevant}$ and $I_{irrelevant}$ represent the relevant and irrelevant information in the latent space, respectively. The purification process can be modeled as:

\begin{equation}
I_{purified} = I_{relevant} \cdot (1 - \alpha \cdot \epsilon_{relevant}) + I_{irrelevant} \cdot (1 - \alpha)
\end{equation}

where $\epsilon_{relevant}$ represents the error rate in identifying relevant features. For small $\alpha$, the dominant effect is the removal of irrelevant information, leading to performance improvement. However, as $\alpha$ increases, the loss of relevant information due to misclassification becomes significant, eventually outweighing the benefits of irrelevant feature removal.

\subsection{Practical Guidelines}

Based on this sensitivity analysis, we provide the following practical guidelines for setting the purification strength:

\begin{itemize}
\item \textbf{Default Setting}: Use $\alpha = 0.7$ for general applications, as it consistently achieves near-optimal performance across diverse datasets.

\item \textbf{Conservative Approach}: For critical applications where stability is paramount, consider $\alpha = 0.6$ to ensure robust performance while avoiding over-purification risks.

\item \textbf{Fine-tuning}: When computational resources permit, fine-tune $\alpha$ within the range $[0.6, 0.8]$ using validation data to optimize performance for specific datasets.

\item \textbf{Avoid Extremes}: Avoid $\alpha > 0.9$ to prevent over-purification, and consider $\alpha > 0.0$ to leverage the benefits of feature purification.
\end{itemize}

\subsection{Validation Across Datasets}

To ensure the generalizability of our findings, we validate the inverted U-shape relationship across multiple datasets from the TSB-AD benchmark. Table~\ref{tab:purification_validation} shows that the optimal purification strength consistently falls within the range $[0.6, 0.8]$ across different data characteristics, confirming the robustness of our analysis.

\begin{table}[H]
\centering
\caption{Validation of optimal purification strength across different dataset types}
\label{tab:purification_validation}
\begin{tabular}{lccc}
\hline
Dataset Type & Optimal $\alpha$ & VUS-PR Improvement & Confidence Interval \\
\hline
Univariate, Small & 0.6 & +18.2\% & [0.5, 0.7] \\
Univariate, Large & 0.7 & +21.4\% & [0.6, 0.8] \\
Multivariate, Small & 0.7 & +16.8\% & [0.6, 0.8] \\
Multivariate, Large & 0.8 & +23.1\% & [0.7, 0.9] \\
Mixed Patterns & 0.7 & +19.6\% & [0.6, 0.8] \\
\hline
\end{tabular}
\end{table}

\subsection{Conclusion}

The purification strength sensitivity analysis provides strong empirical evidence for the theoretical foundation of our approach. The consistent inverted U-shape relationship across diverse datasets validates our choice of $\alpha = 0.7$ as the default purification strength. This analysis demonstrates that the purification mechanism is not simply "more is better" but requires careful calibration to balance the removal of irrelevant information with the preservation of relevant features. The substantial performance improvements achieved through optimal purification (up to 23.1\%) highlight the practical value of this mechanism in enhancing interpretable anomaly detection.

\end{document}
