#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTA-AD + SAE 大规模预训练与全面基准测试
1. 在所有TSB-AD数据集上预训练SAE
2. 结合官方HTA-AD模型
3. 在所有数据集上进行全面评估
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import random
import argparse
import time
import os
import logging
import sys
from sklearn.preprocessing import MinMaxScaler

# 添加TSB-AD路径
sys.path.append('TSB-AD')
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.model_wrapper import *
from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict
from TSB_AD.models.HTA_AD import HTA_Model, HTA_AD

# 设置随机种子
def set_seed(seed=2024):
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True

class LargeScaleSAE(nn.Module):
    """大规模预训练SAE"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 更深的编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU()
        )
        
        # 对应的解码器
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 3, input_dim)
        )
        
        # 初始化
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.xavier_normal_(module.weight, gain=0.5)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    def forward(self, x):
        activations = self.encoder(x)
        reconstructed = self.decoder(activations)
        return reconstructed, activations

class HTA_AD_SAE_Enhanced(HTA_AD):
    """增强的HTA-AD + SAE"""
    def __init__(self, HP, pretrained_sae=None, irrelevant_indices=None, normalize=True):
        super().__init__(HP, normalize)
        
        self.pretrained_sae = pretrained_sae
        self.irrelevant_indices = irrelevant_indices or []
        self.purification_strength = HP.get('purification_strength', 0.7)
        
        if pretrained_sae:
            # 冻结SAE参数
            for param in pretrained_sae.parameters():
                param.requires_grad = False
            pretrained_sae.eval()
    
    def _purify_latent_vector(self, latent_vec):
        """使用预训练SAE净化潜在向量"""
        if self.pretrained_sae is None or len(self.irrelevant_indices) == 0:
            return latent_vec
        
        with torch.no_grad():
            # 完整的SAE前向传播
            _, activations = self.pretrained_sae(latent_vec)

            # 创建无关特征掩码
            irrelevant_mask = torch.zeros_like(activations)
            irrelevant_mask[:, self.irrelevant_indices] = 1.0

            # 提取无关特征的激活
            irrelevant_activations = activations * irrelevant_mask

            # 计算无关特征的重构贡献
            irrelevant_contribution = self.pretrained_sae.decoder(irrelevant_activations)

            # 从原始潜在向量中减去无关贡献
            purified_latent = latent_vec - self.purification_strength * irrelevant_contribution
            
        return purified_latent
    
    def _compute_scores(self, X, fit_scaler=False):
        """重写评分计算，加入SAE净化"""
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: 
            return np.zeros(n_samples)

        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                # 获取潜在向量
                x_permuted = batch_windows.permute(0, 2, 1)
                encoded_cnn = self.model.encoder_cnn(x_permuted)
                encoded_tcn = self.model.encoder_tcn(encoded_cnn)
                encoded_flat = encoded_tcn.flatten(start_dim=1)
                latent_vec = self.model.fc_encode(encoded_flat)
                
                # SAE净化
                purified_latent = self._purify_latent_vector(latent_vec)
                
                # 解码
                decoded_flat = self.model.decoder_fc(purified_latent)
                decoded_unflat = decoded_flat.view(-1, self.model.tcn_output_channels, self.model.downsampled_len)
                decoded_tcn = self.model.decoder_tcn(decoded_unflat)
                reconstructed_permuted = self.model.decoder_cnn(decoded_tcn)
                
                # 调整尺寸
                if reconstructed_permuted.shape[2] != self.model.window_size:
                    reconstructed_permuted = F.interpolate(
                        reconstructed_permuted, size=self.model.window_size, 
                        mode='linear', align_corners=False
                    )
                
                reconstructed = self.model.output_activation(reconstructed_permuted.permute(0, 2, 1))
                
                # 计算重构误差
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        # 映射到原始序列
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(np.array(window_scores)):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        if n_samples > self.window_size - 1:
            scores_mapped[:self.window_size-1] = scores_mapped[self.window_size-1]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

def load_all_datasets_for_pretraining(dataset_dirs, max_datasets_per_dir=50):
    """加载所有TSB-AD数据集用于预训练"""
    print("📊 加载所有TSB-AD数据集用于大规模预训练...")
    
    all_data = []
    total_samples = 0
    dataset_count = 0
    
    for dataset_dir in dataset_dirs:
        if not os.path.exists(dataset_dir):
            print(f"⚠️  目录不存在: {dataset_dir}")
            continue
            
        print(f"📁 处理目录: {dataset_dir}")
        
        # 获取所有CSV文件
        csv_files = []
        for root, dirs, files in os.walk(dataset_dir):
            for file in files:
                if file.endswith('.csv'):
                    csv_files.append(os.path.join(root, file))
        
        # 限制每个目录的数据集数量
        csv_files = csv_files[:max_datasets_per_dir]
        
        for i, file_path in enumerate(csv_files):
            try:
                print(f"  加载 {i+1}/{len(csv_files)}: {os.path.basename(file_path)}")
                
                df = pd.read_csv(file_path)
                
                # 处理标签列
                if 'label' in df.columns:
                    data = df.drop('label', axis=1).values
                elif 'anomaly' in df.columns:
                    data = df.drop('anomaly', axis=1).values
                else:
                    data = df.iloc[:, :-1].values
                
                # 处理多变量数据
                if data.shape[1] > 1:
                    # 对于多变量数据，使用第一个特征
                    data = data[:, 0]
                else:
                    data = data.flatten()
                
                # 数据质量检查
                if len(data) > 1000 and not np.any(np.isnan(data)) and not np.any(np.isinf(data)):
                    # 归一化
                    scaler = MinMaxScaler()
                    data = scaler.fit_transform(data.reshape(-1, 1)).flatten()
                    all_data.append(data)
                    total_samples += len(data)
                    dataset_count += 1
                    print(f"    ✅ 成功加载 {len(data)} 个样本")
                else:
                    print(f"    ⚠️  数据质量不符合要求，跳过")
                
            except Exception as e:
                print(f"    ❌ 加载失败: {e}")
                continue
    
    print(f"✅ 预训练数据加载完成:")
    print(f"   数据集数量: {dataset_count}")
    print(f"   总样本数: {total_samples:,}")
    
    return all_data

def pretrain_sae_large_scale(sae_model, datasets, window_size=128, epochs=100, batch_size=128):
    """大规模预训练SAE"""
    print("🚀 开始大规模SAE预训练...")
    
    # 创建临时HTA-AD模型用于特征提取
    temp_hta_ad = HTA_Model(
        input_dim=1,
        window_size=window_size,
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        cnn_channels=16,
        downsample_stride=2
    )
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    temp_hta_ad.to(device)
    sae_model.to(device)
    
    # 从所有数据集创建窗口
    print("📊 创建训练窗口...")
    all_windows = []
    
    for i, data in enumerate(datasets):
        if len(data) >= window_size:
            # 创建滑动窗口
            stride = max(1, window_size // 8)  # 更密集的采样
            windows = []
            for j in range(0, len(data) - window_size + 1, stride):
                windows.append(data[j:j + window_size])
            
            if len(windows) > 0:
                all_windows.extend(windows)
                print(f"  数据集 {i+1}: 创建 {len(windows)} 个窗口")
    
    all_windows = np.array(all_windows)
    print(f"📊 总训练窗口: {len(all_windows)}")
    
    # 提取潜在向量
    print("🔧 提取潜在向量...")
    temp_hta_ad.eval()
    
    latent_vectors = []
    with torch.no_grad():
        for i in range(0, len(all_windows), batch_size):
            batch = torch.FloatTensor(all_windows[i:i+batch_size]).unsqueeze(-1).to(device)
            
            # HTA-AD编码
            x_permuted = batch.permute(0, 2, 1)
            encoded_cnn = temp_hta_ad.encoder_cnn(x_permuted)
            encoded_tcn = temp_hta_ad.encoder_tcn(encoded_cnn)
            encoded_flat = encoded_tcn.flatten(start_dim=1)
            latent_vec = temp_hta_ad.fc_encode(encoded_flat)
            
            latent_vectors.append(latent_vec.cpu())
            
            if (i // batch_size + 1) % 100 == 0:
                print(f"    处理进度: {i // batch_size + 1} / {len(all_windows) // batch_size + 1}")
    
    latent_vectors = torch.cat(latent_vectors, dim=0).to(device)
    print(f"📊 提取潜在向量: {latent_vectors.shape}")
    
    # 训练SAE
    print("🔧 训练SAE...")
    optimizer = torch.optim.AdamW(sae_model.parameters(), lr=0.001, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.7)
    
    dataset = torch.utils.data.TensorDataset(latent_vectors)
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    sae_model.train()
    
    for epoch in range(epochs):
        total_loss = 0
        valid_batches = 0
        
        for batch in dataloader:
            z_batch = batch[0]
            
            optimizer.zero_grad()
            z_recon, activations = sae_model(z_batch)
            
            # 重构损失
            recon_loss = F.mse_loss(z_recon, z_batch)
            
            # 渐进式稀疏性损失
            sparsity_weight = 0.001 * (1 + epoch / epochs)
            sparsity_loss = sparsity_weight * torch.mean(torch.abs(activations))
            
            loss = recon_loss + sparsity_loss
            
            if torch.isfinite(loss):
                loss.backward()
                torch.nn.utils.clip_grad_norm_(sae_model.parameters(), max_norm=1.0)
                optimizer.step()
                total_loss += loss.item()
                valid_batches += 1
        
        if valid_batches > 0:
            avg_loss = total_loss / valid_batches
            scheduler.step(avg_loss)
            
            if (epoch + 1) % 10 == 0:
                print(f"  Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
    
    print("✅ 大规模SAE预训练完成")
    
    # 保存预训练模型
    torch.save(sae_model.state_dict(), 'large_scale_pretrained_sae.pth')
    print("💾 预训练模型已保存")
    
    return latent_vectors

def identify_irrelevant_features_from_pretraining(sae_model, latent_vectors):
    """从预训练数据中识别无关特征"""
    print("🔍 从预训练数据识别无关特征...")
    
    sae_model.eval()
    with torch.no_grad():
        # 采样部分数据进行分析
        sample_size = min(10000, len(latent_vectors))
        sample_indices = torch.randperm(len(latent_vectors))[:sample_size]
        sample_vectors = latent_vectors[sample_indices]
        
        _, activations = sae_model(sample_vectors)
        activations = activations.cpu().numpy()
    
    irrelevant_indices = []
    
    for i in range(activations.shape[1]):
        feature_acts = activations[:, i]
        
        # 统计分析
        activation_rate = np.mean(feature_acts > 0.01)
        activation_std = np.std(feature_acts)
        activation_mean = np.mean(feature_acts)
        
        # 识别标准
        if (activation_rate < 0.01 or  # 极少激活
            (activation_rate > 0.99 and activation_std < 0.001) or  # 总是激活且无变化
            activation_mean < 0.0001):  # 平均激活极低
            irrelevant_indices.append(i)
    
    # 限制无关特征比例
    max_irrelevant = int(activations.shape[1] * 0.25)  # 最多25%
    if len(irrelevant_indices) > max_irrelevant:
        # 按激活统计排序
        feature_scores = []
        for idx in irrelevant_indices:
            score = np.mean(activations[:, idx]) + np.std(activations[:, idx])
            feature_scores.append((idx, score))
        
        feature_scores.sort(key=lambda x: x[1])
        irrelevant_indices = [idx for idx, _ in feature_scores[:max_irrelevant]]
    
    print(f"📊 识别出 {len(irrelevant_indices)}/{activations.shape[1]} 个无关特征")
    return irrelevant_indices

def run_large_scale_benchmark():
    """运行大规模基准测试"""
    print("🧪 HTA-AD + SAE 大规模基准测试")
    print("=" * 100)
    
    set_seed(2024)
    
    # 1. 大规模预训练阶段
    print("\n🔧 阶段1: 大规模SAE预训练")
    print("-" * 80)
    
    # 加载所有数据集
    dataset_dirs = [
        'TSB-AD/Datasets/TSB-AD-U',
        'TSB-AD/Datasets/TSB-AD-M'
    ]
    
    datasets = load_all_datasets_for_pretraining(dataset_dirs, max_datasets_per_dir=30)
    
    if not datasets:
        print("❌ 预训练数据加载失败")
        return
    
    # 创建大规模SAE
    sae_model = LargeScaleSAE(input_dim=32, hidden_dim=128)
    
    # 预训练
    latent_vectors = pretrain_sae_large_scale(sae_model, datasets, epochs=50)
    
    # 识别无关特征
    irrelevant_indices = identify_irrelevant_features_from_pretraining(sae_model, latent_vectors)
    
    # 2. 基准测试阶段
    print(f"\n📊 阶段2: 全面基准测试")
    print("-" * 80)
    
    # 测试数据集列表
    test_datasets = [
        'TSB-AD/Datasets/TSB-AD-U/001_NAB_id_1_Facility_tr_1007_1st_2014.csv',
        'TSB-AD/Datasets/TSB-AD-U/169_Daphnet_id_1_HumanActivity_tr_9693_1st_20732.csv',
        'TSB-AD/Datasets/TSB-AD-U/531_SMAP_id_1_Sensor_tr_1811_1st_4510.csv',
        'TSB-AD/Datasets/TSB-AD-M/002_MSL_id_1_Sensor_tr_500_1st_900.csv',
    ]
    
    # 获取HTA-AD的最优超参数
    optimal_hp = Optimal_Uni_algo_HP_dict.get('HTA_AD', {
        'window_size': 128,
        'epochs': 30,
        'lr': 1e-3,
        'batch_size': 64,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2
    })
    
    results = []
    
    for dataset_path in test_datasets:
        if not os.path.exists(dataset_path):
            print(f"⚠️  数据集不存在: {dataset_path}")
            continue
            
        dataset_name = os.path.basename(dataset_path).replace('.csv', '')
        print(f"\n🔬 测试数据集: {dataset_name}")
        print("-" * 60)
        
        try:
            # 加载数据
            df = pd.read_csv(dataset_path)
            
            if 'label' in df.columns:
                labels = df['label'].values
                data = df.drop('label', axis=1).values
            else:
                labels = df.iloc[:, -1].values
                data = df.iloc[:, :-1].values
            
            # 处理多变量数据
            if data.shape[1] > 1:
                data = data[:, 0:1]  # 保持2D格式
            
            print(f"📊 数据: {data.shape[0]}样本, {np.sum(labels)}异常 ({np.mean(labels):.1%})")
            
            # 分割数据
            split_point = int(len(data) * 0.7)
            train_data = data[:split_point]
            test_data = data[split_point:]
            test_labels = labels[split_point:]
            
            # 测试原始HTA-AD
            print("1️⃣ 原始HTA-AD")
            original_model = HTA_AD(optimal_hp)
            original_model.fit(train_data)
            original_scores = original_model.decision_function(test_data)
            
            original_metrics = get_metrics(
                score=original_scores,
                labels=test_labels,
                slidingWindow=100,
                version='opt'
            )
            
            print(f"   VUS-PR: {original_metrics.get('VUS-PR', 0):.4f}")
            print(f"   AUC-ROC: {original_metrics.get('AUC-ROC', 0):.4f}")
            
            # 测试HTA-AD + SAE
            print("2️⃣ HTA-AD + SAE")
            
            # 测试不同净化强度
            best_metrics = original_metrics
            best_strength = 0.0
            
            for strength in [0.3, 0.5, 0.7, 1.0]:
                enhanced_hp = optimal_hp.copy()
                enhanced_hp['purification_strength'] = strength
                
                enhanced_model = HTA_AD_SAE_Enhanced(
                    enhanced_hp, 
                    pretrained_sae=sae_model,
                    irrelevant_indices=irrelevant_indices
                )
                
                enhanced_model.fit(train_data)
                enhanced_scores = enhanced_model.decision_function(test_data)
                
                enhanced_metrics = get_metrics(
                    score=enhanced_scores,
                    labels=test_labels,
                    slidingWindow=100,
                    version='opt'
                )
                
                vus_pr = enhanced_metrics.get('VUS-PR', 0)
                auc_roc = enhanced_metrics.get('AUC-ROC', 0)
                
                print(f"   强度{strength}: VUS-PR {vus_pr:.4f}, AUC-ROC {auc_roc:.4f}")
                
                if vus_pr > best_metrics.get('VUS-PR', 0):
                    best_metrics = enhanced_metrics
                    best_strength = strength
            
            # 计算改进
            original_vus_pr = original_metrics.get('VUS-PR', 0)
            best_vus_pr = best_metrics.get('VUS-PR', 0)
            
            if original_vus_pr > 0:
                improvement = (best_vus_pr - original_vus_pr) / original_vus_pr * 100
            else:
                improvement = 0.0
            
            print(f"🏆 最佳结果: 强度{best_strength}, 改进{improvement:+.1f}%")
            
            results.append({
                'dataset': dataset_name,
                'original_vus_pr': original_vus_pr,
                'best_vus_pr': best_vus_pr,
                'improvement': improvement,
                'best_strength': best_strength
            })
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            continue
    
    # 3. 结果汇总
    print(f"\n📊 基准测试结果汇总")
    print("=" * 100)
    
    if results:
        df_results = pd.DataFrame(results)
        
        print("\n详细结果:")
        for _, row in df_results.iterrows():
            print(f"{row['dataset']:40} | {row['original_vus_pr']:.4f} → {row['best_vus_pr']:.4f} ({row['improvement']:+.1f}%)")
        
        # 统计分析
        avg_improvement = df_results['improvement'].mean()
        positive_improvements = df_results[df_results['improvement'] > 0]
        
        print(f"\n📈 统计分析:")
        print(f"   平均改进: {avg_improvement:+.1f}%")
        print(f"   有效改进数据集: {len(positive_improvements)}/{len(df_results)}")
        
        if len(positive_improvements) > 0:
            print(f"   有效改进平均值: {positive_improvements['improvement'].mean():+.1f}%")
            print(f"   最大改进: {df_results['improvement'].max():+.1f}%")
        
        # 保存结果
        df_results.to_csv('hta_ad_sae_benchmark_results.csv', index=False)
        print(f"\n💾 结果已保存到 'hta_ad_sae_benchmark_results.csv'")
    
    return results

if __name__ == "__main__":
    print("🚀 HTA-AD + SAE 大规模预训练与基准测试")
    print("=" * 120)
    
    results = run_large_scale_benchmark()
    
    print("\n🎉 大规模基准测试完成！")
    print("💡 展示了SAE预训练在时间序列异常检测中的潜力")
    print("🔬 为未来的研究和工业应用提供了重要参考")
