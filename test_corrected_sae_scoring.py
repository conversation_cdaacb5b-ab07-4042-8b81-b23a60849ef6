#!/usr/bin/env python3
"""
Test corrected SAE scoring implementation
"""

import torch
import sys
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
sys.path.append('.')
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

from core.models.hta_ad_paper_compliant import HTAADBasic, PostHocSAE

try:
    from TSB_AD.evaluation.metrics import get_metrics
    TSB_AD_AVAILABLE = True
except ImportError:
    TSB_AD_AVAILABLE = False
    print("TSB-AD not available, using dummy metrics")

def create_sliding_windows(data, window_size, stride=1):
    """Create sliding windows from time series data"""
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    n_samples, n_features = data.shape
    windows = []
    
    for i in range(0, n_samples - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def test_corrected_sae_scoring():
    """Test the corrected SAE scoring implementation"""
    print("🧪 Testing Corrected SAE Scoring Implementation")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load a small test dataset
    dataset_path = "TSB-AD/Datasets/TSB-AD-U/140_MSL_id_1_Sensor_tr_530_1st_630.csv"
    
    if not os.path.exists(dataset_path):
        print("❌ Test dataset not found")
        return
    
    # Load data
    df = pd.read_csv(dataset_path).dropna()
    data = df.iloc[:, 0:-1].values.astype(float)
    labels = df['Label'].astype(int).to_numpy()
    
    # Parse train/test split
    train_size = 530
    train_data = data[:train_size]
    test_data = data[train_size:]
    test_labels = labels[train_size:]
    
    print(f"Train data: {train_data.shape}")
    print(f"Test data: {test_data.shape}")
    print(f"Test anomaly ratio: {np.mean(test_labels):.3f}")
    
    # Normalize
    scaler = StandardScaler()
    train_data = scaler.fit_transform(train_data)
    test_data = scaler.transform(test_data)
    
    # Create models
    model = HTAADBasic(input_dim=1, d_model=32, seq_len=100).to(device)
    sae = PostHocSAE(latent_dim=32, hidden_dim=128).to(device)
    
    # Quick training
    print("\n🔧 Quick training...")
    train_windows = create_sliding_windows(train_data, 100)
    train_windows = torch.FloatTensor(train_windows).to(device)
    
    # Train HTA-AD
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    model.train()
    
    for epoch in range(10):
        total_loss = 0
        for i in range(0, len(train_windows), 32):
            batch = train_windows[i:i + 32]
            optimizer.zero_grad()
            outputs = model(batch)
            losses = model.compute_loss(outputs, batch)
            loss = losses['total']
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        
        if (epoch + 1) % 5 == 0:
            print(f"  Epoch {epoch + 1}, Loss: {total_loss / (len(train_windows) // 32 + 1):.4f}")
    
    # Collect latents and train SAE
    print("\n🔧 Training SAE...")
    model.eval()
    all_latents = []
    
    with torch.no_grad():
        for i in range(0, len(train_windows), 32):
            batch = train_windows[i:i + 32]
            outputs = model(batch)
            latents = outputs['latent_vectors']
            all_latents.append(latents)
    
    all_latents = torch.cat(all_latents, dim=0)
    
    # Train SAE
    sae_optimizer = torch.optim.Adam(sae.parameters(), lr=0.001)
    sae.train()
    
    for epoch in range(10):
        total_loss = 0
        for i in range(0, len(all_latents), 64):
            batch = all_latents[i:i + 64]
            sae_optimizer.zero_grad()
            losses = sae.compute_loss(batch)
            loss = losses['total']
            loss.backward()
            sae_optimizer.step()
            total_loss += loss.item()
        
        if (epoch + 1) % 5 == 0:
            print(f"  SAE Epoch {epoch + 1}, Loss: {total_loss / (len(all_latents) // 64 + 1):.4f}")
    
    # Identify irrelevant features
    sae.identify_irrelevant_features(all_latents, max_irrelevant_ratio=0.25)
    
    # Test scoring
    print("\n📊 Testing scoring methods...")
    model.eval()
    sae.eval()
    
    # Test on a few points
    window_size = 100
    pad_size = window_size // 2
    padded_data = np.pad(test_data, ((pad_size, pad_size), (0, 0)), mode='edge')
    
    hta_scores = []
    sae_scores_old = []  # Old incorrect method
    sae_scores_new = []  # New correct method
    
    with torch.no_grad():
        for i in range(min(100, len(test_data))):  # Test first 100 points
            window = padded_data[i:i + window_size]
            window_tensor = torch.FloatTensor(window).unsqueeze(0).to(device)
            
            # HTA-AD forward pass
            outputs = model(window_tensor)
            reconstruction = outputs['reconstruction']
            latent_vectors = outputs['latent_vectors']
            
            # HTA-AD score (reconstruction error)
            center_idx = window_size // 2
            hta_error = torch.mean((window_tensor[0, center_idx] - reconstruction[0, center_idx]) ** 2)
            hta_scores.append(hta_error.cpu().item())
            
            # OLD SAE method (incorrect - latent space error)
            z_hat, _ = sae(latent_vectors)
            old_sae_error = torch.mean((latent_vectors[0, center_idx] - z_hat[0, center_idx]) ** 2)
            sae_scores_old.append(old_sae_error.cpu().item())
            
            # NEW SAE method (correct - purified reconstruction error)
            z_purified = sae.purify_latent(latent_vectors)
            purified_reconstruction = model.decode(z_purified)
            new_sae_error = torch.mean((window_tensor[0, center_idx] - purified_reconstruction[0, center_idx]) ** 2)
            sae_scores_new.append(new_sae_error.cpu().item())
    
    hta_scores = np.array(hta_scores)
    sae_scores_old = np.array(sae_scores_old)
    sae_scores_new = np.array(sae_scores_new)
    
    print(f"\n📈 Score Statistics:")
    print(f"HTA-AD scores:     mean={hta_scores.mean():.6f}, std={hta_scores.std():.6f}, range=[{hta_scores.min():.6f}, {hta_scores.max():.6f}]")
    print(f"SAE scores (old):  mean={sae_scores_old.mean():.6f}, std={sae_scores_old.std():.6f}, range=[{sae_scores_old.min():.6f}, {sae_scores_old.max():.6f}]")
    print(f"SAE scores (new):  mean={sae_scores_new.mean():.6f}, std={sae_scores_new.std():.6f}, range=[{sae_scores_new.min():.6f}, {sae_scores_new.max():.6f}]")
    
    print(f"\n🔍 Score Comparison:")
    print(f"HTA-AD vs SAE (old): ratio = {sae_scores_old.mean() / hta_scores.mean():.3f}")
    print(f"HTA-AD vs SAE (new): ratio = {sae_scores_new.mean() / hta_scores.mean():.3f}")
    
    # Check if new method produces more reasonable scores
    if abs(sae_scores_new.mean() / hta_scores.mean() - 1.0) < abs(sae_scores_old.mean() / hta_scores.mean() - 1.0):
        print("✅ New SAE scoring method produces more reasonable scores!")
    else:
        print("⚠️  New method may need further adjustment")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    import os
    test_corrected_sae_scoring()
