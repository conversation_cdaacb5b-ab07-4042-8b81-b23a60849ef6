2025-06-17 17:31:29,725 - INFO - 🚀 开始LERN v2.3 全数据集基准测试
2025-06-17 17:31:29,725 - INFO - 💻 使用GPU: 1
2025-06-17 17:31:29,725 - INFO - 📁 数据集目录: ../Datasets/TSB-AD-U/
2025-06-17 17:31:29,725 - INFO - 💾 结果保存到: ./lern_gpu1_small_results/
2025-06-17 17:31:29,725 - INFO - 🔧 超参数配置: {'window_size': 50, 'epochs': 100, 'lr': 0.001, 'batch_size': 64, 'latent_dim': 64, 'poly_degree': 3, 'patience': 10}
2025-06-17 17:31:29,726 - INFO - 📂 总共需要处理 50 个数据集
2025-06-17 17:31:29,727 - INFO - 🚀 开始处理: 001_NAB_id_1_Facility_tr_1007_1st_2014.csv
2025-06-17 17:31:29,730 - INFO -    数据形状: (4031, 1), 异常率: 0.085, 训练集: 1007
2025-06-17 17:31:30,629 - INFO - <PERSON><PERSON> pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:32:15,846 - INFO -    ✅ 成功! 时间: 46.1s, AUC-ROC: 0.7595, AUC-PR: 0.5213
2025-06-17 17:32:15,846 - INFO - 🚀 开始处理: 002_NAB_id_2_WebService_tr_1500_1st_4106.csv
2025-06-17 17:32:15,848 - INFO -    数据形状: (6000, 1), 异常率: 0.106, 训练集: 1500
2025-06-17 17:32:15,886 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:33:20,925 - INFO -    ✅ 成功! 时间: 64.8s, AUC-ROC: 0.3876, AUC-PR: 0.2910
2025-06-17 17:33:20,925 - INFO - 🚀 开始处理: 003_NAB_id_3_WebService_tr_1362_1st_1462.csv
2025-06-17 17:33:20,927 - INFO -    数据形状: (15852, 1), 异常率: 0.096, 训练集: 1362
2025-06-17 17:33:20,965 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:35:33,703 - INFO -    ✅ 成功! 时间: 132.6s, AUC-ROC: 0.5430, AUC-PR: 0.2455
2025-06-17 17:35:33,704 - INFO - 🚀 开始处理: 004_NAB_id_4_Facility_tr_1007_1st_1437.csv
2025-06-17 17:35:33,705 - INFO -    数据形状: (4031, 1), 异常率: 0.100, 训练集: 1007
2025-06-17 17:35:33,751 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:35:50,898 - INFO -    ✅ 成功! 时间: 17.1s, AUC-ROC: 0.7499, AUC-PR: 0.4172
2025-06-17 17:35:50,899 - INFO - 🚀 开始处理: 005_NAB_id_5_Traffic_tr_594_1st_1645.csv
2025-06-17 17:35:50,900 - INFO -    数据形状: (2379, 1), 异常率: 0.100, 训练集: 594
2025-06-17 17:35:50,937 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:36:10,304 - INFO -    ✅ 成功! 时间: 19.4s, AUC-ROC: 0.4203, AUC-PR: 0.1001
2025-06-17 17:36:10,304 - INFO - 🚀 开始处理: 006_NAB_id_6_Traffic_tr_2579_1st_5839.csv
2025-06-17 17:36:10,306 - INFO -    数据形状: (10319, 1), 异常率: 0.100, 训练集: 2579
2025-06-17 17:36:10,342 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:38:00,532 - INFO -    ✅ 成功! 时间: 109.4s, AUC-ROC: 0.7022, AUC-PR: 0.2796
2025-06-17 17:38:00,533 - INFO - 🚀 开始处理: 007_NAB_id_7_Traffic_tr_624_1st_2087.csv
2025-06-17 17:38:00,534 - INFO -    数据形状: (2499, 1), 异常率: 0.099, 训练集: 624
2025-06-17 17:38:00,571 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:38:21,863 - INFO -    ✅ 成功! 时间: 21.0s, AUC-ROC: 0.6816, AUC-PR: 0.5116
2025-06-17 17:38:21,863 - INFO - 🚀 开始处理: 008_NAB_id_8_Synthetic_tr_1007_1st_2734.csv
2025-06-17 17:38:21,864 - INFO -    数据形状: (4031, 1), 异常率: 0.100, 训练集: 1007
2025-06-17 17:38:21,901 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:39:06,867 - INFO -    ✅ 成功! 时间: 44.9s, AUC-ROC: 0.7796, AUC-PR: 0.5590
2025-06-17 17:39:06,867 - INFO - 🚀 开始处理: 009_NAB_id_9_Traffic_tr_500_1st_438.csv
2025-06-17 17:39:06,868 - INFO -    数据形状: (2161, 1), 异常率: 0.100, 训练集: 500
2025-06-17 17:39:06,907 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:39:28,602 - INFO -    ✅ 成功! 时间: 21.5s, AUC-ROC: 0.5894, AUC-PR: 0.1694
2025-06-17 17:39:28,602 - INFO - 🚀 开始处理: 010_NAB_id_10_WebService_tr_500_1st_271.csv
2025-06-17 17:39:28,603 - INFO -    数据形状: (1000, 1), 异常率: 0.148, 训练集: 500
2025-06-17 17:39:28,642 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:39:38,643 - INFO -    ✅ 成功! 时间: 9.6s, AUC-ROC: 0.9967, AUC-PR: 0.9798
2025-06-17 17:39:38,645 - INFO - 💾 已保存批次 1 的中间结果 (10/50)
2025-06-17 17:39:38,645 - INFO - 🚀 开始处理: 011_NAB_id_11_Facility_tr_1007_1st_1526.csv
2025-06-17 17:39:38,646 - INFO -    数据形状: (4031, 1), 异常率: 0.118, 训练集: 1007
2025-06-17 17:39:38,682 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:40:24,344 - INFO -    ✅ 成功! 时间: 45.3s, AUC-ROC: 0.8245, AUC-PR: 0.7014
2025-06-17 17:40:24,345 - INFO - 🚀 开始处理: 012_NAB_id_12_Synthetic_tr_1007_1st_2787.csv
2025-06-17 17:40:24,346 - INFO -    数据形状: (4031, 1), 异常率: 0.127, 训练集: 1007
2025-06-17 17:40:24,382 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-17 17:40:54,269 - WARNING - ⚠️ 用户中断测试，保存当前结果...
2025-06-17 17:40:54,270 - INFO - ⏱️ 总测试时间: 564.5s (0.2h)
2025-06-17 17:40:54,276 - INFO - 
================================================================================
2025-06-17 17:40:54,276 - INFO - 🏆 LERN v2.3 全数据集基准测试报告
2025-06-17 17:40:54,276 - INFO - ================================================================================
2025-06-17 17:40:54,276 - INFO - 📊 测试概况:
2025-06-17 17:40:54,276 - INFO -    总数据集: 11
2025-06-17 17:40:54,276 - INFO -    成功测试: 11
2025-06-17 17:40:54,276 - INFO -    成功率: 100.00%
2025-06-17 17:40:54,276 - INFO -    总运行时间: 531.5s (0.1h)
2025-06-17 17:40:54,276 - INFO -    平均运行时间: 48.3s
2025-06-17 17:40:54,276 - INFO - 
📈 性能指标 (平均值):
2025-06-17 17:40:54,276 - INFO -    AUC-ROC: 0.6758 ± 0.1796
2025-06-17 17:40:54,276 - INFO -    AUC-PR: 0.4342 ± 0.2569
2025-06-17 17:40:54,276 - INFO -    VUS-ROC: 0.7123 ± 0.1788
2025-06-17 17:40:54,276 - INFO -    VUS-PR: 0.4491 ± 0.2590
2025-06-17 17:40:54,276 - INFO -    Affiliation-F: 0.8401 ± 0.1593
2025-06-17 17:40:54,276 - INFO - 
📂 按数据集类型统计:
2025-06-17 17:40:54,276 - INFO -    1: 1个数据集, 平均AUC-ROC: 0.7595, 平均AUC-PR: 0.5213
2025-06-17 17:40:54,276 - INFO -    2: 1个数据集, 平均AUC-ROC: 0.3876, 平均AUC-PR: 0.2910
2025-06-17 17:40:54,277 - INFO -    3: 1个数据集, 平均AUC-ROC: 0.5430, 平均AUC-PR: 0.2455
2025-06-17 17:40:54,277 - INFO -    4: 1个数据集, 平均AUC-ROC: 0.7499, 平均AUC-PR: 0.4172
2025-06-17 17:40:54,277 - INFO -    5: 1个数据集, 平均AUC-ROC: 0.4203, 平均AUC-PR: 0.1001
2025-06-17 17:40:54,277 - INFO -    6: 1个数据集, 平均AUC-ROC: 0.7022, 平均AUC-PR: 0.2796
2025-06-17 17:40:54,277 - INFO -    7: 1个数据集, 平均AUC-ROC: 0.6816, 平均AUC-PR: 0.5116
2025-06-17 17:40:54,277 - INFO -    8: 1个数据集, 平均AUC-ROC: 0.7796, 平均AUC-PR: 0.5590
2025-06-17 17:40:54,277 - INFO -    9: 1个数据集, 平均AUC-ROC: 0.5894, 平均AUC-PR: 0.1694
2025-06-17 17:40:54,277 - INFO -    10: 1个数据集, 平均AUC-ROC: 0.9967, 平均AUC-PR: 0.9798
2025-06-17 17:40:54,277 - INFO -    11: 1个数据集, 平均AUC-ROC: 0.8245, 平均AUC-PR: 0.7014
2025-06-17 17:40:54,277 - INFO - 
📁 结果文件:
2025-06-17 17:40:54,277 - INFO -    详细结果: ./lern_gpu1_small_results/lern_full_benchmark_results_20250617_174054.csv
2025-06-17 17:40:54,277 - INFO -    汇总统计: ./lern_gpu1_small_results/lern_full_benchmark_summary_20250617_174054.json
2025-06-17 17:40:54,277 - INFO -    最新结果: ./lern_gpu1_small_results/latest_results.csv
2025-06-17 17:40:54,277 - INFO - 
🏅 性能最佳的数据集:
2025-06-17 17:40:54,277 - INFO -    AUC-ROC最佳: 010_NAB_id_10_WebService_tr_500_1st_271.csv (AUC-ROC: 0.9967)
2025-06-17 17:40:54,277 - INFO -    AUC-PR最佳: 010_NAB_id_10_WebService_tr_500_1st_271.csv (AUC-PR: 0.9798)
