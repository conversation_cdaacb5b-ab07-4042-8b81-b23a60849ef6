#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LERN v2.3 小规模基准测试脚本 (测试前10个数据集)
"""

import subprocess
import sys
import os

def main():
    """运行小规模基准测试"""
    
    print("🧪 开始LERN v2.3 小规模基准测试 (前10个数据集)")
    print("=" * 60)
    
    # 构建命令
    cmd = [
        sys.executable, "run_lern_full_benchmark.py",
        "--max_files", "10",
        "--batch_size", "5",
        "--gpu", "0",
        "--save_dir", "./lern_small_test_results/"
    ]
    
    # 运行测试
    try:
        result = subprocess.run(cmd, check=False)
        
        if result.returncode == 0:
            print("\n✅ 小规模测试完成！")
            print("📁 结果保存在: ./lern_small_test_results/")
            print("📊 请查看 latest_results.csv 和 latest_summary.json")
        else:
            print(f"\n❌ 测试失败，返回码: {result.returncode}")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n💥 发生异常: {str(e)}")

if __name__ == "__main__":
    main() 