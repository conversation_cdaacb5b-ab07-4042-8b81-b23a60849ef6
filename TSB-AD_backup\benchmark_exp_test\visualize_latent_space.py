import os
import sys
import torch
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.io as pio
from plotly.subplots import make_subplots
from sklearn.manifold import TSNE
from torch.utils.data import TensorDataset, DataLoader

# --- Path Setup ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Model and Data Imports ---
from TSB_AD.models.AnomalyTransformer import AnomalyTransformer
from benchmark_exp.hta_ad import HTA_AD

def load_dataset_from_path(dataset_path):
    """Loads a dataset and splits it into train and test sets."""
    if not os.path.exists(dataset_path):
        return None, None, None
        
    df = pd.read_csv(dataset_path)
    
    # Heuristic to find split point. Many datasets in this repo
    # indicate train size in the filename, e.g., _tr_1234_
    try:
        train_size_str = os.path.basename(dataset_path).split('_')[-3]
        split_point = int(train_size_str)
    except (ValueError, IndexError):
        # Fallback for filenames that don't match the pattern
        split_point = int(len(df) * 0.5)

    train_df = df.iloc[:split_point]
    test_df = df.iloc[split_point:]
    
    return train_df.values[:, 0].astype(np.float32), \
           test_df.values[:, 0].astype(np.float32), \
           test_df.values[:, 1].astype(int)


def get_latent_vectors_and_errors(model, X, window_size, device):
    """
    Extracts latent vectors and reconstruction errors for each window in X.
    """
    internal_model = model.model
    if internal_model is None:
        raise ValueError("Model's internal_model is not initialized.")
    internal_model.eval()

    # Create windows
    windows = np.array([X[i:i + window_size] for i in range(len(X) - window_size + 1)])
    if len(windows) == 0:
        return np.array([]), np.array([])
        
    windows_tensor = torch.from_numpy(windows).unsqueeze(-1)
    dataset = TensorDataset(windows_tensor)
    loader = DataLoader(dataset, batch_size=128, shuffle=False)

    all_latents = []
    all_errors = []

    with torch.no_grad():
        for (batch_windows,) in loader:
            batch_windows = batch_windows.to(device)
            
            if isinstance(model, AnomalyTransformer):
                # Run the encoder part to get the latent vector representation
                embedded = internal_model.embedding(batch_windows)
                latent_repr, _, _, _ = internal_model.encoder(embedded)
                
                # Run the projection on the latent representation to get the reconstruction
                recons = internal_model.projection(latent_repr)
                
                # For t-SNE, we need a single vector per window, so we average
                latent = latent_repr.mean(dim=1)

            elif isinstance(model, HTA_AD):
                # Run the encoder part to get the latent vector
                x_permuted = batch_windows.permute(0, 2, 1)
                enc_cnn = internal_model.encoder_cnn(x_permuted)
                enc_tcn = internal_model.encoder_tcn(enc_cnn)
                flattened = enc_tcn.reshape(enc_tcn.shape[0], -1)
                latent = internal_model.fc_encode(flattened)

                # Run the full forward pass to get the reconstruction
                recons = internal_model(batch_windows)
            else:
                raise TypeError(f"Model type {type(model)} not supported.")
            
            # By now, `recons` is guaranteed to be a tensor for all models.
            error = torch.mean((batch_windows - recons)**2, dim=(1, 2))

            all_latents.append(latent.cpu().numpy())
            all_errors.append(error.cpu().numpy())

    return np.concatenate(all_latents), np.concatenate(all_errors)


def main():
    """
    Main function to run the latent space visualization.
    """
    DATASET_NAME = '836_Exathlon_id_27_Facility_tr_10766_1st_12590'
    
    # --- Data Loading ---
    print(f"Loading dataset: {DATASET_NAME}")
    dataset_path_u = os.path.join('Datasets/TSB-AD-U/', DATASET_NAME + '.csv')
    dataset_path_m = os.path.join('Datasets/TSB-AD-M/', DATASET_NAME + '.csv')

    if os.path.exists(dataset_path_u):
        full_path = dataset_path_u
    elif os.path.exists(dataset_path_m):
        full_path = dataset_path_m
    else:
        print(f"Error: Dataset '{DATASET_NAME}.csv' not found in TSB-AD-U or TSB-AD-M directories.")
        return

    train_data, test_data, labels = load_dataset_from_path(full_path)
    full_series = np.concatenate([train_data, test_data])
    
    # --- Model Training and Latent Vector Extraction ---
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    models_to_analyze = {
        'HTA_AD': HTA_AD(HP={'window_size': 128, 'epochs': 10}),
        'AnomalyTransformer': AnomalyTransformer(win_size=128, num_epochs=10, device=device)
    }

    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=list(models_to_analyze.keys()),
        horizontal_spacing=0.08
    )

    for i, (model_name, model_instance) in enumerate(models_to_analyze.items()):
        print(f"--- Processing {model_name} ---")
        
        # Train model
        print("Training model...")
        train_data_reshaped = train_data.reshape(-1, 1)
        model_instance.fit(train_data_reshaped)
        
        # Get latent vectors
        print("Extracting latent vectors and reconstruction errors...")
        current_window_size = model_instance.win_size if isinstance(model_instance, AnomalyTransformer) else model_instance.window_size
        latents, errors = get_latent_vectors_and_errors(
            model_instance, full_series, current_window_size, device
        )

        if latents.shape[0] == 0:
            print("No latent vectors generated. Skipping.")
            continue
        
        # Dimensionality Reduction
        print("Performing t-SNE...")
        tsne = TSNE(n_components=2, perplexity=40, max_iter=1000, random_state=42)
        latents_2d = tsne.fit_transform(latents)
        
        # Plotting
        print("Plotting results...")
        fig.add_trace(
            go.Scatter(
                x=latents_2d[:, 0],
                y=latents_2d[:, 1],
                mode='markers',
                marker=dict(
                    color=errors,
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(
                        title=dict(
                           text="Reconstruction<br>Error (MSE)",
                           side="right"
                        )
                    ),
                    size=8,
                    opacity=0.8,
                    line_width=0
                ),
                showlegend=False
            ),
            row=1, col=i + 1
        )

    # --- Final Layout and Styling ---
    fig.update_layout(
        title_text='<b>Latent Space Structure vs. Reconstruction Error</b>',
        title_x=0.5,
        font=dict(size=14, family="Times New Roman"),
        template='plotly_white',
        width=2000,
        height=800,
    )
    
    fig.update_xaxes(title_text="t-SNE Dimension 1", row=1, col=1)
    fig.update_yaxes(title_text="t-SNE Dimension 2", row=1, col=1)
    fig.update_xaxes(title_text="t-SNE Dimension 1", row=1, col=2)
    fig.update_yaxes(title_text="t-SNE Dimension 2", row=1, col=2)

    # --- Save Figure ---
    output_dir = 'visualizations/latent_space_comparison/'
    os.makedirs(output_dir, exist_ok=True)
    
    output_filename = f'latent_space_by_error_{DATASET_NAME}_plotly.png'
    output_path = os.path.join(output_dir, output_filename)
    pio.write_image(fig, output_path, scale=2)
    
    print(f"\nAnalysis complete. Plot saved to: {output_path}")

if __name__ == '__main__':
    main() 