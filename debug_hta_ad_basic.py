#!/usr/bin/env python3
"""
调试基础HTA-AD问题
"""

import numpy as np
import sys
import traceback
import warnings
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

def debug_hta_ad():
    """调试基础HTA-AD"""
    print("🔍 调试基础HTA-AD")
    
    try:
        from TSB_AD.models.HTA_AD import HTA_AD
        
        # 生成简单测试数据
        np.random.seed(42)
        train_data = np.sin(np.linspace(0, 4*np.pi, 200)) + 0.1 * np.random.randn(200)
        test_data = np.sin(np.linspace(4*np.pi, 8*np.pi, 100)) + 0.1 * np.random.randn(100)
        
        print(f"训练数据形状: {train_data.shape}")
        print(f"测试数据形状: {test_data.shape}")
        
        # 确保数据是2D的
        if train_data.ndim == 1:
            train_data = train_data.reshape(-1, 1)
        if test_data.ndim == 1:
            test_data = test_data.reshape(-1, 1)
            
        print(f"调整后训练数据形状: {train_data.shape}")
        print(f"调整后测试数据形状: {test_data.shape}")
        
        # 创建模型
        HP = {
            'window_size': 32,
            'epochs': 3,
            'lr': 1e-3,
            'batch_size': 16,
            'latent_dim': 8,
            'tcn_channels': [16, 16],
            'cnn_channels': 8,
            'downsample_stride': 2,
            'gpu': 0
        }
        
        print("创建HTA-AD模型...")
        model = HTA_AD(HP=HP, normalize=True)
        
        print("训练模型...")
        model.fit(train_data)
        
        print("计算异常分数...")
        scores = model.decision_function(test_data)
        
        print(f"✅ 成功！异常分数形状: {scores.shape}")
        print(f"分数范围: [{scores.min():.4f}, {scores.max():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 失败: {e}")
        traceback.print_exc()
        return False


def debug_wrapper():
    """调试包装器函数"""
    print("\n🔍 调试包装器函数")
    
    try:
        from TSB_AD.model_wrapper import run_HTA_AD
        
        # 生成简单测试数据
        np.random.seed(42)
        train_data = np.sin(np.linspace(0, 4*np.pi, 200)) + 0.1 * np.random.randn(200)
        test_data = np.sin(np.linspace(4*np.pi, 8*np.pi, 100)) + 0.1 * np.random.randn(100)
        
        print(f"训练数据形状: {train_data.shape}")
        print(f"测试数据形状: {test_data.shape}")
        
        print("运行包装器函数...")
        scores = run_HTA_AD(
            train_data, test_data,
            window_size=32,
            epochs=3,
            lr=1e-3,
            batch_size=16,
            latent_dim=8
        )
        
        print(f"✅ 成功！异常分数形状: {scores.shape}")
        print(f"分数范围: [{scores.min():.4f}, {scores.max():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 失败: {e}")
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("🚀 HTA-AD 基础调试")
    print("=" * 50)
    
    # 测试1: 直接使用模型类
    success1 = debug_hta_ad()
    
    # 测试2: 使用包装器函数
    success2 = debug_wrapper()
    
    print("\n" + "=" * 50)
    print("🎯 调试结果总结")
    print("=" * 50)
    print(f"直接模型使用: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"包装器函数: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
