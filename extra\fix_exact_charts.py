#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据用户提供的图表精确修复小数位数显示
将0.387修正为0.39，其他数值统一保留两位小数
"""

import matplotlib.pyplot as plt
import numpy as np

# 设置字体和样式
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 11
plt.rcParams['figure.dpi'] = 300

def create_multivariate_chart_exact():
    """根据用户图片精确重建多变量图表"""
    
    # 从用户图片中提取的精确数据，将0.387改为0.39
    methods = ['HTA_AD', 'LSTMED', 'OmniAnomaly', 'CNN', 'PCA', 'USAD', 'AutoEncoder', 
               'KMeansAD', 'CBLOF', 'MCD', 'OCSVM', 'Donut', 'RobustPCA', 'DIF', 
               'EFA', 'FITS', 'ConvTAD', 'Telemanom', 'HBOS', 'TimesNet', 'KNN', 
               'TranAD', 'LOF', 'AnomalyTransformer']
    
    # 原始数值，关键修改：0.387 → 0.39
    values = [0.39, 0.31, 0.31, 0.31, 0.31, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 
              0.24, 0.21, 0.21, 0.21, 0.20, 0.20, 0.19, 0.19, 0.18, 0.18, 0.14, 0.12]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(15, 6))
    
    # 创建柱状图
    colors = ['#C0392B' if method == 'HTA_AD' else '#5D6D7E' for method in methods]
    bars = ax.bar(range(len(methods)), values, color=colors, alpha=0.85, width=0.8)
    
    # 在每个柱子上添加数值标签，统一保留两位小数
    for i, (bar, value) in enumerate(zip(bars, values)):
        height = bar.get_height()
        # 特别标注HTA_AD的修改
        if methods[i] == 'HTA_AD':
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.008,
                    f'{value:.2f}', ha='center', va='bottom', 
                    fontsize=11, fontweight='bold', color='#C0392B')
        else:
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{value:.2f}', ha='center', va='bottom', 
                    fontsize=10, fontweight='bold', color='#2C3E50')
    
    # 设置图表样式
    ax.set_ylabel('VUS-PR Score', fontsize=13, fontweight='bold')
    ax.set_xlabel('Method', fontsize=13, fontweight='bold')
    ax.set_ylim(0, 0.42)
    
    # 设置x轴标签
    ax.set_xticks(range(len(methods)))
    ax.set_xticklabels(methods, rotation=45, ha='right', fontsize=10)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='y', linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置背景色
    ax.set_facecolor('#FAFAFA')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('figures/bar_chart_tsb-ad-m.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    print("✅ 多变量图表已更新：0.387 → 0.39")

def create_univariate_chart_exact():
    """根据用户图片精确重建单变量图表"""
    
    # 从用户图片中提取的精确数据
    methods = ['HTA_AD', 'Sub-PCA', 'KShapeAD', 'POI-GPD', 'SampledDCNN', 'MSCRED', 
               'MSCRED(FT)', 'NormalizingFlow', 'USAD', 'Sub-LOF', 'AutoEncoder', 'STAMP', 
               'CNN', 'LSTMED', 'IForest', 'TimesNet', 'Donut', 'RobustPCA', 'Telemanom', 
               'AutoRegression', 'TimesNet', 'AutoLSTM', 'TranAD', 'FITS', 'Sub-HBOS', 
               'EFA', 'Sub-KNN', 'Sub-OCSVM', 'Sub-LOF', 'Sub-IForest', 'Donut', 'LOF', 
               'AnomalyTransformer']
    
    values = [0.44, 0.42, 0.40, 0.39, 0.39, 0.39, 0.38, 0.37, 0.36, 0.35, 0.35, 0.34, 
              0.34, 0.33, 0.32, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 0.26, 0.26, 
              0.25, 0.24, 0.24, 0.23, 0.23, 0.22, 0.20, 0.17, 0.12]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(18, 6))
    
    # 创建柱状图
    colors = ['#C0392B' if method == 'HTA_AD' else '#5D6D7E' for method in methods]
    bars = ax.bar(range(len(methods)), values, color=colors, alpha=0.85, width=0.8)
    
    # 在每个柱子上添加数值标签，统一保留两位小数
    for i, (bar, value) in enumerate(zip(bars, values)):
        height = bar.get_height()
        if methods[i] == 'HTA_AD':
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.008,
                    f'{value:.2f}', ha='center', va='bottom', 
                    fontsize=11, fontweight='bold', color='#C0392B')
        else:
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{value:.2f}', ha='center', va='bottom', 
                    fontsize=9, fontweight='bold', color='#2C3E50')
    
    # 设置图表样式
    ax.set_ylabel('VUS-PR Score', fontsize=13, fontweight='bold')
    ax.set_xlabel('Method', fontsize=13, fontweight='bold')
    ax.set_ylim(0, 0.48)
    
    # 设置x轴标签
    ax.set_xticks(range(len(methods)))
    ax.set_xticklabels(methods, rotation=45, ha='right', fontsize=9)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='y', linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置背景色
    ax.set_facecolor('#FAFAFA')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('figures/bar_chart_tsb-ad-u.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    print("✅ 单变量图表已更新：所有数值统一两位小数")

if __name__ == "__main__":
    print("🔧 根据用户图片精确修复图表...")
    print("=" * 60)
    
    # 创建figures目录
    import os
    os.makedirs('figures', exist_ok=True)
    
    # 生成修正后的图表
    create_multivariate_chart_exact()
    print()
    create_univariate_chart_exact()
    
    print("\n" + "=" * 60)
    print("✅ 图表修复完成！")
    print("\n📋 修改总结:")
    print("   🔸 多变量图表：HTA-AD从0.387修正为0.39")
    print("   🔸 所有数值：统一保留两位小数格式")
    print("   🔸 视觉样式：保持与原图一致")
    print("\n📁 输出文件:")
    print("   📊 figures/bar_chart_tsb-ad-m.png")
    print("   📊 figures/bar_chart_tsb-ad-u.png")
    print("\n💡 这些文件可以直接替换论文中的原图表")
