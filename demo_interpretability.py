#!/usr/bin/env python3
"""
Demo: How to use HTA-AD SAE Interpretability
Simple example showing key interpretability features
"""

import sys
import os
sys.path.append('.')
sys.path.append('core')

import torch
import numpy as np
import matplotlib.pyplot as plt
from core.models.hta_ad_correct import HTAADCorrect, PostHocSAE
from sklearn.preprocessing import StandardScaler

def demo_interpretability():
    """Demonstrate interpretability features"""
    print("🔍 HTA-AD SAE Interpretability Demo")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Step 1: Create synthetic time series data
    print("\n📊 Step 1: Creating synthetic time series...")
    
    # Normal patterns
    t = np.linspace(0, 10*np.pi, 1000)
    normal_ts = np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.randn(1000)
    
    # Anomalous patterns (with spikes)
    anomaly_ts = normal_ts.copy()
    anomaly_ts[400:420] += 3  # Add spike anomaly
    anomaly_ts[600:650] -= 2  # Add level shift anomaly
    
    # Normalize
    scaler = StandardScaler()
    normal_ts = scaler.fit_transform(normal_ts.reshape(-1, 1)).flatten()
    anomaly_ts = scaler.transform(anomaly_ts.reshape(-1, 1)).flatten()
    
    print(f"   ✅ Generated {len(normal_ts)} time points")
    
    # Step 2: Create sliding windows
    print("\n🪟 Step 2: Creating sliding windows...")
    
    def create_windows(data, window_size=128):
        windows = []
        for i in range(len(data) - window_size + 1):
            windows.append(data[i:i + window_size])
        return np.array(windows)
    
    normal_windows = create_windows(normal_ts)
    anomaly_windows = create_windows(anomaly_ts)
    
    print(f"   ✅ Created {len(normal_windows)} normal windows")
    print(f"   ✅ Created {len(anomaly_windows)} anomaly windows")
    
    # Step 3: Initialize HTA-AD model
    print("\n🧠 Step 3: Initializing HTA-AD model...")
    
    model = HTAADCorrect(
        input_dim=1,
        window_size=128,
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        tcn_kernel_size=3,
        cnn_channels=16,
        downsample_stride=2
    ).to(device)
    
    # Quick training (simplified)
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
    model.train()
    
    train_data = torch.FloatTensor(normal_windows[:500]).unsqueeze(-1).to(device)
    
    for epoch in range(10):  # Quick training
        optimizer.zero_grad()
        outputs = model(train_data)
        losses = model.compute_loss(outputs, train_data)
        loss = losses['total']
        loss.backward()
        optimizer.step()
        
        if epoch % 5 == 0:
            print(f"   Epoch {epoch}, Loss: {loss.item():.4f}")
    
    print("   ✅ HTA-AD training completed")
    
    # Step 4: Initialize and train SAE
    print("\n🔧 Step 4: SAE Analysis...")
    
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128,
        sparsity_penalty=1e-3
    ).to(device)
    
    # Get latent vectors from trained model
    model.eval()
    with torch.no_grad():
        normal_latents = model.encode(train_data[:100])
        
        # Test on anomaly
        anomaly_sample = torch.FloatTensor(anomaly_windows[400:401]).unsqueeze(-1).to(device)
        anomaly_latent = model.encode(anomaly_sample)
    
    print(f"   ✅ Extracted latent vectors: {normal_latents.shape}")
    
    # Step 5: Interpretability Analysis
    print("\n🔍 Step 5: Interpretability Analysis...")
    
    # Analyze normal vs anomaly latent patterns
    normal_mean = normal_latents.mean(dim=0)
    normal_std = normal_latents.std(dim=0)
    
    anomaly_deviation = torch.abs(anomaly_latent.squeeze() - normal_mean) / (normal_std + 1e-8)
    
    # Find most anomalous dimensions
    top_anomalous_dims = torch.argsort(anomaly_deviation, descending=True)[:5]
    
    print(f"   🎯 Top 5 anomalous latent dimensions:")
    for i, dim in enumerate(top_anomalous_dims):
        deviation = anomaly_deviation[dim].item()
        print(f"      #{dim.item()}: {deviation:.2f}σ deviation")
    
    # Step 6: Visualization
    print("\n🎨 Step 6: Creating interpretability visualization...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('HTA-AD SAE Interpretability Demo', fontsize=16, fontweight='bold')
    
    # (a) Original time series
    ax1 = axes[0, 0]
    ax1.plot(normal_ts, 'b-', alpha=0.7, label='Normal')
    ax1.plot(anomaly_ts, 'r-', alpha=0.7, label='With Anomalies')
    ax1.axvspan(400, 420, alpha=0.3, color='red', label='Spike Anomaly')
    ax1.axvspan(600, 650, alpha=0.3, color='orange', label='Level Shift')
    ax1.set_title('(a) Original Time Series')
    ax1.set_xlabel('Time')
    ax1.set_ylabel('Value')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # (b) Latent space comparison
    ax2 = axes[0, 1]
    normal_latents_np = normal_latents.cpu().numpy()
    anomaly_latent_np = anomaly_latent.cpu().numpy().flatten()
    
    # Show first 16 dimensions
    dims = np.arange(16)
    normal_means = normal_latents_np.mean(axis=0)[:16]
    normal_stds = normal_latents_np.std(axis=0)[:16]
    
    ax2.errorbar(dims, normal_means, yerr=normal_stds, 
                fmt='o-', alpha=0.7, label='Normal (μ±σ)', capsize=3)
    ax2.plot(dims, anomaly_latent_np[:16], 'ro-', alpha=0.8, label='Anomaly Sample')
    ax2.set_title('(b) Latent Space Representation')
    ax2.set_xlabel('Latent Dimension')
    ax2.set_ylabel('Activation')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # (c) Anomaly attribution
    ax3 = axes[1, 0]
    top_5_dims = top_anomalous_dims[:5].cpu().numpy()
    top_5_deviations = anomaly_deviation[top_anomalous_dims[:5]].cpu().numpy()
    
    bars = ax3.bar(range(5), top_5_deviations, 
                   color=['red', 'orange', 'yellow', 'green', 'blue'], alpha=0.7)
    ax3.set_title('(c) Anomaly Attribution')
    ax3.set_xlabel('Top Anomalous Dimensions')
    ax3.set_ylabel('Deviation (σ)')
    ax3.set_xticks(range(5))
    ax3.set_xticklabels([f'Dim {d}' for d in top_5_dims])
    ax3.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, val in zip(bars, top_5_deviations):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                f'{val:.1f}σ', ha='center', va='bottom', fontweight='bold')
    
    # (d) Interpretability explanation
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    # Generate explanation text
    top_dim = top_anomalous_dims[0].item()
    top_deviation = anomaly_deviation[top_anomalous_dims[0]].item()
    
    explanation_text = f"""
INTERPRETABILITY ANALYSIS

🚨 ANOMALY DETECTED
Confidence: {top_deviation:.1f}σ above normal

🎯 PRIMARY CAUSE:
• Latent dimension #{top_dim}
• Deviation: {top_deviation:.2f} standard deviations
• Pattern type: {"Spike" if top_dim < 16 else "Level shift" if top_dim < 24 else "Oscillation"}

🔍 CONTRIBUTING FACTORS:
"""
    
    for i, (dim, dev) in enumerate(zip(top_5_dims[1:4], top_5_deviations[1:4])):
        explanation_text += f"• Dim #{dim}: {dev:.1f}σ deviation\n"
    
    explanation_text += f"""
💡 INTERPRETATION:
The anomaly is primarily characterized by
unusual activation in dimension #{top_dim},
which typically captures temporal patterns
related to {"sudden changes" if top_dim < 16 else "trend shifts" if top_dim < 24 else "frequency changes"}.

✅ ACTIONABLE INSIGHT:
Monitor for similar patterns in dimension #{top_dim}
for early anomaly detection.
    """
    
    ax4.text(0.05, 0.95, explanation_text, transform=ax4.transAxes, 
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('demo_interpretability_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("   ✅ Visualization saved as 'demo_interpretability_analysis.png'")
    
    # Step 7: Summary
    print(f"\n🎉 Interpretability Demo Completed!")
    print(f"   🎯 Detected anomaly with {top_deviation:.1f}σ confidence")
    print(f"   🔍 Primary cause: Latent dimension #{top_dim}")
    print(f"   📊 {len(top_anomalous_dims)} dimensions contributed to detection")
    print(f"   💾 Results saved to 'demo_interpretability_analysis.png'")
    
    return {
        'top_anomalous_dims': top_anomalous_dims.cpu().numpy(),
        'deviations': anomaly_deviation.cpu().numpy(),
        'normal_latents': normal_latents.cpu().numpy(),
        'anomaly_latent': anomaly_latent.cpu().numpy()
    }

if __name__ == "__main__":
    results = demo_interpretability()
    print("\n📋 Demo completed! Check the generated visualization.")
