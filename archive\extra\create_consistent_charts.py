#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建视觉一致的并排柱状图
解决论文中Figure 2的高度不匹配问题
"""

import matplotlib.pyplot as plt
import numpy as np
import os

# 设置全局样式参数，确保两个图表完全一致
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 11
plt.rcParams['figure.dpi'] = 300
plt.rcParams['axes.linewidth'] = 1.0
plt.rcParams['grid.linewidth'] = 0.5

def create_consistent_charts():
    """创建视觉完全一致的单变量和多变量基准测试图表，优化用于学术论文双栏布局"""

    # 针对学术论文优化的统一参数
    FIGURE_HEIGHT = 4.5      # 适合双栏布局的高度
    FIGURE_WIDTH_RATIO = 0.35  # 宽度比例因子
    Y_MAX = 0.50            # 统一y轴最大值
    BAR_WIDTH = 0.75        # 优化的柱子宽度
    LABEL_FONTSIZE = 9      # 适合论文的标签字体
    VALUE_FONTSIZE = 8      # 适合论文的数值字体
    AXIS_FONTSIZE = 10      # 轴标签字体大小

    # 统一的颜色方案
    HTA_COLOR = '#E74C3C'    # HTA-AD红色
    OTHER_COLOR = '#7F8C8D'  # 其他方法灰色
    GRID_ALPHA = 0.3         # 网格透明度
    
    # === 单变量数据 ===
    uni_methods = ['HTA_AD', 'Sub-PCA', 'KShapeAD', 'POI-GPD', 'SampledDCNN', 'MSCRED', 
                   'MSCRED(FT)', 'NormalizingFlow', 'USAD', 'Sub-LOF', 'AutoEncoder', 'STAMP', 
                   'CNN', 'LSTMED', 'IForest', 'TimesNet', 'Donut', 'RobustPCA', 'Telemanom', 
                   'AutoRegression', 'AutoLSTM', 'TranAD', 'FITS', 'Sub-HBOS', 'EFA', 
                   'Sub-KNN', 'Sub-OCSVM', 'Sub-LOF2', 'Sub-IForest2', 'LOF', 'AnomalyTransformer']
    
    uni_values = [0.44, 0.42, 0.40, 0.39, 0.39, 0.39, 0.38, 0.37, 0.36, 0.35, 0.35, 0.34, 
                  0.34, 0.33, 0.32, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 0.26, 0.25, 
                  0.24, 0.24, 0.23, 0.23, 0.22, 0.20, 0.12]
    
    # === 多变量数据 ===
    multi_methods = ['HTA_AD', 'LSTMED', 'OmniAnomaly', 'CNN', 'PCA', 'USAD', 'AutoEncoder', 
                     'KMeansAD', 'CBLOF', 'MCD', 'OCSVM', 'Donut', 'RobustPCA', 'DIF', 
                     'EFA', 'FITS', 'ConvTAD', 'Telemanom', 'HBOS', 'TimesNet', 'KNN', 
                     'TranAD', 'LOF', 'AnomalyTransformer']
    
    # 关键修正：0.387 → 0.39
    multi_values = [0.39, 0.31, 0.31, 0.31, 0.31, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 
                    0.24, 0.21, 0.21, 0.21, 0.20, 0.20, 0.19, 0.19, 0.18, 0.18, 0.14, 0.12]
    
    # 计算适合双栏布局的宽度
    uni_width = len(uni_methods) * FIGURE_WIDTH_RATIO  # 单变量图表宽度
    multi_width = len(multi_methods) * FIGURE_WIDTH_RATIO  # 多变量图表宽度

    # 确保两个图表高度完全一致，宽度按内容调整
    print(f"📐 图表尺寸: 单变量({uni_width:.1f}×{FIGURE_HEIGHT}), 多变量({multi_width:.1f}×{FIGURE_HEIGHT})")

    # === 创建单变量图表 ===
    fig1, ax1 = plt.subplots(figsize=(uni_width, FIGURE_HEIGHT))
    
    # 创建柱状图
    uni_colors = [HTA_COLOR if method == 'HTA_AD' else OTHER_COLOR for method in uni_methods]
    bars1 = ax1.bar(range(len(uni_methods)), uni_values, color=uni_colors, 
                    alpha=0.85, width=BAR_WIDTH, edgecolor='white', linewidth=0.5)
    
    # 添加数值标签
    for i, (bar, value) in enumerate(zip(bars1, uni_values)):
        height = bar.get_height()
        color = HTA_COLOR if uni_methods[i] == 'HTA_AD' else '#2C3E50'
        weight = 'bold' if uni_methods[i] == 'HTA_AD' else 'normal'
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.2f}', ha='center', va='bottom', 
                fontsize=VALUE_FONTSIZE, fontweight=weight, color=color)
    
    # 设置图表样式（优化用于学术论文）
    ax1.set_ylabel('VUS-PR Score', fontsize=AXIS_FONTSIZE, fontweight='bold')
    ax1.set_xlabel('Method', fontsize=AXIS_FONTSIZE, fontweight='bold')
    ax1.set_ylim(0, Y_MAX)
    ax1.set_xlim(-0.5, len(uni_methods) - 0.5)

    # 设置y轴刻度，确保两图一致
    ax1.set_yticks(np.arange(0, Y_MAX + 0.1, 0.1))

    # 设置x轴标签
    ax1.set_xticks(range(len(uni_methods)))
    ax1.set_xticklabels(uni_methods, rotation=45, ha='right', fontsize=LABEL_FONTSIZE)
    
    # 添加网格和样式
    ax1.grid(True, alpha=GRID_ALPHA, axis='y', linestyle='-')
    ax1.set_axisbelow(True)
    ax1.set_facecolor('#FAFAFA')
    
    # 调整布局并保存
    plt.tight_layout()
    plt.savefig('figures/bar_chart_tsb-ad-u.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    # === 创建多变量图表 ===
    fig2, ax2 = plt.subplots(figsize=(multi_width, FIGURE_HEIGHT))
    
    # 创建柱状图
    multi_colors = [HTA_COLOR if method == 'HTA_AD' else OTHER_COLOR for method in multi_methods]
    bars2 = ax2.bar(range(len(multi_methods)), multi_values, color=multi_colors, 
                    alpha=0.85, width=BAR_WIDTH, edgecolor='white', linewidth=0.5)
    
    # 添加数值标签
    for i, (bar, value) in enumerate(zip(bars2, multi_values)):
        height = bar.get_height()
        color = HTA_COLOR if multi_methods[i] == 'HTA_AD' else '#2C3E50'
        weight = 'bold' if multi_methods[i] == 'HTA_AD' else 'normal'
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.2f}', ha='center', va='bottom', 
                fontsize=VALUE_FONTSIZE, fontweight=weight, color=color)
    
    # 设置图表样式（与单变量完全一致）
    ax2.set_ylabel('VUS-PR Score', fontsize=AXIS_FONTSIZE, fontweight='bold')
    ax2.set_xlabel('Method', fontsize=AXIS_FONTSIZE, fontweight='bold')
    ax2.set_ylim(0, Y_MAX)  # 关键：使用相同的y轴范围
    ax2.set_xlim(-0.5, len(multi_methods) - 0.5)

    # 设置y轴刻度，与单变量图表完全一致
    ax2.set_yticks(np.arange(0, Y_MAX + 0.1, 0.1))

    # 设置x轴标签
    ax2.set_xticks(range(len(multi_methods)))
    ax2.set_xticklabels(multi_methods, rotation=45, ha='right', fontsize=LABEL_FONTSIZE)
    
    # 添加网格和样式（与单变量完全一致）
    ax2.grid(True, alpha=GRID_ALPHA, axis='y', linestyle='-')
    ax2.set_axisbelow(True)
    ax2.set_facecolor('#FAFAFA')
    
    # 调整布局并保存
    plt.tight_layout()
    plt.savefig('figures/bar_chart_tsb-ad-m.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    return uni_width, multi_width

def create_side_by_side_preview():
    """创建并排预览图，检查视觉一致性"""
    
    # 重新加载保存的图片进行预览
    import matplotlib.image as mpimg
    
    try:
        img1 = mpimg.imread('figures/bar_chart_tsb-ad-u.png')
        img2 = mpimg.imread('figures/bar_chart_tsb-ad-m.png')
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        ax1.imshow(img1)
        ax1.set_title('(a) Univariate benchmark (TSB-AD-U)', fontsize=12, fontweight='bold')
        ax1.axis('off')
        
        ax2.imshow(img2)
        ax2.set_title('(b) Multivariate benchmark (TSB-AD-M)', fontsize=12, fontweight='bold')
        ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig('figures/combined_preview.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 并排预览图已生成：figures/combined_preview.png")
        
    except FileNotFoundError:
        print("⚠️  无法创建预览图，请确保单独的图表文件已生成")

if __name__ == "__main__":
    print("🔧 创建视觉一致的并排柱状图...")
    print("=" * 60)
    
    # 创建figures目录
    os.makedirs('figures', exist_ok=True)
    
    # 生成一致的图表
    uni_width, multi_width = create_consistent_charts()
    
    print(f"✅ 单变量图表已生成 (宽度: {uni_width:.1f})")
    print(f"✅ 多变量图表已生成 (宽度: {multi_width:.1f})")
    print(f"✅ 统一高度: 6英寸")
    print(f"✅ 统一y轴范围: 0-0.50")
    print(f"✅ HTA-AD多变量数值: 0.39 (已修正)")
    
    # 创建预览图
    print("\n🖼️  生成并排预览图...")
    create_side_by_side_preview()
    
    print("\n" + "=" * 60)
    print("✅ 图表一致性修复完成！")
    print("\n📋 关键改进:")
    print("   🔸 统一图表高度：6英寸")
    print("   🔸 统一y轴范围：0-0.50")
    print("   🔸 统一字体大小和样式")
    print("   🔸 统一柱子宽度和间距")
    print("   🔸 统一颜色方案和网格样式")
    print("\n📁 输出文件:")
    print("   📊 figures/bar_chart_tsb-ad-u.png (单变量)")
    print("   📊 figures/bar_chart_tsb-ad-m.png (多变量)")
    print("   📊 figures/combined_preview.png (并排预览)")
    print("\n💡 现在两个图表在论文中并排显示时将完全一致！")
