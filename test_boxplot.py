#!/usr/bin/env python3
"""
Test script to create a simple boxplot to verify colors work
"""

import numpy as np
import matplotlib.pyplot as plt

# Set style
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'font.size': 11,
    'axes.labelsize': 12,
    'axes.titlesize': 14,
    'figure.dpi': 300
})

def test_boxplot():
    """Create a test boxplot with colors"""
    print("🧪 Testing boxplot colors...")
    
    # Generate test data
    np.random.seed(42)
    normal_data = [np.random.normal(0, 1, 100) for _ in range(5)]
    anomaly_data = [np.random.normal(2, 1.5, 100) for _ in range(5)]
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    positions = np.arange(5)
    
    # Create side-by-side box plots
    bp1 = ax.boxplot(normal_data, positions=positions-0.2, widths=0.3,
                     patch_artist=True, 
                     boxprops=dict(facecolor='lightblue', color='blue', alpha=0.8),
                     medianprops=dict(color='darkblue', linewidth=2),
                     whiskerprops=dict(color='blue'),
                     capprops=dict(color='blue'),
                     flierprops=dict(marker='o', markerfacecolor='lightblue', 
                                   markeredgecolor='blue', markersize=4))
    
    bp2 = ax.boxplot(anomaly_data, positions=positions+0.2, widths=0.3,
                     patch_artist=True,
                     boxprops=dict(facecolor='lightcoral', color='red', alpha=0.8),
                     medianprops=dict(color='darkred', linewidth=2),
                     whiskerprops=dict(color='red'),
                     capprops=dict(color='red'),
                     flierprops=dict(marker='o', markerfacecolor='lightcoral', 
                                   markeredgecolor='red', markersize=4))
    
    ax.set_xlabel('Feature ID')
    ax.set_ylabel('Activation Value')
    ax.set_xticks(positions)
    ax.set_xticklabels([f'F{i}' for i in range(5)])
    ax.legend([bp1["boxes"][0], bp2["boxes"][0]], ['Normal', 'Anomaly'], loc='upper left')
    ax.grid(True, alpha=0.3)
    ax.set_title('Test Boxplot: Normal vs Anomaly')
    
    plt.tight_layout()
    plt.savefig('test_boxplot.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Test boxplot saved as 'test_boxplot.png'")

if __name__ == "__main__":
    test_boxplot()
