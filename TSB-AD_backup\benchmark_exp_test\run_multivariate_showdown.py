# -*- coding: utf-8 -*-
# run_multivariate_comparison.py
#
# A script to run a focused comparison on MULTIVARIATE datasets.
# MODIFIED TO RUN ONLY HTA-AD on GPU 1

import os
import sys
import pandas as pd
import random
import time
from tqdm import tqdm
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import precision_recall_curve


# --- <PERSON><PERSON><PERSON><PERSON><PERSON> and Seaborn Setup ---
try:
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid")
    # A simple check for a common Chinese font
    if 'SimHei' in [f.name for f in plt.matplotlib.font_manager.fontManager.ttflist]:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        print("🎨 使用中文字体: SimHei")
except Exception:
    print("🎨 字体设置失败，将使用默认字体")


# --- Path Setup ---
# No longer needed, running as a module handles the path.
# current_dir = os.path.dirname(os.path.abspath(__file__))
# project_root = os.path.dirname(current_dir)
# if project_root not in sys.path:
#     sys.path.insert(0, project_root)

# --- TSB-AD Imports ---
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.model_wrapper import run_Unsupervise_AD

# --- User Model Imports ---
from benchmark_exp.HTA_AD_M import HTA_AD_M
# from benchmark_exp.LTS_AD_M import LTS_AD_M

# Configuration
DATA_PATH = './Datasets/TSB-AD-M/'
# ALL_DATASETS = sorted([f for f in os.listdir(DATA_PATH) if f.endswith('.csv')])
# # --- Run on a subset of datasets for speed ---
# NUM_DATASETS_TO_RUN = 20
# ALL_DATASETS = ALL_DATASETS[:NUM_DATASETS_TO_RUN]

GPU_ID = 1 
MAX_WORKERS = 1
output_dir = 'multivariate_showdown_results'
os.makedirs(output_dir, exist_ok=True)

# --- Model Definitions ---
MODELS = {
    'HTA-AD-M': {
        'call_type': 'custom',
        'model': HTA_AD_M,
        'params': {
            'gpu': GPU_ID,
            'epochs': 30, 
            'lr': 1e-3, 
            'batch_size': 128, 
            'window_size': 100,
            'latent_dim': 32,
            'patience': 5,
            'tcn_channels': [32, 64],
            'cnn_channels': 16,
            'downsample_stride': 2
        }
    }
}

DATA_ROOT = './Datasets/TSB-AD-M'
RESULTS_DIR = 'multivariate_showdown_results'
TRAIN_SPLIT = 0.6

def run_single_experiment(model_name, model_info, dataset_filename):
    """Runs a single model on a single dataset."""
    
    results_path = os.path.join(RESULTS_DIR, model_name)
    os.makedirs(results_path, exist_ok=True)
    result_file = os.path.join(results_path, os.path.splitext(dataset_filename)[0] + '.json')
    
    if os.path.exists(result_file):
        print(f"  -> Skipping {model_name} on {os.path.basename(dataset_filename)[:-4]}, results exist.")
        return

    print(f"  -> Running Model: {model_name} on {dataset_filename}")
    
    try:
        df = pd.read_csv(os.path.join(DATA_ROOT, dataset_filename))
        X = df.iloc[:, :-1].values.astype(float)
        y = df.iloc[:, -1].values.astype(int)
        
        train_size = int(len(X) * TRAIN_SPLIT)
        X_train, X_test = X[:train_size], X[train_size:]
        y_test = y[train_size:]

        scores = None
        start_time = time.time()

        call_type = model_info.get('call_type', 'custom')
        model_params = model_info.get('params', {})

        if call_type == 'native':
            print("  ... running native model via wrapper ...")
            scores_full = run_Unsupervise_AD(model_name, X, train_size=train_size, **model_params)
            scores = scores_full[train_size:]
        else: # 'custom'
            print("  ... initializing custom model ...")
            model_class = model_info['model']
            detector = model_class(HP=model_params)
            
            print("  ... fitting model ...")
            detector.fit(X_train, X_test)
            
            print("  ... getting scores ...")
            scores = detector.decision_function(X_test)

        end_time = time.time()
        runtime = end_time - start_time

        if scores is not None and len(scores) == len(y_test):
            print("  ... calculating metrics ...")
            try:
                # --- Pre-computation Check ---
                # Check if there are any anomalies in the ground truth
                if np.sum(y_test) == 0:
                    print("  ⚠️ No anomalies in the test set. Skipping metric calculation.")
                    result_metrics = {'Runtime': runtime, 'Note': 'No anomalies in test set'}
                else:
            result_metrics = get_metrics(scores, y_test)
            result_metrics['Runtime'] = runtime
            except IndexError as e:
                print(f"  ❌ IndexError during metric calculation: {e}. Likely an issue with anomaly sequence processing in the framework.")
                result_metrics = {'Runtime': runtime, 'Note': f'Metric calculation failed due to IndexError'}
            
            with open(result_file, 'w') as f:
                json.dump(result_metrics, f, indent=4)
            print(f"  ✅ Saved results to {result_file}")
        else:
            raise ValueError(f"Score length ({len(scores) if scores is not None else 0}) does not match test label length ({len(y_test)}).")

    except Exception as e:
        import traceback
        error_msg = f"❌ Error running {model_name} on {dataset_filename}: {e}\n{traceback.format_exc()}"
        print(error_msg)
        with open(result_file.replace('.json', '.error'), 'w') as f:
            f.write(error_msg)

def main():
    """Main function to run the benchmark."""
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    all_datasets = sorted([f for f in os.listdir(DATA_ROOT) if f.endswith('.csv')])
    
    # --- Run on a random subset of 10 datasets ---
    num_datasets_to_run = 10
    if len(all_datasets) > num_datasets_to_run:
        datasets_to_run = random.sample(all_datasets, num_datasets_to_run)
    else:
        datasets_to_run = all_datasets
    
    print(f"🔬 Starting Multivariate Showdown on {len(datasets_to_run)} datasets.")
    print(f"   Models to test: {list(MODELS.keys())}")
    print(f"   Saving results to: {RESULTS_DIR}\n")

    for i, dataset_filename in enumerate(datasets_to_run):
        print(f"--- Processing Dataset {i+1}/{len(datasets_to_run)}: {dataset_filename} ---")
        for model_name, model_info in MODELS.items():
            run_single_experiment(model_name, model_info, dataset_filename)
            
    print("\n🎉 All experiments finished.")

if __name__ == '__main__':
    main()