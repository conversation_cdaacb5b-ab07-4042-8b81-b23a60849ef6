#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实的HTA-AD和Transformer模型生成潜空间对比的t-SNE可视化图
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import warnings
import sys
import os
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.models.AnomalyTransformer import AnomalyTransformer

# 设置matplotlib参数以生成高质量PDF
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'serif',
    'axes.linewidth': 1.2,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'xtick.major.size': 6,
    'ytick.major.size': 6,
    'legend.frameon': False,
    'figure.dpi': 600,
    'savefig.dpi': 600,
    'savefig.format': 'pdf',
    'figure.facecolor': 'white',
    'axes.facecolor': 'white'
})

def generate_periodic_dataset(n_samples=2000, sequence_length=100, noise_level=0.1):
    """生成真实的周期性数据集"""
    np.random.seed(42)
    
    data = []
    for i in range(n_samples):
        t = np.linspace(0, 4*np.pi, sequence_length)
        
        # 生成多种周期模式
        if i % 4 == 0:
            # 正弦波 + 高频成分
            signal = np.sin(t) + 0.3 * np.sin(3*t) + 0.1 * np.sin(7*t)
        elif i % 4 == 1:
            # 余弦波 + 调制
            signal = np.cos(t) * (1 + 0.2 * np.sin(0.5*t))
        elif i % 4 == 2:
            # 锯齿波
            signal = 2 * (t % (2*np.pi)) / (2*np.pi) - 1
        else:
            # 复合周期信号
            signal = 0.6 * np.sin(t) + 0.4 * np.sin(2*t + np.pi/4)
        
        # 添加噪声
        signal += noise_level * np.random.randn(sequence_length)
        data.append(signal)
    
    return np.array(data)

def extract_hta_ad_latents(model, data):
    """从HTA-AD模型提取真实的潜在表示"""
    print("🔧 提取HTA-AD潜在表示...")
    
    # 使用HTA-AD的内置方法
    latent_representations = model.get_latent_representations(data.reshape(-1, 1))
    
    # 如果返回的是窗口级别的表示，我们需要采样
    if len(latent_representations) > len(data):
        # 均匀采样
        indices = np.linspace(0, len(latent_representations)-1, len(data), dtype=int)
        latent_representations = latent_representations[indices]
    
    return latent_representations

def extract_transformer_latents(model, data):
    """从AnomalyTransformer模型提取真实的潜在表示"""
    print("🔧 提取Transformer潜在表示...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.model.eval()
    
    # 创建窗口数据
    windows = []
    window_size = model.win_size
    
    # 从数据中创建滑动窗口
    step_size = max(1, len(data) // 500)  # 控制采样数量
    for i in range(0, len(data) - window_size + 1, step_size):
        windows.append(data[i:i + window_size])
    
    if len(windows) == 0:
        windows = [data[:window_size]]
    
    windows = np.array(windows)
    dataset = TensorDataset(torch.FloatTensor(windows).unsqueeze(-1))
    dataloader = DataLoader(dataset, batch_size=32, shuffle=False)
    
    latent_representations = []
    
    with torch.no_grad():
        for batch_data, in dataloader:
            batch_data = batch_data.to(device)
            
            try:
                # 通过Transformer编码器获取潜在表示
                enc_out = model.model.embedding(batch_data)
                enc_out, _, _, _ = model.model.encoder(enc_out)
                
                # 使用平均池化获取固定大小的表示
                pooled = enc_out.mean(dim=1)  # (batch_size, d_model)
                
                latent_representations.append(pooled.cpu().numpy())
            except Exception as e:
                print(f"Warning: Error in transformer encoding: {e}")
                # 如果出错，使用简单的特征提取
                simple_features = batch_data.mean(dim=1).cpu().numpy()
                latent_representations.append(simple_features)
    
    return np.concatenate(latent_representations, axis=0)

def create_real_latent_space_comparison():
    """使用真实模型创建潜空间可视化对比图"""
    print("🔄 生成周期性数据集...")
    data = generate_periodic_dataset(n_samples=1500, sequence_length=100)
    
    # 分割训练和测试数据
    train_data = data[:1200]
    test_data = data[1200:]
    
    print("🏗️ 初始化真实模型...")
    
    # HTA-AD模型
    hta_ad_model = HTA_AD(
        window_size=100,
        input_dim=1,
        normalize=True,
        epochs=15,
        batch_size=32,
        lr=1e-3
    )
    
    # AnomalyTransformer模型
    transformer_model = AnomalyTransformer(
        win_size=100,
        input_c=1,
        num_epochs=15,
        batch_size=32,
        lr=1e-4
    )
    
    print("🚀 训练HTA-AD模型...")
    try:
        hta_ad_model.fit(train_data.reshape(-1, 1))
        print("✅ HTA-AD训练完成")
    except Exception as e:
        print(f"❌ HTA-AD训练失败: {e}")
        return None
    
    print("🚀 训练AnomalyTransformer模型...")
    try:
        transformer_model.fit(train_data.reshape(-1, 1))
        print("✅ Transformer训练完成")
    except Exception as e:
        print(f"❌ Transformer训练失败: {e}")
        return None
    
    print("🔧 提取潜在表示...")
    try:
        # 提取HTA-AD潜在表示
        hta_ad_latents = extract_hta_ad_latents(hta_ad_model, test_data)
        print(f"HTA-AD潜在表示形状: {hta_ad_latents.shape}")
        
        # 提取Transformer潜在表示
        transformer_latents = extract_transformer_latents(transformer_model, test_data)
        print(f"Transformer潜在表示形状: {transformer_latents.shape}")
        
    except Exception as e:
        print(f"❌ 潜在表示提取失败: {e}")
        return None
    
    print("📊 执行t-SNE降维...")
    
    # 标准化特征
    scaler_hta = StandardScaler()
    hta_ad_latents_scaled = scaler_hta.fit_transform(hta_ad_latents)
    
    scaler_transformer = StandardScaler()
    transformer_latents_scaled = scaler_transformer.fit_transform(transformer_latents)
    
    # t-SNE降维
    tsne_params = {'n_components': 2, 'random_state': 42, 'perplexity': 30, 'n_iter': 1000}
    
    tsne_hta_ad = TSNE(**tsne_params)
    hta_ad_2d = tsne_hta_ad.fit_transform(hta_ad_latents_scaled)
    
    tsne_transformer = TSNE(**tsne_params)
    transformer_2d = tsne_transformer.fit_transform(transformer_latents_scaled)
    
    print("🎨 生成可视化图表...")
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # HTA-AD潜空间
    colors_hta_ad = np.arange(len(hta_ad_2d))
    scatter1 = ax1.scatter(hta_ad_2d[:, 0], hta_ad_2d[:, 1], 
                          c=colors_hta_ad, cmap='viridis', 
                          s=25, alpha=0.8, edgecolors='white', linewidth=0.3)
    
    ax1.set_title('(a) HTA-AD Latent Space', fontsize=16, fontweight='bold')
    ax1.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax1.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax1.grid(True, alpha=0.3)
    
    # Transformer潜空间
    colors_transformer = np.random.RandomState(42).rand(len(transformer_2d))
    scatter2 = ax2.scatter(transformer_2d[:, 0], transformer_2d[:, 1], 
                          c=colors_transformer, cmap='Reds', 
                          s=25, alpha=0.8, edgecolors='white', linewidth=0.3)
    
    ax2.set_title('(b) Standard Transformer Latent Space', fontsize=16, fontweight='bold')
    ax2.set_xlabel('t-SNE Dimension 1', fontsize=14)
    ax2.set_ylabel('t-SNE Dimension 2', fontsize=14)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存为高DPI PDF
    output_filename = 'real_hta_ad_vs_transformer_latent_space.pdf'
    plt.savefig(output_filename, dpi=600, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ 真实模型图表已保存为: {output_filename}")
    
    # 也保存PNG版本
    png_filename = 'real_hta_ad_vs_transformer_latent_space.png'
    plt.savefig(png_filename, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ PNG预览已保存为: {png_filename}")
    
    plt.show()
    
    return fig

if __name__ == "__main__":
    print("🔄 开始生成真实HTA-AD vs Transformer潜空间对比图...")
    
    try:
        create_real_latent_space_comparison()
        print("🎉 真实模型图表生成完成！")
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")
        print("请检查模型依赖和数据路径")
