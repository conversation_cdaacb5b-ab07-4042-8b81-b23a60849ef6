#!/usr/bin/env python3
"""
Test script for HTA-AD Paper Compliant Implementation
"""

import torch
import sys
sys.path.append('.')
from core.models.hta_ad_paper_compliant import HTAADBasic, PostHocSAE

def test_basic_functionality():
    """Test basic HTA-AD and SAE functionality"""
    print('🚀 Testing HTA-AD Paper Compliant Implementation...')
    print()

    # Create basic HTA-AD model
    model = HTAADBasic(
        input_dim=1,
        d_model=32,
        seq_len=100
    )

    print(f'✅ HTAADBasic model created successfully')
    print(f'   - Input dim: {model.input_dim}')
    print(f'   - Model dim: {model.d_model}')
    print(f'   - Sequence length: {model.seq_len}')
    print()

    # Test with sample data
    batch_size = 8
    x = torch.randn(batch_size, model.seq_len, model.input_dim)
    print(f'📊 Testing with sample data: {x.shape}')

    # Forward pass
    outputs = model(x)
    x_recon = outputs['reconstruction']
    z = outputs['latent_vectors']
    
    print(f'✅ Forward pass successful')
    print(f'   - Input: {x.shape}')
    print(f'   - Latent: {z.shape}')
    print(f'   - Reconstruction: {x_recon.shape}')
    print()
    
    # Create SAE
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128
    )
    
    print(f'✅ PostHocSAE created successfully')
    print(f'   - Latent dim: {sae.latent_dim}')
    print(f'   - Hidden dim: {sae.hidden_dim}')
    print()
    
    # Test SAE
    z_hat, f = sae(z)
    print(f'✅ SAE forward pass successful')
    print(f'   - Latent input: {z.shape}')
    print(f'   - Features: {f.shape}')
    print(f'   - Reconstructed latent: {z_hat.shape}')
    sparsity = (f > 0).float().mean().item()
    print(f'   - Feature sparsity: {sparsity:.3f}')
    print()
    
    # Test loss computation
    hta_losses = model.compute_loss(outputs, x)
    sae_losses = sae.compute_loss(z)
    
    print(f'✅ Loss computation successful')
    print(f'   - HTA-AD reconstruction loss: {hta_losses["total"].item():.4f}')
    print(f'   - SAE reconstruction loss: {sae_losses["reconstruction"].item():.4f}')
    print(f'   - SAE sparsity loss: {sae_losses["sparsity"].item():.4f}')
    print()
    
    return model, sae, z

def test_advanced_features(model, sae, z):
    """Test advanced SAE features"""
    print('🧪 Testing SAE Advanced Features...')
    print()
    
    # Generate more data for feature analysis
    all_z = []
    for i in range(20):  # Simulate 20 batches
        x_batch = torch.randn(8, model.seq_len, model.input_dim)
        outputs_batch = model(x_batch)
        z_batch = outputs_batch['latent_vectors']
        all_z.append(z_batch)
    
    all_z = torch.cat(all_z, dim=0)  # [160, 100, 32]
    print(f'📊 Collected {all_z.shape[0]} latent vectors for analysis')
    
    # Test irrelevant feature identification
    print('🔍 Testing irrelevant feature identification...')
    irrelevant_mask = sae.identify_irrelevant_features(all_z)
    print(f'   - Irrelevant features: {torch.sum(irrelevant_mask).item():.0f}/{len(irrelevant_mask)}')
    print()
    
    # Test purification
    print('🧹 Testing latent space purification...')
    z_sample = z[0:4]  # Take first 4 samples
    z_purified = sae.purify_latent(z_sample, alpha=0.7)
    
    print(f'   - Original latent shape: {z_sample.shape}')
    print(f'   - Purified latent shape: {z_purified.shape}')
    
    # Measure purification effect
    purification_diff = torch.norm(z_sample - z_purified, dim=-1).mean()
    print(f'   - Average purification magnitude: {purification_diff.item():.4f}')
    print()
    
    # Test anomaly explanation
    print('🔬 Testing anomaly explanation...')
    explanations = sae.explain_anomaly(z_sample, top_k=5)
    
    print(f'   - Top feature indices shape: {explanations["top_features"].shape}')
    print(f'   - Top feature values shape: {explanations["top_activations"].shape}')
    print(f'   - Feature activations shape: {explanations["feature_activations"].shape}')
    print(f'   - Feature importance shape: {explanations["feature_importance"].shape}')

    # Show top features for first sample (first timestep)
    top_features = explanations['top_features'][0, 0]  # [top_k]
    top_values = explanations['top_activations'][0, 0]  # [top_k]
    print(f'   - Top 5 features for sample 1: {top_features.tolist()}')
    print(f'   - Their activation values: {[f"{v:.4f}" for v in top_values.tolist()]}')
    print()

if __name__ == "__main__":
    try:
        # Test basic functionality
        model, sae, z = test_basic_functionality()
        
        # Test advanced features
        test_advanced_features(model, sae, z)
        
        print('🎉 All tests passed! HTA-AD implementation is working correctly.')
        
    except Exception as e:
        print(f'❌ Error during testing: {e}')
        import traceback
        traceback.print_exc()
