#!/usr/bin/env python3
"""
Test fixed HTA-AD implementation with correct training settings
"""

import torch
import sys
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
sys.path.append('.')
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

from core.models.hta_ad_correct import HTAADCorrect, PostHocSAE

try:
    from TSB_AD.evaluation.metrics import get_metrics
    from TSB_AD.utils.slidingWindows import find_length_rank
    TSB_AD_AVAILABLE = True
except ImportError:
    TSB_AD_AVAILABLE = False
    print("TSB-AD not available")

def create_sliding_windows(data, window_size, stride=1):
    """Create sliding windows from time series data"""
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    n_samples, n_features = data.shape
    windows = []
    
    for i in range(0, n_samples - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def test_fixed_hta_ad():
    """Test fixed HTA-AD with correct training settings"""
    print("🧪 Testing Fixed HTA-AD Implementation")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load test dataset
    dataset_path = "TSB-AD/Datasets/TSB-AD-U/140_MSL_id_1_Sensor_tr_530_1st_630.csv"
    
    if not os.path.exists(dataset_path):
        print("❌ Test dataset not found")
        return
    
    # Load data
    df = pd.read_csv(dataset_path)
    data = df.iloc[:, 0].values.reshape(-1, 1)
    labels = df.iloc[:, 1].values
    
    train_size = 530
    train_data = data[:train_size]
    test_data = data[train_size:]
    test_labels = labels[train_size:]
    
    print(f"Train data: {train_data.shape}")
    print(f"Test data: {test_data.shape}")
    print(f"Test anomaly ratio: {np.mean(test_labels):.3f}")
    
    # Normalize
    scaler = StandardScaler()
    train_data = scaler.fit_transform(train_data)
    test_data = scaler.transform(test_data)
    
    # Create models with backup file settings
    print("\n🏗️  Creating models with backup file settings...")
    model = HTAADCorrect(
        input_dim=1,
        window_size=128,  # From backup
        latent_dim=32,
        tcn_channels=[32, 32, 32],
        tcn_kernel_size=3,
        cnn_channels=16,
        downsample_stride=2
    ).to(device)
    
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128,
        sparsity_penalty=1e-3
    ).to(device)
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"SAE parameters: {sum(p.numel() for p in sae.parameters()):,}")
    
    # Create training windows
    train_windows = create_sliding_windows(train_data, 128)  # From backup
    train_windows = torch.FloatTensor(train_windows).to(device)
    
    print(f"Training windows shape: {train_windows.shape}")
    
    # Train HTA-AD with backup settings
    print("\n🔧 Training HTA-AD with backup settings...")
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-5)  # From backup
    model.train()
    
    for epoch in range(30):  # From backup
        total_loss = 0
        batch_size = 64  # From backup
        
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch)
            losses = model.compute_loss(outputs, batch)
            loss = losses['total']
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 5 == 0:
            avg_loss = total_loss / (len(train_windows) // batch_size + 1)
            print(f"  Epoch {epoch + 1}/30, Loss: {avg_loss:.4f}")
    
    # Collect latent vectors for SAE training
    print("\n🔧 Collecting latent vectors...")
    model.eval()
    all_latents = []
    
    with torch.no_grad():
        batch_size = 64
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            outputs = model(batch)
            latents = outputs['latent_vectors']
            all_latents.append(latents)
    
    all_latents = torch.cat(all_latents, dim=0)
    print(f"Collected {all_latents.shape[0]} latent vectors")
    
    # Train SAE
    print("\n🔧 Training SAE...")
    sae_optimizer = torch.optim.AdamW(sae.parameters(), lr=1e-3, weight_decay=1e-5)
    sae.train()
    
    for epoch in range(15):
        total_loss = 0
        batch_size = 64
        
        for i in range(0, len(all_latents), batch_size):
            batch = all_latents[i:i + batch_size]
            
            sae_optimizer.zero_grad()
            losses = sae.compute_loss(batch)
            loss = losses['total']
            
            loss.backward()
            sae_optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 5 == 0:
            avg_loss = total_loss / (len(all_latents) // batch_size + 1)
            print(f"  SAE Epoch {epoch + 1}/15, Loss: {avg_loss:.4f}")
    
    # Identify irrelevant features
    print("\n🔍 Identifying irrelevant features...")
    sae.identify_irrelevant_features(all_latents, max_irrelevant_ratio=0.25, only_dead_features=True)
    
    # Test anomaly detection
    print("\n📊 Testing anomaly detection...")
    model.eval()
    sae.eval()
    
    # Test on subset
    window_size = 128
    pad_size = window_size // 2
    padded_data = np.pad(test_data, ((pad_size, pad_size), (0, 0)), mode='edge')
    
    hta_scores = []
    sae_scores = []
    
    with torch.no_grad():
        for i in range(min(200, len(test_data))):
            window = padded_data[i:i + window_size]
            window_tensor = torch.FloatTensor(window).unsqueeze(0).to(device)
            
            # HTA-AD forward pass
            outputs = model(window_tensor)
            reconstruction = outputs['reconstruction']
            latent_vectors = outputs['latent_vectors']
            
            # HTA-AD score
            hta_error = torch.mean((window_tensor - reconstruction) ** 2)
            hta_scores.append(hta_error.cpu().item())
            
            # SAE purified reconstruction
            z_purified = sae.purify_latent(latent_vectors)
            purified_reconstruction = model.decode(z_purified)
            sae_error = torch.mean((window_tensor - purified_reconstruction) ** 2)
            sae_scores.append(sae_error.cpu().item())
    
    hta_scores = np.array(hta_scores)
    sae_scores = np.array(sae_scores)
    
    print(f"\n📈 Score Statistics:")
    print(f"HTA-AD scores: mean={hta_scores.mean():.6f}, std={hta_scores.std():.6f}")
    print(f"SAE scores:    mean={sae_scores.mean():.6f}, std={sae_scores.std():.6f}")
    print(f"Score ratio:   {sae_scores.mean() / hta_scores.mean():.3f}")
    
    # Check if SAE actually changes scores
    score_diff = np.abs(sae_scores - hta_scores).mean()
    print(f"Average score difference: {score_diff:.6f}")
    
    if score_diff > 1e-6:
        print("✅ SAE purification is working - scores are different!")
    else:
        print("⚠️  SAE purification may not be working - scores are identical")
    
    # TSB-AD evaluation if available
    if TSB_AD_AVAILABLE and len(np.unique(test_labels[:200])) > 1:
        try:
            slidingWindow = find_length_rank(test_data[:200], rank=1)
            hta_metrics = get_metrics(hta_scores, test_labels[:200], slidingWindow=slidingWindow)
            sae_metrics = get_metrics(sae_scores, test_labels[:200], slidingWindow=slidingWindow)
            
            print(f"\n📊 TSB-AD Metrics:")
            print(f"HTA-AD: VUS-PR={hta_metrics.get('VUS-PR', 0):.4f}, VUS-ROC={hta_metrics.get('VUS-ROC', 0):.4f}")
            print(f"SAE:    VUS-PR={sae_metrics.get('VUS-PR', 0):.4f}, VUS-ROC={sae_metrics.get('VUS-ROC', 0):.4f}")
        except Exception as e:
            print(f"TSB-AD metrics failed: {e}")
    
    print("\n🎉 Fixed HTA-AD test completed!")
    print("Settings used: AdamW optimizer, lr=1e-3, weight_decay=1e-5, window_size=128, batch_size=64")

if __name__ == "__main__":
    import os
    test_fixed_hta_ad()
