# HTA-AD 超参数设计详细分析

## 📊 超参数概览

基于代码分析，HTA-AD的超参数设计包含以下几个层次：

### 🏗️ **核心架构参数 (HP)**

| 参数名 | 默认值 | 范围/选项 | 作用 | 影响 |
|--------|--------|-----------|------|------|
| `window_size` | 128 | [32, 64, 96, 128, 192, 256] | 时间窗口大小 | 影响模型感受野和计算复杂度 |
| `latent_dim` | 32 | [16, 32, 64, 128] | 潜在空间维度 | 控制信息压缩程度和表征能力 |
| `tcn_channels` | [32, 32, 32] | [[16,16], [32,32,32], [64,64,64]] | TCN通道配置 | 决定时序建模能力 |
| `cnn_channels` | 16 | [8, 16, 32, 64] | CNN通道数 | 影响局部特征提取能力 |
| `downsample_stride` | 2 | [1, 2, 4] | 下采样步长 | 控制序列压缩比例 |
| `tcn_kernel_size` | 3 | [3, 5, 7] | TCN卷积核大小 | 影响时序感受野 |

### 🎯 **训练参数**

| 参数名 | 默认值 | 范围/选项 | 作用 | 调优建议 |
|--------|--------|-----------|------|----------|
| `epochs` | 30 | [10, 20, 30, 50, 100] | 训练轮数 | 根据数据集大小调整 |
| `lr` | 1e-3 | [1e-4, 1e-3, 1e-2] | 学习率 | 影响收敛速度和稳定性 |
| `batch_size` | 64 | [16, 32, 64, 128] | 批次大小 | 影响训练稳定性和内存使用 |
| `gpu` | 0 | [0, 1, 2, ...] 或 -1 | GPU设备ID | -1表示使用CPU |

### 🧠 **SAE增强参数 (sae_config)**

| 参数名 | 默认值 | 范围/选项 | 作用 | 调优建议 |
|--------|--------|-----------|------|----------|
| `hidden_dim` | 128 | [64, 128, 256, 512] | SAE隐藏层维度 | 通常是latent_dim的2-4倍 |
| `sparsity_weight` | 0.01 | [0.001, 0.01, 0.1] | 稀疏性正则化权重 | 控制特征稀疏程度 |
| `purification_strength` | 0.5 | [0.1, 0.3, 0.5, 0.7, 1.0] | 净化强度 | 控制无关特征移除程度 |

## 🔧 **超参数设计原理**

### 1. **Hourglass架构设计**

```python
# 架构流程：输入 -> CNN下采样 -> TCN编码 -> 潜在空间 -> TCN解码 -> CNN上采样 -> 输出
input_shape: (batch, window_size, input_dim)
↓ CNN Downsampler (stride=2)
downsampled: (batch, window_size/2, cnn_channels)
↓ TCN Encoder
encoded: (batch, window_size/2, tcn_channels[-1])
↓ Flatten + FC
latent: (batch, latent_dim)
↓ FC + Unflatten
decoded: (batch, window_size/2, tcn_channels[-1])
↓ TCN Decoder
upsampled: (batch, window_size/2, tcn_channels[-1])
↓ CNN Upsampler (transpose conv)
output: (batch, window_size, input_dim)
```

### 2. **感受野计算**

TCN的感受野计算公式：
```
RF(L) = 1 + (kernel_size - 1) * (2^L - 1)
```

对于默认配置 `tcn_kernel_size=3`, `tcn_channels=[32,32,32]` (3层)：
- RF = 1 + (3-1) * (2^3 - 1) = 1 + 2 * 7 = 15

对于更大的核 `tcn_kernel_size=7`：
- RF = 1 + (7-1) * (2^3 - 1) = 1 + 6 * 7 = 43

### 3. **参数量分析**

```python
# 主要参数量来源：
# 1. CNN Encoder: input_dim * cnn_channels * 7 + cnn_channels
# 2. TCN Layers: sum(in_ch * out_ch * kernel_size for each layer)
# 3. Latent FC: (tcn_channels[-1] * downsampled_len) * latent_dim
# 4. Decoder: 对称结构，参数量相同

# 示例计算 (input_dim=1, window_size=128):
# CNN: 1 * 16 * 7 = 112
# TCN: 16*32*3 + 32*32*3 + 32*32*3 = 1536 + 3072 + 3072 = 7680
# FC: (32 * 64) * 32 = 65536
# Total ≈ 146K parameters (单变量)
```

## 📈 **性能调优指南**

### 1. **数据集规模导向**

| 数据集大小 | window_size | epochs | batch_size | 建议配置 |
|------------|-------------|--------|------------|----------|
| 小型 (<1K) | 32-64 | 50-100 | 16-32 | 轻量配置 |
| 中型 (1K-10K) | 64-128 | 30-50 | 32-64 | 标准配置 |
| 大型 (>10K) | 128-256 | 20-30 | 64-128 | 高效配置 |

### 2. **数据特征导向**

| 数据特征 | 推荐配置 | 原因 |
|----------|----------|------|
| 高频信号 | 大window_size, 小stride | 捕获更多细节 |
| 低频信号 | 小window_size, 大stride | 提高效率 |
| 多变量强相关 | 大cnn_channels | 增强特征提取 |
| 长期依赖 | 深层TCN, 大kernel | 扩大感受野 |

### 3. **计算资源导向**

| 资源限制 | 调整策略 | 参数建议 |
|----------|----------|----------|
| 内存不足 | 减小batch_size, window_size | batch_size=16, window_size=64 |
| 计算能力弱 | 减少通道数和层数 | tcn_channels=[16,16], cnn_channels=8 |
| 训练时间紧 | 减少epochs, 增大lr | epochs=10, lr=5e-3 |

## 🎛️ **超参数调优策略**

### 1. **分层调优法**

```python
# 第一阶段：架构参数
architecture_grid = {
    'window_size': [64, 128, 192],
    'latent_dim': [16, 32, 64],
    'tcn_channels': [[16,16], [32,32,32], [64,64]]
}

# 第二阶段：训练参数
training_grid = {
    'lr': [1e-4, 1e-3, 5e-3],
    'batch_size': [32, 64, 128],
    'epochs': [20, 30, 50]
}

# 第三阶段：SAE参数
sae_grid = {
    'hidden_dim': [64, 128, 256],
    'sparsity_weight': [0.001, 0.01, 0.1],
    'purification_strength': [0.3, 0.5, 0.7]
}
```

### 2. **自适应调优**

```python
def adaptive_hp_selection(data_shape, data_type):
    """根据数据特征自适应选择超参数"""
    n_samples, n_features = data_shape
    
    # 基础配置
    hp = {
        'window_size': min(128, n_samples // 10),
        'latent_dim': max(16, min(64, n_features * 8)),
        'batch_size': min(64, n_samples // 20),
        'epochs': 30 if n_samples > 1000 else 50
    }
    
    # 根据数据类型调整
    if data_type == 'multivariate':
        hp['cnn_channels'] = min(32, n_features * 4)
        hp['tcn_channels'] = [32, 32, 32]
    else:
        hp['cnn_channels'] = 16
        hp['tcn_channels'] = [16, 32, 32]
    
    return hp
```

## 🔍 **实际使用建议**

### 1. **快速开始配置**

```python
# 轻量级配置 (快速测试)
quick_hp = {
    'window_size': 64,
    'epochs': 10,
    'lr': 1e-3,
    'batch_size': 32,
    'latent_dim': 16,
    'tcn_channels': [16, 16],
    'cnn_channels': 8,
    'downsample_stride': 2
}

# 标准配置 (平衡性能)
standard_hp = {
    'window_size': 128,
    'epochs': 30,
    'lr': 1e-3,
    'batch_size': 64,
    'latent_dim': 32,
    'tcn_channels': [32, 32, 32],
    'cnn_channels': 16,
    'downsample_stride': 2
}

# 高性能配置 (追求最佳效果)
high_performance_hp = {
    'window_size': 192,
    'epochs': 50,
    'lr': 5e-4,
    'batch_size': 128,
    'latent_dim': 64,
    'tcn_channels': [64, 64, 64, 64],
    'cnn_channels': 32,
    'downsample_stride': 2
}
```

### 2. **SAE配置建议**

```python
# 基础SAE配置
basic_sae = {
    'hidden_dim': 128,
    'sparsity_weight': 0.01,
    'purification_strength': 0.3
}

# 强解释性配置
interpretable_sae = {
    'hidden_dim': 256,
    'sparsity_weight': 0.1,  # 更稀疏
    'purification_strength': 0.7  # 更强净化
}

# 高性能配置
performance_sae = {
    'hidden_dim': 512,
    'sparsity_weight': 0.001,  # 较少稀疏约束
    'purification_strength': 0.5
}
```

## ⚠️ **常见问题和解决方案**

### 1. **训练不稳定**
- **现象**: Loss震荡，不收敛
- **解决**: 降低学习率 (lr=1e-4)，增加batch_size

### 2. **过拟合**
- **现象**: 训练集表现好，测试集差
- **解决**: 减少latent_dim，增加sparsity_weight

### 3. **欠拟合**
- **现象**: 训练和测试都表现差
- **解决**: 增加模型容量 (更多TCN层，更大通道数)

### 4. **内存不足**
- **现象**: CUDA out of memory
- **解决**: 减小batch_size和window_size

### 5. **训练太慢**
- **现象**: 每个epoch耗时过长
- **解决**: 减少epochs，使用更大batch_size，启用GPU

这个超参数设计体现了HTA-AD在效率和性能之间的平衡，通过合理的参数选择可以适应不同的应用场景。
