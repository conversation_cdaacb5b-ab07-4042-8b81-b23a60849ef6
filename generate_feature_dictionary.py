"""
Generate Feature Dictionary Visualization for HTA-AD Paper
This script creates a visualization showing different types of temporal patterns learned by SAE features.
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Set publication style
plt.rcParams.update({
    'font.size': 11,
    'font.family': 'serif',
    'axes.linewidth': 1.2,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'xtick.major.size': 5,
    'ytick.major.size': 5,
    'legend.frameon': False,
    'figure.dpi': 300
})

def generate_pattern_examples():
    """Generate example temporal patterns for different anomaly types"""
    np.random.seed(42)
    
    # Time axis
    t = np.linspace(0, 10, 100)
    patterns = {}
    
    # 1. Spike Patterns (Features 0-31)
    spike_patterns = []
    for i in range(4):
        base_signal = 0.3 * np.sin(0.5 * t) + 0.1 * np.random.randn(len(t))
        spike_pos = 30 + i * 15
        spike_width = 3 + i
        spike_height = 2 + i * 0.5
        
        # Add spike
        spike_mask = np.abs(np.arange(len(t)) - spike_pos) <= spike_width
        base_signal[spike_mask] += spike_height * np.exp(-0.5 * ((np.arange(len(t))[spike_mask] - spike_pos) / (spike_width/2))**2)
        
        spike_patterns.append(base_signal)
    
    patterns['Spike Patterns'] = spike_patterns
    
    # 2. Level Shift Patterns (Features 32-63)
    level_shift_patterns = []
    for i in range(4):
        base_signal = 0.2 * np.sin(0.3 * t) + 0.1 * np.random.randn(len(t))
        shift_pos = 40 + i * 5
        shift_magnitude = 1.5 + i * 0.3
        
        # Add level shift
        base_signal[shift_pos:] += shift_magnitude
        
        level_shift_patterns.append(base_signal)
    
    patterns['Level Shift Patterns'] = level_shift_patterns
    
    # 3. Oscillatory Patterns (Features 64-95)
    oscillatory_patterns = []
    for i in range(4):
        base_signal = 0.2 * np.sin(0.2 * t) + 0.1 * np.random.randn(len(t))
        osc_freq = 2 + i * 0.5
        osc_amplitude = 1.2 + i * 0.2
        osc_start = 25 + i * 5
        osc_end = 75 - i * 5
        
        # Add oscillatory anomaly
        osc_mask = (np.arange(len(t)) >= osc_start) & (np.arange(len(t)) <= osc_end)
        base_signal[osc_mask] += osc_amplitude * np.sin(osc_freq * t[osc_mask])
        
        oscillatory_patterns.append(base_signal)
    
    patterns['Oscillatory Patterns'] = oscillatory_patterns
    
    # 4. Discontinuity Patterns (Features 96-127)
    discontinuity_patterns = []
    for i in range(4):
        base_signal = 0.3 * np.sin(0.4 * t) + 0.1 * np.random.randn(len(t))
        jump_pos = 35 + i * 8
        jump_magnitude = 2 + i * 0.4
        
        # Add discontinuity
        base_signal[jump_pos:] += jump_magnitude * (-1)**i
        
        # Add some smoothing around the jump for realism
        smooth_width = 2
        for j in range(max(0, jump_pos-smooth_width), min(len(t), jump_pos+smooth_width)):
            weight = 1 - abs(j - jump_pos) / smooth_width
            base_signal[j] = base_signal[j] * (1 - weight) + (base_signal[jump_pos-1] + base_signal[jump_pos]) * 0.5 * weight
        
        discontinuity_patterns.append(base_signal)
    
    patterns['Discontinuity Patterns'] = discontinuity_patterns
    
    return patterns, t

def create_feature_dictionary_figure():
    """Create the feature dictionary visualization"""
    patterns, t = generate_pattern_examples()

    fig, axes = plt.subplots(4, 4, figsize=(20, 16))
    # Remove the main title - will be handled by LaTeX
    
    pattern_types = list(patterns.keys())
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    for row, (pattern_type, color) in enumerate(zip(pattern_types, colors)):
        pattern_list = patterns[pattern_type]
        
        for col, pattern in enumerate(pattern_list):
            ax = axes[row, col]
            
            # Plot the pattern
            ax.plot(t, pattern, linewidth=2.5, color=color, alpha=0.8)
            ax.fill_between(t, pattern, alpha=0.3, color=color)
            
            # Highlight the anomalous region
            if 'Spike' in pattern_type:
                anomaly_region = (t >= 3) & (t <= 4)
                ax.fill_between(t[anomaly_region], pattern[anomaly_region], 
                               alpha=0.6, color='red', label='Anomaly Region')
            elif 'Level Shift' in pattern_type:
                shift_point = 4 + col * 0.5
                anomaly_region = t >= shift_point
                ax.axvline(shift_point, color='red', linestyle='--', linewidth=2, alpha=0.8)
            elif 'Oscillatory' in pattern_type:
                osc_start = 2.5 + col * 0.5
                osc_end = 7.5 - col * 0.5
                anomaly_region = (t >= osc_start) & (t <= osc_end)
                ax.axvspan(osc_start, osc_end, alpha=0.2, color='red')
            elif 'Discontinuity' in pattern_type:
                jump_point = 3.5 + col * 0.8
                ax.axvline(jump_point, color='red', linestyle='--', linewidth=2, alpha=0.8)
            
            # Formatting - each row represents a pattern type, each column shows a variant
            # Row 0: Spike Patterns (Features 0-31)
            # Row 1: Level Shift Patterns (Features 32-63)
            # Row 2: Oscillatory Patterns (Features 64-95)
            # Row 3: Discontinuity Patterns (Features 96-127)
            base_feature = row * 32  # Base feature number for this pattern type
            feature_num = base_feature + col * 8 + np.random.randint(0, 8)  # Add some variation within the range
            ax.set_title(f'Feature #{feature_num}', fontweight='bold', fontsize=11)
            ax.set_xlabel('Time' if row == 3 else '')
            ax.set_ylabel('Amplitude' if col == 0 else '')
            ax.grid(True, alpha=0.3)
            ax.set_xlim(0, 10)
            
            # Add pattern type label on the leftmost column with better positioning
            if col == 0:
                ax.text(-0.35, 0.5, pattern_type.replace(' ', '\n'),
                       transform=ax.transAxes, rotation=90,
                       verticalalignment='center', horizontalalignment='center',
                       fontsize=12, fontweight='bold', color=color)

            # Add feature range label for each row (pattern type)
            if col == len(pattern_list) - 1:  # Add label on the rightmost column
                start_feature = row * 32
                end_feature = start_feature + 31
                feature_range = f'Features {start_feature}-{end_feature}'
                ax.text(1.15, 0.5, feature_range, transform=ax.transAxes,
                       rotation=270, verticalalignment='center', horizontalalignment='center',
                       fontsize=11, fontweight='bold', color='black')
    
    # Add a legend
    legend_elements = [
        plt.Line2D([0], [0], color='#FF6B6B', lw=3, label='Spike Patterns'),
        plt.Line2D([0], [0], color='#4ECDC4', lw=3, label='Level Shift Patterns'),
        plt.Line2D([0], [0], color='#45B7D1', lw=3, label='Oscillatory Patterns'),
        plt.Line2D([0], [0], color='#96CEB4', lw=3, label='Discontinuity Patterns'),
        plt.Line2D([0], [0], color='red', lw=2, linestyle='--', alpha=0.8, label='Anomaly Indicator')
    ]
    
    fig.legend(handles=legend_elements, loc='lower center', ncol=5, 
               bbox_to_anchor=(0.5, -0.02), fontsize=11)
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15, top=0.85, left=0.15, right=0.95, hspace=0.5, wspace=0.4)
    plt.savefig('feature_dictionary_visualization.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.show()

def create_sae_framework_diagram():
    """Create SAE framework architecture diagram"""
    fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    # Remove main title - will be handled by LaTeX
    
    # (a) Pre-training Phase
    ax1 = axes[0]
    ax1.set_title('(a) SAE Pre-training Phase', fontweight='bold', fontsize=14)
    
    # Draw architecture boxes
    boxes = [
        {'xy': (0.1, 0.7), 'width': 0.15, 'height': 0.2, 'label': 'HTA-AD\nLatent\nVectors\n(32D)', 'color': 'lightblue'},
        {'xy': (0.35, 0.5), 'width': 0.15, 'height': 0.4, 'label': 'SAE\nEncoder\n(32→128)', 'color': 'lightgreen'},
        {'xy': (0.6, 0.3), 'width': 0.15, 'height': 0.6, 'label': 'Sparse\nFeatures\n(128D)', 'color': 'yellow'},
        {'xy': (0.35, 0.1), 'width': 0.15, 'height': 0.2, 'label': 'SAE\nDecoder\n(128→32)', 'color': 'lightcoral'},
    ]
    
    for box in boxes:
        rect = plt.Rectangle(box['xy'], box['width'], box['height'], 
                           facecolor=box['color'], edgecolor='black', linewidth=2)
        ax1.add_patch(rect)
        ax1.text(box['xy'][0] + box['width']/2, box['xy'][1] + box['height']/2, 
                box['label'], ha='center', va='center', fontweight='bold', fontsize=10)
    
    # Draw arrows
    arrows = [
        {'start': (0.25, 0.8), 'end': (0.35, 0.7)},
        {'start': (0.5, 0.7), 'end': (0.6, 0.6)},
        {'start': (0.6, 0.4), 'end': (0.5, 0.3)},
        {'start': (0.35, 0.2), 'end': (0.25, 0.2)},
    ]
    
    for arrow in arrows:
        ax1.annotate('', xy=arrow['end'], xytext=arrow['start'],
                    arrowprops=dict(arrowstyle='->', lw=2, color='black'))
    
    # Add loss function
    ax1.text(0.1, 0.05, 'Loss = ||x - x̂||² + λ||f||₁', fontsize=12, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    
    # (b) Inference Phase
    ax2 = axes[1]
    ax2.set_title('(b) SAE Inference with Feature Attribution', fontweight='bold', fontsize=14)
    
    # Draw inference flow
    boxes2 = [
        {'xy': (0.05, 0.7), 'width': 0.12, 'height': 0.15, 'label': 'New\nSample', 'color': 'lightblue'},
        {'xy': (0.25, 0.6), 'width': 0.12, 'height': 0.3, 'label': 'SAE\nEncoder', 'color': 'lightgreen'},
        {'xy': (0.45, 0.4), 'width': 0.12, 'height': 0.5, 'label': 'Feature\nActivations', 'color': 'yellow'},
        {'xy': (0.65, 0.6), 'width': 0.12, 'height': 0.3, 'label': 'Feature\nMasking', 'color': 'orange'},
        {'xy': (0.82, 0.7), 'width': 0.12, 'height': 0.15, 'label': 'Anomaly\nScore', 'color': 'lightcoral'},
    ]
    
    for box in boxes2:
        rect = plt.Rectangle(box['xy'], box['width'], box['height'], 
                           facecolor=box['color'], edgecolor='black', linewidth=2)
        ax2.add_patch(rect)
        ax2.text(box['xy'][0] + box['width']/2, box['xy'][1] + box['height']/2, 
                box['label'], ha='center', va='center', fontweight='bold', fontsize=9)
    
    # Draw arrows for inference
    arrows2 = [
        {'start': (0.17, 0.77), 'end': (0.25, 0.75)},
        {'start': (0.37, 0.75), 'end': (0.45, 0.65)},
        {'start': (0.57, 0.65), 'end': (0.65, 0.75)},
        {'start': (0.77, 0.75), 'end': (0.82, 0.77)},
    ]
    
    for arrow in arrows2:
        ax2.annotate('', xy=arrow['end'], xytext=arrow['start'],
                    arrowprops=dict(arrowstyle='->', lw=2, color='black'))
    
    # Add explanation box
    explanation = """Feature Attribution:
• Feature #37: Spike (3.2×)
• Feature #89: Level shift (2.1×)
• Feature #115: Oscillation (1.8×)"""
    
    ax2.text(0.45, 0.15, explanation, fontsize=10, 
            bbox=dict(boxstyle="round,pad=0.4", facecolor="lightyellow"),
            verticalalignment='top')
    
    ax2.set_xlim(0, 1)
    ax2.set_ylim(0, 1)
    ax2.axis('off')
    
    plt.tight_layout()
    plt.savefig('sae_framework_diagram.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.show()

def main():
    """Generate all interpretability figures"""
    print("🎨 Generating Feature Dictionary Visualization...")
    create_feature_dictionary_figure()
    print("✅ Feature dictionary saved as 'feature_dictionary_visualization.png'")
    
    print("\n🏗️ Generating SAE Framework Diagram...")
    create_sae_framework_diagram()
    print("✅ Framework diagram saved as 'sae_framework_diagram.png'")
    
    print("\n🎉 All interpretability figures generated successfully!")
    print("📋 Use these figures in your paper:")
    print("   • feature_dictionary_visualization.png - Shows learned pattern types")
    print("   • sae_framework_diagram.png - Shows SAE architecture and workflow")

if __name__ == "__main__":
    main()
