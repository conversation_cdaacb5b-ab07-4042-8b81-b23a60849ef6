#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验2: 潜空间可视化 (图2)
对比 HTA-AD 和 AnomalyTransformer 学习到的潜空间结构
"""

import numpy as np
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import sys
import os
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import torch

# 确保可以导入 TSB_AD 模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict

def generate_complex_timeseries():
    """生成复杂的时间序列数据用于潜空间分析"""
    print("📊 尝试加载真实数据集进行潜空间分析...")
    
    # 首先尝试加载真实数据集
    try:
        datasets_to_try = [
            'Datasets/TSB-AD-M/057_SMD_id_1_Facility_tr_4529_1st_4629.csv',
            'Datasets/TSB-AD-M/001_Genesis_id_1_Sensor_tr_4055_1st_15538.csv'
        ]
        
        for dataset_path in datasets_to_try:
            data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), dataset_path)
            if os.path.exists(data_path):
                print(f"✅ 加载真实数据: {dataset_path}")
                df = pd.read_csv(data_path).dropna()
                
                if 'Label' in df.columns:
                    data = df.iloc[:, :-1].values.astype(float)
                    labels_numeric = df['Label'].astype(int).to_numpy()
                else:
                    data = df.values.astype(float)
                    labels_numeric = np.zeros(len(data))
                
                # 截取一个合理的长度进行分析
                max_length = 3000
                if len(data) > max_length:
                    data = data[:max_length]
                    labels_numeric = labels_numeric[:max_length]
                
                # 创建模式标签
                patterns_labels = []
                for i, label in enumerate(labels_numeric):
                    if label == 1:
                        patterns_labels.append('异常')
                    elif i < len(data) // 4:
                        patterns_labels.append('模式A')
                    elif i < len(data) // 2:
                        patterns_labels.append('模式B')  
                    elif i < len(data) * 3 // 4:
                        patterns_labels.append('模式C')
                    else:
                        patterns_labels.append('模式D')
                
                print(f"✅ 真实数据加载完成: {data.shape}, 包含 {len(set(patterns_labels))} 种模式")
                return data, patterns_labels
                
    except Exception as e:
        print(f"⚠️ 真实数据加载失败: {e}")
    
    # Fallback: 生成模拟数据
    print("🔧 生成复杂时间序列数据...")
    
    np.random.seed(42)
    length = 1500
    time = np.arange(length)
    
    # 创建多种不同的时间模式
    patterns = []
    labels = []
    
    # 模式1: 正弦波
    pattern1 = np.sin(time * 0.1) + 0.1 * np.random.randn(length)
    patterns.append(pattern1)
    labels.extend(['正弦波'] * length)
    
    # 模式2: 锯齿波
    pattern2 = 2 * (time % 50) / 50 - 1 + 0.1 * np.random.randn(length)
    patterns.append(pattern2)
    labels.extend(['锯齿波'] * length)
    
    # 模式3: 脉冲信号
    pattern3 = np.zeros(length)
    for i in range(0, length, 100):
        if i + 10 < length:
            pattern3[i:i+10] = 1
    pattern3 += 0.1 * np.random.randn(length)
    patterns.append(pattern3)
    labels.extend(['脉冲'] * length)
    
    # 模式4: 异常模式（突然的峰值）
    pattern4 = 0.1 * np.random.randn(length)
    for i in range(0, length, 200):
        if i < length:
            pattern4[i] = 3
    patterns.append(pattern4)
    labels.extend(['异常'] * length)
    
    # 合并所有模式
    all_data = np.concatenate(patterns)
    all_labels = labels
    
    # 转换为多维数据
    data_2d = np.column_stack([
        all_data, 
        all_data * 0.8 + 0.1 * np.random.randn(len(all_data))
    ])
    
    print(f"✅ 模拟数据生成完成: {data_2d.shape}, 包含 {len(set(all_labels))} 种模式")
    return data_2d, all_labels

def extract_latent_representations(model, data, window_size=128):
    """从训练好的模型中提取潜空间表示"""
    print("🧠 提取潜空间表示...")
    
    try:
        model.model.eval()
        
        # 创建窗口
        if hasattr(model, '_create_windows'):
            windows = model._create_windows(model.ts_scaler.transform(data) if model.normalize else data)
        else:
            # 手动创建窗口
            if len(data) < window_size:
                return np.array([]), np.array([])
            
            windows = []
            for i in range(len(data) - window_size + 1):
                windows.append(data[i:i+window_size])
            windows = np.array(windows)
        
        if len(windows) == 0:
            return np.array([]), np.array([])
        
        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=64, shuffle=False)
        
        latent_vectors = []
        window_indices = []
        
        with torch.no_grad():
            for i, (batch_windows,) in enumerate(loader):
                batch_windows = batch_windows.to(model.device)
                
                # 获取潜空间表示
                x_permuted = batch_windows.permute(0, 2, 1)
                encoded_cnn = model.model.encoder_cnn(x_permuted)
                encoded_tcn = model.model.encoder_tcn(encoded_cnn)
                encoded_flat = encoded_tcn.flatten(start_dim=1)
                latent_vec = model.model.fc_encode(encoded_flat)
                
                latent_vectors.extend(latent_vec.cpu().numpy())
                
                # 记录窗口中心的索引
                start_idx = i * 64
                for j in range(len(latent_vec)):
                    window_idx = start_idx + j
                    center_idx = window_idx + window_size // 2
                    window_indices.append(center_idx)
        
        return np.array(latent_vectors), np.array(window_indices)
        
    except Exception as e:
        print(f"潜空间提取失败: {e}")
        return np.array([]), np.array([])

def create_mock_transformer_latents(data, window_size=128):
    """创建模拟的Transformer潜空间表示（展示离散、无序的特点）"""
    print("🤖 创建模拟的Transformer潜空间...")
    
    # 模拟Transformer的"离散、无序"潜空间
    num_windows = max(0, len(data) - window_size + 1)
    latent_dim = 32
    
    # 创建几个离散的聚类中心
    cluster_centers = np.random.randn(5, latent_dim) * 3
    
    latent_vectors = []
    window_indices = []
    
    for i in range(num_windows):
        # 随机选择一个聚类中心，添加噪声
        cluster_idx = np.random.randint(0, len(cluster_centers))
        latent_vec = cluster_centers[cluster_idx] + 0.5 * np.random.randn(latent_dim)
        latent_vectors.append(latent_vec)
        
        center_idx = i + window_size // 2
        window_indices.append(center_idx)
    
    return np.array(latent_vectors), np.array(window_indices)

def run_latent_space_analysis():
    """运行潜空间分析实验"""
    print("🧪 开始潜空间分析实验...")
    
    # 生成数据
    data, pattern_labels = generate_complex_timeseries()
    
    # 训练 HTA-AD 模型
    print("🤖 训练 HTA-AD 模型...")
    train_size = int(len(data) * 0.8)
    train_data = data[:train_size]
    
    hta_hp = Optimal_Uni_algo_HP_dict.get('HTA_AD', {})
    hta_hp['epochs'] = 30
    hta_hp['window_size'] = 64  # 使用较小的窗口以获得更多样本
    
    hta_model = HTA_AD(HP=hta_hp, normalize=True)
    hta_model.fit(train_data)
    
    # 提取潜空间表示
    print("🧠 提取 HTA-AD 潜空间表示...")
    hta_latents, hta_indices = extract_latent_representations(hta_model, data, hta_hp['window_size'])
    
    print("🤖 生成模拟 Transformer 潜空间...")
    at_latents, at_indices = create_mock_transformer_latents(data, hta_hp['window_size'])
    
    if len(hta_latents) == 0 or len(at_latents) == 0:
        print("❌ 潜空间提取失败")
        return
    
    # 为潜空间点分配模式标签
    hta_pattern_labels = [pattern_labels[min(idx, len(pattern_labels)-1)] for idx in hta_indices]
    at_pattern_labels = [pattern_labels[min(idx, len(pattern_labels)-1)] for idx in at_indices]
    
    # 创建可视化
    create_latent_space_plot(hta_latents, at_latents, hta_pattern_labels, at_pattern_labels)
    
    return hta_latents, at_latents, hta_pattern_labels, at_pattern_labels

def create_latent_space_plot(hta_latents, at_latents, hta_labels, at_labels):
    """创建潜空间可视化图"""
    print("📊 创建潜空间可视化...")
    
    # 使用t-SNE降维到2D
    print("🔄 执行t-SNE降维...")
    
    # 合并数据进行一致的降维
    all_latents = np.vstack([hta_latents, at_latents])
    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    all_2d = tsne.fit_transform(all_latents)
    
    # 分离结果
    hta_2d = all_2d[:len(hta_latents)]
    at_2d = all_2d[len(hta_latents):]
    
    # 创建子图
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=[
            "HTA-AD 潜空间 (连续、有序流形)",
            "AnomalyTransformer 潜空间 (离散、无序点集)"
        ],
        horizontal_spacing=0.1
    )
    
    # 定义颜色映射
    unique_labels = list(set(hta_labels + at_labels))
    colors = px.colors.qualitative.Set1[:len(unique_labels)]
    color_map = dict(zip(unique_labels, colors))
    
    # HTA-AD 潜空间 (左图)
    for label in unique_labels:
        mask = np.array(hta_labels) == label
        if np.any(mask):
            fig.add_trace(
                go.Scatter(
                    x=hta_2d[mask, 0],
                    y=hta_2d[mask, 1],
                    mode='markers',
                    name=f'HTA-{label}',
                    marker=dict(
                        color=color_map[label],
                        size=8,
                        opacity=0.7,
                        line=dict(width=1, color='white')
                    ),
                    legendgroup='hta',
                    legendgrouptitle_text="HTA-AD"
                ),
                row=1, col=1
            )
    
    # AnomalyTransformer 潜空间 (右图)
    for label in unique_labels:
        mask = np.array(at_labels) == label
        if np.any(mask):
            fig.add_trace(
                go.Scatter(
                    x=at_2d[mask, 0],
                    y=at_2d[mask, 1],
                    mode='markers',
                    name=f'AT-{label}',
                    marker=dict(
                        color=color_map[label],
                        size=8,
                        opacity=0.7,
                        symbol='diamond',
                        line=dict(width=1, color='white')
                    ),
                    legendgroup='at',
                    legendgrouptitle_text="AnomalyTransformer"
                ),
                row=1, col=2
            )
    
    # 添加流形结构指示（仅对HTA-AD）
    # 为HTA-AD添加连接线以显示连续性
    for label in unique_labels:
        mask = np.array(hta_labels) == label
        if np.any(mask):
            label_points = hta_2d[mask]
            if len(label_points) > 1:
                # 按时间顺序连接点（这里简化为按x坐标排序）
                sorted_indices = np.argsort(label_points[:, 0])
                sorted_points = label_points[sorted_indices]
                
                fig.add_trace(
                    go.Scatter(
                        x=sorted_points[:, 0],
                        y=sorted_points[:, 1],
                        mode='lines',
                        line=dict(
                            color=color_map[label],
                            width=1,
                            dash='dot'
                        ),
                        opacity=0.3,
                        showlegend=False,
                        hoverinfo='skip'
                    ),
                    row=1, col=1
                )
    
    # 更新布局
    fig.update_layout(
        title={
            'text': "潜空间结构对比：HTA-AD vs AnomalyTransformer",
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 16}
        },
        height=600,
        template='plotly_white',
        font=dict(family="Arial, sans-serif", size=12),
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.01,
            groupclick="toggleitem"
        )
    )
    
    # 更新轴标签
    fig.update_xaxes(title_text="t-SNE 维度 1", row=1, col=1)
    fig.update_xaxes(title_text="t-SNE 维度 1", row=1, col=2)
    fig.update_yaxes(title_text="t-SNE 维度 2", row=1, col=1)
    fig.update_yaxes(title_text="t-SNE 维度 2", row=1, col=2)
    
    # 保存图片
    output_path = os.path.join(project_root, 'latent_space_comparison.html')
    fig.write_html(output_path)
    print(f"✅ 潜空间对比图已保存: {output_path}")
    
    try:
        png_path = os.path.join(project_root, 'latent_space_comparison.png')
        fig.write_image(png_path, width=1400, height=600, scale=2)
        print(f"✅ PNG格式已保存: {png_path}")
    except:
        print("⚠️  PNG保存失败，请安装 kaleido: pip install kaleido")
    
    # 计算流形质量指标
    calculate_manifold_quality(hta_2d, at_2d, hta_labels, at_labels)
    
    return fig

def calculate_manifold_quality(hta_2d, at_2d, hta_labels, at_labels):
    """计算流形质量指标"""
    print("\n📊 计算流形质量指标...")
    
    from sklearn.metrics import silhouette_score
    from sklearn.neighbors import NearestNeighbors
    
    # 计算轮廓系数
    try:
        # 将标签转换为数值
        unique_labels = list(set(hta_labels + at_labels))
        hta_numeric_labels = [unique_labels.index(label) for label in hta_labels]
        at_numeric_labels = [unique_labels.index(label) for label in at_labels]
        
        hta_silhouette = silhouette_score(hta_2d, hta_numeric_labels)
        at_silhouette = silhouette_score(at_2d, at_numeric_labels)
        
        print(f"HTA-AD 轮廓系数: {hta_silhouette:.4f}")
        print(f"AnomalyTransformer 轮廓系数: {at_silhouette:.4f}")
        
        # 计算局部密度
        hta_nn = NearestNeighbors(n_neighbors=5).fit(hta_2d)
        at_nn = NearestNeighbors(n_neighbors=5).fit(at_2d)
        
        hta_distances, _ = hta_nn.kneighbors(hta_2d)
        at_distances, _ = at_nn.kneighbors(at_2d)
        
        hta_avg_distance = np.mean(hta_distances[:, 1:])  # 排除自身
        at_avg_distance = np.mean(at_distances[:, 1:])
        
        print(f"HTA-AD 平均近邻距离: {hta_avg_distance:.4f}")
        print(f"AnomalyTransformer 平均近邻距离: {at_avg_distance:.4f}")
        
        print(f"\n🎯 结论:")
        print(f"- 更高的轮廓系数表示更好的聚类结构")
        print(f"- 更小的近邻距离表示更紧密的流形结构")
        print(f"- HTA-AD 展现了{'更好' if hta_silhouette > at_silhouette else '较差'}的聚类结构")
        
    except Exception as e:
        print(f"质量指标计算失败: {e}")

if __name__ == "__main__":
    print("🚀 启动潜空间可视化实验")
    print("=" * 60)
    
    # 运行实验
    try:
        hta_latents, at_latents, hta_labels, at_labels = run_latent_space_analysis()
        
        print("\n" + "=" * 60)
        print("🎉 潜空间可视化实验完成!")
        print("📊 可视化结果已保存到项目根目录")
        
    except Exception as e:
        print(f"❌ 实验失败: {e}")
        import traceback
        traceback.print_exc()