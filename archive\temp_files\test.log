This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.7.23)  23 JUL 2025 01:22
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**test.tex
(./test.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(c:/texlive/2024/texmf-dist/tex/latex/ieeetran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count188
\@IEEEtrantmpcountB=\count189
\@IEEEtrantmpcountC=\count190
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 5
03.
(c:/texlive/2024/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count191
\c@subsection=\count192
\c@subsubsection=\count193
\c@paragraph=\count194
\c@IEEEsubequation=\count195
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count196
\c@table=\count197
\@IEEEeqnnumcols=\count198
\@IEEEeqncolcnt=\count199
\@IEEEsubeqnnumrollback=\count266
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count267
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count268
\@IEEEtranrubishbin=\box52
)
** ATTENTION: Overriding command lockouts (line 2).
(c:/texlive/2024/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
)
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen164
))
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen165
)
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count269
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count270
\leftroot@=\count271
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count272
\DOTSCASE@=\count273
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen166
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count274
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count275
\dotsspace@=\muskip16
\c@parentequation=\count276
\dspbrk@lvl=\count277
\tag@help=\toks19
\row@=\count278
\column@=\count279
\maxfields@=\count280
\andhelp@=\toks20
\eqnshift@=\dimen167
\alignsep@=\dimen168
\tagshift@=\dimen169
\tagwidth@=\dimen170
\totwidth@=\dimen171
\lineht@=\dimen172
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(c:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(c:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen173
\Gin@req@width=\dimen174
)
(c:/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
)
(c:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(c:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(c:/texlive/2024/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)

(c:/texlive/2024/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(c:/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen175
\captionmargin=\dimen176
\caption@leftmargin=\dimen177
\caption@rightmargin=\dimen178
\caption@width=\dimen179
\caption@indent=\dimen180
\caption@parindent=\dimen181
\caption@hangindent=\dimen182
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEta
blestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\norma
lfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace
 {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptio
nsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspac
e \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\nor
malfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\n
ormalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONcon
ference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }
\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi 
\fi  on input line 1175.
)

Package caption Warning: Unknown document class (or package),
(caption)                standard defaults will be used.
See the caption package documentation for explanation.

\c@caption@flags=\count281
\c@continuedfloat=\count282
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count283
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count284
) (c:/texlive/2024/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip54
\enit@outerparindent=\dimen183
\enit@toks=\toks24
\enit@inbox=\box55
\enit@count@id=\count285
\enitdp@description=\count286
)
(c:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen184
\lightrulewidth=\dimen185
\cmidrulewidth=\dimen186
\belowrulesep=\dimen187
\belowbottomsep=\dimen188
\aboverulesep=\dimen189
\abovetopsep=\dimen190
\cmidrulesep=\dimen191
\cmidrulekern=\dimen192
\defaultaddspace=\dimen193
\@cmidla=\count287
\@cmidlb=\count288
\@aboverulesep=\dimen194
\@belowrulesep=\dimen195
\@thisruleclass=\count289
\@lastruleclass=\count290
\@thisrulewidth=\dimen196
)
(c:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip55
\multirow@cntb=\count291
\multirow@dima=\skip56
\bigstrutjot=\dimen197
)
(c:/texlive/2024/texmf-dist/tex/latex/threeparttable/threeparttable.sty
Package: threeparttable 2003/06/13  v 3.0
\@tempboxb=\box56
)
(c:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'

(c:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\c@ALC@unique=\count292
\c@ALC@line=\count293
\c@ALC@rem=\count294
\c@ALC@depth=\count295
\ALC@tlm=\skip57
\algorithmicindent=\skip58
)
(c:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(c:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count296
\float@exts=\toks25
\float@box=\box57
\@float@everytoks=\toks26
\@floatcapt=\box58
)
\@float@every@algorithm=\toks27
\c@algorithm=\count297
)
(c:/texlive/2024/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(c:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(c:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks28
\pgfutil@tempdima=\dimen198
\pgfutil@tempdimb=\dimen199
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box59
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(c:/texlive/2024/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks29
\pgfkeys@temptoks=\toks30

(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.co
de.tex
\pgfkeys@tmptoks=\toks31
))
\pgf@x=\dimen256
\pgf@y=\dimen257
\pgf@xa=\dimen258
\pgf@ya=\dimen259
\pgf@xb=\dimen260
\pgf@yb=\dimen261
\pgf@xc=\dimen262
\pgf@yc=\dimen263
\pgf@xd=\dimen264
\pgf@yd=\dimen265
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count298
\c@pgf@countb=\count299
\c@pgf@countc=\count300
\c@pgf@countd=\count301
\t@pgf@toka=\toks32
\t@pgf@tokb=\toks33
\t@pgf@tokc=\toks34
\pgf@sys@id@count=\count302
 (c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count303
\pgfsyssoftpath@bigbuffer@items=\count304
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen266
\pgfmath@count=\count305
\pgfmath@box=\box60
\pgfmath@toks=\toks35
\pgfmath@stack@operand=\toks36
\pgfmath@stack@operation=\toks37
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.te
x)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric
.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.t
ex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.co
de.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.te
x)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithm
etics.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count306
))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen267
\pgf@picmaxx=\dimen268
\pgf@picminy=\dimen269
\pgf@picmaxy=\dimen270
\pgf@pathminx=\dimen271
\pgf@pathmaxx=\dimen272
\pgf@pathminy=\dimen273
\pgf@pathmaxy=\dimen274
\pgf@xx=\dimen275
\pgf@xy=\dimen276
\pgf@yx=\dimen277
\pgf@yy=\dimen278
\pgf@zx=\dimen279
\pgf@zy=\dimen280
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.cod
e.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen281
\pgf@path@lasty=\dimen282
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.te
x
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen283
\pgf@shorten@start@additional=\dimen284
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box61
\pgf@hbox=\box62
\pgf@layerbox@main=\box63
\pgf@picture@serial@count=\count307
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code
.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen285
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.c
ode.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen286
\pgf@pt@y=\dimen287
\pgf@pt@temp=\dimen288
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.co
de.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen289
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen290
\pgf@sys@shading@range@num=\count308
\pgf@shadingcount=\count309
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box64
) (c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code
.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (c:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.te
x
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box65
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.st
y
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen291
\pgf@nodesepend=\dimen292
)
(c:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.st
y
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (c:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(c:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(c:/texlive/2024/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen293
\pgffor@skip=\dimen294
\pgffor@stack=\toks38
\pgffor@toks=\toks39
))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.co
de.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count310
\pgfplotmarksize=\dimen295
)
\tikz@lastx=\dimen296
\tikz@lasty=\dimen297
\tikz@lastxsaved=\dimen298
\tikz@lastysaved=\dimen299
\tikz@lastmovetox=\dimen300
\tikz@lastmovetoy=\dimen301
\tikzleveldistance=\dimen302
\tikzsiblingdistance=\dimen303
\tikz@figbox=\box66
\tikz@figbox@bg=\box67
\tikz@tempbox=\box68
\tikz@tempbox@bg=\box69
\tikztreelevel=\count311
\tikznumberofchildren=\count312
\tikznumberofcurrentchild=\count313
\tikz@fig@count=\count314

(c:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count315
\pgfmatrixcurrentcolumn=\count316
\pgf@matrix@numberofcolumns=\count317
)
\tikz@expandcount=\count318

(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(c:/texlive/2024/texmf-dist/tex/latex/pgfplots/pgfplots.sty
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks40
\t@pgfplots@tokb=\toks41
\t@pgfplots@tokc=\toks42
\pgfplots@tmpa=\dimen304
\c@pgfplots@coordindex=\count319
\c@pgfplots@scanlineindex=\count320

(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code.te
x))
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.tex)

(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfs
upp_loader.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks43
\t@pgf@tokb=\toks44
\t@pgf@tokc=\toks45

(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfs
upp_pgfutil-common-lists.tex))
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststru
cture.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststru
ctureext.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray.co
de.tex
\c@pgfplotsarray@tmp=\count321
)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatrix.c
ode.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshared.c
ode.tex
\c@pgfplotstable@counta=\count322
\t@pgfplotstable@a=\toks46
)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque.co
de.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.code.
tex))
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code.te
x)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.surfsh
ading.code.tex
\c@pgfplotslibrarysurf@no=\count323

(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfsha
ding.pgfsys-pdftex.def)))
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex))
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.code.t
ex
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.tex
))) (c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex)

(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.code.t
ex)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarydecorations.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.code.t
ex
\pgfdecoratedcompleteddistance=\dimen305
\pgfdecoratedremainingdistance=\dimen306
\pgfdecoratedinputsegmentcompleteddistance=\dimen307
\pgfdecoratedinputsegmentremainingdistance=\dimen308
\pgf@decorate@distancetomove=\dimen309
\pgf@decorate@repeatstate=\count324
\pgfdecorationsegmentamplitude=\dimen310
\pgfdecorationsegmentlength=\dimen311
)
\tikz@lib@dec@box=\box70
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarydecorations.pathmorphing.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydec
orations.pathmorphing.code.tex))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarydecorations.pathreplacing.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydec
orations.pathreplacing.code.tex))
(c:/texlive/2024/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.conto
urlua.code.tex)
\pgfplots@numplots=\count325
\pgfplots@xmin@reg=\dimen312
\pgfplots@xmax@reg=\dimen313
\pgfplots@ymin@reg=\dimen314
\pgfplots@ymax@reg=\dimen315
\pgfplots@zmin@reg=\dimen316
\pgfplots@zmax@reg=\dimen317
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.code.
tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryshapes.code.tex
File: tikzlibraryshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.g
eometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.m
isc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryshapes.symbols.code.tex
File: tikzlibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.s
ymbols.code.tex
File: pgflibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryshapes.arrows.code.tex
File: tikzlibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.a
rrows.code.tex
File: pgflibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryshapes.callouts.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.c
allouts.code.tex))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryshapes.multipart.code.tex
File: tikzlibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.m
ultipart.code.tex
File: pgflibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodepartlowerbox=\box71
\pgfnodeparttwobox=\box72
\pgfnodepartthreebox=\box73
\pgfnodepartfourbox=\box74
\pgfnodeparttwentybox=\box75
\pgfnodepartnineteenbox=\box76
\pgfnodeparteighteenbox=\box77
\pgfnodepartseventeenbox=\box78
\pgfnodepartsixteenbox=\box79
\pgfnodepartfifteenbox=\box80
\pgfnodepartfourteenbox=\box81
\pgfnodepartthirteenbox=\box82
\pgfnodeparttwelvebox=\box83
\pgfnodepartelevenbox=\box84
\pgfnodeparttenbox=\box85
\pgfnodepartninebox=\box86
\pgfnodeparteightbox=\box87
\pgfnodepartsevenbox=\box88
\pgfnodepartsixbox=\box89
\pgfnodepartfivebox=\box90
)))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryarrows.code.tex
File: tikzlibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.tex
File: pgflibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\arrowsize=\dimen318
))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryshadows.code.tex
File: tikzlibraryshadows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryfadings.code.tex
File: tikzlibraryfadings.code.tex 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryfadings.code.te
x
File: pgflibraryfadings.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (c:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count326
\l__pdf_internal_box=\box91
)
(./test.aux)
\openout1 = `test.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.

-- Lines per column: 56 (exact).
(c:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count327
\scratchdimen=\dimen319
\scratchbox=\box92
\nofMPsegments=\count328
\nofMParguments=\count329
\everyMPshowfont=\toks47
\MPscratchCnt=\count330
\MPscratchDim=\dimen320
\MPnumerator=\count331
\makeMPintoPDFobject=\count332
\everyMPtoPDFconversion=\toks48
) (c:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(c:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: threeparttable package is loaded.
Package caption Info: End \AtBeginDocument code.


Package pgfplots Warning: running in backwards compatibility mode (unsuitable t
ick labels; missing features). Consider writing \pgfplotsset{compat=1.18} into 
your preamble.
 on input line 22.


LaTeX Warning: File `figures/hta_ad_vs_transformer_latent_space_beautified.png'
 not found on input line 78.


! Package pdftex.def Error: File `figures/hta_ad_vs_transformer_latent_space_be
autified.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.78 ...s_transformer_latent_space_beautified.png}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

LaTeX Font Info:    Trying to load font information for OT1+pcr on input line 7
8.
(c:/texlive/2024/texmf-dist/tex/latex/psnfss/ot1pcr.fd
File: ot1pcr.fd 2001/06/04 font definitions for OT1/pcr.
)
Underfull \vbox (badness 3407) has occurred while \output is active []

 [1{c:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{c:/texlive/20
24/texmf-dist/fonts/enc/dvips/base/8r.enc}


]
LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 1
07.

(c:/texlive/2024/texmf-dist/tex/latex/psnfss/ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
)
Underfull \hbox (badness 1496) in paragraph at lines 99--116
[]\OT1/ptm/m/n/10 We pro-pose the Hour-glass Tem-po-ral Au-toen-coder for
 []


Underfull \hbox (badness 2435) in paragraph at lines 119--122
\OT1/ptm/m/n/10 that de-com-poses la-tent rep-re-sen-ta-tions into human-
 []


LaTeX Warning: Citation `zhou2021informer' on page 2 undefined on input line 14
9.


LaTeX Warning: Citation `xu2022anomalytransformer' on page 2 undefined on input
 line 149.


LaTeX Warning: Citation `paparrizos2024tsb' on page 2 undefined on input line 1
54.


LaTeX Warning: Citation `deng2021graph' on page 2 undefined on input line 160.


LaTeX Warning: Citation `jin2024survey' on page 2 undefined on input line 160.


LaTeX Warning: Citation `deng2021graph' on page 2 undefined on input line 163.


LaTeX Warning: Citation `deng2021graph' on page 2 undefined on input line 166.


LaTeX Warning: Citation `zhao2024graph' on page 2 undefined on input line 168.

LaTeX Font Info:    Trying to load font information for U+msa on input line 183
.
(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 183
.

(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Warning: Citation `lecun1998gradient' on page 2 undefined on input line 1
83.


LaTeX Warning: Citation `bai2018empirical' on page 2 undefined on input line 18
4.


LaTeX Warning: Citation `xu2024permutation' on page 2 undefined on input line 1
90.


LaTeX Warning: Citation `zeng2023are' on page 2 undefined on input line 197.

[2]

LaTeX Warning: Citation `paparrizos2024tsb' on page 3 undefined on input line 2
04.


LaTeX Warning: Citation `paparrizos2024tsb' on page 3 undefined on input line 2
08.


LaTeX Warning: File `figures/hta_ad_architecture.pdf' not found on input line 2
57.


! Package pdftex.def Error: File `figures/hta_ad_architecture.pdf' not found: u
sing draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.257 ...xtwidth]{figures/hta_ad_architecture.pdf}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: File `figures/cnn_feature_analysis.png' not found on input line 
304.


! Package pdftex.def Error: File `figures/cnn_feature_analysis.png' not found: 
using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.304 ...nwidth]{figures/cnn_feature_analysis.png}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

[3]

LaTeX Warning: File `figures/TCN Block.pdf' not found on input line 333.


! Package pdftex.def Error: File `figures/TCN Block.pdf' not found: using draft
 setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.333 ...h=0.9\columnwidth]{figures/TCN Block.pdf}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: File `figures/tcn_receptive_field.png' not found on input line 3
52.


! Package pdftex.def Error: File `figures/tcn_receptive_field.png' not found: u
sing draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.352 ...mnwidth]{figures/tcn_receptive_field.png}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

[4]

LaTeX Warning: File `figures/SAE_train.pdf' not found on input line 483.


! Package pdftex.def Error: File `figures/SAE_train.pdf' not found: using draft
 setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.483 ...=0.75\columnwidth]{figures/SAE_train.pdf}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: File `figures/sae_inference.pdf' not found on input line 491.


! Package pdftex.def Error: File `figures/sae_inference.pdf' not found: using d
raft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.491 ...=\columnwidth]{figures/sae_inference.pdf}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

[5]
<figures/combined_tsb_ad_results.png, id=59, 1136.8071pt x 378.213pt>
File: figures/combined_tsb_ad_results.png Graphic file (type png)
<use figures/combined_tsb_ad_results.png>
Package pdftex.def Info: figures/combined_tsb_ad_results.png  used on input lin
e 598.
(pdftex.def)             Requested size: 516.0pt x 171.67743pt.


LaTeX Warning: File `figures/shuffling_experiment_Synthetic_Strong_Temporal_Dep
endencies.png' not found on input line 645.


! Package pdftex.def Error: File `figures/shuffling_experiment_Synthetic_Strong
_Temporal_Dependencies.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.645 ...nthetic_Strong_Temporal_Dependencies.png}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

[6]

LaTeX Warning: File `figures/reconstruction_analysis.png' not found on input li
ne 675.


! Package pdftex.def Error: File `figures/reconstruction_analysis.png' not foun
d: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.675 ...dth]{figures/reconstruction_analysis.png}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

<figures/interpretability_core.pdf, id=64, 783.8986pt x 548.45717pt>
File: figures/interpretability_core.pdf Graphic file (type pdf)
<use figures/interpretability_core.pdf>
Package pdftex.def Info: figures/interpretability_core.pdf  used on input line 
692.
(pdftex.def)             Requested size: 252.0pt x 176.31328pt.

Underfull \hbox (badness 1728) in paragraph at lines 693--693
\OT1/ptm/m/n/10 spike, level shift, os-cil-la-tory, and dis-con-ti-nu-ity pat-t
erns.
 []


LaTeX Warning: Reference `fig:interpretability_core' on page 7 undefined on inp
ut line 700.


LaTeX Warning: Reference `fig:interpretability_core' on page 7 undefined on inp
ut line 716.

[7]
No file test.bbl.
[8

] [9] [10] [11


] [12 <./figures/combined_tsb_ad_results.png>]
! Interruption.
<argument> ...\__shipout_drop_firstpage_specials: 
                                                  \set@typeset@protect \hook...
l.846 \onecolumn
                  % 切换到单栏格式
? 
! Emergency stop.
<argument> ...\__shipout_drop_firstpage_specials: 
                                                  \set@typeset@protect \hook...
l.846 \onecolumn
                  % 切换到单栏格式
End of file on the terminal!

 
Here is how much of TeX's memory you used:
 29074 strings out of 474116
 747606 string characters out of 5747717
 1959190 words of memory out of 5000000
 50963 multiletter control sequences out of 15000+600000
 596667 words of font info for 111 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 84i,11n,87p,869b,609s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!
