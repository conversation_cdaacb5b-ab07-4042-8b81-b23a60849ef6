# -*- coding: utf-8 -*-
# Lightweight Time Series Anomaly Detector (LTS_AD)
# A simple, effective, and lightweight model for time series anomaly detection.

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import random, argparse, time, os, sys
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.metrics import precision_recall_curve

warnings.filterwarnings('ignore')

# --- Matplotlib and Seaborn Setup ---
try:
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid")
    import matplotlib.font_manager as fm
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    # Check for a common Chinese font
    if any('SimHei' in f for f in available_fonts):
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        print("🎨 使用中文字体: SimHei")
    else:
        print("🎨 未找到中文字体，将使用默认字体")
except Exception as e:
    print(f"🎨 字体设置失败: {e}")

# --- TSB-AD Imports ---
# This structure assumes the script might be run from different locations.
# It tries to add the project root to sys.path for robust imports.
try:
    from TSB_AD.models.base import BaseDetector
    from TSB_AD.utils.slidingWindows import find_length_rank
    from TSB_AD.evaluation.metrics import get_metrics
except ImportError:
    # Get the directory of the current script
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Get the parent directory (project root)
    project_root = os.path.dirname(current_dir)
    # Add the project root to the Python path
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    print(f"✅ Added project root to path: {project_root}")
    try:
        from TSB_AD.models.base import BaseDetector
        from TSB_AD.utils.slidingWindows import find_length_rank
        from TSB_AD.evaluation.metrics import get_metrics
    except ImportError:
        print("❌ 无法导入TSB_AD包。请确保在项目根目录下运行 `pip install -e .` 来安装项目。")
        sys.exit(1)


# ----------------------------------------------------
# 1. LTS_M_Model Definition (Multivariate)
# ----------------------------------------------------
class LTS_M_Model(nn.Module):
    def __init__(self, input_dim, window_size=100, latent_dim=16, rnn_layers=1):
        super(LTS_M_Model, self).__init__()
        self.input_dim = input_dim
        self.window_size = window_size
        self.latent_dim = latent_dim
        
        self.channel_mixer = nn.Linear(input_dim, input_dim)
        
        # --- Encoder (preserved from original LTS_AD) ---
        self.encoder_cnn = nn.Sequential(
            nn.Conv1d(in_channels=input_dim, out_channels=8 * input_dim, kernel_size=7, padding=3, stride=2, groups=input_dim),
            nn.GELU(),
            nn.Conv1d(in_channels=8 * input_dim, out_channels=16 * input_dim, kernel_size=5, padding=2, stride=2, groups=input_dim),
            nn.GELU(),
            nn.Conv1d(in_channels=16 * input_dim, out_channels=32 * input_dim, kernel_size=3, padding=1, stride=2, groups=input_dim),
            nn.GELU(),
        )
        
        self.encoder_output_size = self._get_conv_output_size(window_size)
        
        self.encoder_gru = nn.GRU(
            input_size=32 * input_dim, 
            hidden_size=latent_dim, 
            num_layers=rnn_layers, 
            batch_first=True
        )

        # --- Decoder (preserved from original LTS_AD) ---
        self.decoder_gru = nn.GRU(
            input_size=latent_dim, 
            hidden_size=latent_dim, 
            num_layers=rnn_layers, 
            batch_first=True
        )
        
        self.decoder_proj = nn.Linear(latent_dim, 32 * self.input_dim)

        self.decoder_cnn = nn.Sequential(
            nn.ConvTranspose1d(in_channels=32 * input_dim, out_channels=16 * input_dim, kernel_size=3, stride=2, padding=1, output_padding=1, groups=input_dim),
            nn.GELU(),
            nn.ConvTranspose1d(in_channels=16 * input_dim, out_channels=8 * input_dim, kernel_size=5, stride=2, padding=2, output_padding=1, groups=input_dim),
            nn.GELU(),
            nn.ConvTranspose1d(in_channels=8 * input_dim, out_channels=input_dim, kernel_size=7, stride=2, padding=3, output_padding=1, groups=input_dim),
        )

    def _get_conv_output_size(self, window_size):
        with torch.no_grad():
            x = torch.zeros(1, self.input_dim, window_size)
            x = self.encoder_cnn(x)
            return x.shape[2]

    def forward(self, x):
        # x shape: (batch_size, window_size, input_dim)
        
        # 1. Mix channels first
        x = self.channel_mixer(x) # -> (batch_size, window_size, input_dim)
        
        # 2. Permute for Conv1d
        x = x.permute(0, 2, 1) # -> (batch_size, input_dim, window_size)
        
        # --- Encoder ---
        encoded_conv = self.encoder_cnn(x) # -> (B, 32 * D, L_down)
        encoded_conv_flat = encoded_conv.permute(0, 2, 1) # -> (B, L_down, 32 * D)
        
        _, latent_vector = self.encoder_gru(encoded_conv_flat)
        latent_vector = latent_vector.permute(1, 0, 2) # -> (B, 1, latent_dim)
        
        # --- Decoder ---
        decoder_input_gru = latent_vector.repeat(1, self.encoder_output_size, 1) # -> (B, L_down, latent_dim)
        decoded_gru_output, _ = self.decoder_gru(decoder_input_gru) # -> (B, L_down, latent_dim)

        projected_decoder = self.decoder_proj(decoded_gru_output) # -> (B, L_down, 32 * D)
        projected_decoder = projected_decoder.permute(0, 2, 1) # -> (B, 32 * D, L_down)
        
        reconstructed = self.decoder_cnn(projected_decoder) # -> (B, D, L_orig)
        
        # Permute back to (batch_size, window_size, input_dim)
        reconstructed = reconstructed.permute(0, 2, 1) # -> (B, L_orig, D)

        if reconstructed.shape[1] != self.window_size:
            # This might happen due to padding/stride arithmetic, ensure output size is correct
            reconstructed = F.interpolate(reconstructed.permute(0,2,1), size=self.window_size, mode='linear', align_corners=False).permute(0,2,1)

        return reconstructed, latent_vector, latent_vector # Return dummy values for compatibility

# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class LTS_AD_M(BaseDetector):
    def __init__(self, HP, normalize=True):
        super(LTS_AD_M, self).__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        self.window_size = HP.get('window_size', 100)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 128)
        self.latent_dim = HP.get('latent_dim', 16)
        
        self.model = None
        self.ts_scaler = StandardScaler()
        self.score_scaler = MinMaxScaler()
        self.criterion = nn.MSELoss()

    def _create_windows(self, X):
        if len(X) < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def fit(self, X, y=None):
        input_dim = X.shape[1]
        
        if self.model is None:
            self.model = LTS_M_Model(
                input_dim=input_dim,
                window_size=self.window_size,
                latent_dim=self.latent_dim,
            ).to(self.device)

        X_original_for_scoring = X
        if self.normalize:
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        if len(windows) == 0:
            self.decision_scores_ = np.zeros(X.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.lr)

        self.model.train()
        for epoch in range(self.epochs):
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                reconstructed, _, _ = self.model(batch_windows)
                loss = self.criterion(reconstructed, batch_windows)
                    loss.backward()
                    optimizer.step()
        
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self

    def decision_function(self, X):
        return self._compute_scores(X, fit_scaler=False)
    
    def _compute_scores(self, X, fit_scaler=False):
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed, _, _ = self.model(batch_windows)
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())

        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(np.array(window_scores)):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        if n_samples > self.window_size - 1:
             scores_mapped[:self.window_size-1] = scores_mapped[self.window_size-1]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

# ----------------------------------------------------
# 3. Visualization Function
# ----------------------------------------------------
def create_visualizations(filename, data, label, output, train_size, save_dir, model_name="LTS_AD"):
    """
    Creates a multi-panel visualization to show detection results, anomaly scores,
    and a comparison of predictions vs. ground truth.
    """
    os.makedirs(save_dir, exist_ok=True)
    try:
        # --- Data Preparation ---
        df = pd.DataFrame({
            'value': data.flatten(),
            'score': output,
            'label': label
        })
        
        # --- Threshold Calculation ---
        # Use precision-recall curve to find an optimal threshold for F1-score
        # This provides a "smart" threshold for visualization
        precision, recall, thresholds = precision_recall_curve(df['label'], df['score'])
        # handle division by zero
        f1_scores = np.divide(2 * recall * precision, recall + precision, out=np.zeros_like(recall), where=(recall + precision) != 0)

        # Find the threshold that gives the best F1 score
        best_f1_idx = np.argmax(f1_scores)
        smart_threshold = thresholds[best_f1_idx]
        
        df['pred'] = (df['score'] >= smart_threshold).astype(int)

        # --- Plotting ---
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(18, 10), sharex=True, 
                                           gridspec_kw={'height_ratios': [2, 1, 1]})
        
        # --- Plot 1: Time Series and True Anomalies ---
        ax1.plot(df.index, df['value'], color='cornflowerblue', alpha=0.9, label='Time Series', zorder=1)
        true_anomalies = df[df['label'] == 1]
        if not true_anomalies.empty:
            ax1.scatter(true_anomalies.index, true_anomalies['value'], color='red', marker='o', s=50, zorder=2, label=f'True Anomalies ({len(true_anomalies)})')
        ax1.axvline(x=train_size, color='seagreen', linestyle='--', linewidth=2, label='Train/Test Split')
        ax1.set_title(f'{model_name} Detection Results: {os.path.basename(filename)}', fontsize=16)
        ax1.set_ylabel('Value', fontsize=12)
        ax1.legend(loc='upper right')
        ax1.grid(True, which='major', linestyle='--', linewidth=0.5)

        # --- Plot 2: Anomaly Scores and Threshold ---
        ax2.plot(df.index, df['score'], color='darkviolet', label=f'{model_name} Anomaly Score', zorder=1)
        ax2.axhline(y=smart_threshold, color='darkorange', linestyle='--', linewidth=2, label=f'Smart Threshold={smart_threshold:.4f}', zorder=2)
        ax2.fill_between(df.index, df['score'], smart_threshold, where=df['score'] >= smart_threshold,
                         color='darkviolet', alpha=0.3, interpolate=True)
        ax2.set_ylabel('Anomaly Score', fontsize=12)
        ax2.legend(loc='upper right')
        ax2.grid(True, which='major', linestyle='--', linewidth=0.5)

        # --- Plot 3: Ground Truth vs. Prediction ---
        # Plot ground truth bars
        ax3.fill_between(df.index, 0, 1, where=df['label'] == 1,
                         color='lightcoral', alpha=0.8, step='mid', label='Ground Truth')
        # Plot prediction bars
        ax3.fill_between(df.index, 1, 2, where=df['pred'] == 1,
                         color='cornflowerblue', alpha=0.8, step='mid', label='Prediction')
        ax3.set_yticks([0.5, 1.5])
        ax3.set_yticklabels(['True', 'Pred'])
        ax3.set_ylabel('Labels', fontsize=12)
        ax3.set_xlabel('Time Step', fontsize=12)
        ax3.legend(loc='upper right')
        ax3.grid(False)

        plt.tight_layout()
        
        save_path = os.path.join(save_dir, f"{model_name}_{os.path.basename(filename).replace('.csv', '')}_detection_v2.png")
        plt.savefig(save_path, dpi=300)
        plt.close(fig)
        print(f"   -> 新版可视化结果已保存到: {save_path}")

    except Exception as e:
        print(f"❌ 创建可视化失败: {e}")
        import traceback
        traceback.print_exc()

# ----------------------------------------------------
# 4. Main Execution Block
# ----------------------------------------------------
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Run Lightweight Time Series Anomaly Detector (LTS_AD)')
    parser.add_argument('--filename', type=str, default='001_NAB_id_1_Facility_tr_1007_1st_2014.csv')
    parser.add_argument('--data_direc', type=str, default='./Datasets/TSB-AD-U/')
    parser.add_argument('--save_dir', type=str, default='./lts_ad_results/')
    args = parser.parse_args()

    LTS_AD_HP = {
        'window_size': 100,
        'epochs': 30,
        'lr': 1e-3,
        'batch_size': 128,
        'latent_dim': 16,
    }
    
    try:
        filepath = os.path.join(args.data_direc, args.filename)
        print(f"📂 加载数据: {filepath}")
        df = pd.read_csv(filepath).dropna()
        data = df.iloc[:, 0:-1].values.astype(float)
        label = df.iloc[:, -1].astype(int).to_numpy()
        print(f'   数据形状: {data.shape}, 标签形状: {label.shape}')

        train_index_str = args.filename.split('.')[0].split('_')[-3]
        train_size = int(train_index_str)
        data_train = data[:train_size]
        print(f'   训练集大小: {train_size}, 测试集大小: {len(data) - train_size}')

        start_time = time.time()
        clf = LTS_AD_M(HP=LTS_AD_HP)
        clf.fit(data_train)
        scores = clf.decision_function(data)
        runtime = time.time() - start_time
        print(f"⏱️ 模型运行时间: {runtime:.2f}s")
        
        min_len = min(len(scores), len(label))
        scores, label = scores[:min_len], label[:min_len]
        
        slidingWindow = find_length_rank(data[:len(label)].flatten().reshape(-1, 1), rank=1)
        results = get_metrics(scores, label, slidingWindow=slidingWindow)
        print(f"📊 评估结果: {results}")

        create_visualizations(
            filename=args.filename,
            data=data[:len(label)],
            label=label,
            output=scores,
            train_size=train_size,
            save_dir=args.save_dir,
            model_name="LTS_AD"
        )
        print("\n🎉 LTS_AD 实验完成!")

    except FileNotFoundError:
        print(f"❌ 错误: 数据文件未找到 at {filepath}")
    except Exception as e:
        print(f"\n❌ 实验失败: {args.filename}")
        import traceback
        traceback.print_exc() 