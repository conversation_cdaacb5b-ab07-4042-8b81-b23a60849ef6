#!/usr/bin/env python
"""
GPU1小规模测试：验证LERN v2.0在GPU1上的运行情况
"""

import os
import subprocess
import time

# 设置GPU设备
os.environ["CUDA_VISIBLE_DEVICES"] = "1"

def test_small_datasets():
    """测试几个小数据集"""
    # 选择一些小数据集进行测试
    test_datasets = [
        "001_NAB_id_1_Facility_tr_1007_1st_2014.csv",
        "003_NAB_id_3_WebService_tr_1362_1st_1462.csv", 
        "005_NAB_id_5_Traffic_tr_594_1st_1645.csv",
        "007_NAB_id_7_Traffic_tr_624_1st_2087.csv",
        "010_NAB_id_10_WebService_tr_500_1st_271.csv"
    ]
    
    print("🎯 LERN v2.0 GPU1 小规模测试")
    print("=" * 60)
    print(f"🔧 GPU设备: {os.environ.get('CUDA_VISIBLE_DEVICES', 'default')}")
    
    results = []
    
    for i, dataset in enumerate(test_datasets, 1):
        print(f"\n📊 测试 {i}/{len(test_datasets)}: {dataset}")
        print("-" * 50)
        
        start_time = time.time()
        
        # 构建命令
        cmd = [
            "python", "Run_LERN_Detector.py",
            "--filename", dataset,
            "--data_direc", "../Datasets/TSB-AD-U/",
            "--AD_Name", f"LERN_v2_GPU1_Test_{i}"
        ]
        
        try:
            # 运行命令
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=600  # 10分钟超时
            )
            
            elapsed_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ 成功! 耗时: {elapsed_time:.1f}s")
                
                # 提取关键信息
                output_lines = result.stdout.split('\n')
                for line in output_lines:
                    if "📊 评估结果:" in line:
                        print(f"📈 {line}")
                        break
                
                results.append({
                    'dataset': dataset,
                    'status': 'SUCCESS',
                    'time': f"{elapsed_time:.1f}s"
                })
                
            else:
                print(f"❌ 失败!")
                print(f"错误: {result.stderr[:300]}")
                results.append({
                    'dataset': dataset,
                    'status': 'FAILED',
                    'time': f"{elapsed_time:.1f}s"
                })
        
        except subprocess.TimeoutExpired:
            elapsed_time = time.time() - start_time
            print(f"⏰ 超时! (10分钟)")
            results.append({
                'dataset': dataset,
                'status': 'TIMEOUT',
                'time': f"{elapsed_time:.1f}s"
            })
        
        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"💥 异常: {str(e)}")
            results.append({
                'dataset': dataset,
                'status': 'ERROR',
                'time': f"{elapsed_time:.1f}s"
            })
    
    # 总结
    print(f"\n🏁 小规模测试完成!")
    print("=" * 60)
    
    success_count = len([r for r in results if r['status'] == 'SUCCESS'])
    total_count = len(results)
    
    print(f"📊 结果统计:")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试都成功! GPU1运行正常，可以开始大规模测试。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查问题后再进行大规模测试。")
        return False

if __name__ == "__main__":
    success = test_small_datasets()
    if success:
        print("\n🚀 准备开始大规模测试...")
        print("运行命令: python run_all_datasets_gpu1.py")
    else:
        print("\n🛑 请先解决问题再继续。") 