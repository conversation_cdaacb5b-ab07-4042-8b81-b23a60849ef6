#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验3: 训练集打乱实验
验证模型是否真正学习到了时序依赖性
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import sys
import os

# 确保可以导入 TSB_AD 模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from TSB_AD.model_wrapper import run_Semisupervise_AD
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict

# 设置matplotlib全局样式
plt.style.use('default')
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'font.size': 14,
    'axes.linewidth': 1.2,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'figure.facecolor': 'white',
    'axes.facecolor': 'white'
})

def load_or_generate_data():
    """Generate synthetic test data with strong temporal dependencies"""
    print("📊 Preparing experimental data...")
                
    # Always use synthetic data for this experiment to ensure strong temporal patterns
    
    # Generate sophisticated synthetic data with strong temporal dependencies
    print("🔧 Generating synthetic time series with strong temporal patterns...")
    np.random.seed(42)
    
    length = 3000
    time = np.arange(length)
    
    # 1. Primary periodic pattern (daily cycle)
    daily_cycle = 2 * np.sin(2 * np.pi * time / 288)  # 288 points per cycle
    
    # 2. Secondary periodic pattern (weekly cycle)  
    weekly_cycle = 1.5 * np.sin(2 * np.pi * time / (288 * 7))  # 7-day cycle
    
    # 3. Short-term dependency - AR(2) process
    ar_component = np.zeros(length)
    ar_component[0] = 0.1 * np.random.randn()
    ar_component[1] = 0.1 * np.random.randn()
    for i in range(2, length):
        ar_component[i] = 0.7 * ar_component[i-1] - 0.2 * ar_component[i-2] + 0.1 * np.random.randn()
    
    # 4. Trend component
    trend = 0.0005 * time + 0.3 * np.sin(2 * np.pi * time / length)
    
    # Combine all components
    signal = daily_cycle + weekly_cycle + ar_component + trend
    
    # 5. Add Gaussian noise
    noise = 0.2 * np.random.randn(length)
    signal += noise
    
    # 6. Inject temporally-dependent anomalies
    labels = np.zeros(length)
    
    # Split into train/test ranges first to ensure both have anomalies
    train_size = int(length * 0.6)
    
    anomaly_scenarios = [
        # Training set anomalies (first 60% of data)
        {'start': 400, 'duration': 12, 'type': 'spike', 'intensity': 3.0},
        {'start': 800, 'duration': 10, 'type': 'phase_shift', 'intensity': np.pi/2},
        {'start': 1200, 'duration': 15, 'type': 'drift', 'intensity': 1.5},
        {'start': 1500, 'duration': 8, 'type': 'variance', 'intensity': 2.5},
        
        # Test set anomalies (last 40% of data) 
        {'start': train_size + 200, 'duration': 10, 'type': 'spike', 'intensity': 4.0},
        {'start': train_size + 500, 'duration': 8, 'type': 'spike', 'intensity': -3.5},
        {'start': train_size + 800, 'duration': 144, 'type': 'phase_shift', 'intensity': np.pi},
        {'start': train_size + 1000, 'duration': 50, 'type': 'variance', 'intensity': 3.0}
    ]
    
    for anomaly in anomaly_scenarios:
        start_idx = anomaly['start']
        duration = anomaly['duration']
        end_idx = min(start_idx + duration, length)
        
        if anomaly['type'] == 'spike':
            # Sudden spikes
            signal[start_idx:end_idx] += anomaly['intensity'] * np.random.randn(end_idx - start_idx)
        
        elif anomaly['type'] == 'phase_shift':
            # Phase shift breaking periodicity
            anomaly_time = np.arange(end_idx - start_idx)
            signal[start_idx:end_idx] += 2 * np.sin(2 * np.pi * anomaly_time / 288 + anomaly['intensity'])
        
        elif anomaly['type'] == 'drift':
            # Linear drift
            drift_values = np.linspace(0, anomaly['intensity'], end_idx - start_idx)
            signal[start_idx:end_idx] += drift_values
        
        elif anomaly['type'] == 'variance':
            # Variance burst
            signal[start_idx:end_idx] += anomaly['intensity'] * np.random.randn(end_idx - start_idx)
        
        # Mark anomaly points
        labels[start_idx:end_idx] = 1
    
    print(f"✅ Generated synthetic data: length={length}, anomaly_ratio={labels.mean():.3f}")
    print(f"   Daily cycles: {length/288:.1f}, Weekly cycles: {length/(288*7):.1f}")
    
    # Convert to multivariate with correlated features
    signal_2 = signal * 0.8 + 0.5 * np.sin(2 * np.pi * time / 144) + 0.15 * np.random.randn(length)  # Related but different pattern
    signal_3 = signal * 0.6 + ar_component * 0.4 + 0.15 * np.random.randn(length)  # Mix of original and AR
    
    data = np.column_stack([signal, signal_2, signal_3])
    
    # Split into train/test (using the same train_size defined above)
    data_train = data[:train_size]
    data_test = data[train_size:]
    test_labels = labels[train_size:]
    
    train_anomaly_ratio = labels[:train_size].mean()
    test_anomaly_ratio = labels[train_size:].mean()
    
    print(f"✅ Synthetic data generation completed: {data.shape}, training set: {data_train.shape}")
    print(f"   Training set anomaly ratio: {train_anomaly_ratio:.3f}, Test set anomaly ratio: {test_anomaly_ratio:.3f}")
    return data_train, data_test, test_labels, "Synthetic_Strong_Temporal_Dependencies"

def shuffle_training_data(data_train, shuffle_ratio=1.0):
    """Shuffle the temporal order of training data
    
    This function ONLY rearranges the order of existing data points.
    No new values are created and no noise is added.
    """
    shuffled_data = data_train.copy()
    
    if shuffle_ratio == 0.0:
        # No shuffling
        return shuffled_data
    elif shuffle_ratio == 1.0:
        # Complete shuffling - randomly permute the entire sequence
        shuffled_indices = np.random.permutation(len(data_train))
        shuffled_data = data_train[shuffled_indices]
        
        # Verify no data corruption: check that we have the same data points
        assert np.allclose(np.sort(data_train.flatten()), np.sort(shuffled_data.flatten())), \
            "Shuffling corrupted data - values don't match!"
            
    else:
        # Partial shuffling - shuffle segments of the data
        n_samples = len(data_train)
        segment_size = int(n_samples * shuffle_ratio)
        
        # Divide data into segments and shuffle some of them
        n_segments = n_samples // segment_size
        
        for i in range(n_segments):
            start_idx = i * segment_size
            end_idx = min((i + 1) * segment_size, n_samples)
            
            # Randomly decide whether to shuffle this segment
            if np.random.random() < shuffle_ratio:
                segment_indices = np.arange(start_idx, end_idx)
                shuffled_segment_indices = np.random.permutation(segment_indices)
                shuffled_data[start_idx:end_idx] = data_train[shuffled_segment_indices]
    
    return shuffled_data

def run_shuffling_experiment():
    """Run training data shuffling experiment"""
    print("🧪 Starting training data shuffling experiment...")
    
    # Load data
    data_train, data_test, labels, dataset_name = load_or_generate_data()
    
    # Define models to test - including AnomalyTransformer
    models_to_test = ['HTA_AD', 'AnomalyTransformer']  # Can add other models: 'CNN', 'LSTMED'
    
    # Define shuffle ratios
    shuffle_ratios = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
    
    results = {}
    
    for model_name in models_to_test:
        print(f"\n🤖 Testing model: {model_name}")
        
        model_results = {
            'shuffle_ratios': [],
            'vus_pr_scores': [],
            'vus_roc_scores': [],
            'auc_pr_scores': []
        }
        
        for shuffle_ratio in shuffle_ratios:
            print(f"  📊 Shuffle ratio: {shuffle_ratio:.1%}")
            
            try:
                # Shuffle training data
                if shuffle_ratio > 0:
                    shuffled_train_data = shuffle_training_data(data_train, shuffle_ratio)
                else:
                    shuffled_train_data = data_train.copy()
                
                # Get model hyperparameters
                hp = Optimal_Uni_algo_HP_dict.get(model_name, {})
                
                # Run model
                scores = run_Semisupervise_AD(model_name, shuffled_train_data, data_test, **hp)
                
                # Evaluate results
                if isinstance(scores, np.ndarray):
                    # Normalize scores
                    from sklearn.preprocessing import MinMaxScaler
                    scaler = MinMaxScaler()
                    scores_normalized = scaler.fit_transform(scores.reshape(-1, 1)).ravel()
                    
                    # Calculate evaluation metrics
                    metrics = get_metrics(scores_normalized, labels, slidingWindow=64)
                    
                    model_results['shuffle_ratios'].append(shuffle_ratio)
                    model_results['vus_pr_scores'].append(metrics.get('VUS-PR', 0))
                    model_results['vus_roc_scores'].append(metrics.get('VUS-ROC', 0))
                    model_results['auc_pr_scores'].append(metrics.get('AUC-PR', 0))
                    
                    print(f"    VUS-PR: {metrics.get('VUS-PR', 0):.4f}")
                
                else:
                    print(f"    ❌ 模型运行失败: {scores}")
                    model_results['shuffle_ratios'].append(shuffle_ratio)
                    model_results['vus_pr_scores'].append(0)
                    model_results['vus_roc_scores'].append(0)
                    model_results['auc_pr_scores'].append(0)
                    
            except Exception as e:
                print(f"    ❌ 错误: {e}")
                model_results['shuffle_ratios'].append(shuffle_ratio)
                model_results['vus_pr_scores'].append(0)
                model_results['vus_roc_scores'].append(0)
                model_results['auc_pr_scores'].append(0)
        
        results[model_name] = model_results
    
    # 创建可视化
    create_shuffling_plot(results, dataset_name)
    
    return results

def create_shuffling_plot(results, dataset_name):
    """Create training data shuffling experiment result plot"""
    print("📊 Creating experimental result visualization...")
    
    # Create subplots - now 2x2 with sequence visualization replacing AUC-PR
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    
    # Define colors
    colors = {
        'HTA_AD': '#28B463',
        'AnomalyTransformer': '#E74C3C'
    }
    
    # Plot VUS-PR and VUS-ROC metrics
    metrics = ['vus_pr_scores', 'vus_roc_scores']
    metric_names = ['VUS-PR', 'VUS-ROC']
    positions = [(0, 0), (0, 1)]
    
    for metric, metric_name, (row, col) in zip(metrics, metric_names, positions):
        for model_name, model_results in results.items():
            if len(model_results['shuffle_ratios']) > 0:
                axes[row, col].plot(
                    model_results['shuffle_ratios'],
                    model_results[metric],
                    label=f'{model_name}',
                    color=colors.get(model_name, '#333333'),
                    linewidth=2,
                    marker='o'
                )
                axes[row, col].set_xlabel("Training Data Shuffle Ratio")
                axes[row, col].set_ylabel(metric_name)
                axes[row, col].grid(True, linestyle='--', alpha=0.5)
                axes[row, col].legend()
    
    # Bottom left: Training sequence visualization - same y-axis range
    # Show original vs shuffled training data for visualization
    data_train, data_test, test_labels, dataset_name = load_or_generate_data()
    
    # Original sequence (first 500 points)
    sample_length = 500
    time_points = np.arange(sample_length)
    original_signal = data_train[:sample_length, 0]  # First feature
    
    # Shuffled sequence - use same seed for reproducible shuffling
    np.random.seed(42)  # For consistent shuffling in visualization
    shuffled_data = shuffle_training_data(data_train, shuffle_ratio=1.0)
    shuffled_signal = shuffled_data[:sample_length, 0]
    
    # Plot both sequences on the same y-axis range with different styles
    axes[1, 0].plot(time_points, original_signal, 'b-', alpha=0.7, linewidth=1.5, label='Original Sequence')
    axes[1, 0].plot(time_points, shuffled_signal, 'r-', alpha=0.5, linewidth=1.0, label='Shuffled Sequence')
    
    # Set labels and styling
    axes[1, 0].set_xlabel("Time Points")
    axes[1, 0].set_ylabel("Signal Value")
    axes[1, 0].set_title("Training Sequence Visualization", fontsize=16, pad=20)
    axes[1, 0].grid(True, linestyle='--', alpha=0.3)
    axes[1, 0].legend(loc='upper right')
    
    # Remove explanation text for cleaner visualization
    
    # Fourth subplot: Performance degradation analysis
    for model_name, model_results in results.items():
        if len(model_results['vus_pr_scores']) > 0:
            baseline_score = model_results['vus_pr_scores'][0]  # Performance without shuffling
            degradation = [
                (baseline_score - score) / baseline_score * 100 if baseline_score > 0 else 0
                for score in model_results['vus_pr_scores']
            ]
            
            axes[1, 1].plot(
                model_results['shuffle_ratios'],
                degradation,
                label=f'{model_name} Performance Degradation',
                color=colors.get(model_name, '#333333'),
                linewidth=2,
                linestyle='--',
                marker='d'
            )
            axes[1, 1].set_xlabel("Training Data Shuffle Ratio")
            axes[1, 1].set_ylabel("Performance Degradation (%)")
            axes[1, 1].grid(True, linestyle='--', alpha=0.5)
            axes[1, 1].legend()
    
    # Add zero line
    axes[1, 1].axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    
    # Set subplot titles
    axes[0, 0].set_title("VUS-PR Performance Comparison", fontsize=16, pad=20)
    axes[0, 1].set_title("VUS-ROC Performance Comparison", fontsize=16, pad=20)
    # axes[1, 0] title already set in the sequence visualization section above
    axes[1, 1].set_title("Performance Degradation Analysis", fontsize=16, pad=20)
    
    # Adjust subplot layout - no main title needed
    plt.tight_layout()
    plt.subplots_adjust(hspace=0.3, wspace=0.3)
    
    # Remove annotations section - cleaner visualization
    
    # Save plot
    output_path = os.path.join(project_root, f'shuffling_experiment_{dataset_name.replace(" ", "_")}.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"✅ Experimental result plot saved: {output_path}")
    
    # Output analysis results
    print_analysis_results(results)
    
    # 关闭图形以释放内存
    plt.close(fig)
    
    return fig

def print_analysis_results(results):
    """Print analysis results"""
    print("\n📊 Experimental Result Analysis:")
    print("=" * 50)
    
    for model_name, model_results in results.items():
        if len(model_results['vus_pr_scores']) >= 2:
            baseline = model_results['vus_pr_scores'][0]
            final = model_results['vus_pr_scores'][-1]
            change = final - baseline
            change_percent = (change / baseline * 100) if baseline > 0 else 0
            
            print(f"\n🤖 {model_name}:")
            print(f"   Baseline Performance (no shuffle): {baseline:.4f}")
            print(f"   Fully Shuffled Performance: {final:.4f}")
            print(f"   Performance Change: {change:.4f} ({change_percent:+.1f}%)")
            
            if change < -0.01:
                print(f"   ✅ Model learned temporal dependencies (performance degraded)")
            elif change > 0.01:
                print(f"   🚨 Model may not learn temporal dependencies (performance improved)")
            else:
                print(f"   ⚠️  Performance change is not significant")
    
    print("\n🎯 Summary:")
    print("- Models that truly learn temporal dependencies should degrade when training data is shuffled")
    print("- If performance improves, the model relies more on point distribution features than temporal structure")
    print("- The magnitude of performance degradation reflects the model's sensitivity to temporal information")

if __name__ == "__main__":
    print("🚀 Starting Training Data Shuffling Experiment")
    print("=" * 60)
    
    try:
        # Set random seed for reproducibility
        np.random.seed(42)
        
        # Run experiment
        results = run_shuffling_experiment()
        
        print("\n" + "=" * 60)
        print("🎉 Training Data Shuffling Experiment Completed!")
        print("📊 Visualization results saved to project root directory")
        
    except Exception as e:
        print(f"❌ Experiment failed: {e}")
        import traceback
        traceback.print_exc()