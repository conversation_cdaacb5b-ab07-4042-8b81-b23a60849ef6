import pandas as pd
import os
import json
import time
from datetime import datetime
import numpy as np
from tqdm import tqdm
import sys
import matplotlib.pyplot as plt
import seaborn as sns

# --- Matplotlib and Seaborn Setup ---
try:
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid")
    import matplotlib.font_manager as fm
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    # Check for a common Chinese font
    if any('SimHei' in f for f in available_fonts):
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        print("🎨 使用中文字体: SimHei")
    else:
        print("🎨 未找到中文字体，将使用默认字体")
except Exception as e:
    print(f"🎨 字体设置失败: {e}")

def load_all_metrics(target_dir):
    """加载所有数据集的指标文件"""
    all_metrics = []
    
    # 遍历所有数据集目录
    for dataset_dir in os.listdir(target_dir):
        dataset_path = os.path.join(target_dir, dataset_dir)
        if not os.path.isdir(dataset_path):
            continue
            
        # 查找指标文件 - 支持多种可能的文件名
        possible_metrics_files = [
            os.path.join(dataset_path, 'HTA_AD_metrics.csv'),
            os.path.join(dataset_path, 'HTA-AD_metrics.csv'),
            os.path.join(dataset_path, 'metrics.csv'),
            os.path.join(dataset_path, 'metrics.json')
        ]
        
        metrics_file = None
        for file_path in possible_metrics_files:
            if os.path.exists(file_path):
                metrics_file = file_path
                break
        
        if metrics_file:
            try:
                if metrics_file.endswith('.csv'):
                    # 读取CSV文件
                    df = pd.read_csv(metrics_file)
                    if not df.empty:
                        metrics = df.iloc[0].to_dict()  # 取第一行作为指标
                        metrics['dataset'] = dataset_dir
                        metrics['dataset_path'] = dataset_path
                        all_metrics.append(metrics)
                elif metrics_file.endswith('.json'):
                    # 读取JSON文件
                    with open(metrics_file, 'r') as f:
                        metrics = json.load(f)
                        metrics['dataset'] = dataset_dir
                        metrics['dataset_path'] = dataset_path
                        all_metrics.append(metrics)
                except Exception as e:
                print(f"⚠️  读取 {metrics_file} 失败: {e}")
                continue
    
    if not all_metrics:
        return pd.DataFrame()

    df = pd.DataFrame(all_metrics)
    return df

def calculate_statistics(df):
    """计算统计信息"""
    if df.empty:
        return {}
    
    # 定义要计算的指标
    metrics_to_calculate = ['AUC-PR', 'AUC-ROC', 'F1', 'PA-F1', 'Precision', 'Recall']
    available_metrics = [m for m in metrics_to_calculate if m in df.columns]
    
    stats = {}
    for metric in available_metrics:
        # 过滤掉NaN值
        valid_values = df[metric].dropna()
        if len(valid_values) > 0:
            stats[metric] = {
                'count': len(valid_values),
                'mean': valid_values.mean(),
                'median': valid_values.median(),
                'std': valid_values.std(),
                'min': valid_values.min(),
                'max': valid_values.max(),
                'q25': valid_values.quantile(0.25),
                'q75': valid_values.quantile(0.75)
            }
    
    return stats

def print_statistics(stats, total_datasets):
    """打印统计信息"""
    print(f"\n📊 HTA_AD 多变量异常检测结果统计 (共 {total_datasets} 个数据集)")
    print("=" * 80)
    
    if not stats:
        print("❌ 暂无可用数据")
        return
    
    for metric, values in stats.items():
        print(f"\n🔍 {metric}:")
        print(f"   数据集数量: {values['count']}")
        print(f"   平均值:     {values['mean']:.4f}")
        print(f"   中位数:     {values['median']:.4f}")
        print(f"   标准差:     {values['std']:.4f}")
        print(f"   最小值:     {values['min']:.4f}")
        print(f"   最大值:     {values['max']:.4f}")
        print(f"   25分位数:   {values['q25']:.4f}")
        print(f"   75分位数:   {values['q75']:.4f}")

def create_and_save_boxplot(df, target_dir):
    """生成并保存箱线图"""
    if df.empty:
        return

    plot_file = os.path.join(target_dir, 'hta_ad_multivariate_boxplot.png')
    
    metrics_to_plot = ['AUC-PR', 'AUC-ROC', 'F1', 'PA-F1', 'Precision', 'Recall']
    metrics_to_plot = [m for m in metrics_to_plot if m in df.columns]
    
    if not metrics_to_plot:
        return

    num_metrics = len(metrics_to_plot)
    cols = min(3, num_metrics)
    rows = (num_metrics + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(6 * cols, 5 * rows))
    if rows == 1 and cols == 1:
        axes = [axes]
    elif rows == 1:
        axes = axes.reshape(1, -1)
    elif cols == 1:
        axes = axes.reshape(-1, 1)

    fig.suptitle(f'HTA_AD 多变量异常检测性能分布 ({len(df)} 个数据集)', fontsize=16, y=0.98)

    for i, metric in enumerate(metrics_to_plot):
        row = i // cols
        col = i % cols
        ax = axes[row, col]
        
        # 过滤掉NaN值
        valid_data = df[metric].dropna()
        if len(valid_data) > 0:
            ax.boxplot(valid_data, patch_artist=True, 
                      boxprops=dict(facecolor='lightblue', alpha=0.7))
            ax.scatter(np.random.normal(1, 0.1, len(valid_data)), valid_data, 
                      alpha=0.6, color='red', s=20)
        
        ax.set_title(f'{metric}', fontsize=12)
        ax.set_ylabel('Score', fontsize=10)
        ax.grid(True, alpha=0.3)

    # 隐藏多余的子图
    for i in range(num_metrics, rows * cols):
        row = i // cols
        col = i % cols
        axes[row, col].set_visible(False)

    plt.tight_layout()
    
    try:
        plt.savefig(plot_file, dpi=150, bbox_inches='tight')
        plt.close(fig)
        print(f"   📊 箱线图已更新: {plot_file}", end='\r')
    except Exception as e:
        print(f"   ❌ 保存箱线图失败: {e}")

def create_performance_table(df, target_dir):
    """创建性能汇总表"""
    if df.empty:
        return
    
    metrics_to_include = ['AUC-PR', 'AUC-ROC', 'F1', 'PA-F1', 'Precision', 'Recall']
    available_metrics = [m for m in metrics_to_include if m in df.columns]
    
    if not available_metrics:
        return
    
    # 计算每个指标的统计信息
    summary_data = []
    for metric in available_metrics:
        valid_values = df[metric].dropna()
        if len(valid_values) > 0:
            summary_data.append({
                '指标': metric,
                '数据集数量': len(valid_values),
                '平均值': f"{valid_values.mean():.4f}",
                '中位数': f"{valid_values.median():.4f}",
                '标准差': f"{valid_values.std():.4f}",
                '最小值': f"{valid_values.min():.4f}",
                '最大值': f"{valid_values.max():.4f}"
            })
    
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        csv_file = os.path.join(target_dir, 'hta_ad_multivariate_summary.csv')
        summary_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"   📋 汇总表已保存: {csv_file}", end='\r')

def monitor_and_calculate_averages(target_dir, sleep_interval=10):
    """持续监控目标目录并计算统计信息"""
    print(f"🔍 开始监控 HTA_AD 多变量实验结果: {target_dir}")
    print("按 Ctrl+C 停止监控\n")
    
    while True:
        try:
            # 清屏
            os.system('clear')
            
            # 加载所有指标
        metrics_df = load_all_metrics(target_dir)

        if not metrics_df.empty:
                # 计算统计信息
                stats = calculate_statistics(metrics_df)
            
                # 打印统计信息
                print_statistics(stats, len(metrics_df))
                
                # 创建可视化
                create_and_save_boxplot(metrics_df, target_dir)
                create_performance_table(metrics_df, target_dir)
                
                # 显示最新更新的数据集
                print(f"\n📈 最新更新的数据集:")
                latest_datasets = metrics_df.nlargest(5, 'dataset')  # 按数据集名称排序，取最新的5个
                for _, row in latest_datasets.iterrows():
                    print(f"   • {row['dataset']}")
                
            else:
                print("⏳ 等待实验结果...")
            
            # 显示时间戳
            print(f"\n🕐 最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 80)
            
        except KeyboardInterrupt:
            print("\n\n🛑 监控已停止")
            break
        except Exception as e:
            print(f"\n❌ 监控过程中出现错误: {e}")
        
        # 等待下一次检查
        for _ in range(sleep_interval):
            time.sleep(1)

if __name__ == '__main__':
    TARGET_DIR = 'hta_ad_multivariate_results'
    monitor_and_calculate_averages(TARGET_DIR) 