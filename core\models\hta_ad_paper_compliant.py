#!/usr/bin/env python3
"""
Paper-Compliant HTA-AD Implementation
Following the exact two-stage architecture described in the paper:
1. Stage 1: Train basic HTA-AD model
2. Stage 2: Train post-hoc SAE on collected latent vectors
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import math


class HTAADBasic(nn.Module):
    """
    Basic HTA-AD model (Hourglass Temporal Autoencoder for Anomaly Detection)
    This is the core model that gets trained first, following the paper's approach
    """
    
    def __init__(self, 
                 input_dim: int = 1,
                 d_model: int = 32,  # Paper: 32-dimensional latent vector
                 n_heads: int = 4,
                 n_layers: int = 2,
                 seq_len: int = 128,  # Paper: window size 128
                 dropout: float = 0.1):
        super(HTAADBasic, self).__init__()
        
        # Store configuration parameters
        self.input_dim = input_dim
        self.d_model = d_model
        self.n_heads = n_heads
        self.n_layers = n_layers
        self.seq_len = seq_len
        self.dropout = dropout
        
        # Input projection
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # Positional encoding
        self.pos_encoding = self._create_positional_encoding(seq_len, d_model)
        
        # Transformer encoder (hourglass bottleneck)
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # Transformer decoder
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_decoder = nn.TransformerDecoder(decoder_layer, num_layers=n_layers)
        
        # Output projection
        self.output_projection = nn.Linear(d_model, input_dim)
        
        # Initialize weights
        self._init_weights()
    
    def _create_positional_encoding(self, seq_len: int, d_model: int) -> torch.Tensor:
        """Create positional encoding"""
        pe = torch.zeros(1, seq_len, d_model)
        position = torch.arange(0, seq_len).unsqueeze(1).float()
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           -(math.log(10000.0) / d_model))
        
        pe[0, :, 0::2] = torch.sin(position * div_term)
        pe[0, :, 1::2] = torch.cos(position * div_term)
        
        return nn.Parameter(pe, requires_grad=False)
    
    def _init_weights(self):
        """Initialize model weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def encode(self, x: torch.Tensor) -> torch.Tensor:
        """
        Encode input to latent representation
        Args:
            x: [batch_size, seq_len, input_dim]
        Returns:
            z: [batch_size, seq_len, d_model] - 32-dimensional latent vectors
        """
        batch_size, seq_len, _ = x.shape
        
        # Input projection and positional encoding
        x = self.input_projection(x)
        
        # Handle variable sequence lengths
        if seq_len <= self.pos_encoding.size(1):
            pos_enc = self.pos_encoding[:, :seq_len, :].to(x.device)
        else:
            pos_enc = self._create_positional_encoding(seq_len, self.d_model).to(x.device)
        
        x = x + pos_enc
        
        # Transformer encoding (hourglass bottleneck)
        z = self.transformer_encoder(x)
        
        return z
    
    def decode(self, z: torch.Tensor) -> torch.Tensor:
        """
        Decode latent representation to reconstruction
        Args:
            z: [batch_size, seq_len, d_model]
        Returns:
            reconstruction: [batch_size, seq_len, input_dim]
        """
        # Transformer decoding
        decoded = self.transformer_decoder(z, z)  # Self-attention decoding
        
        # Output projection
        reconstruction = self.output_projection(decoded)
        
        return reconstruction
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass
        Args:
            x: [batch_size, seq_len, input_dim]
        Returns:
            Dict containing reconstruction and latent vectors
        """
        # Encode to latent space
        z = self.encode(x)
        
        # Decode to reconstruction
        reconstruction = self.decode(z)
        
        return {
            'reconstruction': reconstruction,
            'latent_vectors': z  # These will be collected for SAE training
        }
    
    def compute_loss(self, outputs: Dict[str, torch.Tensor], targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute reconstruction loss (only MSE, as per paper)
        """
        reconstruction_loss = F.mse_loss(outputs['reconstruction'], targets)
        
        return {
            'total': reconstruction_loss,
            'reconstruction': reconstruction_loss
        }


class PostHocSAE(nn.Module):
    """
    Post-hoc Sparse Autoencoder for interpretability
    Trained separately on collected latent vectors from HTA-AD
    """
    
    def __init__(self, 
                 latent_dim: int = 32,      # Paper: 32-dimensional input
                 hidden_dim: int = 128,     # Paper: 128 interpretable features
                 sparsity_weight: float = 0.001):  # Paper: λ = 0.001
        super(PostHocSAE, self).__init__()
        
        self.latent_dim = latent_dim
        self.hidden_dim = hidden_dim
        self.sparsity_weight = sparsity_weight
        
        # SAE encoder: 32 → 128
        self.encoder = nn.Linear(latent_dim, hidden_dim, bias=True)
        
        # SAE decoder: 128 → 32
        self.decoder = nn.Linear(hidden_dim, latent_dim, bias=True)
        
        # Initialize weights
        self._init_weights()
        
        # For tracking irrelevant features (will be computed during training)
        self.register_buffer('irrelevant_mask', torch.zeros(hidden_dim))
    
    def _init_weights(self):
        """Initialize SAE weights"""
        nn.init.xavier_uniform_(self.encoder.weight)
        nn.init.zeros_(self.encoder.bias)
        nn.init.xavier_uniform_(self.decoder.weight)
        nn.init.zeros_(self.decoder.bias)
    
    def forward(self, z: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through SAE
        Args:
            z: [batch_size, latent_dim] - latent vectors from HTA-AD
        Returns:
            z_hat: [batch_size, latent_dim] - reconstructed latent vectors
            f: [batch_size, hidden_dim] - sparse feature activations
        """
        # Encode: z → f (sparse features)
        f = F.relu(self.encoder(z))  # Paper: ReLU activation
        
        # Decode: f → z_hat
        z_hat = self.decoder(f)
        
        return z_hat, f
    
    def compute_loss(self, z: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute SAE loss: reconstruction + sparsity
        Args:
            z: [batch_size, latent_dim] - original latent vectors
        Returns:
            Dict of losses
        """
        z_hat, f = self.forward(z)
        
        # Reconstruction loss
        reconstruction_loss = F.mse_loss(z_hat, z)
        
        # Sparsity loss (L1 penalty)
        sparsity_loss = torch.mean(torch.abs(f))
        
        # Total SAE loss (Equation in paper)
        total_loss = reconstruction_loss + self.sparsity_weight * sparsity_loss
        
        return {
            'total': total_loss,
            'reconstruction': reconstruction_loss,
            'sparsity': sparsity_loss
        }
    
    def identify_irrelevant_features(self, latent_vectors: torch.Tensor,
                                   dead_threshold: float = 0.01,
                                   ubiquitous_threshold: float = 0.1) -> torch.Tensor:
        """
        Identify task-irrelevant features based on activation statistics
        Args:
            latent_vectors: [n_samples, seq_len, latent_dim] - collected latent vectors
            dead_threshold: threshold for identifying dead features
            ubiquitous_threshold: threshold for identifying ubiquitous features
        Returns:
            irrelevant_mask: [hidden_dim] - binary mask (1 = irrelevant)
        """
        # Get feature activations for all latent vectors
        # Reshape to [n_samples * seq_len, latent_dim] for batch processing
        n_samples, seq_len, latent_dim = latent_vectors.shape
        latent_flat = latent_vectors.reshape(-1, latent_dim)

        # Get feature activations
        _, feature_activations = self.forward(latent_flat)  # [n_samples * seq_len, hidden_dim]

        # Compute activation statistics
        activation_rates = torch.mean((feature_activations > 0).float(), dim=0)  # How often each feature activates
        activation_variance = torch.var(feature_activations, dim=0)    # Variance of activations
        
        # Identify dead features (rarely activated)
        dead_features = activation_rates < dead_threshold
        
        # Identify ubiquitous features (always active with low variance)
        ubiquitous_features = (activation_rates > (1 - ubiquitous_threshold)) & (activation_variance < ubiquitous_threshold)
        
        # Combine to get irrelevant features
        irrelevant_mask = (dead_features | ubiquitous_features).float()
        
        # Store for later use
        self.irrelevant_mask = irrelevant_mask
        
        print(f"Identified {torch.sum(irrelevant_mask).item():.0f}/{len(irrelevant_mask)} irrelevant features")
        print(f"  - Dead features: {torch.sum(dead_features).item():.0f}")
        print(f"  - Ubiquitous features: {torch.sum(ubiquitous_features).item():.0f}")
        
        return irrelevant_mask
    
    def purify_latent(self, z: torch.Tensor, alpha: float = 0.7) -> torch.Tensor:
        """
        Purify latent vectors by removing irrelevant feature contributions
        Args:
            z: [batch_size, latent_dim] - original latent vectors
            alpha: purification strength (paper: α = 0.7)
        Returns:
            z_purified: [batch_size, latent_dim] - purified latent vectors
        """
        # Get feature activations
        _, f = self.forward(z)
        
        # Isolate irrelevant features
        # f: [batch_size, seq_len, hidden_dim], irrelevant_mask: [hidden_dim]
        f_irr = f * self.irrelevant_mask.unsqueeze(0).unsqueeze(0)  # f ⊙ M_irr
        
        # Reconstruct irrelevant contributions
        c_irr = self.decoder(f_irr)  # SAE-Decoder(f_irr)
        
        # Remove irrelevant contributions
        z_purified = z - alpha * c_irr  # z - α · c_irr
        
        return z_purified
    
    def explain_anomaly(self, z: torch.Tensor, top_k: int = 10) -> Dict[str, torch.Tensor]:
        """
        Provide feature attribution for anomaly explanation
        Args:
            z: [batch_size, latent_dim] - latent vectors of anomalies
            top_k: number of top contributing features to return
        Returns:
            Dict containing feature attributions
        """
        # Get feature activations
        _, f = self.forward(z)
        
        # Find top-k most activated features
        top_values, top_indices = torch.topk(f, k=top_k, dim=1)
        
        return {
            'feature_activations': f,
            'top_features': top_indices,
            'top_activations': top_values,
            'feature_importance': torch.mean(torch.abs(f), dim=0)  # Global feature importance
        }
