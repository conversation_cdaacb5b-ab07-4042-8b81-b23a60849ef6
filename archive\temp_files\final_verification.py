#!/usr/bin/env python3
"""
Final verification that our boxplots work correctly
"""

import numpy as np
import matplotlib.pyplot as plt
import os

def create_perfect_boxplot():
    """Create a perfect example of what the boxplot should look like"""
    print("🎯 Creating perfect boxplot example...")
    
    # Set style to match our main visualization
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.rcParams.update({
        'font.size': 11,
        'axes.labelsize': 12,
        'axes.titlesize': 14,
        'figure.dpi': 300
    })
    
    # Generate clear, distinct data
    np.random.seed(42)
    n_features = 10
    n_samples = 200
    
    normal_data = []
    anomaly_data = []
    
    for i in range(n_features):
        # Normal: lower values, tighter distribution
        normal_vals = np.random.normal(0.02 + i*0.01, 0.015, n_samples)
        normal_vals = np.clip(normal_vals, 0, None)
        
        # Anomaly: higher values, wider distribution
        anomaly_vals = np.random.normal(0.08 + i*0.02, 0.03, n_samples//2)
        anomaly_vals = np.clip(anomaly_vals, 0, None)
        
        normal_data.append(normal_vals)
        anomaly_data.append(anomaly_vals)
    
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    positions = np.arange(n_features)
    
    # Create side-by-side box plots
    bp1 = ax.boxplot(normal_data, positions=positions-0.2, widths=0.35,
                     patch_artist=True, 
                     boxprops=dict(facecolor='lightblue', color='blue', alpha=0.8, linewidth=1.5),
                     medianprops=dict(color='darkblue', linewidth=2),
                     whiskerprops=dict(color='blue', linewidth=1.5),
                     capprops=dict(color='blue', linewidth=1.5),
                     flierprops=dict(marker='o', markerfacecolor='lightblue', 
                                   markeredgecolor='blue', markersize=3, alpha=0.6))
    
    bp2 = ax.boxplot(anomaly_data, positions=positions+0.2, widths=0.35,
                     patch_artist=True,
                     boxprops=dict(facecolor='lightcoral', color='red', alpha=0.8, linewidth=1.5),
                     medianprops=dict(color='darkred', linewidth=2),
                     whiskerprops=dict(color='red', linewidth=1.5),
                     capprops=dict(color='red', linewidth=1.5),
                     flierprops=dict(marker='o', markerfacecolor='lightcoral', 
                                   markeredgecolor='red', markersize=3, alpha=0.6))
    
    ax.set_xlabel('Feature ID')
    ax.set_ylabel('Activation Value')
    ax.set_xticks(positions)
    ax.set_xticklabels([f'F{i}' for i in range(n_features)], rotation=45)
    ax.legend([bp1["boxes"][0], bp2["boxes"][0]], ['Normal', 'Anomaly'], loc='upper left')
    ax.grid(True, alpha=0.3)
    ax.set_title('Perfect Boxplot Example: Normal vs Anomaly')
    ax.set_ylim(bottom=-0.01)
    
    plt.tight_layout()
    plt.savefig('perfect_boxplot_example.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Perfect boxplot saved as 'perfect_boxplot_example.png'")

def check_files():
    """Check what files we have"""
    print("\n📁 Current visualization files:")
    png_files = [f for f in os.listdir('.') if f.endswith('.png')]
    for png_file in sorted(png_files):
        size_mb = os.path.getsize(png_file) / (1024 * 1024)
        print(f"   • {png_file} ({size_mb:.2f} MB)")

if __name__ == "__main__":
    print("🔧 Final Verification of Boxplot Fix")
    print("=" * 50)
    
    create_perfect_boxplot()
    check_files()
    
    print("\n✅ Now check the new 'comprehensive_feature_analysis.png'")
    print("   The boxplot should show BLUE and RED boxes, not just dots!")
