# -*- coding: utf-8 -*-
# Lightweight Time Series Anomaly Detector (LTS_AD)
# A simple, effective, and lightweight model for time series anomaly detection.

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import random, argparse, time, os, sys
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.metrics import precision_recall_curve

warnings.filterwarnings('ignore')

# --- Matplotlib and Seaborn Setup ---
try:
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid")
    import matplotlib.font_manager as fm
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    # Check for a common Chinese font
    if any('SimHei' in f for f in available_fonts):
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        print("🎨 使用中文字体: SimHei")
    else:
        print("🎨 未找到中文字体，将使用默认字体")
except Exception as e:
    print(f"🎨 字体设置失败: {e}")

# --- TSB-AD Imports ---
# This structure assumes the script might be run from different locations.
# It tries to add the project root to sys.path for robust imports.
try:
    from TSB_AD.models.base import BaseDetector
    from TSB_AD.utils.slidingWindows import find_length_rank
    from TSB_AD.evaluation.metrics import get_metrics
except ImportError:
    # Get the directory of the current script
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Get the parent directory (project root)
    project_root = os.path.dirname(current_dir)
    # Add the project root to the Python path
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    print(f"✅ Added project root to path: {project_root}")
    try:
        from TSB_AD.models.base import BaseDetector
        from TSB_AD.utils.slidingWindows import find_length_rank
        from TSB_AD.evaluation.metrics import get_metrics
    except ImportError:
        print("❌ 无法导入TSB_AD包。请确保在项目根目录下运行 `pip install -e .` 来安装项目。")
        sys.exit(1)


# ----------------------------------------------------
# 1. Lightweight Model Definition
# ----------------------------------------------------
class LTS_Model(nn.Module):
    """
    Lightweight Convolutional-Recurrent Autoencoder for Time Series.
    """
    def __init__(self, window_size=100, latent_dim=16, rnn_layers=1):
        super(LTS_Model, self).__init__()
        
        # --- Encoder ---
        self.encoder_cnn = nn.Sequential(
            nn.Conv1d(in_channels=1, out_channels=8, kernel_size=7, padding=3, stride=2),
            nn.GELU(),
            nn.Conv1d(in_channels=8, out_channels=16, kernel_size=5, padding=2, stride=2),
            nn.GELU(),
            nn.Conv1d(in_channels=16, out_channels=32, kernel_size=3, padding=1, stride=2),
            nn.GELU(),
        )
        
        self.encoder_output_size = self._get_conv_output_size(window_size)
        
        self.encoder_gru = nn.GRU(
            input_size=32, 
            hidden_size=latent_dim, 
            num_layers=rnn_layers, 
            batch_first=True
        )

        # --- Decoder ---
        self.decoder_gru = nn.GRU(
            input_size=latent_dim, 
            hidden_size=latent_dim, 
            num_layers=rnn_layers, 
            batch_first=True
        )
        
        self.decoder_proj = nn.Linear(latent_dim, self.encoder_output_size * 32)

        self.decoder_cnn = nn.Sequential(
            nn.ConvTranspose1d(in_channels=32, out_channels=16, kernel_size=3, stride=2, padding=1, output_padding=1),
            nn.GELU(),
            nn.ConvTranspose1d(in_channels=16, out_channels=8, kernel_size=5, stride=2, padding=2, output_padding=1),
            nn.GELU(),
            nn.ConvTranspose1d(in_channels=8, out_channels=1, kernel_size=7, stride=2, padding=3, output_padding=1),
        )
        
        self.window_size = window_size

    def _get_conv_output_size(self, window_size):
        with torch.no_grad():
            x = torch.zeros(1, 1, window_size)
            x = self.encoder_cnn(x)
            return x.shape[2]

    def forward(self, x):
        batch_size = x.shape[0]
        x = x.unsqueeze(1)
        
        encoded_conv = self.encoder_cnn(x)
        encoded_conv_flat = encoded_conv.permute(0, 2, 1)
        
        _, latent_vector = self.encoder_gru(encoded_conv_flat)
        latent_vector = latent_vector.permute(1, 0, 2)
        
        # Decoder
        decoder_input_gru = latent_vector.permute(1, 0, 2).repeat(1, self.encoder_output_size, 1)
        decoded_gru_output, _ = self.decoder_gru(decoder_input_gru)
        decoded_gru_output = decoded_gru_output.permute(0, 2, 1) # -> (B, latent_dim, seq)

        projected_decoder = self.decoder_proj(latent_vector.squeeze(0))
        projected_decoder = projected_decoder.view(batch_size, 32, self.encoder_output_size)
        
        reconstructed = self.decoder_cnn(projected_decoder)
        reconstructed = reconstructed.squeeze(1)

        if reconstructed.shape[1] != self.window_size:
            reconstructed = F.interpolate(reconstructed.unsqueeze(1), size=self.window_size, mode='linear', align_corners=False).squeeze(1)

        return reconstructed

# ----------------------------------------------------
# 2. TSB-AD Compatible Detector Class
# ----------------------------------------------------
class LTS_AD(BaseDetector):
    def __init__(self, HP, normalize=True):
        super().__init__()
        self.HP = HP
        self.normalize = normalize
        gpu_id = HP.get('gpu', 0)
        self.device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        self.window_size = HP.get('window_size', 100)
        self.epochs = HP.get('epochs', 20)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 16)
        
        print(f"🔄 初始化LTS_AD检测器... (设备: {self.device})")
        
        self.model = LTS_Model(
            window_size=self.window_size,
            latent_dim=self.latent_dim
        ).to(self.device)

        self.ts_scaler = StandardScaler()
        self.score_scaler = MinMaxScaler()
        self.criterion = nn.MSELoss()
        
        self.training_history = {}

    def _create_windows(self, X):
        if len(X.shape) > 1:
            X = X[:, 0]
        
        if len(X) < self.window_size:
            return np.empty((0, self.window_size))
            
        return np.lib.stride_tricks.as_strided(
            X,
            shape=(len(X) - self.window_size + 1, self.window_size),
            strides=(X.strides[0], X.strides[0])
        )

    def fit(self, X, y=None):
        print("\n💪 开始训练LTS_AD模型...")
        # Keep a reference to the original data for scoring later
        X_original_for_scoring = X

        if self.normalize:
            # X is reassigned to the normalized version for training
            X = self.ts_scaler.fit_transform(X)
        
        windows = self._create_windows(X)
        print(f"   创建了 {len(windows)} 个训练窗口")

        if len(windows) == 0:
            print("⚠️ 警告: 数据长度不足以创建窗口，跳过训练。")
            self.decision_scores_ = np.zeros(X_original_for_scoring.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        optimizer = optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', factor=0.5, patience=5, verbose=False)

        self.model.train()
        epoch_losses = []
        for epoch in range(self.epochs):
            batch_losses = []
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                optimizer.zero_grad()
                reconstructed = self.model(batch_windows)
                loss = self.criterion(reconstructed, batch_windows)
                
                if not (torch.isnan(loss) or torch.isinf(loss)):
                    loss.backward()
                    optimizer.step()
                    batch_losses.append(loss.item())
            
            epoch_loss = np.mean(batch_losses) if batch_losses else 0
            epoch_losses.append(epoch_loss)
            scheduler.step(epoch_loss)
            
            if (epoch + 1) % 5 == 0 or epoch == 0:
                print(f"   Epoch {epoch+1:3d}/{self.epochs}: Loss={epoch_loss:.6f}")
        
        self.training_history = {'loss': epoch_losses}
        print("   ✅ 模型训练完成!")
        
        # Use the original, unnormalized data to compute scores for fitting the score scaler
        self.decision_scores_ = self._compute_scores(X_original_for_scoring, fit_scaler=True)
        return self

    def decision_function(self, X):
        return self._compute_scores(X, fit_scaler=False)
    
    def _compute_scores(self, X, fit_scaler=False):
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0:
            return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed = self.model(batch_windows)
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=1)
                window_scores.extend(errors.cpu().numpy())
        
        window_scores = np.array(window_scores)

        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(window_scores):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        # Fill edges
        first_score_idx = self.window_size - 1
        if n_samples > first_score_idx:
             scores_mapped[:first_score_idx] = scores_mapped[first_score_idx]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

# ----------------------------------------------------
# 3. Visualization Function
# ----------------------------------------------------
def create_visualizations(filename, data, label, output, train_size, save_dir, model_name="LTS_AD"):
    """
    Creates a multi-panel visualization to show detection results, anomaly scores,
    and a comparison of predictions vs. ground truth.
    """
    os.makedirs(save_dir, exist_ok=True)
    try:
        # --- Data Preparation ---
        df = pd.DataFrame({
            'value': data.flatten(),
            'score': output,
            'label': label
        })
        
        # --- Threshold Calculation ---
        # Use precision-recall curve to find an optimal threshold for F1-score
        # This provides a "smart" threshold for visualization
        precision, recall, thresholds = precision_recall_curve(df['label'], df['score'])
        # handle division by zero
        f1_scores = np.divide(2 * recall * precision, recall + precision, out=np.zeros_like(recall), where=(recall + precision) != 0)

        # Find the threshold that gives the best F1 score
        best_f1_idx = np.argmax(f1_scores)
        smart_threshold = thresholds[best_f1_idx]
        
        df['pred'] = (df['score'] >= smart_threshold).astype(int)

        # --- Plotting ---
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(18, 10), sharex=True, 
                                           gridspec_kw={'height_ratios': [2, 1, 1]})
        
        # --- Plot 1: Time Series and True Anomalies ---
        ax1.plot(df.index, df['value'], color='cornflowerblue', alpha=0.9, label='Time Series', zorder=1)
        true_anomalies = df[df['label'] == 1]
        if not true_anomalies.empty:
            ax1.scatter(true_anomalies.index, true_anomalies['value'], color='red', marker='o', s=50, zorder=2, label=f'True Anomalies ({len(true_anomalies)})')
        ax1.axvline(x=train_size, color='seagreen', linestyle='--', linewidth=2, label='Train/Test Split')
        ax1.set_title(f'{model_name} Detection Results: {os.path.basename(filename)}', fontsize=16)
        ax1.set_ylabel('Value', fontsize=12)
        ax1.legend(loc='upper right')
        ax1.grid(True, which='major', linestyle='--', linewidth=0.5)

        # --- Plot 2: Anomaly Scores and Threshold ---
        ax2.plot(df.index, df['score'], color='darkviolet', label=f'{model_name} Anomaly Score', zorder=1)
        ax2.axhline(y=smart_threshold, color='darkorange', linestyle='--', linewidth=2, label=f'Smart Threshold={smart_threshold:.4f}', zorder=2)
        ax2.fill_between(df.index, df['score'], smart_threshold, where=df['score'] >= smart_threshold,
                         color='darkviolet', alpha=0.3, interpolate=True)
        ax2.set_ylabel('Anomaly Score', fontsize=12)
        ax2.legend(loc='upper right')
        ax2.grid(True, which='major', linestyle='--', linewidth=0.5)

        # --- Plot 3: Ground Truth vs. Prediction ---
        # Plot ground truth bars
        ax3.fill_between(df.index, 0, 1, where=df['label'] == 1,
                         color='lightcoral', alpha=0.8, step='mid', label='Ground Truth')
        # Plot prediction bars
        ax3.fill_between(df.index, 1, 2, where=df['pred'] == 1,
                         color='cornflowerblue', alpha=0.8, step='mid', label='Prediction')
        ax3.set_yticks([0.5, 1.5])
        ax3.set_yticklabels(['True', 'Pred'])
        ax3.set_ylabel('Labels', fontsize=12)
        ax3.set_xlabel('Time Step', fontsize=12)
        ax3.legend(loc='upper right')
        ax3.grid(False)

        plt.tight_layout()
        
        save_path = os.path.join(save_dir, f"{model_name}_{os.path.basename(filename).replace('.csv', '')}_detection_v2.png")
        plt.savefig(save_path, dpi=300)
        plt.close(fig)
        print(f"   -> 新版可视化结果已保存到: {save_path}")

    except Exception as e:
        print(f"❌ 创建可视化失败: {e}")
        import traceback
        traceback.print_exc()

# ----------------------------------------------------
# 4. Main Execution Block
# ----------------------------------------------------
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Run Lightweight Time Series Anomaly Detector (LTS_AD)')
    parser.add_argument('--filename', type=str, default='001_NAB_id_1_Facility_tr_1007_1st_2014.csv')
    parser.add_argument('--data_direc', type=str, default='./Datasets/TSB-AD-U/')
    parser.add_argument('--save_dir', type=str, default='./lts_ad_results/')
    args = parser.parse_args()

    LTS_AD_HP = {
        'window_size': 100,
        'epochs': 30,
        'lr': 1e-3,
        'batch_size': 128,
        'latent_dim': 16,
    }
    
    try:
        filepath = os.path.join(args.data_direc, args.filename)
        print(f"📂 加载数据: {filepath}")
        df = pd.read_csv(filepath).dropna()
        data = df.iloc[:, 0:-1].values.astype(float)
        label = df.iloc[:, -1].astype(int).to_numpy()
        print(f'   数据形状: {data.shape}, 标签形状: {label.shape}')

        train_index_str = args.filename.split('.')[0].split('_')[-3]
        train_size = int(train_index_str)
        data_train = data[:train_size]
        print(f'   训练集大小: {train_size}, 测试集大小: {len(data) - train_size}')

        start_time = time.time()
        clf = LTS_AD(HP=LTS_AD_HP)
        clf.fit(data_train)
        scores = clf.decision_function(data)
        runtime = time.time() - start_time
        print(f"⏱️ 模型运行时间: {runtime:.2f}s")
        
        min_len = min(len(scores), len(label))
        scores, label = scores[:min_len], label[:min_len]
        
        slidingWindow = find_length_rank(data[:len(label)].flatten().reshape(-1, 1), rank=1)
        results = get_metrics(scores, label, slidingWindow=slidingWindow)
        print(f"📊 评估结果: {results}")

        create_visualizations(
            filename=args.filename,
            data=data[:len(label)],
            label=label,
            output=scores,
            train_size=train_size,
            save_dir=args.save_dir,
            model_name="LTS_AD"
        )
        print("\n🎉 LTS_AD 实验完成!")

    except FileNotFoundError:
        print(f"❌ 错误: 数据文件未找到 at {filepath}")
    except Exception as e:
        print(f"\n❌ 实验失败: {args.filename}")
        import traceback
        traceback.print_exc() 