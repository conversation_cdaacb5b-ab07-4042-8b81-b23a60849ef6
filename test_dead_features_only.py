#!/usr/bin/env python3
"""
Test SAE with only dead features purification
"""

import torch
import sys
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
sys.path.append('.')

from core.models.hta_ad_paper_compliant import H<PERSON><PERSON><PERSON>asic, PostHocSAE

def create_sliding_windows(data, window_size, stride=1):
    """Create sliding windows from time series data"""
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    n_samples, n_features = data.shape
    windows = []
    
    for i in range(0, n_samples - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def test_dead_features_only():
    """Test SAE with only dead features purification"""
    print("🧪 Testing SAE with Dead Features Only")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create models
    model = HTAADBasic(input_dim=1, d_model=32, seq_len=100).to(device)
    sae = PostHocSAE(latent_dim=32, hidden_dim=128).to(device)
    
    # Generate realistic training data
    np.random.seed(42)
    torch.manual_seed(42)
    
    # Create synthetic time series with patterns
    n_samples = 500
    time_series = []
    for i in range(n_samples):
        t = np.linspace(0, 4*np.pi, 100)
        signal = (np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.randn(100) + 0.01*i)
        time_series.append(signal.reshape(-1, 1))
    
    time_series = np.array(time_series)
    print(f"Generated time series shape: {time_series.shape}")
    
    # Normalize
    scaler = StandardScaler()
    time_series_flat = time_series.reshape(-1, 1)
    time_series_normalized = scaler.fit_transform(time_series_flat).reshape(time_series.shape)
    time_series_tensor = torch.FloatTensor(time_series_normalized).to(device)
    
    # Train HTA-AD
    print("\n🔧 Training HTA-AD...")
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    model.train()
    
    for epoch in range(15):
        optimizer.zero_grad()
        outputs = model(time_series_tensor)
        losses = model.compute_loss(outputs, time_series_tensor)
        loss = losses['total']
        loss.backward()
        optimizer.step()
        
        if (epoch + 1) % 5 == 0:
            print(f"  Epoch {epoch + 1}, Loss: {loss.item():.4f}")
    
    # Get latent vectors
    model.eval()
    with torch.no_grad():
        outputs = model(time_series_tensor)
        latent_vectors = outputs['latent_vectors']
    
    print(f"Latent vectors shape: {latent_vectors.shape}")
    
    # Train SAE
    print("\n🔧 Training SAE...")
    sae_optimizer = torch.optim.Adam(sae.parameters(), lr=0.001)
    sae.train()
    
    for epoch in range(15):
        sae_optimizer.zero_grad()
        losses = sae.compute_loss(latent_vectors)
        loss = losses['total']
        loss.backward()
        sae_optimizer.step()
        
        if (epoch + 1) % 5 == 0:
            print(f"  SAE Epoch {epoch + 1}, Loss: {loss.item():.4f}")
    
    # Test different purification strategies
    print("\n📊 Testing different purification strategies:")
    
    print("\n1. All irrelevant features (dead + ubiquitous):")
    sae.identify_irrelevant_features(latent_vectors, max_irrelevant_ratio=0.25, only_dead_features=False)
    
    print("\n2. Only dead features:")
    sae.identify_irrelevant_features(latent_vectors, max_irrelevant_ratio=0.25, only_dead_features=True)
    
    # Test purification effect
    print("\n🔍 Testing purification effect...")
    sae.eval()
    model.eval()
    
    # Take a test sample
    test_sample = time_series_tensor[:5]  # First 5 samples
    
    with torch.no_grad():
        # Original reconstruction
        original_outputs = model(test_sample)
        original_reconstruction = original_outputs['reconstruction']
        original_latents = original_outputs['latent_vectors']
        
        # Purified reconstruction (only dead features)
        purified_latents = sae.purify_latent(original_latents)
        purified_reconstruction = model.decode(purified_latents)
        
        # Compute errors
        original_error = torch.mean((test_sample - original_reconstruction) ** 2, dim=(1, 2))
        purified_error = torch.mean((test_sample - purified_reconstruction) ** 2, dim=(1, 2))
        
        print(f"\n📈 Reconstruction errors:")
        print(f"Original HTA-AD: {original_error.mean().item():.6f} ± {original_error.std().item():.6f}")
        print(f"SAE (dead only): {purified_error.mean().item():.6f} ± {purified_error.std().item():.6f}")
        
        # Check if purification helps
        improvement = (original_error.mean() - purified_error.mean()) / original_error.mean() * 100
        print(f"Improvement: {improvement.item():.2f}%")
        
        if improvement > 0:
            print("✅ Dead feature purification improves reconstruction!")
        else:
            print("⚠️  Dead feature purification increases reconstruction error")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    test_dead_features_only()
