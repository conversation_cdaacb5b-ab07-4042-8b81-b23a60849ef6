# HTA-AD Integration with TSB-AD Benchmark

## Overview

**HTA-AD (Hourglass Temporal Autoencoder for Anomaly Detection)** is a novel deep learning model that combines the strengths of CNN-based downsampling for efficiency and TCN-based feature extraction for performance in time series anomaly detection.

This document describes how HTA-AD has been integrated into the TSB-AD (Time Series Benchmark for Anomaly Detection) framework.

## Architecture

HTA-AD uses an hourglass-shaped architecture:

1. **Encoder Path:**
   - CNN Downsampler: Reduces temporal resolution for efficiency
   - Temporal Convolutional Network (TCN): Extracts temporal features with dilated convolutions
   - Bottleneck: Compresses to latent representation

2. **Decoder Path:**
   - Expansion: Reconstructs from latent space
   - Inverse TCN: Reconstructs temporal patterns
   - CNN Upsampler: Restores original temporal resolution

## Key Features

- ✅ **Efficient Processing**: CNN downsampling reduces computational complexity
- ✅ **Temporal Modeling**: TCN captures long-range dependencies with dilated convolutions
- ✅ **Flexible Architecture**: Configurable channels, window sizes, and latent dimensions
- ✅ **TSB-AD Compatible**: Fully integrated with TSB-AD benchmark framework
- ✅ **GPU Accelerated**: CUDA support for faster training and inference

## Installation & Setup

### Prerequisites

```bash
# Install TSB-AD first
git clone https://github.com/TheDatumOrg/TSB-AD.git
cd TSB-AD
pip install -r requirements.txt
pip install -e .
```

### Integration Steps

1. **Copy HTA-AD model file:**
   ```bash
   cp HTA_AD.py TSB-AD/TSB_AD/models/
   ```

2. **The model wrapper is already updated** to include HTA-AD in the `Semisupervise_AD_Pool`.

## Usage

### Basic Usage

```python
import sys
sys.path.insert(0, 'TSB-AD')

from TSB_AD.model_wrapper import run_Semisupervise_AD
import numpy as np

# Prepare your data
data_train = np.random.randn(1000, 3)  # Training data
data_test = np.random.randn(500, 3)    # Test data

# Run HTA-AD
scores = run_Semisupervise_AD(
    'HTA_AD',
    data_train,
    data_test,
    window_size=128,
    epochs=30,
    lr=1e-3,
    batch_size=64
)

print(f"Anomaly scores: {scores.shape}")
```

### Advanced Usage with Custom Parameters

```python
# Custom hyperparameters
scores = run_Semisupervise_AD(
    'HTA_AD',
    data_train,
    data_test,
    window_size=96,           # Sliding window size
    epochs=50,                # Training epochs
    lr=5e-4,                 # Learning rate
    batch_size=32,           # Batch size
    latent_dim=64,           # Latent space dimension
    tcn_channels=[64, 48, 32], # TCN channel configuration
    cnn_channels=24,         # CNN channels
    downsample_stride=2,     # Downsampling stride
    gpu=0                    # GPU device ID
)
```

### Direct Model Usage

```python
from TSB_AD.models.HTA_AD import HTA_AD

# Configure hyperparameters
HP = {
    'window_size': 128,
    'epochs': 30,
    'lr': 1e-3,
    'batch_size': 64,
    'latent_dim': 32,
    'tcn_channels': [32, 32, 32],
    'cnn_channels': 16,
    'downsample_stride': 2,
    'gpu': 0
}

# Initialize and train
detector = HTA_AD(HP=HP, normalize=True)
detector.fit(data_train)
scores = detector.decision_function(data_test)
```

## Hyperparameters

| Parameter | Description | Default | Range |
|-----------|-------------|---------|-------|
| `window_size` | Sliding window size for sequence processing | 128 | 32-512 |
| `epochs` | Number of training epochs | 30 | 10-100 |
| `lr` | Learning rate for AdamW optimizer | 1e-3 | 1e-5 to 1e-2 |
| `batch_size` | Training batch size | 64 | 16-256 |
| `latent_dim` | Latent space dimension | 32 | 8-128 |
| `tcn_channels` | TCN channel configuration | [32, 32, 32] | List of ints |
| `cnn_channels` | CNN channel count | 16 | 8-64 |
| `downsample_stride` | CNN downsampling stride | 2 | 1-4 |
| `gpu` | GPU device ID | 0 | 0+ |

## Performance Characteristics

### Computational Complexity
- **Training**: O(n × w × c) where n=samples, w=window_size, c=channels
- **Inference**: O(n × w × c) with efficient sliding window processing
- **Memory**: Reduced by CNN downsampling (typically 2-4x reduction)

### Recommended Settings

| Data Type | Window Size | Latent Dim | TCN Channels | Epochs |
|-----------|-------------|------------|--------------|--------|
| Univariate | 64-128 | 16-32 | [16, 16] | 20-30 |
| Multivariate (2-5 features) | 96-128 | 32-48 | [32, 32, 32] | 30-40 |
| High-dimensional (5+ features) | 128-256 | 48-64 | [64, 48, 32] | 40-50 |

## Testing

Run the integration tests:

```bash
python test_hta_ad_integration.py
```

Run usage examples:

```bash
python hta_ad_example.py
```

## Benchmark Integration

HTA-AD is now part of the TSB-AD benchmark and can be used in all standard evaluation workflows:

```python
# Standard TSB-AD evaluation
from TSB_AD.model_wrapper import run_Semisupervise_AD
from TSB_AD.evaluation.metrics import get_metrics

# Load your dataset
data_train = ...  # Your training data
data_test = ...   # Your test data
labels = ...      # Ground truth labels

# Run HTA-AD
scores = run_Semisupervise_AD('HTA_AD', data_train, data_test)

# Evaluate performance
results = get_metrics(scores, labels)
print(f"AUC-ROC: {results['AUC-ROC']}")
print(f"AUC-PR: {results['AUC-PR']}")
```

## Model Architecture Details

### TCN (Temporal Convolutional Network) Components
- **Dilated Convolutions**: Capture long-range dependencies efficiently
- **Residual Connections**: Enable deep network training
- **Weight Normalization**: Improve training stability

### CNN Downsampling Strategy
- **Efficiency**: Reduces sequence length by stride factor
- **Information Preservation**: Uses appropriate kernel sizes and padding
- **Learnable**: Parameters adapt during training

### Hourglass Design Benefits
- **Computational Efficiency**: Processes shorter sequences in middle layers
- **Feature Hierarchy**: Learns multi-scale temporal representations
- **Reconstruction Quality**: Symmetric encoder-decoder ensures good reconstruction

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce `batch_size` or `window_size`
   - Use smaller `tcn_channels` configuration

2. **Poor Performance**
   - Increase `epochs` for better convergence
   - Adjust `window_size` to match your data's temporal patterns
   - Try different `tcn_channels` configurations

3. **Training Instability**
   - Lower the learning rate (`lr`)
   - Enable normalization (`normalize=True`)
   - Reduce model complexity

### Performance Tips

- **For long sequences**: Use larger `window_size` and `downsample_stride`
- **For complex patterns**: Increase `latent_dim` and use deeper `tcn_channels`
- **For real-time applications**: Use smaller models with `tcn_channels=[16, 16]`

## Citation

If you use HTA-AD in your research, please cite:

```bibtex
@article{hta_ad_2024,
  title={HTA-AD: Hourglass Temporal Autoencoder for Anomaly Detection},
  author={[Your Name]},
  journal={[Journal/Conference]},
  year={2024}
}
```

## License

This implementation follows the same license as TSB-AD (Apache 2.0).

## Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## Contact

For questions or issues related to HTA-AD integration, please open an issue in the repository.
