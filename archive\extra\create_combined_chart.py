#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成双栏组合图表，适合直接插入LaTeX论文
避免子图调整的麻烦
"""

import matplotlib.pyplot as plt
import numpy as np
import os

# 设置学术论文风格
plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['font.size'] = 12
plt.rcParams['figure.dpi'] = 300
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['grid.linewidth'] = 0.6

def create_combined_chart():
    """创建双栏组合图表，适合学术论文直接插入"""
    
    # 图表参数
    FIGURE_WIDTH = 16    # 双栏总宽度
    FIGURE_HEIGHT = 5.5  # 适合论文的高度
    
    # 字体大小设置
    TITLE_SIZE = 14      # 子图标题
    AXIS_LABEL_SIZE = 12 # 轴标签
    TICK_LABEL_SIZE = 10 # 刻度标签
    VALUE_LABEL_SIZE = 8 # 数值标签
    
    # 颜色设置
    HTA_COLOR = '#D32F2F'    # HTA-AD红色
    OTHER_COLOR = '#616161'  # 其他方法灰色
    
    # 数据定义
    uni_methods = ['HTA_AD', 'Sub-PCA', 'KShapeAD', 'POI-GPD', 'SampledDCNN', 'MSCRED', 
                   'MSCRED(FT)', 'NormalizingFlow', 'USAD', 'Sub-LOF', 'AutoEncoder', 'STAMP', 
                   'CNN', 'LSTMED', 'IForest', 'TimesNet', 'Donut', 'RobustPCA', 'Telemanom', 
                   'AutoRegression', 'AutoLSTM', 'TranAD', 'FITS', 'Sub-HBOS', 'EFA', 
                   'Sub-KNN', 'Sub-OCSVM', 'Sub-LOF2', 'Sub-IForest2', 'LOF', 'AnomalyTransformer']
    
    uni_values = [0.44, 0.42, 0.40, 0.39, 0.39, 0.39, 0.38, 0.37, 0.36, 0.35, 0.35, 0.34, 
                  0.34, 0.33, 0.32, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 0.26, 0.25, 
                  0.24, 0.24, 0.23, 0.23, 0.22, 0.20, 0.12]
    
    multi_methods = ['HTA_AD', 'LSTMED', 'OmniAnomaly', 'CNN', 'PCA', 'USAD', 'AutoEncoder', 
                     'KMeansAD', 'CBLOF', 'MCD', 'OCSVM', 'Donut', 'RobustPCA', 'DIF', 
                     'EFA', 'FITS', 'ConvTAD', 'Telemanom', 'HBOS', 'TimesNet', 'KNN', 
                     'TranAD', 'LOF', 'AnomalyTransformer']
    
    # 关键：确保多变量HTA-AD为0.39
    multi_values = [0.39, 0.31, 0.31, 0.31, 0.31, 0.30, 0.30, 0.29, 0.27, 0.27, 0.26, 0.26, 
                    0.24, 0.21, 0.21, 0.21, 0.20, 0.20, 0.19, 0.19, 0.18, 0.18, 0.14, 0.12]
    
    # 创建双栏图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(FIGURE_WIDTH, FIGURE_HEIGHT))
    
    # 通用设置函数
    def setup_subplot(ax, methods, values, title, subplot_label):
        """设置子图的通用样式"""
        # 创建柱状图
        colors = [HTA_COLOR if method == 'HTA_AD' else OTHER_COLOR for method in methods]
        bars = ax.bar(range(len(methods)), values, color=colors, alpha=0.9, 
                     width=0.7, edgecolor='white', linewidth=0.8)
        
        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, values)):
            height = bar.get_height()
            color = HTA_COLOR if methods[i] == 'HTA_AD' else '#424242'
            weight = 'bold' if methods[i] == 'HTA_AD' else 'normal'
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.008,
                   f'{value:.2f}', ha='center', va='bottom', 
                   fontsize=VALUE_LABEL_SIZE, fontweight=weight, color=color)
        
        # 设置标题和标签
        ax.set_title(f'({subplot_label}) {title}', fontsize=TITLE_SIZE, fontweight='bold', pad=15)
        ax.set_ylabel('VUS-PR Score', fontsize=AXIS_LABEL_SIZE, fontweight='bold')
        ax.set_xlabel('Method', fontsize=AXIS_LABEL_SIZE, fontweight='bold')
        
        # 统一y轴范围
        ax.set_ylim(0, 0.50)
        ax.set_xlim(-0.5, len(methods) - 0.5)
        
        # 设置刻度
        y_ticks = np.arange(0, 0.51, 0.1)
        ax.set_yticks(y_ticks)
        ax.set_yticklabels([f'{tick:.1f}' for tick in y_ticks], fontsize=TICK_LABEL_SIZE)
        ax.set_xticks(range(len(methods)))
        ax.set_xticklabels(methods, rotation=45, ha='right', fontsize=TICK_LABEL_SIZE)
        
        # 设置网格和背景
        ax.grid(True, alpha=0.4, axis='y', linestyle='-', color='#E0E0E0')
        ax.set_axisbelow(True)
        ax.set_facecolor('#FAFAFA')
        
        # 设置边框
        for spine in ax.spines.values():
            spine.set_linewidth(1.2)
            spine.set_color('#CCCCCC')
    
    # 设置左侧子图（单变量）
    setup_subplot(ax1, uni_methods, uni_values, 'Univariate benchmark (TSB-AD-U)', 'a')
    
    # 设置右侧子图（多变量）
    setup_subplot(ax2, multi_methods, multi_values, 'Multivariate benchmark (TSB-AD-M)', 'b')
    
    # 调整子图间距
    plt.tight_layout(pad=2.0)
    
    # 保存组合图表
    plt.savefig('figures/combined_tsb_ad_results.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none', pad_inches=0.2)
    plt.show()
    
    print("✅ 双栏组合图表已生成")
    return FIGURE_WIDTH, FIGURE_HEIGHT

def create_latex_code():
    """生成对应的LaTeX插入代码"""
    
    latex_code = '''
% 使用单张组合图片替代复杂的子图设置
\\begin{figure*}[htbp]
    \\centering
    \\includegraphics[width=\\textwidth]{figures/combined_tsb_ad_results.png}
    \\caption{Performance ranking on TSB-AD benchmark using VUS-PR metric. HTA-AD (highlighted in red) achieves state-of-the-art performance on both univariate (0.44) and multivariate (0.39) benchmarks with a single unified architecture, demonstrating the first successful solution to the specialization curse in time series anomaly detection. Both charts use identical height and y-axis scaling for fair visual comparison.}
    \\label{fig:main_results}
\\end{figure*}
'''
    
    with open('figures/latex_combined_figure.tex', 'w', encoding='utf-8') as f:
        f.write(latex_code.strip())
    
    print("📝 LaTeX代码已生成：figures/latex_combined_figure.tex")

if __name__ == "__main__":
    print("🎨 生成双栏组合图表，适合直接插入LaTeX...")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs('figures', exist_ok=True)
    
    # 生成组合图表
    width, height = create_combined_chart()
    
    # 生成LaTeX代码
    create_latex_code()
    
    print(f"\n✅ 组合图表生成完成！")
    print(f"📐 图表尺寸: {width} × {height} 英寸")
    print(f"🎯 HTA-AD多变量: 0.39 (已修正)")
    print(f"📊 统一y轴: 0-0.50")
    
    print("\n" + "=" * 60)
    print("🎉 双栏组合图表完成！")
    print("\n📋 特点:")
    print("   🔸 单张图片，避免LaTeX子图调整问题")
    print("   🔸 完美的双栏布局，适合学术论文")
    print("   🔸 统一的视觉样式和比例")
    print("   🔸 高分辨率，适合印刷")
    print("\n📁 输出文件:")
    print("   📊 figures/combined_tsb_ad_results.png")
    print("   📝 figures/latex_combined_figure.tex")
    print("\n💡 直接复制LaTeX代码到论文中即可！")
