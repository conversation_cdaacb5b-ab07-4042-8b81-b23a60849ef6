#!/usr/bin/env python3
"""
SAE Pre-trainer for HTA-AD
Implements pre-training of Sparse Autoencoder on collected latent vectors
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
import json
import time
from typing import Dict, List, Tuple, Optional
from torch.utils.data import DataLoader, TensorDataset

class SAEPretrainer:
    """
    Pre-trainer for Sparse Autoencoder (SAE)
    Handles collection of latent vectors and pre-training of SAE
    """
    
    def __init__(self, 
                 sae_model,
                 config: Dict = None):
        """
        Initialize SAE pre-trainer
        Args:
            sae_model: SAE model instance
            config: Training configuration
        """
        self.sae = sae_model
        self.config = config or self._default_config()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Training history
        self.training_history = {
            'total_loss': [],
            'reconstruction_loss': [],
            'sparsity_loss': [],
            'feature_stats': []
        }
        
        # Latent vector collection
        self.collected_latents = []
        self.collection_metadata = {}
    
    def _default_config(self) -> Dict:
        """Default training configuration"""
        return {
            'learning_rate': 1e-3,
            'weight_decay': 1e-5,
            'batch_size': 64,
            'epochs': 50,
            'sparsity_penalty': 1e-3,
            'patience': 10,
            'min_delta': 1e-6,
            'save_checkpoints': True,
            'checkpoint_dir': 'results/models/sae_checkpoints',
            'log_interval': 5
        }
    
    def collect_latent_vectors(self, 
                             hta_ad_model, 
                             data_loader: DataLoader,
                             max_samples: int = 10000) -> torch.Tensor:
        """
        Collect latent vectors from HTA-AD model for SAE pre-training
        Args:
            hta_ad_model: Trained HTA-AD model
            data_loader: DataLoader with training data
            max_samples: Maximum number of latent vectors to collect
        Returns:
            collected_latents: Tensor of collected latent vectors
        """
        print(f"🔧 Collecting latent vectors for SAE pre-training...")
        print(f"   Max samples: {max_samples}")
        
        hta_ad_model.eval()
        collected_latents = []
        total_collected = 0
        
        with torch.no_grad():
            for batch_idx, (batch_data,) in enumerate(data_loader):
                if total_collected >= max_samples:
                    break

                batch_data = batch_data.to(self.device)

                # Debug: check input data
                if batch_idx == 0:
                    print(f"   First batch input stats: mean={batch_data.mean().item():.4f}, std={batch_data.std().item():.4f}")

                outputs = hta_ad_model(batch_data)
                latent_vectors = outputs['latent_vectors']

                # Debug: check latent vectors
                if batch_idx == 0:
                    print(f"   First batch latent stats: mean={latent_vectors.mean().item():.4f}, std={latent_vectors.std().item():.4f}")
                    if torch.all(latent_vectors == 0):
                        print(f"   ❌ Warning: First batch latents are all zero!")

                collected_latents.append(latent_vectors.cpu())
                total_collected += latent_vectors.shape[0]

                if (batch_idx + 1) % 10 == 0:
                    print(f"   Collected {total_collected} latent vectors...")
        
        # Concatenate all collected latents
        all_latents = torch.cat(collected_latents, dim=0)
        
        # Limit to max_samples
        if len(all_latents) > max_samples:
            indices = torch.randperm(len(all_latents))[:max_samples]
            all_latents = all_latents[indices]
        
        print(f"✅ Collected {len(all_latents)} latent vectors")
        print(f"   Shape: {all_latents.shape}")
        print(f"   Stats: mean={all_latents.mean().item():.4f}, std={all_latents.std().item():.4f}")
        
        # Store collection metadata
        self.collection_metadata = {
            'num_samples': len(all_latents),
            'latent_dim': all_latents.shape[1],
            'mean': all_latents.mean().item(),
            'std': all_latents.std().item(),
            'min': all_latents.min().item(),
            'max': all_latents.max().item()
        }
        
        self.collected_latents = all_latents.to(self.device)
        return self.collected_latents
    
    def pretrain(self, 
                latent_vectors: Optional[torch.Tensor] = None,
                validation_split: float = 0.2) -> Dict:
        """
        Pre-train the SAE on collected latent vectors
        Args:
            latent_vectors: Latent vectors to train on (if None, use collected)
            validation_split: Fraction of data for validation
        Returns:
            training_results: Dictionary with training results
        """
        if latent_vectors is None:
            if len(self.collected_latents) == 0:
                raise ValueError("No latent vectors available. Call collect_latent_vectors first.")
            latent_vectors = self.collected_latents
        
        print(f"🚀 Starting SAE pre-training...")
        print(f"   Training samples: {len(latent_vectors)}")
        print(f"   Latent dimension: {latent_vectors.shape[1]}")
        print(f"   SAE hidden dimension: {self.sae.hidden_dim}")
        
        # Split data
        n_samples = len(latent_vectors)
        n_val = int(n_samples * validation_split)
        n_train = n_samples - n_val
        
        indices = torch.randperm(n_samples)
        train_indices = indices[:n_train]
        val_indices = indices[n_train:]
        
        train_data = latent_vectors[train_indices]
        val_data = latent_vectors[val_indices]
        
        print(f"   Train samples: {len(train_data)}")
        print(f"   Validation samples: {len(val_data)}")
        
        # Create data loaders
        train_dataset = TensorDataset(train_data)
        val_dataset = TensorDataset(val_data)
        
        train_loader = DataLoader(train_dataset, 
                                batch_size=self.config['batch_size'], 
                                shuffle=True)
        val_loader = DataLoader(val_dataset, 
                              batch_size=self.config['batch_size'], 
                              shuffle=False)
        
        # Setup optimizer
        optimizer = optim.AdamW(self.sae.parameters(), 
                              lr=self.config['learning_rate'],
                              weight_decay=self.config['weight_decay'])
        
        # Setup early stopping
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None
        
        # Training loop
        self.sae.train()
        start_time = time.time()
        
        for epoch in range(self.config['epochs']):
            # Training phase
            train_losses = self._train_epoch(train_loader, optimizer)
            
            # Validation phase
            val_losses = self._validate_epoch(val_loader)
            
            # Record history
            self.training_history['total_loss'].append(train_losses['total'])
            self.training_history['reconstruction_loss'].append(train_losses['reconstruction'])
            self.training_history['sparsity_loss'].append(train_losses['sparsity'])
            
            # Feature statistics
            feature_stats = self._compute_feature_stats(train_data)
            self.training_history['feature_stats'].append(feature_stats)
            
            # Early stopping check
            if val_losses['total'] < best_val_loss - self.config['min_delta']:
                best_val_loss = val_losses['total']
                patience_counter = 0
                best_model_state = self.sae.state_dict().copy()
            else:
                patience_counter += 1
            
            # Logging
            if (epoch + 1) % self.config['log_interval'] == 0:
                elapsed = time.time() - start_time
                print(f"   Epoch {epoch + 1}/{self.config['epochs']} ({elapsed:.1f}s)")
                print(f"     Train - Total: {train_losses['total']:.6f}, "
                      f"Recon: {train_losses['reconstruction']:.6f}, "
                      f"Sparse: {train_losses['sparsity']:.6f}")
                print(f"     Val   - Total: {val_losses['total']:.6f}")
                print(f"     Features - Active: {feature_stats['active_rate']:.3f}, "
                      f"Dead: {feature_stats['dead_count']}")
            
            # Early stopping
            if patience_counter >= self.config['patience']:
                print(f"   Early stopping at epoch {epoch + 1}")
                break
            
            # Save checkpoint
            if self.config['save_checkpoints'] and (epoch + 1) % 10 == 0:
                self._save_checkpoint(epoch + 1)
        
        # Load best model
        if best_model_state is not None:
            self.sae.load_state_dict(best_model_state)
            print(f"✅ Loaded best model (val_loss: {best_val_loss:.6f})")
        
        # Final feature identification
        print(f"\n🔍 Identifying irrelevant features after pre-training...")
        self.sae.identify_irrelevant_features(train_data, 
                                            max_irrelevant_ratio=0.25, 
                                            only_dead_features=True)
        
        training_time = time.time() - start_time
        print(f"✅ SAE pre-training completed in {training_time:.1f}s")
        
        return {
            'training_time': training_time,
            'final_train_loss': train_losses['total'],
            'final_val_loss': val_losses['total'],
            'best_val_loss': best_val_loss,
            'epochs_trained': epoch + 1,
            'feature_stats': feature_stats,
            'collection_metadata': self.collection_metadata
        }
    
    def _train_epoch(self, train_loader: DataLoader, optimizer) -> Dict[str, float]:
        """Train for one epoch"""
        self.sae.train()
        total_losses = []
        recon_losses = []
        sparse_losses = []
        
        for batch_data, in train_loader:
            batch_data = batch_data.to(self.device)
            
            optimizer.zero_grad()
            losses = self.sae.compute_loss(batch_data)
            loss = losses['total']
            
            loss.backward()
            optimizer.step()
            
            total_losses.append(losses['total'].item())
            recon_losses.append(losses['reconstruction'].item())
            sparse_losses.append(losses['sparsity'].item())
        
        return {
            'total': np.mean(total_losses),
            'reconstruction': np.mean(recon_losses),
            'sparsity': np.mean(sparse_losses)
        }
    
    def _validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """Validate for one epoch"""
        self.sae.eval()
        total_losses = []
        
        with torch.no_grad():
            for batch_data, in val_loader:
                batch_data = batch_data.to(self.device)
                losses = self.sae.compute_loss(batch_data)
                total_losses.append(losses['total'].item())
        
        return {'total': np.mean(total_losses)}
    
    def _compute_feature_stats(self, data: torch.Tensor) -> Dict:
        """Compute feature activation statistics"""
        self.sae.eval()
        with torch.no_grad():
            _, features = self.sae(data)
            active_features = torch.sum(features > 0, dim=0)
            active_rate = (active_features > 0).float().mean().item()
            dead_count = torch.sum(active_features == 0).item()
            
        return {
            'active_rate': active_rate,
            'dead_count': dead_count,
            'avg_activations': active_features.float().mean().item()
        }
    
    def _save_checkpoint(self, epoch: int):
        """Save training checkpoint"""
        os.makedirs(self.config['checkpoint_dir'], exist_ok=True)
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.sae.state_dict(),
            'training_history': self.training_history,
            'config': self.config,
            'collection_metadata': self.collection_metadata
        }
        
        checkpoint_path = os.path.join(self.config['checkpoint_dir'], 
                                     f'sae_checkpoint_epoch_{epoch}.pt')
        torch.save(checkpoint, checkpoint_path)
    
    def save_pretrained_model(self, save_path: str):
        """Save the pre-trained SAE model"""
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        save_data = {
            'model_state_dict': self.sae.state_dict(),
            'model_config': {
                'latent_dim': self.sae.latent_dim,
                'hidden_dim': self.sae.hidden_dim,
                'sparsity_penalty': self.sae.sparsity_penalty
            },
            'training_history': self.training_history,
            'collection_metadata': self.collection_metadata,
            'irrelevant_mask': self.sae.irrelevant_mask
        }
        
        torch.save(save_data, save_path)
        print(f"💾 Pre-trained SAE saved to: {save_path}")
    
    def load_pretrained_model(self, load_path: str):
        """Load a pre-trained SAE model"""
        checkpoint = torch.load(load_path, map_location=self.device)
        
        self.sae.load_state_dict(checkpoint['model_state_dict'])
        self.training_history = checkpoint.get('training_history', {})
        self.collection_metadata = checkpoint.get('collection_metadata', {})
        
        # Load irrelevant mask if available
        if 'irrelevant_mask' in checkpoint and checkpoint['irrelevant_mask'] is not None:
            self.sae.irrelevant_mask = checkpoint['irrelevant_mask']
        
        print(f"📂 Pre-trained SAE loaded from: {load_path}")
        return checkpoint.get('model_config', {})
