#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Robustness Analysis: Testing HTA-AD performance with noisy training data
Based on improved implementation from benchmark_exp_test
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import json
import plotly.graph_objects as go
import plotly.io as pio
from sklearn.metrics import precision_recall_curve, auc

# Path Setup
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Model Imports
from TSB_AD.models.HTA_AD import HTA_AD

np.random.seed(42)
torch.manual_seed(42)

def load_clean_dataset(dataset_path):
    """Loads a dataset and splits it into clean train and test sets."""
    if not os.path.exists(dataset_path):
        raise FileNotFoundError(f"Dataset not found at {dataset_path}")
        
    df = pd.read_csv(dataset_path)
    
    # Parse training size from filename - improved
    file_base = os.path.basename(dataset_path)
    split_point = None
    
    if '_tr_' in file_base:
        try:
            parts = file_base.split('_tr_')
            if len(parts) > 1:
                train_part = parts[1].split('_')[0]
                if train_part.isdigit():
                    split_point = int(train_part)
        except (ValueError, IndexError):
            pass
    
    if split_point is None:
        split_point = int(len(df) * 0.7)
        print(f"Using default split point: {split_point} (70% of {len(df)})")
    else:
        print(f"Parsed split point from filename: {split_point}")

    # Handle multivariate data - use first feature for univariate analysis
    if 'Label' in df.columns:
        data_values = df.iloc[:, :-1].values.astype(np.float32)
        labels = df['Label'].values.astype(int)
        data_values = data_values[:, 0].reshape(-1, 1)  # Use first feature
    elif len(df.columns) >= 2:
        try:
            data_values = df.iloc[:, :-1].values.astype(np.float32)
            labels = df.iloc[:, -1].values.astype(int)
            data_values = data_values[:, 0].reshape(-1, 1)  # Use first feature
        except (ValueError, TypeError):
            # If last column can't be converted to int, use all as features
            data_values = df.values.astype(np.float32)
            labels = np.zeros(len(data_values), dtype=int)
            data_values = data_values[:, 0].reshape(-1, 1)
    else:
        data_values = df.iloc[:, 0].values.reshape(-1, 1).astype(np.float32)
        labels = np.zeros(len(data_values), dtype=int)

    # Ensure split point doesn't exceed data length
    split_point = min(split_point, len(data_values) - 100)  # Leave at least 100 for test
    
    train_data = data_values[:split_point]
    test_data = data_values[split_point:]
    test_labels = labels[split_point:]
    
    return train_data, test_data, test_labels

def inject_noise(data, noise_ratio, magnitude_std=1.5):
    """Injects spike-like noise into a dataset by adding/subtracting large values."""
    if noise_ratio == 0:
        return data
    
    noisy_data = data.copy()
    num_points = len(data)
    num_noise_points = int(num_points * noise_ratio)
    
    if num_noise_points > num_points:
        num_noise_points = num_points
        
    noise_indices = np.random.choice(num_points, num_noise_points, replace=False)
    
    signal_std = data.std()
    if signal_std == 0:
        signal_std = 1.0
        
    noise_magnitude_std = signal_std * magnitude_std
    
    for idx in noise_indices:
        noise = np.random.normal(0, noise_magnitude_std)
        noisy_data[idx, 0] += noise
        
    return noisy_data

def calculate_vus_pr(labels, scores):
    """Calculates the Volume Under the Precision-Recall Surface (VUS-PR)."""
    if len(np.unique(labels)) < 2:
        return 0.0
    precision, recall, _ = precision_recall_curve(labels, scores)
    pr_auc = auc(recall, precision)
    return pr_auc

def run_analysis_on_dataset(dataset_name):
    """Runs the full noisy training analysis for a single dataset."""
    print(f"\n=========================================================")
    print(f"========== Running Analysis for: {dataset_name} ==========")
    print(f"=========================================================\n")

    # Config
    WINDOW_SIZE = 128
    NOISE_LEVELS = np.linspace(0, 1.0, 11)  # 0% to 100% noise in 10% increments
    NOISE_MAGNITUDE = 2.0

    # Load Data
    print(f"Loading clean dataset: {dataset_name}")
    
    # Try different dataset paths
    dataset_paths = [
        os.path.join(project_root, 'Datasets/TSB-AD-U/TSB-AD-U/', dataset_name + '.csv'), # Corrected path
        os.path.join(project_root, 'Datasets/TSB-AD-M/', dataset_name + '.csv')
    ]
    
    dataset_path = None
    for path in dataset_paths:
        if os.path.exists(path):
            dataset_path = path
            break
    
    if dataset_path is None:
        print(f"SKIPPING: Could not find dataset {dataset_name}")
        return None

    try:
        clean_train_data, clean_test_data, clean_test_labels = load_clean_dataset(dataset_path)
    except Exception as e:
        print(f"SKIPPING: Failed to load dataset {dataset_name}: {e}")
        return None

    results = []

    # Run Experiment for Each Noise Level
    for noise_level in NOISE_LEVELS:
        print(f"\n--- Testing with {noise_level*100:.1f}% noise in training data (Magnitude: {NOISE_MAGNITUDE}) ---")
        
        # 1. Inject noise into training data
        print("Injecting noise...")
        noisy_train_data = inject_noise(clean_train_data, noise_level, magnitude_std=NOISE_MAGNITUDE)
        
        # 2. Train a new model on the noisy data
        print("Training HTA_AD model on noisy data...")
        try:
            model = HTA_AD(HP={'window_size': WINDOW_SIZE, 'epochs': 10, 'batch_size': 64, 'lr': 1e-3})
            model.fit(noisy_train_data)
            
            # 3. Evaluate on the CLEAN test data
            print("Evaluating model on clean test data...")
            anomaly_scores = model.decision_function(clean_test_data)
            
            # 4. Calculate performance metric (VUS-PR)
            vus_pr_score = calculate_vus_pr(clean_test_labels, anomaly_scores)
            print(f"Performance (VUS-PR Score): {vus_pr_score:.4f}")
            results.append(vus_pr_score)
            
        except Exception as e:
            print(f"Error during training/evaluation: {e}")
            results.append(0.0)  # Failed experiment

    # Visualization (Line Plot) with Plotly
    print("\nGenerating final performance degradation plot with Plotly...")
    
    # Convert noise levels to percentage for the x-axis
    noise_percentages = [l * 100 for l in NOISE_LEVELS]
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=noise_percentages, 
        y=results,
        mode='lines+markers+text',
        marker=dict(size=10, color='#1f77b4'),
        line=dict(color='#1f77b4', width=2),
        text=[f'{r:.3f}' for r in results],
        textposition='top center',
        textfont=dict(size=14, weight='bold'),
        name='HTA_AD Performance'
    ))

    fig.update_layout(
        title=f'<b>HTA_AD Performance on {dataset_name}<br>with Noisy Training Data</b>',
        xaxis_title='<b>Percentage of Noise Contamination in Training Data (%)</b>',
        yaxis_title='<b>Performance on Clean Test Data (VUS-PR Score)</b>',
        font=dict(family="Times New Roman", size=16),
        plot_bgcolor='white',
        xaxis=dict(
            tickmode='array',
            tickvals=noise_percentages,
            ticktext=[f'{p:.0f}%' for p in noise_percentages],
            gridcolor='#E5E5E5',
            showline=True,
            linecolor='black',
            linewidth=1
        ),
        yaxis=dict(
            gridcolor='#E5E5E5',
            showline=True,
            linecolor='black',
            linewidth=1
        ),
        height=700,
        width=1200,
        showlegend=False
    )
    
    # Set y-axis to be slightly larger than the max result for clarity
    if results and max(results) > 0:
        fig.update_yaxes(range=[0, max(results) * 1.1])

    # Save Figure
    output_dir = os.path.join(project_root, 'visualizations', 'robustness_analysis')
    os.makedirs(output_dir, exist_ok=True)
        
    output_filename = f'noisy_training_degradation_{dataset_name}_plotly.png'
    output_path = os.path.join(output_dir, output_filename)
    
    try:
        pio.write_image(fig, output_path, scale=2)
        print(f"\nAnalysis complete for {dataset_name}. Plot saved to: {output_path}")
    except Exception as e:
        print(f"Failed to save plot: {e}")

    return {"scores": results, "noise_levels": noise_percentages}

def main():
    """Main function to run the noisy training analysis across multiple datasets."""
    
    # Select a diverse set of datasets for the experiment (univariate and multivariate)
    DATASET_NAMES = [
        # Univariate
        '001_NAB_id_1_Facility_tr_1007_1st_2014',         
        '561_YAHOO_id_11_WebService_tr_500_1st_359',
        '815_Exathlon_id_6_Facility_tr_10766_1st_12590',
        # Multivariate
        '002_MSL_id_1_Sensor_tr_500_1st_900',
        '018_Daphnet_id_1_HumanActivity_tr_9693_1st_20732',
        '172_SWaT_id_2_Sensor_tr_23700_1st_23800'
    ]

    all_results = {}
    for name in DATASET_NAMES:
        result = run_analysis_on_dataset(name)
        if result:
            all_results[name] = result

    # Save all results to a JSON file
    results_dir = os.path.join(project_root, 'visualizations', 'robustness_analysis')
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    
    results_filepath = os.path.join(results_dir, 'robustness_results.json')
    
    # Check if results exist, otherwise run the analysis
    if os.path.exists(results_filepath):
        print("Found existing results file. Loading data...")
        with open(results_filepath, 'r') as f:
            all_results = json.load(f)
    else:
        print("No results file found. Running full analysis...")
        all_results = {}
        for name in DATASET_NAMES:
            result = run_analysis_on_dataset(name)
            if result:
                all_results[name] = result
        
    with open(results_filepath, 'w') as f:
        json.dump(all_results, f, indent=4)
    print(f"\nAll experiment results saved to: {results_filepath}")


    # Create summary plot
    if all_results:
        create_summary_plot(all_results, results_dir)

def create_summary_plot(all_results, output_dir):
    """Create a summary plot comparing all datasets, filtering out low-performers."""
    print("Creating summary comparison plot...")
    
    # Apply a publication-ready theme
    pio.templates["paper_theme"] = go.layout.Template(
        layout=go.Layout(
            font=dict(family="Arial", size=20, color="black"),
            xaxis=dict(showline=True, showgrid=True, gridcolor='lightgrey', linecolor='black', mirror=True, ticks='outside'),
            yaxis=dict(showline=True, showgrid=True, gridcolor='lightgrey', linecolor='black', mirror=True, ticks='outside'),
            plot_bgcolor='white',
            legend=dict(font=dict(size=18))
        )
    )
    pio.templates.default = "paper_theme"
    
    fig = go.Figure()
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    
    # Filter and plot
    filtered_results = {name: data for name, data in all_results.items() if data['scores'][0] >= 0.2}

    for i, (dataset_name, results) in enumerate(filtered_results.items()):
        color = colors[i % len(colors)]
        
        # Create a cleaner, shorter name for the legend
        parts = dataset_name.split('_')
        display_name = f"{parts[1]}_{parts[2]}"
        
        fig.add_trace(go.Scatter(
            x=results['noise_levels'],
            y=results['scores'],
            mode='lines+markers',
            name=display_name,
            line=dict(color=color, width=3),
            marker=dict(size=9)
        ))
    
    fig.update_layout(
        xaxis_title='<b>Percentage of Noise in Training Data (%)</b>',
        yaxis_title='<b>VUS-PR Score</b>',
        font=dict(size=22),
        legend=dict(
            yanchor="bottom",
            y=0.01,
            xanchor="right",
            x=0.99,
            bgcolor='rgba(255,255,255,0.7)',
            bordercolor='black',
            borderwidth=1
        ),
        height=800,
        width=1200
    )
    
    # Customize axis tick formats
    fig.update_xaxes(tickvals=np.arange(0, 101, 10), ticktext=[f'{int(x)}%' for x in np.arange(0, 101, 10)])
    
    summary_path = os.path.join(output_dir, 'robustness_summary_beautified.png')
    try:
        pio.write_image(fig, summary_path, scale=3)
        print(f"Beautified summary plot saved to: {summary_path}")
    except Exception as e:
        print(f"Failed to save summary plot: {e}")

if __name__ == '__main__':
    print("🚀 Starting HTA-AD Robustness Analysis")
    print("=" * 60)
    main()
    print("\n" + "=" * 60)
    print("🎉 Robustness analysis completed!")
    print("📊 Results saved to visualizations/robustness_analysis/ directory")