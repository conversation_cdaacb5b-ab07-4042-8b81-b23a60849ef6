#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
10个数据集的全面基准测试
包括单变量和多变量时间序列，测试SAE的适应性
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import sys
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns

# 添加TSB-AD路径
sys.path.append('TSB-AD')
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.models.HTA_AD import HTA_AD
from TSB_AD.HP_list import Optimal_Uni_algo_HP_dict

# 导入SAE模型
from run_benchmark_with_pretrained_sae import LargeScaleSAE, safe_get_metrics

class MultivariateSAE(nn.Module):
    """支持多变量的SAE"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU()
        )
        
        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, input_dim)
        )
        
    def forward(self, x):
        activations = self.encoder(x)
        reconstructed = self.decoder(activations)
        return reconstructed, activations

class HTA_AD_SAE_Multivariate(HTA_AD):
    """支持多变量的HTA-AD + SAE"""
    def __init__(self, HP, pretrained_sae=None, sae_scaler=None, irrelevant_indices=None, normalize=True):
        super().__init__(HP, normalize)

        self.pretrained_sae = pretrained_sae
        self.sae_scaler = sae_scaler
        self.irrelevant_indices = irrelevant_indices or []
        self.purification_strength = HP.get('purification_strength', 0.7)
        
    def _ensure_sae_device(self):
        """确保SAE在正确的设备上"""
        if self.pretrained_sae is not None:
            self.pretrained_sae = self.pretrained_sae.to(self.device)
            for param in self.pretrained_sae.parameters():
                param.requires_grad = False
            self.pretrained_sae.eval()
    
    def _purify_latent_vector(self, latent_vec):
        """使用预训练SAE净化潜在向量"""
        if self.pretrained_sae is None or len(self.irrelevant_indices) == 0:
            return latent_vec

        with torch.no_grad():
            # 如果有归一化器，先归一化
            if self.sae_scaler is not None:
                latent_vec_cpu = latent_vec.cpu().numpy()
                latent_vec_normalized = self.sae_scaler.transform(latent_vec_cpu)
                latent_vec_input = torch.FloatTensor(latent_vec_normalized).to(latent_vec.device)
            else:
                latent_vec_input = latent_vec

            z_recon, activations = self.pretrained_sae(latent_vec_input)

            # 如果有归一化器，反归一化重构结果
            if self.sae_scaler is not None:
                z_recon_cpu = z_recon.cpu().numpy()
                z_recon_denormalized = self.sae_scaler.inverse_transform(z_recon_cpu)
                z_recon = torch.FloatTensor(z_recon_denormalized).to(latent_vec.device)

            # 计算无关特征的贡献
            irrelevant_mask = torch.zeros_like(activations)
            irrelevant_mask[:, self.irrelevant_indices] = 1.0

            irrelevant_activations = activations * irrelevant_mask

            # 重构无关特征贡献
            if self.sae_scaler is not None:
                irrelevant_recon = self.pretrained_sae.decoder(irrelevant_activations)
                irrelevant_recon_cpu = irrelevant_recon.cpu().numpy()
                irrelevant_contribution = torch.FloatTensor(
                    self.sae_scaler.inverse_transform(irrelevant_recon_cpu)
                ).to(latent_vec.device)
            else:
                irrelevant_contribution = self.pretrained_sae.decoder(irrelevant_activations)

            # 净化
            purified_latent = latent_vec - self.purification_strength * irrelevant_contribution

        return purified_latent
    
    def _compute_scores(self, X, fit_scaler=False):
        """重写评分计算，加入SAE净化"""
        # 确保SAE在正确的设备上
        self._ensure_sae_device()
        
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: 
            return np.zeros(n_samples)

        dataset = torch.utils.data.TensorDataset(torch.from_numpy(windows).float())
        loader = torch.utils.data.DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                # 获取潜在向量
                x_permuted = batch_windows.permute(0, 2, 1)
                encoded_cnn = self.model.encoder_cnn(x_permuted)
                encoded_tcn = self.model.encoder_tcn(encoded_cnn)
                encoded_flat = encoded_tcn.flatten(start_dim=1)
                latent_vec = self.model.fc_encode(encoded_flat)
                
                # SAE净化
                purified_latent = self._purify_latent_vector(latent_vec)
                
                # 解码
                decoded_flat = self.model.decoder_fc(purified_latent)
                decoded_unflat = decoded_flat.view(-1, self.model.tcn_output_channels, self.model.downsampled_len)
                decoded_tcn = self.model.decoder_tcn(decoded_unflat)
                reconstructed_permuted = self.model.decoder_cnn(decoded_tcn)
                
                # 调整尺寸
                if reconstructed_permuted.shape[2] != self.model.window_size:
                    reconstructed_permuted = F.interpolate(
                        reconstructed_permuted, size=self.model.window_size, 
                        mode='linear', align_corners=False
                    )
                
                reconstructed = self.model.output_activation(reconstructed_permuted.permute(0, 2, 1))
                
                # 计算重构误差
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())
        
        # 映射到原始序列
        scores_mapped = np.zeros(n_samples)
        counts = np.zeros(n_samples)
        for i, score in enumerate(np.array(window_scores)):
            start, end = i, i + self.window_size
            scores_mapped[start:end] += score
            counts[start:end] += 1
        scores_mapped[counts > 0] /= counts[counts > 0]
        
        if n_samples > self.window_size - 1:
            scores_mapped[:self.window_size-1] = scores_mapped[self.window_size-1]

        if fit_scaler:
            return self.score_scaler.fit_transform(scores_mapped.reshape(-1, 1)).ravel()
        else:
            return self.score_scaler.transform(scores_mapped.reshape(-1, 1)).ravel()

def get_10_test_datasets():
    """获取10个测试数据集（包括单变量和多变量）"""
    datasets = [
        # 单变量数据集
        {
            'name': 'NAB_Facility',
            'path': 'TSB-AD/Datasets/TSB-AD-U/001_NAB_id_1_Facility_tr_1007_1st_2014.csv',
            'type': 'univariate'
        },
        {
            'name': 'NAB_WebService',
            'path': 'TSB-AD/Datasets/TSB-AD-U/002_NAB_id_2_WebService_tr_1500_1st_4106.csv',
            'type': 'univariate'
        },
        {
            'name': 'NAB_Traffic',
            'path': 'TSB-AD/Datasets/TSB-AD-U/005_NAB_id_5_Traffic_tr_594_1st_1645.csv',
            'type': 'univariate'
        },
        {
            'name': 'NAB_Synthetic',
            'path': 'TSB-AD/Datasets/TSB-AD-U/008_NAB_id_8_Synthetic_tr_1007_1st_2734.csv',
            'type': 'univariate'
        },
        {
            'name': 'NAB_Environment',
            'path': 'TSB-AD/Datasets/TSB-AD-U/016_NAB_id_16_Environment_tr_1816_1st_3540.csv',
            'type': 'univariate'
        },
        # 多变量数据集
        {
            'name': 'MSL_Sensor_1',
            'path': 'TSB-AD/Datasets/TSB-AD-M/002_MSL_id_1_Sensor_tr_500_1st_900.csv',
            'type': 'multivariate'
        },
        {
            'name': 'MSL_Sensor_2',
            'path': 'TSB-AD/Datasets/TSB-AD-M/003_MSL_id_2_Sensor_tr_883_1st_1238.csv',
            'type': 'multivariate'
        },
        {
            'name': 'MSL_Sensor_5',
            'path': 'TSB-AD/Datasets/TSB-AD-M/006_MSL_id_5_Sensor_tr_1150_1st_1250.csv',
            'type': 'multivariate'
        },
        {
            'name': 'Genesis_Sensor',
            'path': 'TSB-AD/Datasets/TSB-AD-M/001_Genesis_id_1_Sensor_tr_4055_1st_15538.csv',
            'type': 'multivariate'
        },
        {
            'name': 'Daphnet_Activity',
            'path': 'TSB-AD/Datasets/TSB-AD-M/018_Daphnet_id_1_HumanActivity_tr_9693_1st_20732.csv',
            'type': 'multivariate'
        }
    ]
    
    return datasets

def load_dataset(dataset_info):
    """加载数据集"""
    if not os.path.exists(dataset_info['path']):
        return None, None, None
    
    df = pd.read_csv(dataset_info['path'])
    
    # 处理不同的标签列名
    if 'label' in df.columns:
        labels = df['label'].values
        data = df.drop('label', axis=1).values
    elif 'Label' in df.columns:
        labels = df['Label'].values
        data = df.drop('Label', axis=1).values
    elif 'anomaly' in df.columns:
        labels = df['anomaly'].values
        data = df.drop('anomaly', axis=1).values
    else:
        # 假设最后一列是标签
        labels = df.iloc[:, -1].values
        data = df.iloc[:, :-1].values
    
    # 对于单变量数据集，只取第一列
    if dataset_info['type'] == 'univariate' and data.shape[1] > 1:
        data = data[:, 0:1]
    
    return data, labels, dataset_info['type']

def create_adaptive_sae(data_type, latent_dim):
    """根据数据类型创建适应的SAE"""
    if data_type == 'univariate':
        # 使用预训练的单变量SAE
        sae_model = LargeScaleSAE(input_dim=latent_dim, hidden_dim=128)
        if os.path.exists('large_scale_pretrained_sae.pth'):
            state_dict = torch.load('large_scale_pretrained_sae.pth', map_location='cpu')
            sae_model.load_state_dict(state_dict)
            return sae_model, None
        else:
            print("⚠️  单变量预训练SAE不存在")
            return None, None
    else:
        # 使用预训练的多变量SAE
        sae_model = MultivariateSAE(input_dim=latent_dim, hidden_dim=128)
        if os.path.exists('multivariate_pretrained_sae.pth'):
            state_dict = torch.load('multivariate_pretrained_sae.pth', map_location='cpu')
            sae_model.load_state_dict(state_dict)

            # 加载归一化器
            import pickle
            scaler = None
            if os.path.exists('multivariate_pretrained_sae_scaler.pkl'):
                with open('multivariate_pretrained_sae_scaler.pkl', 'rb') as f:
                    scaler = pickle.load(f)

            return sae_model, scaler
        else:
            print("⚠️  多变量预训练SAE不存在")
            return None, None

def identify_irrelevant_features_adaptive(sae_model, sample_size=5000):
    """自适应识别无关特征"""
    sae_model.eval()
    with torch.no_grad():
        # 创建随机输入样本
        input_dim = sae_model.input_dim
        random_inputs = torch.randn(sample_size, input_dim)
        _, activations = sae_model(random_inputs)
        activations = activations.numpy()
    
    irrelevant_indices = []
    
    for i in range(activations.shape[1]):
        feature_acts = activations[:, i]
        
        # 统计分析
        activation_rate = np.mean(feature_acts > 0.01)
        activation_std = np.std(feature_acts)
        activation_mean = np.mean(feature_acts)
        
        # 识别标准
        if (activation_rate < 0.02 or  # 极少激活
            (activation_rate > 0.98 and activation_std < 0.001) or  # 总是激活且无变化
            activation_mean < 0.0001):  # 平均激活极低
            irrelevant_indices.append(i)
    
    # 限制无关特征比例
    max_irrelevant = int(activations.shape[1] * 0.3)  # 最多30%
    if len(irrelevant_indices) > max_irrelevant:
        # 按激活统计排序
        feature_scores = []
        for idx in irrelevant_indices:
            score = np.mean(activations[:, idx]) + np.std(activations[:, idx])
            feature_scores.append((idx, score))
        
        feature_scores.sort(key=lambda x: x[1])
        irrelevant_indices = [idx for idx, _ in feature_scores[:max_irrelevant]]
    
    return irrelevant_indices

def run_comprehensive_benchmark():
    """运行10个数据集的全面基准测试"""
    print("🚀 10个数据集的全面基准测试")
    print("=" * 100)
    print("📊 测试范围: 单变量 + 多变量时间序列")
    print("🔧 SAE适应性: 自动适配不同数据类型")
    print("=" * 100)
    
    datasets = get_10_test_datasets()
    
    # 获取HTA-AD的最优超参数
    optimal_hp = Optimal_Uni_algo_HP_dict.get('HTA_AD', {
        'window_size': 128,
        'epochs': 30,
        'lr': 1e-3,
        'batch_size': 64,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2
    })
    
    results = []
    successful_tests = 0
    
    for i, dataset_info in enumerate(datasets, 1):
        print(f"\n🔬 [{i}/10] 测试数据集: {dataset_info['name']}")
        print(f"📁 类型: {dataset_info['type']}")
        print("-" * 80)
        
        try:
            # 加载数据
            data, labels, data_type = load_dataset(dataset_info)
            
            if data is None:
                print(f"⚠️  数据集不存在，跳过")
                continue
            
            print(f"📊 数据形状: {data.shape}")
            print(f"📊 异常样本: {np.sum(labels)} / {len(labels)} ({np.mean(labels):.1%})")
            
            # 分割数据
            split_point = int(len(data) * 0.7)
            train_data = data[:split_point]
            test_data = data[split_point:]
            test_labels = labels[split_point:]
            
            # 检查测试集标签分布
            unique_labels = np.unique(test_labels)
            if len(unique_labels) < 2:
                print(f"⚠️  测试集只有一种标签，跳过")
                continue
            
            anomaly_ratio = np.mean(test_labels)
            if anomaly_ratio == 0 or anomaly_ratio == 1:
                print(f"⚠️  测试集异常比例异常，跳过")
                continue
            
            # 测试原始HTA-AD
            print("1️⃣ 原始HTA-AD")
            original_model = HTA_AD(optimal_hp)
            original_model.fit(train_data)
            original_scores = original_model.decision_function(test_data)
            
            original_metrics = safe_get_metrics(
                scores=original_scores,
                labels=test_labels,
                slidingWindow=100
            )
            
            print(f"   VUS-PR: {original_metrics.get('VUS-PR', 0):.4f}")
            print(f"   AUC-ROC: {original_metrics.get('AUC-ROC', 0):.4f}")
            
            # 创建适应的SAE
            print("2️⃣ HTA-AD + SAE (自适应)")
            sae_model, sae_scaler = create_adaptive_sae(data_type, optimal_hp['latent_dim'])

            if sae_model is None:
                print(f"   ❌ 无法加载预训练SAE，跳过")
                continue

            irrelevant_indices = identify_irrelevant_features_adaptive(sae_model)

            print(f"   SAE类型: 预训练 ({data_type})")
            print(f"   归一化器: {'是' if sae_scaler is not None else '否'}")
            print(f"   无关特征: {len(irrelevant_indices)}/{sae_model.hidden_dim}")
            
            # 测试不同净化强度
            best_metrics = original_metrics
            best_strength = 0.0
            
            for strength in [0.3, 0.5, 0.7, 1.0]:
                enhanced_hp = optimal_hp.copy()
                enhanced_hp['purification_strength'] = strength
                
                enhanced_model = HTA_AD_SAE_Multivariate(
                    enhanced_hp,
                    pretrained_sae=sae_model,
                    sae_scaler=sae_scaler,
                    irrelevant_indices=irrelevant_indices
                )
                
                enhanced_model.fit(train_data)
                enhanced_scores = enhanced_model.decision_function(test_data)
                
                enhanced_metrics = safe_get_metrics(
                    scores=enhanced_scores,
                    labels=test_labels,
                    slidingWindow=100
                )
                
                vus_pr = enhanced_metrics.get('VUS-PR', 0)
                
                if vus_pr > best_metrics.get('VUS-PR', 0):
                    best_metrics = enhanced_metrics
                    best_strength = strength
            
            # 计算改进
            original_vus_pr = original_metrics.get('VUS-PR', 0)
            best_vus_pr = best_metrics.get('VUS-PR', 0)
            
            if original_vus_pr > 0:
                improvement = (best_vus_pr - original_vus_pr) / original_vus_pr * 100
            else:
                improvement = 0.0
            
            print(f"🏆 最佳结果: 强度{best_strength}, VUS-PR {best_vus_pr:.4f} ({improvement:+.1f}%)")
            
            results.append({
                'dataset': dataset_info['name'],
                'type': data_type,
                'data_shape': f"{data.shape[0]}x{data.shape[1]}",
                'anomaly_ratio': f"{np.mean(labels):.1%}",
                'original_vus_pr': original_vus_pr,
                'original_auc_roc': original_metrics.get('AUC-ROC', 0),
                'best_vus_pr': best_vus_pr,
                'best_auc_roc': best_metrics.get('AUC-ROC', 0),
                'improvement': improvement,
                'best_strength': best_strength,
                'irrelevant_features': len(irrelevant_indices)
            })
            
            successful_tests += 1
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    return results, successful_tests

def analyze_results(results):
    """分析测试结果"""
    if not results:
        print("❌ 没有成功的测试结果")
        return
    
    df_results = pd.DataFrame(results)
    
    print(f"\n📊 测试结果汇总 ({len(results)}个数据集)")
    print("=" * 120)
    
    # 按数据类型分组分析
    univariate_results = df_results[df_results['type'] == 'univariate']
    multivariate_results = df_results[df_results['type'] == 'multivariate']
    
    print(f"\n📈 详细结果:")
    print(f"{'数据集':<20} {'类型':<12} {'形状':<12} {'异常率':<8} {'原始VUS-PR':<12} {'最佳VUS-PR':<12} {'改进':<10}")
    print("-" * 120)
    
    for _, row in df_results.iterrows():
        print(f"{row['dataset']:<20} {row['type']:<12} {row['data_shape']:<12} {row['anomaly_ratio']:<8} "
              f"{row['original_vus_pr']:<12.4f} {row['best_vus_pr']:<12.4f} {row['improvement']:>+7.1f}%")
    
    # 统计分析
    print(f"\n📊 统计分析:")
    print("-" * 80)
    
    overall_improvement = df_results['improvement'].mean()
    positive_improvements = df_results[df_results['improvement'] > 0]
    
    print(f"总体平均改进: {overall_improvement:+.2f}%")
    print(f"有效改进数据集: {len(positive_improvements)}/{len(df_results)} ({len(positive_improvements)/len(df_results)*100:.1f}%)")
    
    if len(positive_improvements) > 0:
        print(f"有效改进平均值: {positive_improvements['improvement'].mean():+.2f}%")
        print(f"最大改进: {df_results['improvement'].max():+.2f}%")
        print(f"最小改进: {df_results['improvement'].min():+.2f}%")
    
    # 按数据类型分析
    if len(univariate_results) > 0:
        uni_improvement = univariate_results['improvement'].mean()
        print(f"\n单变量数据集平均改进: {uni_improvement:+.2f}%")
    
    if len(multivariate_results) > 0:
        multi_improvement = multivariate_results['improvement'].mean()
        print(f"多变量数据集平均改进: {multi_improvement:+.2f}%")
    
    # 保存结果
    df_results.to_csv('comprehensive_10_dataset_results.csv', index=False)
    print(f"\n💾 详细结果已保存到 'comprehensive_10_dataset_results.csv'")
    
    return df_results

def main():
    """主函数"""
    print("🎯 HTA-AD + SAE 10数据集全面基准测试")
    print("=" * 120)
    
    results, successful_tests = run_comprehensive_benchmark()
    
    if successful_tests > 0:
        df_results = analyze_results(results)
        
        print(f"\n🎉 基准测试完成！")
        print(f"✅ 成功测试: {successful_tests}/10 个数据集")
        print(f"📊 SAE在多变量数据上的适应性得到验证")
        print(f"💡 为时间序列异常检测提供了全面的性能评估")
    else:
        print(f"\n❌ 所有测试都失败了")

if __name__ == "__main__":
    main()
