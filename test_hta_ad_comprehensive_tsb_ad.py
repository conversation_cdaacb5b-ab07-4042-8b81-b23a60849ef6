#!/usr/bin/env python3
"""
Comprehensive HTA-AD Testing on TSB-AD Dataset with Visualization
Using 10 datasets and TSB-AD official metrics only
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import sys
import os
import time
import warnings
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns

warnings.filterwarnings('ignore')

# Add paths
sys.path.append('.')
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

from core.models.hta_ad_paper_compliant import HTAADBasic, PostHocSAE

# Import TSB-AD evaluation metrics
try:
    from TSB_AD.evaluation.metrics import get_metrics
    TSB_AD_AVAILABLE = True
    print("✅ TSB-AD evaluation metrics loaded successfully")
except ImportError:
    print("❌ TSB-AD evaluation metrics not available")
    TSB_AD_AVAILABLE = False
    sys.exit(1)

def parse_dataset_filename(filename):
    """Parse TSB-AD dataset filename to extract train/test split info"""
    parts = filename.split('_')
    train_size = None
    test_size = None
    
    for i, part in enumerate(parts):
        if part == 'tr' and i + 1 < len(parts):
            train_size = int(parts[i + 1])
        elif part == '1st' and i + 1 < len(parts):
            test_size = int(parts[i + 1].split('.')[0])
    
    return train_size, test_size

def load_tsb_ad_dataset(dataset_path):
    """Load TSB-AD dataset with proper train/test split"""
    try:
        df = pd.read_csv(dataset_path)
        data = df.iloc[:, 0].values.reshape(-1, 1)
        labels = df.iloc[:, 1].values
        
        filename = os.path.basename(dataset_path)
        train_size, test_size = parse_dataset_filename(filename)
        
        if train_size is None or test_size is None:
            print(f"Warning: Could not parse sizes from {filename}")
            split_idx = int(0.7 * len(data))
            train_data = data[:split_idx]
            train_labels = labels[:split_idx]
            test_data = data[split_idx:]
            test_labels = labels[split_idx:]
        else:
            train_data = data[:train_size]
            train_labels = labels[:train_size]
            test_data = data[train_size:train_size + test_size]
            test_labels = labels[train_size:train_size + test_size]
        
        return train_data, train_labels, test_data, test_labels
    except Exception as e:
        print(f"Error loading dataset {dataset_path}: {e}")
        return None, None, None, None

def create_sliding_windows(data, window_size, stride=1):
    """Create sliding windows from time series data"""
    if len(data.shape) == 1:
        data = data.reshape(-1, 1)
    
    n_samples, n_features = data.shape
    windows = []
    
    for i in range(0, n_samples - window_size + 1, stride):
        window = data[i:i + window_size]
        windows.append(window)
    
    return np.array(windows)

def train_models_on_dataset(train_data, input_dim, device='cpu', epochs=30):
    """Train HTA-AD and SAE models"""
    model = HTAADBasic(
        input_dim=input_dim,
        d_model=32,
        seq_len=100
    ).to(device)
    
    sae = PostHocSAE(
        latent_dim=32,
        hidden_dim=128
    ).to(device)
    
    train_windows = create_sliding_windows(train_data, 100)
    train_windows = torch.FloatTensor(train_windows).to(device)
    
    if len(train_windows) == 0:
        return None, None
    
    # Train HTA-AD
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    model.train()
    
    for epoch in range(epochs):
        total_loss = 0
        batch_size = 32
        
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch)
            losses = model.compute_loss(outputs, batch)
            loss = losses['total']
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 10 == 0:
            avg_loss = total_loss / (len(train_windows) // batch_size + 1)
            print(f"    Epoch {epoch + 1}/{epochs}, Loss: {avg_loss:.4f}")
    
    # Collect latent vectors for SAE training
    model.eval()
    all_latents = []
    
    with torch.no_grad():
        batch_size = 32
        for i in range(0, len(train_windows), batch_size):
            batch = train_windows[i:i + batch_size]
            outputs = model(batch)
            latents = outputs['latent_vectors']
            all_latents.append(latents)
    
    all_latents = torch.cat(all_latents, dim=0)
    
    # Train SAE
    sae_optimizer = torch.optim.Adam(sae.parameters(), lr=0.001)
    sae.train()
    
    for epoch in range(epochs // 2):
        total_loss = 0
        batch_size = 64
        
        for i in range(0, len(all_latents), batch_size):
            batch = all_latents[i:i + batch_size]
            
            sae_optimizer.zero_grad()
            losses = sae.compute_loss(batch)
            loss = losses['total']
            
            loss.backward()
            sae_optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 5 == 0:
            avg_loss = total_loss / (len(all_latents) // batch_size + 1)
            print(f"    SAE Epoch {epoch + 1}/{epochs // 2}, Loss: {avg_loss:.4f}")
    
    # Identify irrelevant features
    sae.identify_irrelevant_features(all_latents)
    
    return model, sae

def evaluate_model_tsb_ad(model, sae, test_data, test_labels, window_size=100, device='cpu'):
    """Evaluate using TSB-AD metrics only"""
    model.eval()
    sae.eval()
    
    if len(test_data) < window_size:
        return None
    
    # Point-wise evaluation
    pad_size = window_size // 2
    padded_data = np.pad(test_data, ((pad_size, pad_size), (0, 0)), mode='edge')
    
    point_scores_hta = []
    point_scores_sae = []
    
    with torch.no_grad():
        for i in range(len(test_data)):
            window = padded_data[i:i + window_size]
            window_tensor = torch.FloatTensor(window).unsqueeze(0).to(device)
            
            # HTA-AD forward pass
            outputs = model(window_tensor)
            reconstruction = outputs['reconstruction']
            latent_vectors = outputs['latent_vectors']
            
            # Compute reconstruction error for center point
            center_idx = window_size // 2
            point_error = torch.mean((window_tensor[0, center_idx] - reconstruction[0, center_idx]) ** 2)
            point_scores_hta.append(point_error.cpu().item())
            
            # SAE forward pass
            z_hat, features = sae(latent_vectors)
            sae_error = torch.mean((latent_vectors[0, center_idx] - z_hat[0, center_idx]) ** 2)
            point_scores_sae.append(sae_error.cpu().item())
    
    point_scores_hta = np.array(point_scores_hta)
    point_scores_sae = np.array(point_scores_sae)
    
    results = {}
    
    if len(np.unique(test_labels)) > 1:
        try:
            # TSB-AD official metrics
            hta_metrics = get_metrics(point_scores_hta, test_labels, slidingWindow=window_size)
            sae_metrics = get_metrics(point_scores_sae, test_labels, slidingWindow=window_size)
            
            results['hta_ad_vus_pr'] = hta_metrics.get('VUS-PR', 0)
            results['hta_ad_vus_roc'] = hta_metrics.get('VUS-ROC', 0)
            results['sae_vus_pr'] = sae_metrics.get('VUS-PR', 0)
            results['sae_vus_roc'] = sae_metrics.get('VUS-ROC', 0)
            
            results['hta_ad_f1'] = hta_metrics.get('Standard-F1', 0)
            results['sae_f1'] = sae_metrics.get('Standard-F1', 0)
            
        except Exception as e:
            print(f"    Warning: TSB-AD metrics calculation failed: {e}")
            results = {
                'hta_ad_vus_pr': 0, 'hta_ad_vus_roc': 0,
                'sae_vus_pr': 0, 'sae_vus_roc': 0,
                'hta_ad_f1': 0, 'sae_f1': 0
            }
    else:
        results = {
            'hta_ad_vus_pr': 0, 'hta_ad_vus_roc': 0,
            'sae_vus_pr': 0, 'sae_vus_roc': 0,
            'hta_ad_f1': 0, 'sae_f1': 0
        }
    
    results['n_points'] = len(test_labels)
    results['anomaly_ratio'] = np.mean(test_labels)
    results['point_scores_hta'] = point_scores_hta
    results['point_scores_sae'] = point_scores_sae
    results['test_labels'] = test_labels
    
    return results

def create_visualizations(results_df, save_dir='results/tsb_ad_visualization'):
    """Create comprehensive visualizations"""
    os.makedirs(save_dir, exist_ok=True)
    
    # Set style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # 1. Performance Comparison Bar Chart
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('HTA-AD vs SAE Performance on TSB-AD Datasets', fontsize=16, fontweight='bold')
    
    # VUS-PR comparison
    x = np.arange(len(results_df))
    width = 0.35
    
    axes[0, 0].bar(x - width/2, results_df['hta_ad_vus_pr'], width, label='HTA-AD', alpha=0.8)
    axes[0, 0].bar(x + width/2, results_df['sae_vus_pr'], width, label='SAE', alpha=0.8)
    axes[0, 0].set_title('VUS-PR Comparison')
    axes[0, 0].set_ylabel('VUS-PR Score')
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels([f'D{i+1}' for i in range(len(results_df))], rotation=45)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # VUS-ROC comparison
    axes[0, 1].bar(x - width/2, results_df['hta_ad_vus_roc'], width, label='HTA-AD', alpha=0.8)
    axes[0, 1].bar(x + width/2, results_df['sae_vus_roc'], width, label='SAE', alpha=0.8)
    axes[0, 1].set_title('VUS-ROC Comparison')
    axes[0, 1].set_ylabel('VUS-ROC Score')
    axes[0, 1].set_xticks(x)
    axes[0, 1].set_xticklabels([f'D{i+1}' for i in range(len(results_df))], rotation=45)
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # F1 Score comparison
    axes[1, 0].bar(x - width/2, results_df['hta_ad_f1'], width, label='HTA-AD', alpha=0.8)
    axes[1, 0].bar(x + width/2, results_df['sae_f1'], width, label='SAE', alpha=0.8)
    axes[1, 0].set_title('F1 Score Comparison')
    axes[1, 0].set_ylabel('F1 Score')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels([f'D{i+1}' for i in range(len(results_df))], rotation=45)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Improvement percentage
    vus_pr_improvement = ((results_df['sae_vus_pr'] - results_df['hta_ad_vus_pr']) / 
                         (results_df['hta_ad_vus_pr'] + 1e-8) * 100)
    vus_roc_improvement = ((results_df['sae_vus_roc'] - results_df['hta_ad_vus_roc']) / 
                          (results_df['hta_ad_vus_roc'] + 1e-8) * 100)
    
    axes[1, 1].bar(x - width/2, vus_pr_improvement, width, label='VUS-PR Improvement', alpha=0.8)
    axes[1, 1].bar(x + width/2, vus_roc_improvement, width, label='VUS-ROC Improvement', alpha=0.8)
    axes[1, 1].set_title('SAE Improvement over HTA-AD (%)')
    axes[1, 1].set_ylabel('Improvement (%)')
    axes[1, 1].set_xticks(x)
    axes[1, 1].set_xticklabels([f'D{i+1}' for i in range(len(results_df))], rotation=45)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Summary Statistics
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Average performance
    metrics = ['VUS-PR', 'VUS-ROC', 'F1']
    hta_means = [results_df['hta_ad_vus_pr'].mean(), 
                 results_df['hta_ad_vus_roc'].mean(),
                 results_df['hta_ad_f1'].mean()]
    sae_means = [results_df['sae_vus_pr'].mean(),
                 results_df['sae_vus_roc'].mean(), 
                 results_df['sae_f1'].mean()]
    
    x = np.arange(len(metrics))
    axes[0].bar(x - width/2, hta_means, width, label='HTA-AD', alpha=0.8)
    axes[0].bar(x + width/2, sae_means, width, label='SAE', alpha=0.8)
    axes[0].set_title('Average Performance Across All Datasets')
    axes[0].set_ylabel('Score')
    axes[0].set_xticks(x)
    axes[0].set_xticklabels(metrics)
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Dataset characteristics
    axes[1].scatter(results_df['anomaly_ratio'], results_df['sae_vus_pr'], 
                   s=results_df['n_points']/10, alpha=0.7, label='SAE VUS-PR')
    axes[1].scatter(results_df['anomaly_ratio'], results_df['hta_ad_vus_pr'], 
                   s=results_df['n_points']/10, alpha=0.7, label='HTA-AD VUS-PR')
    axes[1].set_xlabel('Anomaly Ratio')
    axes[1].set_ylabel('VUS-PR Score')
    axes[1].set_title('Performance vs Dataset Characteristics')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/summary_statistics.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 Visualizations saved to {save_dir}/")
    return save_dir

def run_comprehensive_test():
    """Run comprehensive test on 10 TSB-AD datasets"""
    print("🚀 Comprehensive HTA-AD Testing on TSB-AD Dataset")
    print("=" * 60)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Select 10 diverse datasets
    test_datasets = [
        "TSB-AD/Datasets/TSB-AD-U/001_NAB_id_1_Facility_tr_1007_1st_2014.csv",
        "TSB-AD/Datasets/TSB-AD-U/002_NAB_id_2_WebService_tr_1500_1st_4106.csv",
        "TSB-AD/Datasets/TSB-AD-U/140_MSL_id_1_Sensor_tr_530_1st_630.csv",
        "TSB-AD/Datasets/TSB-AD-U/169_Daphnet_id_1_HumanActivity_tr_9693_1st_20732.csv",
        "TSB-AD/Datasets/TSB-AD-U/170_MITDB_id_1_Medical_tr_17675_1st_17775.csv",
        "TSB-AD/Datasets/TSB-AD-U/225_MGAB_id_1_Synthetic_tr_25000_1st_38478.csv",
        "TSB-AD/Datasets/TSB-AD-U/531_SMAP_id_1_Sensor_tr_1811_1st_4510.csv",
        "TSB-AD/Datasets/TSB-AD-U/551_YAHOO_id_1_Synthetic_tr_500_1st_893.csv",
        "TSB-AD/Datasets/TSB-AD-U/810_Exathlon_id_1_Facility_tr_10766_1st_12590.csv",
        "TSB-AD/Datasets/TSB-AD-U/842_OPPORTUNITY_id_1_HumanActivity_tr_7295_1st_27516.csv"
    ]

    results_summary = []

    for i, dataset_path in enumerate(test_datasets):
        if not os.path.exists(dataset_path):
            print(f"❌ Dataset not found: {dataset_path}")
            continue

        dataset_name = os.path.basename(dataset_path)
        print(f"\n📊 [{i+1}/10] Testing: {dataset_name}")
        print("-" * 50)

        # Load dataset
        train_data, train_labels, test_data, test_labels = load_tsb_ad_dataset(dataset_path)
        if train_data is None:
            continue

        print(f"  Train: {train_data.shape[0]} points, {np.mean(train_labels):.3f} anomaly ratio")
        print(f"  Test:  {test_data.shape[0]} points, {np.mean(test_labels):.3f} anomaly ratio")

        # Skip if test set has no anomalies
        if np.sum(test_labels) == 0:
            print("  ⚠️  Skipping: No anomalies in test set")
            continue

        # Normalize data
        scaler = StandardScaler()
        train_data = scaler.fit_transform(train_data)
        test_data = scaler.transform(test_data)

        input_dim = train_data.shape[1]

        try:
            # Train models
            print("  🔧 Training models...")
            start_time = time.time()
            model, sae = train_models_on_dataset(train_data, input_dim, device, epochs=25)
            training_time = time.time() - start_time

            if model is None or sae is None:
                print("  ❌ Training failed")
                continue

            # Evaluate
            print("  📈 Evaluating...")
            start_time = time.time()
            results = evaluate_model_tsb_ad(model, sae, test_data, test_labels, device=device)
            eval_time = time.time() - start_time

            if results is None:
                print("  ❌ Evaluation failed")
                continue

            # Print results
            print(f"  ✅ Results:")
            print(f"     - HTA-AD VUS-PR:  {results['hta_ad_vus_pr']:.4f}")
            print(f"     - HTA-AD VUS-ROC: {results['hta_ad_vus_roc']:.4f}")
            print(f"     - SAE VUS-PR:     {results['sae_vus_pr']:.4f}")
            print(f"     - SAE VUS-ROC:    {results['sae_vus_roc']:.4f}")
            print(f"     - HTA-AD F1:      {results['hta_ad_f1']:.4f}")
            print(f"     - SAE F1:         {results['sae_f1']:.4f}")
            print(f"     - Training: {training_time:.1f}s, Eval: {eval_time:.1f}s")

            # Store results
            results_summary.append({
                'dataset': dataset_name,
                'dataset_id': i + 1,
                'hta_ad_vus_pr': results['hta_ad_vus_pr'],
                'hta_ad_vus_roc': results['hta_ad_vus_roc'],
                'sae_vus_pr': results['sae_vus_pr'],
                'sae_vus_roc': results['sae_vus_roc'],
                'hta_ad_f1': results['hta_ad_f1'],
                'sae_f1': results['sae_f1'],
                'n_points': results['n_points'],
                'anomaly_ratio': results['anomaly_ratio'],
                'training_time': training_time,
                'eval_time': eval_time
            })

        except Exception as e:
            print(f"  ❌ Error: {e}")
            continue

    # Process results
    if not results_summary:
        print("\n❌ No datasets were successfully processed.")
        return

    results_df = pd.DataFrame(results_summary)

    # Print summary
    print("\n" + "=" * 60)
    print("📈 COMPREHENSIVE RESULTS SUMMARY")
    print("=" * 60)

    print("\n📊 Individual Dataset Results:")
    display_df = results_df[['dataset_id', 'hta_ad_vus_pr', 'sae_vus_pr',
                            'hta_ad_vus_roc', 'sae_vus_roc', 'hta_ad_f1', 'sae_f1']]
    print(display_df.to_string(index=False, float_format='%.4f'))

    print(f"\n📊 Average Performance:")
    print(f"   HTA-AD Metrics:")
    print(f"     - VUS-PR:  {results_df['hta_ad_vus_pr'].mean():.4f} ± {results_df['hta_ad_vus_pr'].std():.4f}")
    print(f"     - VUS-ROC: {results_df['hta_ad_vus_roc'].mean():.4f} ± {results_df['hta_ad_vus_roc'].std():.4f}")
    print(f"     - F1:      {results_df['hta_ad_f1'].mean():.4f} ± {results_df['hta_ad_f1'].std():.4f}")

    print(f"   SAE Metrics:")
    print(f"     - VUS-PR:  {results_df['sae_vus_pr'].mean():.4f} ± {results_df['sae_vus_pr'].std():.4f}")
    print(f"     - VUS-ROC: {results_df['sae_vus_roc'].mean():.4f} ± {results_df['sae_vus_roc'].std():.4f}")
    print(f"     - F1:      {results_df['sae_f1'].mean():.4f} ± {results_df['sae_f1'].std():.4f}")

    # Calculate improvements
    vus_pr_improvement = ((results_df['sae_vus_pr'] - results_df['hta_ad_vus_pr']) /
                         (results_df['hta_ad_vus_pr'] + 1e-8) * 100).mean()
    vus_roc_improvement = ((results_df['sae_vus_roc'] - results_df['hta_ad_vus_roc']) /
                          (results_df['hta_ad_vus_roc'] + 1e-8) * 100).mean()

    print(f"\n🚀 SAE Improvements:")
    print(f"     - VUS-PR improvement:  {vus_pr_improvement:+.1f}%")
    print(f"     - VUS-ROC improvement: {vus_roc_improvement:+.1f}%")

    print(f"\n⏱️  Average Times:")
    print(f"     - Training: {results_df['training_time'].mean():.1f}s")
    print(f"     - Evaluation: {results_df['eval_time'].mean():.1f}s")

    # Create visualizations
    print(f"\n🎨 Creating visualizations...")
    viz_dir = create_visualizations(results_df)

    # Save results to CSV
    results_df.to_csv('results/tsb_ad_comprehensive_results.csv', index=False)
    print(f"💾 Results saved to results/tsb_ad_comprehensive_results.csv")

    print(f"\n🎉 Comprehensive testing completed successfully!")
    print(f"   - Tested {len(results_df)} datasets")
    print(f"   - Average SAE VUS-PR: {results_df['sae_vus_pr'].mean():.4f}")
    print(f"   - Average SAE VUS-ROC: {results_df['sae_vus_roc'].mean():.4f}")

if __name__ == "__main__":
    run_comprehensive_test()
