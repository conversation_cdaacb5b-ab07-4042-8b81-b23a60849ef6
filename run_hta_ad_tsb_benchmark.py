#!/usr/bin/env python3
"""
HTA-AD Integration with TSB-AD Benchmark
Following TSB-AD standard interface and evaluation
"""

import pandas as pd
import numpy as np
import torch
import random
import time
import os
import sys
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler, StandardScaler
import warnings

warnings.filterwarnings('ignore')

# Add paths
sys.path.append('.')
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

from core.models.hta_ad_paper_compliant import HTAADBasic, PostHocSAE
from TSB_AD.evaluation.metrics import get_metrics
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.models.base import BaseDetector
from TSB_AD.utils.utility import zscore

# Set random seeds
seed = 2024
torch.manual_seed(seed)
torch.cuda.manual_seed(seed)
torch.cuda.manual_seed_all(seed)
np.random.seed(seed)
random.seed(seed)
torch.backends.cudnn.benchmark = False
torch.backends.cudnn.deterministic = True

class HTA_AD_TSB(BaseDetector):
    """HTA-AD wrapper for TSB-AD benchmark"""
    
    def __init__(self, input_dim=1, d_model=32, seq_len=100, epochs=30, device='auto'):
        super().__init__()
        self.input_dim = input_dim
        self.d_model = d_model
        self.seq_len = seq_len
        self.epochs = epochs
        
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        self.model = None
        self.scaler = StandardScaler()
        
    def create_sliding_windows(self, data, window_size, stride=1):
        """Create sliding windows from time series data"""
        if len(data.shape) == 1:
            data = data.reshape(-1, 1)
        
        n_samples, n_features = data.shape
        windows = []
        
        for i in range(0, n_samples - window_size + 1, stride):
            window = data[i:i + window_size]
            windows.append(window)
        
        return np.array(windows)
    
    def fit(self, X, y=None):
        """Fit HTA-AD model on training data"""
        print(f"  🔧 Training HTA-AD on {X.shape[0]} samples...")
        
        # Normalize data
        X_normalized = self.scaler.fit_transform(X)
        
        # Create model
        self.model = HTAADBasic(
            input_dim=self.input_dim,
            d_model=self.d_model,
            seq_len=self.seq_len
        ).to(self.device)
        
        # Create training windows
        train_windows = self.create_sliding_windows(X_normalized, self.seq_len)
        if len(train_windows) == 0:
            print("    ⚠️  No training windows created - data too short")
            self.decision_scores_ = np.zeros(X.shape[0])
            return self
        
        train_windows = torch.FloatTensor(train_windows).to(self.device)
        
        # Train model
        optimizer = torch.optim.Adam(self.model.parameters(), lr=0.001)
        self.model.train()
        
        for epoch in range(self.epochs):
            total_loss = 0
            batch_size = 32
            
            for i in range(0, len(train_windows), batch_size):
                batch = train_windows[i:i + batch_size]
                
                optimizer.zero_grad()
                outputs = self.model(batch)
                losses = self.model.compute_loss(outputs, batch)
                loss = losses['total']
                
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            if (epoch + 1) % 10 == 0:
                avg_loss = total_loss / (len(train_windows) // batch_size + 1)
                print(f"    Epoch {epoch + 1}/{self.epochs}, Loss: {avg_loss:.4f}")
        
        # Compute training scores for API consistency
        self.decision_scores_ = self.decision_function(X)
        return self
    
    def decision_function(self, X):
        """Compute anomaly scores for test data"""
        if self.model is None:
            return np.zeros(X.shape[0])
        
        # Normalize data using fitted scaler
        X_normalized = self.scaler.transform(X)
        
        self.model.eval()
        scores = []
        
        # Point-wise scoring using sliding windows
        pad_size = self.seq_len // 2
        padded_data = np.pad(X_normalized, ((pad_size, pad_size), (0, 0)), mode='edge')
        
        with torch.no_grad():
            for i in range(X.shape[0]):
                # Extract window centered at point i
                window = padded_data[i:i + self.seq_len]
                window_tensor = torch.FloatTensor(window).unsqueeze(0).to(self.device)
                
                # Forward pass
                outputs = self.model(window_tensor)
                reconstruction = outputs['reconstruction']
                
                # Compute reconstruction error for center point
                center_idx = self.seq_len // 2
                point_error = torch.mean((window_tensor[0, center_idx] - reconstruction[0, center_idx]) ** 2)
                scores.append(point_error.cpu().item())
        
        return np.array(scores)

class HTA_AD_SAE_TSB(HTA_AD_TSB):
    """HTA-AD with SAE wrapper for TSB-AD benchmark"""
    
    def __init__(self, input_dim=1, d_model=32, seq_len=100, sae_hidden_dim=128, epochs=30, device='auto'):
        super().__init__(input_dim, d_model, seq_len, epochs, device)
        self.sae_hidden_dim = sae_hidden_dim
        self.sae = None
    
    def fit(self, X, y=None):
        """Fit HTA-AD and SAE models"""
        print(f"  🔧 Training HTA-AD+SAE on {X.shape[0]} samples...")
        
        # First train HTA-AD
        super().fit(X, y)
        
        if self.model is None:
            return self
        
        # Create SAE
        self.sae = PostHocSAE(
            latent_dim=self.d_model,
            hidden_dim=self.sae_hidden_dim
        ).to(self.device)
        
        # Collect latent vectors for SAE training
        X_normalized = self.scaler.transform(X)
        train_windows = self.create_sliding_windows(X_normalized, self.seq_len)
        train_windows = torch.FloatTensor(train_windows).to(self.device)
        
        self.model.eval()
        all_latents = []
        
        with torch.no_grad():
            batch_size = 32
            for i in range(0, len(train_windows), batch_size):
                batch = train_windows[i:i + batch_size]
                outputs = self.model(batch)
                latents = outputs['latent_vectors']
                all_latents.append(latents)
        
        all_latents = torch.cat(all_latents, dim=0)
        print(f"    Collected {all_latents.shape[0]} latent vectors for SAE")
        
        # Train SAE
        sae_optimizer = torch.optim.Adam(self.sae.parameters(), lr=0.001)
        self.sae.train()
        
        for epoch in range(self.epochs // 2):
            total_loss = 0
            batch_size = 64
            
            for i in range(0, len(all_latents), batch_size):
                batch = all_latents[i:i + batch_size]
                
                sae_optimizer.zero_grad()
                losses = self.sae.compute_loss(batch)
                loss = losses['total']
                
                loss.backward()
                sae_optimizer.step()
                
                total_loss += loss.item()
            
            if (epoch + 1) % 5 == 0:
                avg_loss = total_loss / (len(all_latents) // batch_size + 1)
                print(f"    SAE Epoch {epoch + 1}/{self.epochs // 2}, Loss: {avg_loss:.4f}")
        
        # Identify irrelevant features
        self.sae.identify_irrelevant_features(all_latents)
        
        return self
    
    def decision_function(self, X):
        """Compute anomaly scores using SAE"""
        if self.model is None or self.sae is None:
            return np.zeros(X.shape[0])
        
        # Normalize data
        X_normalized = self.scaler.transform(X)
        
        self.model.eval()
        self.sae.eval()
        scores = []
        
        # Point-wise scoring
        pad_size = self.seq_len // 2
        padded_data = np.pad(X_normalized, ((pad_size, pad_size), (0, 0)), mode='edge')
        
        with torch.no_grad():
            for i in range(X.shape[0]):
                window = padded_data[i:i + self.seq_len]
                window_tensor = torch.FloatTensor(window).unsqueeze(0).to(self.device)
                
                # Get latent vectors
                outputs = self.model(window_tensor)
                latent_vectors = outputs['latent_vectors']
                
                # SAE reconstruction error
                z_hat, features = self.sae(latent_vectors)
                center_idx = self.seq_len // 2
                sae_error = torch.mean((latent_vectors[0, center_idx] - z_hat[0, center_idx]) ** 2)
                scores.append(sae_error.cpu().item())
        
        return np.array(scores)

def visualize_time_series_with_anomalies(data, labels, anomaly_scores, dataset_name, save_dir='results/visualizations'):
    """Visualize time series with anomalies and anomaly scores"""
    os.makedirs(save_dir, exist_ok=True)
    
    fig, axes = plt.subplots(3, 1, figsize=(15, 10))
    fig.suptitle(f'Time Series Analysis: {dataset_name}', fontsize=16, fontweight='bold')
    
    # Plot 1: Original time series with anomaly labels
    axes[0].plot(data, color='blue', alpha=0.7, linewidth=1)
    anomaly_indices = np.where(labels == 1)[0]
    if len(anomaly_indices) > 0:
        axes[0].scatter(anomaly_indices, data[anomaly_indices], color='red', s=20, alpha=0.8, label='True Anomalies')
    axes[0].set_title('Original Time Series with True Anomalies')
    axes[0].set_ylabel('Value')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot 2: Anomaly scores
    axes[1].plot(anomaly_scores, color='orange', linewidth=1)
    axes[1].set_title('Anomaly Scores')
    axes[1].set_ylabel('Anomaly Score')
    axes[1].grid(True, alpha=0.3)
    
    # Plot 3: Combined view
    ax3_twin = axes[2].twinx()
    axes[2].plot(data, color='blue', alpha=0.7, linewidth=1, label='Time Series')
    ax3_twin.plot(anomaly_scores, color='orange', alpha=0.8, linewidth=1, label='Anomaly Scores')
    
    if len(anomaly_indices) > 0:
        axes[2].scatter(anomaly_indices, data[anomaly_indices], color='red', s=20, alpha=0.8, label='True Anomalies')
    
    axes[2].set_title('Time Series with Anomaly Scores')
    axes[2].set_xlabel('Time')
    axes[2].set_ylabel('Time Series Value', color='blue')
    ax3_twin.set_ylabel('Anomaly Score', color='orange')
    
    # Combine legends
    lines1, labels1 = axes[2].get_legend_handles_labels()
    lines2, labels2 = ax3_twin.get_legend_handles_labels()
    axes[2].legend(lines1 + lines2, labels1 + labels2, loc='upper right')
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    safe_name = dataset_name.replace('.csv', '').replace('/', '_')
    plt.savefig(f'{save_dir}/{safe_name}_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return f'{save_dir}/{safe_name}_analysis.png'

def run_hta_ad_on_dataset(dataset_path, model_type='HTA_AD'):
    """Run HTA-AD on a single TSB-AD dataset"""

    # Load dataset
    df = pd.read_csv(dataset_path).dropna()
    data = df.iloc[:, 0:-1].values.astype(float)
    label = df['Label'].astype(int).to_numpy()

    print(f"  📊 Data shape: {data.shape}, Anomaly ratio: {np.mean(label):.3f}")

    # Get sliding window size
    slidingWindow = find_length_rank(data, rank=1)

    # Parse train/test split from filename
    filename = os.path.basename(dataset_path)
    train_index = filename.split('.')[0].split('_')[-3]
    data_train = data[:int(train_index), :]

    print(f"  🔧 Training on {data_train.shape[0]} samples, testing on {data.shape[0]} samples")

    # Create model
    input_dim = data.shape[1] if len(data.shape) > 1 else 1

    if model_type == 'HTA_AD':
        model = HTA_AD_TSB(input_dim=input_dim, epochs=25)
    elif model_type == 'HTA_AD_SAE':
        model = HTA_AD_SAE_TSB(input_dim=input_dim, epochs=25)
    else:
        raise ValueError(f"Unknown model type: {model_type}")

    # Train model
    start_time = time.time()
    model.fit(data_train)
    training_time = time.time() - start_time

    # Get anomaly scores
    start_time = time.time()
    anomaly_scores = model.decision_function(data)
    inference_time = time.time() - start_time

    # Normalize scores to [0, 1]
    anomaly_scores = MinMaxScaler(feature_range=(0, 1)).fit_transform(
        anomaly_scores.reshape(-1, 1)
    ).ravel()

    # Evaluate using TSB-AD metrics
    pred = anomaly_scores > (np.mean(anomaly_scores) + 3 * np.std(anomaly_scores))
    evaluation_result = get_metrics(anomaly_scores, label, slidingWindow=slidingWindow, pred=pred)

    # Create visualization
    dataset_name = os.path.basename(dataset_path)
    viz_path = visualize_time_series_with_anomalies(
        data.flatten() if data.shape[1] == 1 else data[:, 0],  # Use first column for multivariate
        label,
        anomaly_scores,
        dataset_name
    )

    return {
        'dataset': dataset_name,
        'model_type': model_type,
        'evaluation_result': evaluation_result,
        'training_time': training_time,
        'inference_time': inference_time,
        'visualization_path': viz_path,
        'anomaly_scores': anomaly_scores,
        'labels': label
    }

def run_comprehensive_benchmark():
    """Run comprehensive benchmark on 10 TSB-AD datasets"""

    print("🚀 HTA-AD Comprehensive Benchmark on TSB-AD")
    print("=" * 60)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Select 10 diverse datasets
    test_datasets = [
        "TSB-AD/Datasets/TSB-AD-U/001_NAB_id_1_Facility_tr_1007_1st_2014.csv",
        "TSB-AD/Datasets/TSB-AD-U/002_NAB_id_2_WebService_tr_1500_1st_4106.csv",
        "TSB-AD/Datasets/TSB-AD-U/140_MSL_id_1_Sensor_tr_530_1st_630.csv",
        "TSB-AD/Datasets/TSB-AD-U/169_Daphnet_id_1_HumanActivity_tr_9693_1st_20732.csv",
        "TSB-AD/Datasets/TSB-AD-U/170_MITDB_id_1_Medical_tr_17675_1st_17775.csv",
        "TSB-AD/Datasets/TSB-AD-U/225_MGAB_id_1_Synthetic_tr_25000_1st_38478.csv",
        "TSB-AD/Datasets/TSB-AD-U/531_SMAP_id_1_Sensor_tr_1811_1st_4510.csv",
        "TSB-AD/Datasets/TSB-AD-U/551_YAHOO_id_1_Synthetic_tr_500_1st_893.csv",
        "TSB-AD/Datasets/TSB-AD-U/810_Exathlon_id_1_Facility_tr_10766_1st_12590.csv",
        "TSB-AD/Datasets/TSB-AD-U/842_OPPORTUNITY_id_1_HumanActivity_tr_7295_1st_27516.csv"
    ]

    results = []

    for i, dataset_path in enumerate(test_datasets):
        if not os.path.exists(dataset_path):
            print(f"❌ Dataset not found: {dataset_path}")
            continue

        dataset_name = os.path.basename(dataset_path)
        print(f"\n📊 [{i+1}/10] Processing: {dataset_name}")
        print("-" * 50)

        try:
            # Test both HTA-AD and HTA-AD+SAE
            for model_type in ['HTA_AD', 'HTA_AD_SAE']:
                print(f"  🔬 Testing {model_type}...")
                result = run_hta_ad_on_dataset(dataset_path, model_type)
                results.append(result)

                # Print results
                eval_result = result['evaluation_result']
                print(f"    ✅ {model_type} Results:")
                print(f"       - VUS-PR:  {eval_result.get('VUS-PR', 0):.4f}")
                print(f"       - VUS-ROC: {eval_result.get('VUS-ROC', 0):.4f}")
                print(f"       - F1:      {eval_result.get('Standard-F1', 0):.4f}")
                print(f"       - Training: {result['training_time']:.1f}s")
                print(f"       - Inference: {result['inference_time']:.1f}s")
                print(f"       - Visualization: {result['visualization_path']}")

        except Exception as e:
            print(f"  ❌ Error processing {dataset_name}: {e}")
            continue

    # Create summary
    if results:
        print("\n" + "=" * 60)
        print("📈 BENCHMARK SUMMARY")
        print("=" * 60)

        # Separate results by model type
        hta_ad_results = [r for r in results if r['model_type'] == 'HTA_AD']
        sae_results = [r for r in results if r['model_type'] == 'HTA_AD_SAE']

        print(f"\n📊 Results Summary ({len(hta_ad_results)} datasets):")
        print(f"{'Dataset':<40} {'HTA-AD VUS-PR':<15} {'SAE VUS-PR':<15} {'HTA-AD VUS-ROC':<15} {'SAE VUS-ROC':<15}")
        print("-" * 100)

        for i in range(len(hta_ad_results)):
            hta_result = hta_ad_results[i]
            sae_result = sae_results[i] if i < len(sae_results) else None

            dataset_short = hta_result['dataset'][:35] + "..." if len(hta_result['dataset']) > 35 else hta_result['dataset']
            hta_vus_pr = hta_result['evaluation_result'].get('VUS-PR', 0)
            hta_vus_roc = hta_result['evaluation_result'].get('VUS-ROC', 0)

            if sae_result:
                sae_vus_pr = sae_result['evaluation_result'].get('VUS-PR', 0)
                sae_vus_roc = sae_result['evaluation_result'].get('VUS-ROC', 0)
            else:
                sae_vus_pr = sae_vus_roc = 0

            print(f"{dataset_short:<40} {hta_vus_pr:<15.4f} {sae_vus_pr:<15.4f} {hta_vus_roc:<15.4f} {sae_vus_roc:<15.4f}")

        # Calculate averages
        if hta_ad_results:
            avg_hta_vus_pr = np.mean([r['evaluation_result'].get('VUS-PR', 0) for r in hta_ad_results])
            avg_hta_vus_roc = np.mean([r['evaluation_result'].get('VUS-ROC', 0) for r in hta_ad_results])
            avg_hta_f1 = np.mean([r['evaluation_result'].get('Standard-F1', 0) for r in hta_ad_results])

            print(f"\n📊 HTA-AD Average Performance:")
            print(f"   - VUS-PR:  {avg_hta_vus_pr:.4f}")
            print(f"   - VUS-ROC: {avg_hta_vus_roc:.4f}")
            print(f"   - F1:      {avg_hta_f1:.4f}")

        if sae_results:
            avg_sae_vus_pr = np.mean([r['evaluation_result'].get('VUS-PR', 0) for r in sae_results])
            avg_sae_vus_roc = np.mean([r['evaluation_result'].get('VUS-ROC', 0) for r in sae_results])
            avg_sae_f1 = np.mean([r['evaluation_result'].get('Standard-F1', 0) for r in sae_results])

            print(f"\n📊 HTA-AD+SAE Average Performance:")
            print(f"   - VUS-PR:  {avg_sae_vus_pr:.4f}")
            print(f"   - VUS-ROC: {avg_sae_vus_roc:.4f}")
            print(f"   - F1:      {avg_sae_f1:.4f}")

            # Calculate improvement
            if hta_ad_results:
                vus_pr_improvement = ((avg_sae_vus_pr - avg_hta_vus_pr) / (avg_hta_vus_pr + 1e-8)) * 100
                vus_roc_improvement = ((avg_sae_vus_roc - avg_hta_vus_roc) / (avg_hta_vus_roc + 1e-8)) * 100

                print(f"\n🚀 SAE Improvements:")
                print(f"   - VUS-PR:  {vus_pr_improvement:+.1f}%")
                print(f"   - VUS-ROC: {vus_roc_improvement:+.1f}%")

        # Save results
        results_df = pd.DataFrame([
            {
                'dataset': r['dataset'],
                'model_type': r['model_type'],
                'vus_pr': r['evaluation_result'].get('VUS-PR', 0),
                'vus_roc': r['evaluation_result'].get('VUS-ROC', 0),
                'f1': r['evaluation_result'].get('Standard-F1', 0),
                'training_time': r['training_time'],
                'inference_time': r['inference_time']
            } for r in results
        ])

        os.makedirs('results', exist_ok=True)
        results_df.to_csv('results/hta_ad_tsb_benchmark_results.csv', index=False)
        print(f"\n💾 Results saved to results/hta_ad_tsb_benchmark_results.csv")
        print(f"📊 Visualizations saved to results/visualizations/")

        print(f"\n🎉 Benchmark completed successfully!")
        print(f"   - Processed {len(results)} model-dataset combinations")
        print(f"   - Average SAE VUS-PR: {avg_sae_vus_pr:.4f}" if sae_results else "")
        print(f"   - Average SAE VUS-ROC: {avg_sae_vus_roc:.4f}" if sae_results else "")

    else:
        print("\n❌ No datasets were successfully processed.")

if __name__ == "__main__":
    run_comprehensive_benchmark()
