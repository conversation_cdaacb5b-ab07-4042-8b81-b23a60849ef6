#!/usr/bin/env python3
"""
可视化训练结果，检查模型是否训练充分
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import time
import warnings
warnings.filterwarnings('ignore')

# 添加TSB-AD路径
sys.path.append('TSB-AD')
sys.path.append('TSB-AD/TSB_AD')

def generate_test_data_with_labels(n_samples=1000):
    """生成带标签的测试数据"""
    np.random.seed(42)
    
    # 生成正常数据 (多个周期的正弦波)
    t = np.linspace(0, 8*np.pi, n_samples)
    data = np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.randn(n_samples)
    
    # 创建标签数组
    labels = np.zeros(n_samples)
    anomaly_indices = []
    
    # 添加点异常
    for _ in range(20):
        idx = np.random.randint(100, n_samples-100)
        data[idx] += np.random.normal(3, 0.5)
        labels[idx] = 1
        anomaly_indices.append(idx)
    
    # 添加集体异常
    for _ in range(5):
        start_idx = np.random.randint(100, n_samples-150)
        end_idx = start_idx + np.random.randint(20, 50)
        data[start_idx:end_idx] += np.random.normal(1.5, 0.3, end_idx-start_idx)
        labels[start_idx:end_idx] = 1
        anomaly_indices.extend(range(start_idx, end_idx))
    
    return data, labels, anomaly_indices


def train_and_visualize_models():
    """训练模型并可视化结果"""
    print("🎨 训练模型并生成可视化")
    print("=" * 60)
    
    # 生成数据
    data, labels, anomaly_indices = generate_test_data_with_labels(1000)
    
    # 分割数据
    split_point = int(len(data) * 0.7)
    train_data = data[:split_point]
    test_data = data[split_point:]
    test_labels = labels[split_point:]
    
    print(f"📊 数据信息:")
    print(f"  - 训练数据: {len(train_data)} 点")
    print(f"  - 测试数据: {len(test_data)} 点")
    print(f"  - 测试异常: {np.sum(test_labels)} 点")
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('HTA-AD 模型训练和检测结果可视化', fontsize=16)
    
    # 1. 原始数据可视化
    ax1 = axes[0, 0]
    ax1.plot(data, 'b-', alpha=0.7, label='正常数据')
    ax1.scatter(anomaly_indices, data[anomaly_indices], c='red', s=20, label='异常点', alpha=0.8)
    ax1.axvline(x=split_point, color='green', linestyle='--', alpha=0.7, label='训练/测试分割')
    ax1.set_title('原始时间序列数据')
    ax1.set_xlabel('时间步')
    ax1.set_ylabel('值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 测试模型
    models_results = {}
    
    # 2. 测试基础HTA-AD
    print("\n🧪 训练基础HTA-AD...")
    try:
        from TSB_AD.models.HTA_AD import HTA_AD
        
        HP = {
            'window_size': 64,
            'epochs': 15,  # 增加epochs来更好地训练
            'lr': 1e-3,
            'batch_size': 32,
            'latent_dim': 16,
            'tcn_channels': [32, 32, 32],
            'cnn_channels': 16,
            'downsample_stride': 2,
            'gpu': 0
        }
        
        # 确保数据是2D
        train_data_2d = train_data.reshape(-1, 1)
        test_data_2d = test_data.reshape(-1, 1)
        
        model_basic = HTA_AD(HP=HP, normalize=True)
        start_time = time.time()
        model_basic.fit(train_data_2d)
        basic_train_time = time.time() - start_time
        
        scores_basic = model_basic.decision_function(test_data_2d)
        
        models_results['HTA-AD'] = {
            'scores': scores_basic,
            'train_time': basic_train_time,
            'history': getattr(model_basic, 'training_history', {}),
            'model': model_basic
        }
        
        print(f"  ✅ 基础HTA-AD完成 (时间: {basic_train_time:.2f}s)")
        
    except Exception as e:
        print(f"  ❌ 基础HTA-AD失败: {e}")
        models_results['HTA-AD'] = {'error': str(e)}
    
    # 3. 测试HTA-AD-SAE
    print("\n🧪 训练HTA-AD-SAE...")
    try:
        from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE
        
        HP_sae = {
            'window_size': 64,
            'epochs': 15,  # 增加epochs
            'lr': 1e-3,
            'batch_size': 32,
            'latent_dim': 16,
            'tcn_channels': [32, 32, 32],
            'cnn_channels': 16,
            'downsample_stride': 2,
            'gpu': 0
        }
        
        sae_config = {
            'hidden_dim': 64,
            'sparsity_weight': 0.01
        }
        
        model_sae = HTA_AD_SAE(HP=HP_sae, normalize=True, sae_config=sae_config)
        start_time = time.time()
        model_sae.fit(train_data_2d)
        sae_train_time = time.time() - start_time
        
        scores_sae = model_sae.decision_function(test_data_2d)
        
        models_results['HTA-AD-SAE'] = {
            'scores': scores_sae,
            'train_time': sae_train_time,
            'history': getattr(model_sae, 'training_history', {}),
            'model': model_sae
        }
        
        print(f"  ✅ HTA-AD-SAE完成 (时间: {sae_train_time:.2f}s)")
        
    except Exception as e:
        print(f"  ❌ HTA-AD-SAE失败: {e}")
        import traceback
        traceback.print_exc()
        models_results['HTA-AD-SAE'] = {'error': str(e)}
    
    # 4. 可视化训练历史
    ax2 = axes[0, 1]
    for model_name, result in models_results.items():
        if 'error' not in result and 'history' in result:
            history = result['history']
            if 'loss' in history:
                ax2.plot(history['loss'], label=f'{model_name} Loss', marker='o', markersize=3)
    ax2.set_title('训练损失曲线')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_yscale('log')
    
    # 5. 异常分数分布
    ax3 = axes[0, 2]
    for model_name, result in models_results.items():
        if 'error' not in result:
            scores = result['scores']
            ax3.hist(scores, bins=30, alpha=0.6, label=f'{model_name}', density=True)
    ax3.set_title('异常分数分布')
    ax3.set_xlabel('异常分数')
    ax3.set_ylabel('密度')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 6. 检测结果可视化 - HTA-AD
    ax4 = axes[1, 0]
    if 'HTA-AD' in models_results and 'error' not in models_results['HTA-AD']:
        scores = models_results['HTA-AD']['scores']
        ax4.plot(test_data, 'b-', alpha=0.7, label='测试数据')
        ax4.scatter(np.where(test_labels == 1)[0], test_data[test_labels == 1], 
                   c='red', s=30, label='真实异常', alpha=0.8)
        
        # 显示异常分数（归一化到数据范围）
        scores_norm = (scores - scores.min()) / (scores.max() - scores.min()) * (test_data.max() - test_data.min()) + test_data.min()
        ax4.plot(scores_norm, 'orange', alpha=0.8, label='异常分数', linewidth=2)
        
        ax4.set_title('HTA-AD 检测结果')
        ax4.set_xlabel('时间步')
        ax4.set_ylabel('值')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
    else:
        ax4.text(0.5, 0.5, 'HTA-AD 训练失败', ha='center', va='center', transform=ax4.transAxes)
        ax4.set_title('HTA-AD 检测结果')
    
    # 7. 检测结果可视化 - HTA-AD-SAE
    ax5 = axes[1, 1]
    if 'HTA-AD-SAE' in models_results and 'error' not in models_results['HTA-AD-SAE']:
        scores = models_results['HTA-AD-SAE']['scores']
        ax5.plot(test_data, 'b-', alpha=0.7, label='测试数据')
        ax5.scatter(np.where(test_labels == 1)[0], test_data[test_labels == 1], 
                   c='red', s=30, label='真实异常', alpha=0.8)
        
        # 显示异常分数（归一化到数据范围）
        scores_norm = (scores - scores.min()) / (scores.max() - scores.min()) * (test_data.max() - test_data.min()) + test_data.min()
        ax5.plot(scores_norm, 'green', alpha=0.8, label='异常分数', linewidth=2)
        
        ax5.set_title('HTA-AD-SAE 检测结果')
        ax5.set_xlabel('时间步')
        ax5.set_ylabel('值')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
    else:
        ax5.text(0.5, 0.5, 'HTA-AD-SAE 训练失败', ha='center', va='center', transform=ax5.transAxes)
        ax5.set_title('HTA-AD-SAE 检测结果')
    
    # 8. 性能对比
    ax6 = axes[1, 2]
    model_names = []
    train_times = []
    auc_scores = []
    
    for model_name, result in models_results.items():
        if 'error' not in result:
            model_names.append(model_name)
            train_times.append(result['train_time'])
            
            # 计算AUC
            try:
                from sklearn.metrics import roc_auc_score
                auc = roc_auc_score(test_labels, result['scores'])
                auc_scores.append(auc)
            except:
                auc_scores.append(0.5)
    
    if model_names:
        x = np.arange(len(model_names))
        width = 0.35
        
        ax6_twin = ax6.twinx()
        bars1 = ax6.bar(x - width/2, train_times, width, label='训练时间(s)', alpha=0.7, color='skyblue')
        bars2 = ax6_twin.bar(x + width/2, auc_scores, width, label='AUC', alpha=0.7, color='lightcoral')
        
        ax6.set_xlabel('模型')
        ax6.set_ylabel('训练时间 (秒)', color='blue')
        ax6_twin.set_ylabel('AUC', color='red')
        ax6.set_title('性能对比')
        ax6.set_xticks(x)
        ax6.set_xticklabels(model_names, rotation=45)
        
        # 添加数值标签
        for bar, time_val in zip(bars1, train_times):
            ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                    f'{time_val:.1f}s', ha='center', va='bottom')
        
        for bar, auc_val in zip(bars2, auc_scores):
            ax6_twin.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                         f'{auc_val:.3f}', ha='center', va='bottom')
        
        ax6.legend(loc='upper left')
        ax6_twin.legend(loc='upper right')
    
    plt.tight_layout()
    
    # 保存图片
    import os
    os.makedirs('results/visualizations', exist_ok=True)
    plt.savefig('results/visualizations/training_analysis.png', dpi=300, bbox_inches='tight')
    print(f"\n💾 可视化结果已保存到: results/visualizations/training_analysis.png")
    
    plt.show()
    
    return models_results


def print_detailed_analysis(results):
    """打印详细分析"""
    print("\n" + "=" * 60)
    print("🔍 详细训练分析")
    print("=" * 60)
    
    for model_name, result in results.items():
        print(f"\n📊 {model_name}:")
        if 'error' in result:
            print(f"  ❌ 训练失败: {result['error']}")
        else:
            print(f"  ⏱️ 训练时间: {result['train_time']:.2f}s")
            print(f"  📈 异常分数统计:")
            scores = result['scores']
            print(f"    - 平均值: {np.mean(scores):.4f}")
            print(f"    - 标准差: {np.std(scores):.4f}")
            print(f"    - 最小值: {np.min(scores):.4f}")
            print(f"    - 最大值: {np.max(scores):.4f}")
            
            # 检查训练历史
            if 'history' in result and result['history']:
                history = result['history']
                if 'loss' in history:
                    losses = history['loss']
                    print(f"  📉 训练历史:")
                    print(f"    - 初始损失: {losses[0]:.4f}")
                    print(f"    - 最终损失: {losses[-1]:.4f}")
                    print(f"    - 损失下降: {((losses[0] - losses[-1]) / losses[0] * 100):.1f}%")
                    print(f"    - 训练轮数: {len(losses)}")
                    
                    # 检查是否收敛
                    if len(losses) > 5:
                        recent_losses = losses[-5:]
                        loss_std = np.std(recent_losses)
                        if loss_std < 0.001:
                            print(f"    ✅ 模型已收敛 (最后5轮损失标准差: {loss_std:.6f})")
                        else:
                            print(f"    ⚠️ 模型可能未完全收敛 (最后5轮损失标准差: {loss_std:.6f})")


def main():
    """主函数"""
    print("🎨 HTA-AD 训练结果可视化分析")
    print("=" * 60)
    
    try:
        # 训练并可视化
        results = train_and_visualize_models()
        
        # 详细分析
        print_detailed_analysis(results)
        
        print("\n" + "=" * 60)
        print("📝 总结")
        print("=" * 60)
        
        valid_results = {k: v for k, v in results.items() if 'error' not in v}
        
        if len(valid_results) >= 2:
            basic_time = valid_results.get('HTA-AD', {}).get('train_time', 0)
            sae_time = valid_results.get('HTA-AD-SAE', {}).get('train_time', 0)
            
            if basic_time > 0 and sae_time > 0:
                print(f"⏱️ 训练时间对比:")
                print(f"  HTA-AD: {basic_time:.2f}s")
                print(f"  HTA-AD-SAE: {sae_time:.2f}s")
                print(f"  比例: {sae_time/basic_time:.2f}x")
                
                if sae_time < basic_time:
                    print("💡 SAE版本训练更快的可能原因:")
                    print("  1. 核心模型实现更高效")
                    print("  2. SAE帮助更快收敛")
                    print("  3. 批处理优化")
        
        print("🎨 请查看生成的可视化图表来详细分析训练效果！")
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
