#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTA-AD with Sparse Autoencoder (SAE) Integration
Interpretable Time Series Anomaly Detection with Feature Attribution
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
import os
import pickle
from .HTA_AD import HTA_Model, HTA_AD

class SparseAutoencoder(nn.Module):
    """Sparse Autoencoder for interpretable feature learning"""
    
    def __init__(self, input_dim=32, hidden_dim=128, sparsity_weight=0.01):
        super(SparseAutoencoder, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.sparsity_weight = sparsity_weight
        
        # Encoder: latent -> sparse features
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU()
        )
        
        # Decoder: sparse features -> latent
        self.decoder = nn.Linear(hidden_dim, input_dim)
        
    def forward(self, x):
        """Forward pass through SAE"""
        features = self.encoder(x)  # Sparse feature activations
        reconstruction = self.decoder(features)
        return reconstruction, features
    
    def loss_function(self, x, reconstruction, features):
        """SAE loss with reconstruction + sparsity regularization"""
        # Reconstruction loss
        recon_loss = nn.MSELoss()(reconstruction, x)
        # Sparsity loss (L1 regularization)
        sparsity_loss = torch.mean(torch.abs(features))
        return recon_loss + self.sparsity_weight * sparsity_loss

class HTA_AD_SAE(HTA_AD):
    """HTA-AD enhanced with Sparse Autoencoder for interpretability"""
    
    def __init__(self, HP, normalize=True, sae_config=None, pretrained_sae_path=None):
        super().__init__(HP, normalize)
        
        # SAE configuration
        self.sae_config = sae_config or {
            'hidden_dim': 128,
            'sparsity_weight': 0.01,
            'purification_strength': 0.5
        }
        
        # SAE components
        self.sae = None
        self.sae_scaler = None
        self.irrelevant_indices = []
        self.pretrained_sae_path = pretrained_sae_path
        
        # Load pretrained SAE if available
        if pretrained_sae_path and os.path.exists(pretrained_sae_path):
            self._load_pretrained_sae(pretrained_sae_path)
            print(f"✅ Loaded pretrained SAE from {pretrained_sae_path}")
    
    def _load_pretrained_sae(self, path):
        """Load pretrained SAE model and scaler"""
        try:
            checkpoint = torch.load(path, map_location=self.device)
            
            # Initialize SAE with correct dimensions
            latent_dim = checkpoint.get('input_dim', self.latent_dim)
            hidden_dim = checkpoint.get('hidden_dim', self.sae_config['hidden_dim'])
            
            self.sae = SparseAutoencoder(
                input_dim=latent_dim,
                hidden_dim=hidden_dim,
                sparsity_weight=self.sae_config['sparsity_weight']
            ).to(self.device)
            
            self.sae.load_state_dict(checkpoint['model_state_dict'])
            self.sae.eval()
            
            # Load scaler and irrelevant indices
            if 'scaler' in checkpoint:
                self.sae_scaler = checkpoint['scaler']
            if 'irrelevant_indices' in checkpoint:
                self.irrelevant_indices = checkpoint['irrelevant_indices']
                
        except Exception as e:
            print(f"⚠️ Failed to load pretrained SAE: {e}")
            self.sae = None
    
    def _purify_latent_vector(self, latent_vec):
        """Purify latent vector using SAE by removing irrelevant features"""
        if self.sae is None or len(self.irrelevant_indices) == 0:
            return latent_vec
        
        with torch.no_grad():
            # Normalize latent vector if scaler is available
            if self.sae_scaler is not None:
                latent_np = latent_vec.cpu().numpy()
                latent_normalized = self.sae_scaler.transform(latent_np)
                latent_normalized = torch.FloatTensor(latent_normalized).to(self.device)
            else:
                latent_normalized = latent_vec
            
            # Get SAE feature activations
            _, activations = self.sae(latent_normalized)
            
            # Create mask for irrelevant features
            irrelevant_mask = torch.zeros_like(activations)
            irrelevant_mask[:, self.irrelevant_indices] = 1.0
            
            # Extract irrelevant feature activations
            irrelevant_activations = activations * irrelevant_mask
            
            # Compute irrelevant contribution to reconstruction
            irrelevant_contribution = self.sae.decoder(irrelevant_activations)
            
            # Denormalize if needed
            if self.sae_scaler is not None:
                irrelevant_np = irrelevant_contribution.cpu().numpy()
                irrelevant_denorm = self.sae_scaler.inverse_transform(irrelevant_np)
                irrelevant_contribution = torch.FloatTensor(irrelevant_denorm).to(self.device)
            
            # Purify by subtracting irrelevant contribution
            strength = self.sae_config['purification_strength']
            purified_latent = latent_vec - strength * irrelevant_contribution
            
        return purified_latent
    
    def _compute_scores(self, X, fit_scaler=False):
        """Compute anomaly scores with optional SAE purification"""
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: 
            return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                if self.sae is not None:
                    # Use SAE-enhanced forward pass
                    x_permuted = batch_windows.permute(0, 2, 1)
                    
                    # Encode to latent space
                    encoded_cnn = self.model.encoder_cnn(x_permuted)
                    encoded_tcn = self.model.encoder_tcn(encoded_cnn)
                    encoded_flat = encoded_tcn.flatten(start_dim=1)
                    latent_vec = self.model.fc_encode(encoded_flat)
                    
                    # Purify latent vector
                    purified_latent = self._purify_latent_vector(latent_vec)
                    
                    # Decode from purified latent
                    decoded_flat = self.model.decoder_fc(purified_latent)
                    decoded_unflat = decoded_flat.view(-1, self.model.tcn_output_channels, self.model.downsampled_len)
                    decoded_tcn = self.model.decoder_tcn(decoded_unflat)
                    reconstructed_permuted = self.model.decoder_cnn(decoded_tcn)
                    reconstructed = self.model.output_activation(reconstructed_permuted)
                    reconstructed = reconstructed.permute(0, 2, 1)
                else:
                    # Standard HTA-AD forward pass
                    reconstructed = self.model(batch_windows)
                
                # Compute reconstruction errors
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())

        # Convert window scores to point scores
        point_scores = self._window_scores_to_point_scores(window_scores, n_samples)
        return point_scores
    
    def get_feature_attribution(self, X, top_k=10):
        """Get feature attribution for anomaly explanation"""
        if self.sae is None:
            print("⚠️ SAE not available for feature attribution")
            return None
        
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0:
            return None

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        self.sae.eval()
        
        all_attributions = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                # Get latent vectors
                x_permuted = batch_windows.permute(0, 2, 1)
                encoded_cnn = self.model.encoder_cnn(x_permuted)
                encoded_tcn = self.model.encoder_tcn(encoded_cnn)
                encoded_flat = encoded_tcn.flatten(start_dim=1)
                latent_vec = self.model.fc_encode(encoded_flat)
                
                # Get SAE feature activations
                if self.sae_scaler is not None:
                    latent_np = latent_vec.cpu().numpy()
                    latent_normalized = self.sae_scaler.transform(latent_np)
                    latent_normalized = torch.FloatTensor(latent_normalized).to(self.device)
                else:
                    latent_normalized = latent_vec
                
                _, activations = self.sae(latent_normalized)
                all_attributions.append(activations.cpu().numpy())
        
        # Combine all attributions
        attributions = np.vstack(all_attributions)
        
        # Get top-k most activated features for each window
        top_features = []
        for i in range(len(attributions)):
            top_indices = np.argsort(attributions[i])[-top_k:][::-1]
            top_values = attributions[i][top_indices]
            top_features.append({
                'indices': top_indices,
                'values': top_values,
                'window_idx': i
            })
        
        return {
            'attributions': attributions,
            'top_features': top_features,
            'feature_dim': attributions.shape[1]
        }
    
    def save_sae_model(self, path):
        """Save the trained SAE model"""
        if self.sae is None:
            print("⚠️ No SAE model to save")
            return
        
        checkpoint = {
            'model_state_dict': self.sae.state_dict(),
            'input_dim': self.sae.input_dim,
            'hidden_dim': self.sae.hidden_dim,
            'sparsity_weight': self.sae.sparsity_weight,
            'scaler': self.sae_scaler,
            'irrelevant_indices': self.irrelevant_indices,
            'sae_config': self.sae_config
        }
        
        torch.save(checkpoint, path)
        print(f"✅ SAE model saved to {path}")
