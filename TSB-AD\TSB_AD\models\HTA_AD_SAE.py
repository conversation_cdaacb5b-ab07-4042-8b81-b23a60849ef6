#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTA-AD with Sparse Autoencoder (SAE) Integration
Interpretable Time Series Anomaly Detection with Feature Attribution
Updated to use the integrated HTA-AD core models
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
import os
import sys
import pickle
from .HTA_AD import HTA_Model, HTA_AD
from .base import BaseDetector

# Add path to core models
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'core'))
try:
    from models.hta_ad_integrated import HTAADComplete, HTAADTrainer
    from sae_integration.sparse_autoencoder import SparseAutoencoder as CoreSAE
    CORE_AVAILABLE = True
except ImportError:
    print("Warning: Core HTA-AD models not available, using fallback implementation")
    CORE_AVAILABLE = False

class SparseAutoencoder(nn.Module):
    """Sparse Autoencoder for interpretable feature learning"""
    
    def __init__(self, input_dim=32, hidden_dim=128, sparsity_weight=0.01):
        super(SparseAutoencoder, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.sparsity_weight = sparsity_weight
        
        # Encoder: latent -> sparse features
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU()
        )
        
        # Decoder: sparse features -> latent
        self.decoder = nn.Linear(hidden_dim, input_dim)
        
    def forward(self, x):
        """Forward pass through SAE"""
        features = self.encoder(x)  # Sparse feature activations
        reconstruction = self.decoder(features)
        return reconstruction, features
    
    def loss_function(self, x, reconstruction, features):
        """SAE loss with reconstruction + sparsity regularization"""
        # Reconstruction loss
        recon_loss = nn.MSELoss()(reconstruction, x)
        # Sparsity loss (L1 regularization)
        sparsity_loss = torch.mean(torch.abs(features))
        return recon_loss + self.sparsity_weight * sparsity_loss

class HTA_AD_SAE(BaseDetector):
    """HTA-AD enhanced with Sparse Autoencoder for interpretability"""

    def __init__(self, HP, normalize=True, sae_config=None, pretrained_sae_path=None):
        super().__init__()

        # Store hyperparameters
        self.HP = HP
        self.normalize = normalize
        self.sae_config = sae_config or {}
        self.pretrained_sae_path = pretrained_sae_path

        # Initialize components
        self.device = torch.device(f'cuda:{HP.get("gpu", 0)}' if torch.cuda.is_available() else 'cpu')
        self.ts_scaler = MinMaxScaler() if normalize else None

        # Model parameters
        self.window_size = HP.get('window_size', 128)
        self.epochs = HP.get('epochs', 30)
        self.lr = HP.get('lr', 1e-3)
        self.batch_size = HP.get('batch_size', 64)
        self.latent_dim = HP.get('latent_dim', 32)

        # SAE parameters
        self.sae_hidden_dim = sae_config.get('hidden_dim', 128)
        self.sae_sparsity_weight = sae_config.get('sparsity_weight', 0.01)

        # Initialize models
        self.model = None
        self.trainer = None
        self.core_model = None

        # Try to use core models if available
        if CORE_AVAILABLE:
            self._init_core_model()
        else:
            self._init_fallback_model()

    def _init_core_model(self):
        """Initialize using core HTA-AD models"""
        try:
            self.core_model = HTAADComplete(
                input_dim=1,
                d_model=self.latent_dim,
                n_heads=4,
                n_layers=2,
                seq_len=self.window_size,
                sae_hidden_dim=self.sae_hidden_dim,
                sae_sparsity_weight=self.sae_sparsity_weight,
                enable_sae=True
            ).to(self.device)

            self.trainer = HTAADTrainer(self.core_model, device=str(self.device))
            self.use_core = True
            print("✅ Using core HTA-AD models")

        except Exception as e:
            print(f"⚠️ Failed to initialize core models: {e}")
            self._init_fallback_model()

    def _init_fallback_model(self):
        """Initialize using fallback implementation"""
        # Initialize HTA_AD base class attributes
        self.model = None  # Will be initialized in fit()
        self.criterion = torch.nn.MSELoss()

        # Initialize SAE
        self.sae = SparseAutoencoder(
            input_dim=self.latent_dim,
            hidden_dim=self.sae_hidden_dim,
            sparsity_weight=self.sae_sparsity_weight
        ).to(self.device)

        self.sae_scaler = MinMaxScaler() if self.normalize else None
        self.use_core = False
        self.decision_scores_ = None
        print("⚠️ Using fallback HTA-AD implementation")

    def fit(self, X, y=None):
        """Fit the HTA-AD-SAE model"""
        if self.use_core:
            return self._fit_core_model(X, y)
        else:
            return self._fit_fallback_model(X, y)

    def _fit_core_model(self, X, y=None):
        """Fit using core models"""
        try:
            # Normalize data
            if self.normalize:
                X_norm = self.ts_scaler.fit_transform(X.reshape(-1, 1)).flatten()
            else:
                X_norm = X

            # Create sequences
            sequences = self._create_sequences(X_norm)
            if len(sequences) == 0:
                raise ValueError("No sequences created from input data")

            # Create data loader
            dataset = TensorDataset(torch.FloatTensor(sequences))
            train_loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

            # Train model
            history = self.trainer.train(
                train_loader=train_loader,
                epochs=self.epochs,
                lr=self.lr
            )

            print(f"✅ Core model training completed. Final loss: {history['train_loss'][-1]:.4f}")
            return self

        except Exception as e:
            print(f"❌ Core model training failed: {e}")
            # Fallback to original implementation
            self.use_core = False
            return self._fit_fallback_model(X, y)

    def _fit_fallback_model(self, X, y=None):
        """Fit using fallback implementation"""
        # Ensure X is 2D
        if X.ndim == 1:
            X = X.reshape(-1, 1)

        input_dim = X.shape[1]

        # Initialize model if not already done
        if self.model is None:
            self.model = HTA_Model(
                input_dim=input_dim,
                window_size=self.window_size,
                latent_dim=self.latent_dim,
                tcn_channels=self.HP.get('tcn_channels', [32, 32, 32]),
                cnn_channels=self.HP.get('cnn_channels', 16),
                downsample_stride=self.HP.get('downsample_stride', 2)
            ).to(self.device)

        X_original_for_scoring = X
        if self.normalize:
            X = self.ts_scaler.fit_transform(X)

        windows = self._create_windows_fallback(X)
        if len(windows) == 0:
            self.decision_scores_ = np.zeros(X_original_for_scoring.shape[0])
            return self

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        optimizer = torch.optim.AdamW(self.model.parameters(), lr=self.lr, weight_decay=1e-5)

        self.model.train()
        epoch_losses = []
        for epoch in range(self.epochs):
            batch_losses = []
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                optimizer.zero_grad()
                reconstructed = self.model(batch_windows)
                loss = self.criterion(reconstructed, batch_windows)
                loss.backward()
                optimizer.step()
                batch_losses.append(loss.item())

            epoch_loss = np.mean(batch_losses) if batch_losses else 0
            epoch_losses.append(epoch_loss)

        self.training_history = {'loss': epoch_losses}
        self.decision_scores_ = self._compute_scores_fallback(X_original_for_scoring, fit_scaler=True)
        return self

    def _create_sequences(self, data):
        """Create sequences for training"""
        sequences = []
        for i in range(len(data) - self.window_size + 1):
            seq = data[i:i + self.window_size]
            sequences.append(seq.reshape(-1, 1))  # Add feature dimension
        return np.array(sequences)

    def decision_function(self, X):
        """Compute anomaly scores"""
        if self.use_core:
            return self._decision_function_core(X)
        else:
            return self._decision_function_fallback(X)

    def _decision_function_core(self, X):
        """Compute anomaly scores using core models"""
        try:
            # Normalize data
            if self.normalize:
                X_norm = self.ts_scaler.transform(X.reshape(-1, 1)).flatten()
            else:
                X_norm = X

            # Create sequences
            sequences = self._create_sequences(X_norm)
            if len(sequences) == 0:
                return np.zeros(len(X))

            # Detect anomalies
            sequences_tensor = torch.FloatTensor(sequences).to(self.device)
            results = self.core_model.detect_anomalies(sequences_tensor)

            # Convert sequence-level scores to point-level scores
            seq_scores = results['anomaly_scores'].cpu().numpy()
            point_scores = self._sequence_scores_to_point_scores(seq_scores, len(X))

            return point_scores

        except Exception as e:
            print(f"❌ Core model inference failed: {e}")
            # Fallback to original implementation
            return self._decision_function_fallback(X)

    def _decision_function_fallback(self, X):
        """Compute anomaly scores using fallback implementation"""
        return self._compute_scores_fallback(X, fit_scaler=False)

    def _create_windows_fallback(self, X):
        """Create sliding windows for fallback implementation"""
        if X.shape[0] < self.window_size:
            return np.empty((0, self.window_size, X.shape[1]))
        shape = (X.shape[0] - self.window_size + 1, self.window_size, X.shape[1])
        strides = (X.strides[0], X.strides[0], X.strides[1])
        return np.lib.stride_tricks.as_strided(X, shape=shape, strides=strides)

    def _compute_scores_fallback(self, X, fit_scaler=False):
        """Compute anomaly scores for fallback implementation"""
        if X.ndim == 1:
            X = X.reshape(-1, 1)

        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows_fallback(X_norm)
        if len(windows) == 0:
            return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)

        self.model.eval()
        window_scores = []

        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                reconstructed = self.model(batch_windows)
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())

        # Convert window scores to point scores
        point_scores = self._window_scores_to_point_scores_fallback(window_scores, n_samples)
        return point_scores

    def _window_scores_to_point_scores_fallback(self, window_scores, n_samples):
        """Convert window scores to point scores for fallback implementation"""
        point_scores = np.zeros(n_samples)

        for i, score in enumerate(window_scores):
            start_idx = i
            end_idx = min(i + self.window_size, n_samples)
            point_scores[start_idx:end_idx] = np.maximum(
                point_scores[start_idx:end_idx], score
            )

        return point_scores

    def _sequence_scores_to_point_scores(self, seq_scores, n_points):
        """Convert sequence-level scores to point-level scores"""
        point_scores = np.zeros(n_points)

        for i, score in enumerate(seq_scores):
            start_idx = i
            end_idx = min(i + self.window_size, n_points)
            point_scores[start_idx:end_idx] = np.maximum(
                point_scores[start_idx:end_idx], score
            )

        return point_scores

    def _load_pretrained_sae(self, path):
        """Load pretrained SAE model and scaler"""
        try:
            checkpoint = torch.load(path, map_location=self.device)
            
            # Initialize SAE with correct dimensions
            latent_dim = checkpoint.get('input_dim', self.latent_dim)
            hidden_dim = checkpoint.get('hidden_dim', self.sae_config['hidden_dim'])
            
            self.sae = SparseAutoencoder(
                input_dim=latent_dim,
                hidden_dim=hidden_dim,
                sparsity_weight=self.sae_config['sparsity_weight']
            ).to(self.device)
            
            self.sae.load_state_dict(checkpoint['model_state_dict'])
            self.sae.eval()
            
            # Load scaler and irrelevant indices
            if 'scaler' in checkpoint:
                self.sae_scaler = checkpoint['scaler']
            if 'irrelevant_indices' in checkpoint:
                self.irrelevant_indices = checkpoint['irrelevant_indices']
                
        except Exception as e:
            print(f"⚠️ Failed to load pretrained SAE: {e}")
            self.sae = None
    
    def _purify_latent_vector(self, latent_vec):
        """Purify latent vector using SAE by removing irrelevant features"""
        if self.sae is None or len(self.irrelevant_indices) == 0:
            return latent_vec
        
        with torch.no_grad():
            # Normalize latent vector if scaler is available
            if self.sae_scaler is not None:
                latent_np = latent_vec.cpu().numpy()
                latent_normalized = self.sae_scaler.transform(latent_np)
                latent_normalized = torch.FloatTensor(latent_normalized).to(self.device)
            else:
                latent_normalized = latent_vec
            
            # Get SAE feature activations
            _, activations = self.sae(latent_normalized)
            
            # Create mask for irrelevant features
            irrelevant_mask = torch.zeros_like(activations)
            irrelevant_mask[:, self.irrelevant_indices] = 1.0
            
            # Extract irrelevant feature activations
            irrelevant_activations = activations * irrelevant_mask
            
            # Compute irrelevant contribution to reconstruction
            irrelevant_contribution = self.sae.decoder(irrelevant_activations)
            
            # Denormalize if needed
            if self.sae_scaler is not None:
                irrelevant_np = irrelevant_contribution.cpu().numpy()
                irrelevant_denorm = self.sae_scaler.inverse_transform(irrelevant_np)
                irrelevant_contribution = torch.FloatTensor(irrelevant_denorm).to(self.device)
            
            # Purify by subtracting irrelevant contribution
            strength = self.sae_config['purification_strength']
            purified_latent = latent_vec - strength * irrelevant_contribution
            
        return purified_latent
    
    def _compute_scores(self, X, fit_scaler=False):
        """Compute anomaly scores with optional SAE purification"""
        n_samples = X.shape[0]
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0: 
            return np.zeros(n_samples)

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        window_scores = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                if self.sae is not None:
                    # Use SAE-enhanced forward pass
                    x_permuted = batch_windows.permute(0, 2, 1)
                    
                    # Encode to latent space
                    encoded_cnn = self.model.encoder_cnn(x_permuted)
                    encoded_tcn = self.model.encoder_tcn(encoded_cnn)
                    encoded_flat = encoded_tcn.flatten(start_dim=1)
                    latent_vec = self.model.fc_encode(encoded_flat)
                    
                    # Purify latent vector
                    purified_latent = self._purify_latent_vector(latent_vec)
                    
                    # Decode from purified latent
                    decoded_flat = self.model.decoder_fc(purified_latent)
                    decoded_unflat = decoded_flat.view(-1, self.model.tcn_output_channels, self.model.downsampled_len)
                    decoded_tcn = self.model.decoder_tcn(decoded_unflat)
                    reconstructed_permuted = self.model.decoder_cnn(decoded_tcn)
                    reconstructed = self.model.output_activation(reconstructed_permuted)
                    reconstructed = reconstructed.permute(0, 2, 1)
                else:
                    # Standard HTA-AD forward pass
                    reconstructed = self.model(batch_windows)
                
                # Compute reconstruction errors
                errors = torch.mean((reconstructed - batch_windows) ** 2, dim=(1, 2))
                window_scores.extend(errors.cpu().numpy())

        # Convert window scores to point scores
        point_scores = self._window_scores_to_point_scores(window_scores, n_samples)
        return point_scores
    
    def get_feature_attribution(self, X, top_k=10):
        """Get feature attribution for anomaly explanation"""
        if self.sae is None:
            print("⚠️ SAE not available for feature attribution")
            return None
        
        if self.normalize:
            X_norm = self.ts_scaler.transform(X)
        else:
            X_norm = X

        windows = self._create_windows(X_norm)
        if len(windows) == 0:
            return None

        dataset = TensorDataset(torch.from_numpy(windows).float())
        loader = DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
        
        self.model.eval()
        self.sae.eval()
        
        all_attributions = []
        
        with torch.no_grad():
            for (batch_windows,) in loader:
                batch_windows = batch_windows.to(self.device)
                
                # Get latent vectors
                x_permuted = batch_windows.permute(0, 2, 1)
                encoded_cnn = self.model.encoder_cnn(x_permuted)
                encoded_tcn = self.model.encoder_tcn(encoded_cnn)
                encoded_flat = encoded_tcn.flatten(start_dim=1)
                latent_vec = self.model.fc_encode(encoded_flat)
                
                # Get SAE feature activations
                if self.sae_scaler is not None:
                    latent_np = latent_vec.cpu().numpy()
                    latent_normalized = self.sae_scaler.transform(latent_np)
                    latent_normalized = torch.FloatTensor(latent_normalized).to(self.device)
                else:
                    latent_normalized = latent_vec
                
                _, activations = self.sae(latent_normalized)
                all_attributions.append(activations.cpu().numpy())
        
        # Combine all attributions
        attributions = np.vstack(all_attributions)
        
        # Get top-k most activated features for each window
        top_features = []
        for i in range(len(attributions)):
            top_indices = np.argsort(attributions[i])[-top_k:][::-1]
            top_values = attributions[i][top_indices]
            top_features.append({
                'indices': top_indices,
                'values': top_values,
                'window_idx': i
            })
        
        return {
            'attributions': attributions,
            'top_features': top_features,
            'feature_dim': attributions.shape[1]
        }
    
    def save_sae_model(self, path):
        """Save the trained SAE model"""
        if self.sae is None:
            print("⚠️ No SAE model to save")
            return
        
        checkpoint = {
            'model_state_dict': self.sae.state_dict(),
            'input_dim': self.sae.input_dim,
            'hidden_dim': self.sae.hidden_dim,
            'sparsity_weight': self.sae.sparsity_weight,
            'scaler': self.sae_scaler,
            'irrelevant_indices': self.irrelevant_indices,
            'sae_config': self.sae_config
        }
        
        torch.save(checkpoint, path)
        print(f"✅ SAE model saved to {path}")
