# -*- coding: utf-8 -*-
# run_hta_ad_multivariate.py
#
# 在多变量数据集上运行HTA-AD并记录结果

import os
import sys
import pandas as pd
import random
import time
from tqdm import tqdm
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import precision_recall_curve


# --- Matplotlib和Seaborn设置 ---
try:
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid")
    # 检查是否有中文字体
    if 'SimHei' in [f.name for f in plt.matplotlib.font_manager.fontManager.ttflist]:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        print("🎨 使用中文字体: SimHei")
except Exception:
    print("🎨 字体设置失败，将使用默认字体")


# --- 路径设置 ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- TSB-AD导入 ---
from TSB_AD.utils.slidingWindows import find_length_rank
from TSB_AD.evaluation.metrics import get_metrics

# --- 用户模型导入 ---
from benchmark_exp.hta_ad import HTA_AD

# --- 自定义多变量数据加载器 ---
class MultivariateDataset:
    def __init__(self, dataset_path):
        df = pd.read_csv(dataset_path).dropna()
        try:
            train_size_str = os.path.basename(dataset_path).split('_')[-3]
            self.train_size = int(train_size_str)
        except (ValueError, IndexError):
            self.train_size = int(len(df) * 0.7)

        self.data = df.iloc[:, 0:-1].values.astype(float)
        self.label = df.iloc[:, -1].astype(int).to_numpy()
        self.train = self.data[:self.train_size]
        self.name = os.path.basename(dataset_path)

# --- 配置 ---
DATASET_ROOT = 'Datasets/TSB-AD-M/'
# 定义结果目录
OUTPUT_DIR = 'hta_ad_multivariate_results/'

# --- 模型定义 ---
MODELS = {
    'HTA_AD': {
        'class': HTA_AD,
        'params': {'window_size': 128, 'epochs': 30, 'lr': 1e-3, 'batch_size': 64, 'latent_dim': 32, 'tcn_channels': [32,32,32], 'downsample_stride': 4, 'gpu': 0},
        'type': 'user'
    }
}

# --- 可视化函数 ---
def create_visualizations(filename, data, label, output, train_size, save_dir, model_name):
    """
    创建多面板可视化以显示检测结果和异常分数
    """
    os.makedirs(save_dir, exist_ok=True)
    try:
        # 如果数据是多变量的，选择第一个变量进行可视化
        if len(data.shape) > 1 and data.shape[1] > 1:
            plotting_data = data[:, 0]  # 选择第一个特征
            data_label = "Feature 1"
        else:
            plotting_data = data.flatten()
            data_label = "Value"
            
        df = pd.DataFrame({
            'value': plotting_data,
            'score': output,
            'label': label
        })
        
        precision, recall, thresholds = precision_recall_curve(df['label'], df['score'])
        f1_scores = np.divide(2 * recall * precision, recall + precision, out=np.zeros_like(recall), where=(recall + precision) != 0)
        best_f1_idx = np.argmax(f1_scores)
        smart_threshold = thresholds[best_f1_idx] if len(thresholds) > best_f1_idx else 0.5
        df['pred'] = (df['score'] >= smart_threshold).astype(int)

        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(18, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1, 1]})
        
        ax1.plot(df.index, df['value'], color='cornflowerblue', alpha=0.9, label=data_label, zorder=1)
        true_anomalies = df[df['label'] == 1]
        if not true_anomalies.empty:
            ax1.scatter(true_anomalies.index, true_anomalies['value'], color='red', marker='o', s=50, zorder=2, label=f'True Anomalies ({len(true_anomalies)})')
        ax1.axvline(x=train_size, color='seagreen', linestyle='--', linewidth=2, label='Train/Test Split')
        ax1.set_title(f'{model_name} Detection Results: {os.path.basename(filename)}', fontsize=16)
        ax1.set_ylabel(data_label, fontsize=12)
        ax1.legend(loc='upper right')

        ax2.plot(df.index, df['score'], color='darkviolet', label=f'{model_name} Anomaly Score')
        ax2.axhline(y=smart_threshold, color='darkorange', linestyle='--', linewidth=2, label=f'Smart Threshold={smart_threshold:.4f}')
        ax2.fill_between(df.index, df['score'], smart_threshold, where=df['score'] >= smart_threshold, color='darkviolet', alpha=0.3, interpolate=True)
        ax2.set_ylabel('Anomaly Score', fontsize=12)
        ax2.legend(loc='upper right')

        ax3.fill_between(df.index, 0, 1, where=df['label'] == 1, color='lightcoral', alpha=0.8, step='mid', label='Ground Truth')
        ax3.fill_between(df.index, 1, 2, where=df['pred'] == 1, color='cornflowerblue', alpha=0.8, step='mid', label='Prediction')
        ax3.set_yticks([0.5, 1.5])
        ax3.set_yticklabels(['True', 'Pred'])
        ax3.set_xlabel('Time Step', fontsize=12)
        ax3.legend(loc='upper right')

        plt.tight_layout()
        save_path = os.path.join(save_dir, f"{model_name}_{os.path.basename(filename).replace('.csv', '')}_detection.png")
        plt.savefig(save_path, dpi=200)
        plt.close(fig)

    except Exception as e:
        print(f"❌ 创建可视化失败: {e}")
        import traceback
        traceback.print_exc()


def run_single_experiment(model_name, model_info, dataset, output_dir):
    try:
        train_data, full_data, label = dataset.train, dataset.data, dataset.label
        dataset_name = dataset.name
        
        print(f"  -> 运行模型: {model_name}")
        start_time = time.time()
        
        # 初始化模型
        model_type = model_info.get('type', 'user')
        if model_type == 'user':
            model = model_info['class'](HP=model_info['params'])
        else: # baseline
            model = model_info['class'](**model_info['params'])
        
        # 训练模型
        model.fit(train_data)

        # 获取评分和运行时间
        scores = model.decision_function(full_data)
        runtime = time.time() - start_time
        
        min_len = min(len(scores), len(label))
        scores, label = scores[:min_len], label[:min_len]
        
        # --- 保存结果 ---
        # 1. 保存训练损失
        if hasattr(model, 'training_history') and model.training_history:
            loss_path = os.path.join(output_dir, f"{model_name}_loss_history.json")
            with open(loss_path, 'w') as f:
                json.dump(model.training_history, f)

        # 2. 保存指标
        slidingWindow = find_length_rank(full_data[:min_len], rank=1)
        results = get_metrics(scores, label, slidingWindow=slidingWindow)
        results['runtime(s)'] = runtime
        
        metrics_df = pd.DataFrame([results])
        metrics_path = os.path.join(output_dir, f"{model_name}_metrics.csv")
        metrics_df.to_csv(metrics_path, index=False)

        # 3. 保存可视化
        create_visualizations(
            filename=dataset_name,
            data=full_data[:min_len],
            label=label,
            output=scores,
            train_size=dataset.train_size,
            save_dir=output_dir,
            model_name=model_name
        )
        
        print(f"     ✅ 完成。结果已保存到 {output_dir}")
        return results

    except Exception as e:
        print(f"❌ 在{dataset_name}上运行{model_name}时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    all_datasets = sorted([f for f in os.listdir(DATASET_ROOT) if f.endswith('.csv')])
    
    if not all_datasets:
        print(f"❌ {DATASET_ROOT}中没有找到数据集，退出。")
        return
        
    print(f"🔬 在{len(all_datasets)}个多变量数据集上运行HTA-AD...")
    print(f"   详细结果将保存到: {OUTPUT_DIR}")
    print("   模型:", list(MODELS.keys()))

    all_results = []
    
    for dataset_name in tqdm(all_datasets, desc="总进度"):
        dataset_path = os.path.join(DATASET_ROOT, dataset_name)
        
        # 为这个数据集创建专门的输出目录
            dataset_output_dir = os.path.join(OUTPUT_DIR, dataset_name.replace('.csv', ''))
            os.makedirs(dataset_output_dir, exist_ok=True)

        print(f"\n--- 处理数据集: {dataset_name} ---")
        
        for model_name, model_info in MODELS.items():
            # 检查这个模型和数据集的结果是否已存在
            metrics_path = os.path.join(dataset_output_dir, f"{model_name}_metrics.csv")
            if os.path.exists(metrics_path):
                print(f"     ⏩ 跳过对{dataset_name}上的{model_name}，结果已存在。")
                # 仍然加载旧结果以包含在最终摘要中
                try:
                    res_df = pd.read_csv(metrics_path)
                    results = res_df.to_dict('records')[0]
                except Exception as e:
                    print(f"     ⚠️ 无法重新加载{model_name}的现有结果: {e}")
                    results = None # 如果加载失败，确保results为None
            else:
                results = run_single_experiment(model_name, model_info, MultivariateDataset(dataset_path), dataset_output_dir)
            
            if results:
                results['model'] = model_name
                results['dataset'] = dataset_name
                all_results.append(results)
    
    if not all_results:
        print("\n❌ 未生成任何结果。检查错误或所有数据集是否已被跳过。退出。")
        return

    # --- 最终摘要 ---
    summary_df = pd.DataFrame(all_results)
    avg_per_model = summary_df.drop(columns=['dataset']).groupby('model').mean()
    
    print("\n\n--- 平均性能 (HTA-AD多变量测试) ---")
    print(avg_per_model.to_string())
    
    # 还将摘要保存到showdown目录中的CSV文件
    summary_path = os.path.join(OUTPUT_DIR, 'average_performance_summary.csv')
    avg_per_model.to_csv(summary_path)
    print(f"\n✅ 平均性能摘要已保存到{summary_path}")

    # 将所有单个结果也保存到一个大文件中
    full_results_file = os.path.join(OUTPUT_DIR, '_full_raw_results.csv')
    summary_df.to_csv(full_results_file, index=False)
    
    print(f"\n\n🎉 多变量测试完成!")
    print(f"📄 摘要已保存到: {summary_path}")
    print(f"📄 完整原始结果已保存到: {full_results_file}")


if __name__ == '__main__':
    main() 