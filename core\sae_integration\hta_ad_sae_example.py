#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTA-AD with SAE Integration Example
Demonstrates usage for both univariate and multivariate time series
"""

import numpy as np
import pandas as pd
import sys
import os
import torch
from sklearn.metrics import roc_auc_score, average_precision_score

# Add TSB-AD path
sys.path.append('TSB-AD')
from TSB_AD.models.HTA_AD_SAE import HTA_AD_SAE

def create_synthetic_data(n_samples=1000, n_features=1, anomaly_ratio=0.05):
    """Create synthetic time series data with anomalies"""
    np.random.seed(42)
    
    # Generate normal data with seasonal patterns
    t = np.linspace(0, 20, n_samples)
    data = np.zeros((n_samples, n_features))
    
    for i in range(n_features):
        # Base signal with different frequencies
        base_signal = (np.sin(2 * np.pi * (i + 1) * 0.1 * t) + 
                      0.5 * np.sin(2 * np.pi * (i + 1) * 0.05 * t))
        # Add noise
        noise = 0.1 * np.random.randn(n_samples)
        data[:, i] = base_signal + noise
    
    # Add anomalies
    labels = np.zeros(n_samples)
    n_anomalies = int(n_samples * anomaly_ratio)
    anomaly_indices = np.random.choice(n_samples, n_anomalies, replace=False)
    
    for idx in anomaly_indices:
        # Add different types of anomalies
        anomaly_type = np.random.choice(['spike', 'level_shift', 'trend'])
        
        if anomaly_type == 'spike':
            # Sudden spike
            data[idx] += np.random.normal(0, 2, n_features)
        elif anomaly_type == 'level_shift':
            # Level shift for a period
            end_idx = min(idx + 20, n_samples)
            data[idx:end_idx] += np.random.normal(1.5, 0.5, (end_idx - idx, n_features))
        elif anomaly_type == 'trend':
            # Trend change
            end_idx = min(idx + 30, n_samples)
            trend = np.linspace(0, 2, end_idx - idx).reshape(-1, 1)
            data[idx:end_idx] += trend
        
        labels[idx:min(idx + 20, n_samples)] = 1
    
    return data, labels

def example_without_pretrained_sae():
    """Example using HTA-AD with SAE but without pretrained model"""
    print("📊 Example 1: HTA-AD with SAE (No Pretraining)")
    print("=" * 60)
    
    # Create synthetic data
    print("🔧 Creating synthetic univariate data...")
    data, labels = create_synthetic_data(n_samples=1000, n_features=1)
    
    # Split into train/test
    split_idx = int(0.7 * len(data))
    train_data = data[:split_idx]
    test_data = data[split_idx:]
    test_labels = labels[split_idx:]
    
    print(f"   Train samples: {len(train_data)}")
    print(f"   Test samples: {len(test_data)}")
    print(f"   Anomaly ratio: {np.mean(test_labels):.2%}")
    
    # Configure HTA-AD with SAE
    HP = {
        'window_size': 64,
        'epochs': 20,
        'lr': 1e-3,
        'batch_size': 32,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2,
        'gpu': 0 if torch.cuda.is_available() else -1
    }
    
    sae_config = {
        'hidden_dim': 128,
        'sparsity_weight': 0.01,
        'purification_strength': 0.3
    }
    
    # Initialize model
    detector = HTA_AD_SAE(HP=HP, normalize=True, sae_config=sae_config)
    
    # Train model
    print("🚀 Training HTA-AD...")
    detector.fit(train_data)
    
    # Get anomaly scores
    print("🔍 Computing anomaly scores...")
    scores = detector.decision_function(test_data)
    
    # Evaluate performance
    auc_roc = roc_auc_score(test_labels, scores)
    auc_pr = average_precision_score(test_labels, scores)
    
    print(f"✅ Results:")
    print(f"   AUC-ROC: {auc_roc:.4f}")
    print(f"   AUC-PR: {auc_pr:.4f}")
    
    return detector

def example_with_pretrained_sae():
    """Example using HTA-AD with pretrained SAE"""
    print("\n📊 Example 2: HTA-AD with Pretrained SAE")
    print("=" * 60)
    
    # Check if pretrained SAE exists
    pretrained_path = "pretrained_sae.pth"
    if not os.path.exists(pretrained_path):
        print(f"⚠️ Pretrained SAE not found at {pretrained_path}")
        print("   Run sae_pretraining_script.py first to create pretrained model")
        return None
    
    # Create synthetic multivariate data
    print("🔧 Creating synthetic multivariate data...")
    data, labels = create_synthetic_data(n_samples=1000, n_features=3)
    
    # Split into train/test
    split_idx = int(0.7 * len(data))
    train_data = data[:split_idx]
    test_data = data[split_idx:]
    test_labels = labels[split_idx:]
    
    print(f"   Train samples: {len(train_data)}")
    print(f"   Test samples: {len(test_data)}")
    print(f"   Features: {data.shape[1]}")
    print(f"   Anomaly ratio: {np.mean(test_labels):.2%}")
    
    # Configure HTA-AD with pretrained SAE
    HP = {
        'window_size': 64,
        'epochs': 15,  # Fewer epochs since we have pretrained SAE
        'lr': 1e-3,
        'batch_size': 32,
        'latent_dim': 32,
        'tcn_channels': [32, 32, 32],
        'cnn_channels': 16,
        'downsample_stride': 2,
        'gpu': 0 if torch.cuda.is_available() else -1
    }
    
    sae_config = {
        'hidden_dim': 128,
        'sparsity_weight': 0.01,
        'purification_strength': 0.5  # Higher strength with pretrained model
    }
    
    # Initialize model with pretrained SAE
    detector = HTA_AD_SAE(
        HP=HP, 
        normalize=True, 
        sae_config=sae_config,
        pretrained_sae_path=pretrained_path
    )
    
    # Train model
    print("🚀 Training HTA-AD with pretrained SAE...")
    detector.fit(train_data)
    
    # Get anomaly scores
    print("🔍 Computing anomaly scores...")
    scores = detector.decision_function(test_data)
    
    # Evaluate performance
    auc_roc = roc_auc_score(test_labels, scores)
    auc_pr = average_precision_score(test_labels, scores)
    
    print(f"✅ Results:")
    print(f"   AUC-ROC: {auc_roc:.4f}")
    print(f"   AUC-PR: {auc_pr:.4f}")
    
    # Get feature attribution
    print("🔍 Getting feature attribution...")
    attribution = detector.get_feature_attribution(test_data[:100], top_k=5)
    
    if attribution:
        print(f"   Feature dimension: {attribution['feature_dim']}")
        print(f"   Top features for first anomaly window:")
        
        # Find first anomaly window
        anomaly_windows = [i for i, feat in enumerate(attribution['top_features']) 
                          if np.any(test_labels[feat['window_idx']*16:(feat['window_idx']+1)*16])]
        
        if anomaly_windows:
            first_anomaly = attribution['top_features'][anomaly_windows[0]]
            for j, (idx, val) in enumerate(zip(first_anomaly['indices'], first_anomaly['values'])):
                print(f"     Feature #{idx}: {val:.4f}")
    
    return detector

def example_tsb_ad_integration():
    """Example using TSB-AD model wrapper"""
    print("\n📊 Example 3: TSB-AD Integration")
    print("=" * 60)
    
    try:
        from TSB_AD.model_wrapper import run_Semisupervise_AD
        
        # Create synthetic data
        data, labels = create_synthetic_data(n_samples=800, n_features=2)
        
        # Split data
        split_idx = int(0.6 * len(data))
        train_data = data[:split_idx]
        test_data = data[split_idx:]
        test_labels = labels[split_idx:]
        
        print(f"   Using TSB-AD wrapper...")
        print(f"   Train: {len(train_data)}, Test: {len(test_data)}")
        
        # Note: This would require adding HTA_AD_SAE to the model wrapper
        # For now, we'll show how it would be called
        print("   To use with TSB-AD wrapper, add HTA_AD_SAE to model_wrapper.py:")
        print("   scores = run_Semisupervise_AD('HTA_AD_SAE', train_data, test_data)")
        
    except ImportError:
        print("   TSB-AD not available for integration example")

def main():
    """Main function to run all examples"""
    print("🚀 HTA-AD with SAE Integration Examples")
    print("=" * 80)
    
    # Import torch here to check availability
    try:
        import torch
        print(f"🔧 PyTorch available: {torch.__version__}")
        print(f"🔧 CUDA available: {torch.cuda.is_available()}")
    except ImportError:
        print("❌ PyTorch not available!")
        return
    
    # Example 1: Basic usage without pretraining
    detector1 = example_without_pretrained_sae()
    
    # Example 2: With pretrained SAE
    detector2 = example_with_pretrained_sae()
    
    # Example 3: TSB-AD integration
    example_tsb_ad_integration()
    
    print("\n🎉 All examples completed!")
    print("\n💡 Next steps:")
    print("   1. Run sae_pretraining_script.py to create pretrained SAE")
    print("   2. Add HTA_AD_SAE to TSB-AD model wrapper for full integration")
    print("   3. Use get_feature_attribution() for interpretable anomaly detection")

if __name__ == "__main__":
    main()
